{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "mkdir",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "command": "mkdir",
            "args": [
                "-Force",
                "build"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        },
        {
            "label": "qmake-debug",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin/qmake.exe",
            "args": [
                "../${workspaceFolderBasename}.pro",
                "-spec",
                "win32-g++",
                "\"CONFIG+=debug\"",
                "\"CONFIG+=console\""
            ],
            "dependsOn": [
                "mkdir"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "make-debug",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"
                }
            },
            "command": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/mingw32-make.exe",
            "args": [
                "-f",
                "Makefile.Debug",
                "-j16"
            ],
            "dependsOn": [
                "qmake-debug"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "run-debug",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}/build/debug",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}"
                }
            },
            "command": "${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-debug"
            ],
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "qmake-release",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin/qmake.exe",
            "args": [
                "../${workspaceFolderBasename}.pro",
                "-spec",
                "win32-g++",
                "\"CONFIG+=qtquickcompiler\""
            ],
            "dependsOn": [
                "mkdir"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "qmake-release-console",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin/qmake.exe",
            "args": [
                "../${workspaceFolderBasename}.pro",
                "-spec",
                "win32-g++",
                "\"CONFIG+=qtquickcompiler\"",
                "\"CONFIG+=console\"",
                "\"DESTDIR=release_console\"",
                "-o",
                "Makefile.Release.Console"
            ],
            "dependsOn": [
                "mkdir"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "make-release",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"
                }
            },
            "command": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/mingw32-make.exe",
            "args": [
                "-f",
                "Makefile.Release",
                "-j16"
            ],
            "dependsOn": [
                "qmake-release"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "make-release-console",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"
                }
            },
            "command": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/mingw32-make.exe",
            "args": [
                "-f",
                "Makefile.Release.Console",
                "-j16"
            ],
            "dependsOn": [
                "qmake-release-console"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "run-release",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}/build/release",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}"
                }
            },
            "command": "${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-release"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "run-release-console",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}/build/release_console",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}"
                }
            },
            "command": "${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-release-console"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "clean",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/mingw32-make.exe",
            "args": [
                "clean"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            }
        },
        {
            "label": "run-debug-with-console",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build/debug",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}"
                }
            },
            "command": ".\\${workspaceFolderBasename}.exe",
            "group": {
                "kind": "test",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "dependsOn": [
                "make-debug"
            ]
        },
        {
            "label": "build-and-run-debug",
            "dependsOn": [
                "make-debug",
                "run-debug-with-console"
            ],
            "dependsOrder": "sequence",
            "group": {
                "kind": "test",
                "isDefault": true
            }
        },
        {
            "label": "⚡ Fast Incremental Build",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "MAKEFLAGS": "-j16"
                }
            },
            "command": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/mingw32-make.exe",
            "args": [
                "-f",
                "Makefile.Debug"
            ],
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "🚀 Quick Run",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml"
                }
            },
            "command": "${workspaceFolder}\\build\\debug\\${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-debug"
            ],
            "group": {
                "kind": "test",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "🏃 Build & Run",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}",
                    "LANG": "zh_CN.UTF-8",
                    "LC_ALL": "zh_CN.UTF-8"
                }
            },
            "command": "${workspaceFolder}\\build\\debug\\${workspaceFolderBasename}.exe",
            "dependsOn": [
                "⚡ Fast Incremental Build"
            ],
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "🚀 Quick Run Release",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}",
                    "LANG": "zh_CN.UTF-8",
                    "LC_ALL": "zh_CN.UTF-8"
                }
            },
            "command": "${workspaceFolder}\\build\\release\\${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-release"
            ],
            "group": {
                "kind": "test",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        },
        {
            "label": "🚀 Quick Run Release Console",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
                    "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",
                    "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",
                    "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
                    "QT_FORCE_STDERR_LOGGING": "1",
                    "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}",
                    "LANG": "zh_CN.UTF-8",
                    "LC_ALL": "zh_CN.UTF-8"
                }
            },
            "command": "${workspaceFolder}\\build\\release_console\\${workspaceFolderBasename}.exe",
            "dependsOn": [
                "make-release-console"
            ],
            "group": {
                "kind": "test",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            }
        }
    ]
}
