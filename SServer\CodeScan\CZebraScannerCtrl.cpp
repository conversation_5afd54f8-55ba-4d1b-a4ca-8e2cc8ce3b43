#include<QDebug>
#include <QCoreApplication>
#include"CZebraScannerCtrl.h"
#include<QThread>

CZebraScannerCtrl::CZebraScannerCtrl(QString strSerialName,
                                     QString m_strBandRate)
{
    m_bInitOk =false;
    m_pThreadForInit = new QThread();
    m_ScanWorkMode = Mode_SetParam;
    m_readBuf.clear();
    m_readBuf="";
    pExtractScannerThread = new CExtractScannerThread(strSerialName ,m_strBandRate);
    connect(pExtractScannerThread,SIGNAL(sigReciveSeirlaData(QByteArray)),this,SLOT(slotReceSerialData(QByteArray)));
    connect(this,SIGNAL(sigWriteData(QByteArray)),pExtractScannerThread,SLOT(sendFrameData(QByteArray)));
    connect(this,SIGNAL(sigWaitStatusFeedBack(int,int)),this,SLOT(slotWaitStatusFeedBack(int,int))); 
    this->moveToThread(m_pThreadForInit);
    if( pExtractScannerThread->GetIsOpenSerialPort())
    {
        connect(m_pThreadForInit, &QThread::started, this,&CZebraScannerCtrl::InitScanCodeSetting);
        m_pThreadForInit->start();
    }
    //m_ScanCodeStatus = Mode_ScanCode_Ready; //放到了InitScanCodeSetting函数
}


void CZebraScannerCtrl::InitScanCodeSetting()
{
     QString strLog;
    m_ScanWorkMode = Mode_SetParam;
    //防止debug中断再启动
    //先唤醒00  再发 开启ACK  间隔20ms发送
    int iStatus =0 ;
    int iReDoCnt =5;
    int iCnt =0;
    while(iCnt<iReDoCnt)
    {
        _doSendCmd("00",0);//没回复的,有可能没唤醒成功的,放到串口发00也是不回复的
        _doSendCmd(ZebraScannerCMD_OpenAck,1,3000);//开启ACK ,这里两种情况，可能回复（重新断电）可能不回复（没有断电到的情况，所以这种情况有可能报TimeOut）
        //设置强制需要回复等3s，预防回复的情况，等3S,要不可能出现在下下面发送的指令其实回复的是“07C60400FF9F01FD90”

        iStatus=_doSendCmd(ZebraScannerCMD_FactorySet,1,3000);// //超时时间3s    //设置出厂模式，这里如果没有回复的话，说明没有开启ACK成功，应该继续循环
        if(iStatus ==1)
        {
            break;
        }
        iCnt++;
    }

    if(iStatus!=1)
    {
        qDebug()<<__FUNCTION__<<"ZebraScannerCtrl can't set FactoryParam";
        m_bInitOk =false;
        return ;
    }

    int iStatus1,iStatus2,iStatus3,iStatus4 ;
    iStatus1 = iStatus2 = iStatus3 =iStatus4 =0;
    //step3
    //先关掉唤醒功能;    //放第一步，因为要确认唤醒已经关闭才能进行其他操作，需要ACK回复是否关闭成功
    iStatus1=_doSendCmd(ZebraScannerCMD_CloseWake,1);//
    //关ACK
    iStatus2= _doSendCmd(ZebraScannerCMD_CloseAck,1);
    //开打包发送
    iStatus3=_doSendCmd(ZebraScannerCMD_DataPacket);
    //开瞄准模式
    iStatus4=_doSendCmd(ZebraScannerCMD_FocusMode);
   // _doSendCmd(ZebraScannerCMD_CloseBeef);
    if(iStatus1!=1 || iStatus2!=1|| iStatus3!=1|| iStatus4!=1)
    {
        strLog= QString(__func__)+",Init ZebraScanner Error";
        qDebug()<<strLog;
        emit sigError(FT_BarcodeScanner_InitFailed, strLog);
        m_bInitOk =false;
        return ;
    }
    m_ScanCodeStatus = Mode_ScanCode_Ready;
    m_bInitOk =true;
    qDebug()<<__FUNCTION__<<"Init ZebraScanner OK";
    return ;
}

CZebraScannerCtrl::~CZebraScannerCtrl()
{

}

int CZebraScannerCtrl::slotWaitStatusFeedBack(int iNeedACKReply ,int _mode)
{
    int iMaxTime=7000;
    if( ( _mode ==Mode_SetParam  || _mode == Mode_CloseScan) && iNeedACKReply ==0)//设置参数模式，ACK已经关闭的情况，什么也不会回复的，直接sleep返回成功（前提已经关闭唤醒及ACK）
    {
        QThread::msleep(50);
        qDebug()<<__FUNCTION__<<"Set Param or closescan Direct ok";
        return  1;
    }

    int iSleep    = 50;
    int iSleepSum = iMaxTime /iSleep;
    int iCnt =0;
    while(iCnt <iSleepSum)
    {
        if(m_readBuf!="")
        {
            if(m_readBuf.contains("\x05\xD1\x00\x00\x01\xFF") && iNeedACKReply == 1)  //还没唤醒
            {
                qDebug()<<__FUNCTION__<<"Scan  not ready";
                m_readBuf.clear();
                m_readBuf="";
                return  -1;
            }
            if(m_readBuf.contains("\x04\xD0\x00\x00\xFF") && _mode ==Mode_SetParam )  // 接收到这个代表已经唤醒成功，设置参数（关闭唤醒）已经成功
            {
                qDebug()<<__FUNCTION__<<"Set Param ok";
                m_readBuf.clear();
                m_readBuf="";
                return  1;
            }
            if(_mode == Mode_ScanCode && m_ScanCodeStatus ==Mode_ScanCode_DecodeDataOK)  //不是设置参数模式，即是扫码模式，已经在初始化的时候将唤醒，ACK关闭，所以接受的直接是结果
            {
                qDebug()<<__FUNCTION__<<"Receive  scan Rs ok,Code="<<m_readBuf;
                m_ScanCodeStatus = Mode_ScanCode_Ready;
                TranslateScanRs();
                return  1;
            }
        }
        if(m_ScanWorkMode==Mode_CloseScan )
        {
            qDebug()<<__FUNCTION__<<"Close Scan now";
            return  1;
        }
        qApp->processEvents(QEventLoop::AllEvents,iSleep);
        QThread::msleep(iSleep);
        iCnt++;
    }

    if(iCnt>=iSleepSum)
    {
        if(_mode == Mode_ScanCode)
        {
            qDebug()<<"Scan TimeOut";
            emit sigZebraScannerRs("",0);
            emit sigError(FT_BarcodeScanner_ScanFailed_FastInsertion, "DoSingleScanCmd");
        }
        return  0;  //超时
    }

    qDebug()<<__FUNCTION__<<"iCnt="<<iCnt<<",iSleepSum="<<iSleepSum;
    return  1;
}

void CZebraScannerCtrl::slotReceSerialData(QByteArray data)
{
    recvMutex.lock();
    if(data.isEmpty())
    {
        recvMutex.unlock();
        return;
    }
    if(m_ScanWorkMode == Mode_SetParam)  //设置参数是不起用打包发送的
    {
        m_readBuf =m_readBuf + data;
        qDebug()<<__FUNCTION__<<"m_readBuf="<<m_readBuf;
    }

    if(m_ScanWorkMode == Mode_ScanCode)
    {
        if(m_ScanCodeStatus ==Mode_ScanCode_Ready)
        {
            m_readBuf =m_readBuf + data;
            int iLength = static_cast<int>(m_readBuf[0]) & 0xFF;
            qDebug()<<__FUNCTION__<<"m_readBuf="<<m_readBuf<<",Msg ="<<m_readBuf.toHex().toUpper()<<"PackateSize="<<iLength;
            if(iLength+2 ==m_readBuf.size() )
            {
                m_ScanCodeStatus = Mode_ScanCode_DecodeDataOK;
            }
            if(iLength+2 !=m_readBuf.size() )
            {
                qDebug()<<__FUNCTION__<<"not Receive Complete!";
            }
        }
        else
        {
            qDebug()<<__FUNCTION__<<"m_ScanCodeStatus="<<m_ScanCodeStatus;
        }
    }
    if(m_ScanWorkMode == Mode_CloseScan)
    {
     qDebug()<<"Mode is Mode_CloseScan,data ="<<data;
    }
    recvMutex.unlock();
    return;
}

QString CZebraScannerCtrl::GetLastScanRs()
{
    return strLastScanRs;
}

int CZebraScannerCtrl::TranslateScanRs()// 1：成功    -1 fail
{
    QString strRsData,strLog;
    //打包接收的话第6个开始的才是条码信息，最后两个是checksum,参考SSI.pdf  第2-26页
    strRsData=QString::fromLatin1(m_readBuf.mid(5,m_readBuf.length()-5-2));

    m_readBuf.clear();
    m_readBuf="";
    if(strRsData!="NULL")   //判断格式是否符合要求  //产品货号+生产年+月+日+流水号+有效期 eg.W061 24 05  01  24
    {
        strLastScanRs = strRsData;
        bool ok;
        if(/*strRsData.size()!= 14 || */ strRsData.at(0)!="W" /*||  strRsData.mid(6, 2).toInt(&ok)>=13*/ )
        {
            strLog= QString(__func__)+",strRsData donot match,strRsData="+strRsData;
            qDebug()<<strLog;
            emit sigError(FT_BarcodeScanner_FormatMismatch, strLog);
            emit sigZebraScannerRs(strRsData,-1);
            return -1;
        }
    }
    emit sigZebraScannerRs(strRsData,1);
    return 1;
}

void CZebraScannerCtrl::DoSingleScanCmd()//执行扫描指令  1：成功   0 超时
{
    strLastScanRs="";
    QString strLog;
    if(m_bInitOk ==false)
    {
        strLog= QString(__func__)+",Init ZebraScanner error,can not do this Fun";
        qDebug()<<strLog;
        emit sigError(FT_BarcodeScanner_InitFailed, strLog);
        return  ;
    }
    QString strMsg=ZebraScannerCMD_SingleScanTriger;
    // pExtractScannerThread->SendFrameData(strMsg.toLatin1()); 这样写不行，跨线程？
    m_ScanWorkMode = Mode_ScanCode; //先设置模式，再发指令，要不在slotReceSerialData有问题
    emit sigWriteData(QByteArray::fromHex(strMsg.toLatin1()));
    emit sigWaitStatusFeedBack(0,(int)m_ScanWorkMode);
    return ;
}


/**
 * @brief CZebraScannerCtrl::DoSendCmd
 * @param strMsg
 * @param iNeedACKReply 关闭ACK后，之后发送的指令将不再回复
 * @return
 */
int CZebraScannerCtrl::_doSendCmd(QString strMsg,int iNeedACKReply,int iMaxTime )//执行扫描指令  1：成功   0 超时   -1 fail
{
    int iStatus;
    int iResendCnt =15, iCnt =0, iOK=0;
    while(iCnt<iResendCnt)
    {
        // pExtractScannerThread->SendFrameData(strMsg.toLatin1()); 这样写不行，跨线程？
        emit sigWriteData(QByteArray::fromHex(strMsg.toLatin1()));
        qDebug()<<__FUNCTION__<<"send cmd="<<strMsg;
        iStatus = slotWaitStatusFeedBack(iNeedACKReply,m_ScanWorkMode);
        if(iStatus ==0)   //超时
        {
            if(strMsg!=ZebraScannerCMD_OpenAck)  //ACK
            {
                qDebug()<<__FUNCTION__<<"Timeout Cmd = "<<strMsg<<",ScanWorkMode="<<m_ScanWorkMode;
            }
            return 0;
        }
        if(iStatus ==1)  //正常
        {
            iOK =1;
            break;
        }
        iCnt++;  //失败
    }
    // strRsData = m_readBuf;
    m_readBuf.clear();
    m_readBuf="";
    return iStatus;
}

int CZebraScannerCtrl::CloseSingleScanCmd()
{
    QString strLog;
    if(m_bInitOk ==false)
    {
        strLog= QString(__func__)+",Init ZebraScanner error,can not do this Fun";
        qDebug()<<strLog;
        emit sigError(FT_BarcodeScanner_InitFailed, strLog);
        return  -1;
    }
    int iStatus;
    m_ScanWorkMode = Mode_CloseScan;
    qDebug()<<"close----";
    QString strMsg=ZebraScannerCMD_CloseScan;
    emit sigWriteData(QByteArray::fromHex(strMsg.toLatin1()));
    iStatus = slotWaitStatusFeedBack(0,m_ScanWorkMode);  //已经关闭ACK及一直待机状态
    m_ScanCodeStatus = Mode_ScanCode_Ready; //强制设置为ready状态  vip
    m_readBuf.clear();
    m_readBuf="";
    if(iStatus ==0)  //超时
    {
        return 0;
    }
    return 1;
}



