#ifndef CDEVSTATUS
#define CDEVSTATUS
#include <vector>
#include <algorithm>
#include <QObject>
#include <QMutex>

enum  CompAvrStatus {
    CAS_Available = 0,//可用
    CAS_Unavailable//不可用
};

enum  GrabStatus {
    GS_NotGrabbed ,//!< 未抓取
    GS_HasGrabbed, //!< 已抓取
};

enum CompType
{
    CT_SAMPLE_GRIPPER = 0,
    CT_GANTRY_PUMP,
    CT_PCR_GRIPPER,
};

enum CompIndex
{
    CI_START = 0,
    CI_RIGHT = 0, //右组件
    CI_LEFT,//左组件
    CI_MAX,
};

enum ItemType
{
    IT_Unknown,
    IT_SampleTube,       //!< 样本管
    IT_SampleCap,        //!< 样本盖
    IT_Tip200,           //!< 200ul吸头
    IT_Tip1000,          //!< 1000ul吸头
    IT_PCRTube,          //!< PCR扩增管
    IT_PCRCap,           //!< PCR扩增盖
    IT_PCRPlateCover,    //!< PCR扩增区域盖子
    IT_PCRTubeAndCap, //盖了盖的PCR扩增管
    IT_MAX,
    // 可以继续添加其他类型
};

struct DevComponent {
    CompAvrStatus avrStatus;  // 组件的状态
    GrabStatus grabStatus;//抓取/扎取的状态
    quint8 uiGrabItemType;//抓取的物品类型
    QString strSafePos;//安全的清理位置 默认是一个int数字，如果涉及到多个位置以','隔开
};

// DeviceModule 类定义
class GripperPumpModule : public QObject
{
    Q_OBJECT
public:
    explicit GripperPumpModule(QObject *parent = nullptr);
    ~GripperPumpModule();

    // 设置指定组件的可用状态
    void setComponentAvailability(int iCompIndex, CompAvrStatus avrStatus);
    // 设置指定组件的抓取状态
    void setComponentGrabStatus(int iCompIndex, GrabStatus grabStatus);

    // 设置指定组件的物品类型, 安全放置位置
    // 获取指定组件的物品类型,安全放置位置
    QString getSafePos(int iCompIndex);
    void setSafePos(int iCompIndex, QString strSafePos);
    quint8 getItemType(int iCompIndex);
    void setItemType(int iCompIndex, quint8 uiItemType);
    void setItemProperty(int iCompIndex, quint8 itemType, QString strSafePos);
    void getItemProperty(int iCompIndex, quint8 &uiItemType, QString& strSafePos);

    // 获取指定组件的可用状态
    CompAvrStatus getComponentAvailability(int iCompIndex) const;
    // 获取指定组件的抓取状态
    GrabStatus getComponentGrabStatus(int iCompIndex) const;

    // 设置组件状态
    void setComponentStatus(int iCompIndex, CompAvrStatus avrStatus, GrabStatus grabStatus, quint8 uiItemType);

    // 重命名方法：获取当前可用组件的数量
    int availableCompCount() const;

    // 重命名方法：获取当前第一个可用组件的索引（左组件为1，右组件为0）
    int firstAvailableIndex() const;

    void setComponentStatus(int iCompIndex, CompAvrStatus avrStatus,
                            GrabStatus grabStatus,
                            quint8 uiItemType,
                            QString strSafePos);
    DevComponent getDevComponent(quint8 uiCompIndex);
    void Init();

private:
    DevComponent m_compList[CI_MAX]; //!< 存储组件状态，两个组件分别处理
};


class CDevStatus : public QObject
{
    Q_OBJECT
public:
    static CDevStatus& getInstance();
    void reset();

    void setDevCompStatus(QString strParamsStr);

    // 样本抓手模块操作
    void setSampleGripperAvailability(int iCompIndex, CompAvrStatus avrStatus);
    void setSampleGripperGrabStatus(int iCompIndex, GrabStatus grabStatus);
    void setSampleGripperItemType(int iCompIndex, quint8 itemType);
    void setSampleGripperSafePos(int iCompIndex, QString strSafePos);

    CompAvrStatus getSampleGripperAvailability(int iCompIndex);
    GrabStatus getSampleGripperGrabStatus(int iCompIndex);
    quint8 getSampleGripperItemType(int iCompIndex);
    QString getSampleGripperSafePos(int iCompIndex);
    DevComponent  getSampleGripper(int iCompIndex);
    int sampleGripperAvailableCount();
    int sampleGripperFirstAvailableIndex();

    // 龙门架泵模块操作
    void setGantryPumpAvailability(int iCompIndex, CompAvrStatus avrStatus);
    void setGantryPumpGrabStatus(int iCompIndex, GrabStatus grabStatus);
    void setGantryPumpItemType(int iCompIndex, quint8 itemType);
    void setGantryPumpSafePos(int iCompIndex, QString strSafePos);

    CompAvrStatus getGantryPumpAvailability(int iCompIndex);
    GrabStatus getGantryPumpGrabStatus(int iCompIndex);
    quint8 getGantryPumpItemType(int iCompIndex);
    QString getGantryPumpSafePos(int iCompIndex);

    DevComponent getGantryPump(int iCompIndex);
    int gantryPumpAvailableCount();
    int gantryPumpFirstAvailableIndex();

    // PCR抓手操作
    void setPcrGripperStatus(CompAvrStatus avrStatus, GrabStatus grabStatus, quint8 uiItemType, QString strSafePos);
    void setPcrGripperAvailStatus(CompAvrStatus avrStatus);
    void setPcrGripperGrabStatus(GrabStatus grabStatus);
    void setPcrGripperItemType(quint8 uiItemType);
    void setPcrGripperSafePos(QString strSafePos);

    DevComponent getPcrGripper();
    CompAvrStatus getPcrGripperAvailability();
    GrabStatus getPcrGripperGrabStatus();
    quint8 getPcrGripperItemType();
    QString getPcrGripperSafePos();

    // 持久化操作
    void saveToDB();
    void loadFromDB();
    void saveSinglePropertyToDB(const QString &key, const QVariant &value);

    QString getGantryPumpAvailKey(int iCompIndex);
    QString getGantryPumpGrabKey(int iCompIndex);
    QString getGantryPumpItemKey(int iCompIndex);
    QString getGantryPumpPosKey(int iCompIndex);

    QString getSampleGripperAvailKey(int iCompIndex);
    QString getSampleGripperGrabKey(int iCompIndex);
    QString getSampleGripperItemKey(int iCompIndex);
    QString getSampleGripperPosKey(int iCompIndex);

    void tstInit();

    void setUpdateFlag(bool bUpdate);
    bool getUpdateFlag();
private:
    explicit CDevStatus(QObject *parent = nullptr);
    ~CDevStatus();
    void _onPropertySet(const QString &key, const QVariant &value);
    void _loadSampleGripperAttrFromDB();
    void _loadGantryPumpAttrFromDB();
    void _loadPCRGripperAttrFromDB();
    void _initPCRGripper();
private:
    GripperPumpModule m_sampleGripper; // 样本抓手模块
    GripperPumpModule m_gantryPump; // 龙门架泵模块
    DevComponent m_pcrGripper; //PCR抓手
    QMutex m_mutex;
    QAtomicInt m_bUpdate;//是否需要更新数据到数据状态列表里，自检状态不更新
};

#endif // CDEVSTATUS
