#ifndef DSBCL95_H
#define DSBCL95_H

#include <stdint.h>
#include <stddef.h>
#include <QByteArray>
#include <QVector>

/*
 * @author:mflin
 * @created:2023-9-15
 *
 */

#define kBCLPrefix1      0x02
#define kBCLSuffix1      0x0D
#define kBCLSuffix2      0x0A
#define kACK          0x06
#define kCmdPrefix    "PT0020"
typedef enum{
    BCL_CMD_VERSION,//版本指令
    BCL_CMD_OPEN_SINGLE_TRIG,//启动单次触发
    BCL_CMD_CLOSE_SINGLE_TRIG,//关闭单次触发
    BCL_CMD_OPEN_CONT_TRIG,//启动连续触发
    BCL_CMD_CLOSE_CONT_TRIG,//关闭连续触发
    BCL_CMD_RESET,//复位
    BCL_CMD_ROLL_BACK_FACTORY,//恢复出厂

    BCL_CMD_OPEN_AUTO_TRIG=20,//开启自动触发
    BCL_CMD_CLOSE_AUTO_TRIG,//关闭自动触发
    BCL_CMD_READ_TRIG_STATE,//读取触发状态
    BCL_CMD_READ_ONCE,//只读一次
    BCL_CMD_READ_BARCOE_NUM,//读取条码数量
    BCL_CMD_OPEN_NO_DUPLICATE,//打开输出不同条码(无重复）
    BCL_CMD_CLOSE_NO_DUPLICATE,//关闭输出不同条码(可重复）
    BCL_CMD_CLOSE_ITF,//关闭 ITF 码
    BCL_CMD_OPEN_ITF,//打开 ITF 码
    BCL_CMD_CLOSE_Code_39,//关闭 Code 39 码
    BCL_CMD_OPEN_Code_39,//打开 Code 39 码
    BCL_CMD_CLOSE_Code_128,//关闭 Code 128 码
    BCL_CMD_OPEN_Code_128,//打开 Code 128 码
    BCL_CMD_CLOSE_UPC,//关闭 UPC-A/E 码
    BCL_CMD_OPEN_UPC,//打开 UPC-A/E 码
    BCL_CMD_CLOSE_EAN,//关闭 EAN-8/13 码
    BCL_CMD_OPEN_EAN,//打开 EAN-8/13 码
    BCL_CMD_CLOSE_Codebar,//关闭 Codebar 码
    BCL_CMD_OPEN_Codebar,//打开 Codebar 码
    BCL_CMD_CLOSE_Code_93,//关闭 Code 93 码
    BCL_CMD_OPEN_Code_93,//打开 Code 93 码

    BCL_CMD_FIX_NUM_Code_ITF,//设置交叉25码数据长度（固定，默认10）
    BCL_CMD_MIN_NUM_Code_ITF,//交叉25码的最小数据位2位（特殊，不建议）
    BCL_CMD_MAX_NUM_Code_ITF,//交叉25码的最大数据位30位（特殊，不建议）
    BCL_CMD_MIN_NUM_Code_39,//39码的最小数据位1位
    BCL_CMD_MAX_NUM_Code_39,//39码的最大数据位30位
    BCL_CMD_MIN_NUM_Code_128,//128码的最小数据位1位
    BCL_CMD_MAX_NUM_Code_128,//128码的最大数据位30位
    BCL_CMD_MIN_NUM_UPC,//UPC-A/E 码最小数据位1位
    BCL_CMD_MAX_NUM_UPC,//UPC-A/E 码最大数据位30位
    BCL_CMD_MIN_NUM_EAN,//EAN-8/13 码最小数据位1位
    BCL_CMD_MAX_NUM_EAN,//EAN-8/13 码最大数据位30位
    BCL_CMD_MIN_NUM_Codebar,//Codebar 码最小数据位1位
    BCL_CMD_MAX_NUM_Codebar,//Codebar 码最大数据位30位
    BCL_CMD_MIN_NUM_Code_93,//Code 93 码最小数据位1位
    BCL_CMD_MAX_NUM_Code_93,//Code 93 码最大数据位30位

    BCL_CMD_READ_Code_ITF_Data_Info,
    BCL_CMD_READ_Code_39_Data_Info,
    BCL_CMD_READ_Code_128_Data_Info,
    BCL_CMD_READ_Code_UPC_Data_Info,
    BCL_CMD_READ_Code_32_Data_Info,
    BCL_CMD_READ_Code_EAN_Data_Info,
    BCL_CMD_READ_Code_Codebar_Data_Info,
    BCL_CMD_READ_Code_93_Data_Info,

}BCL_CMD;

typedef enum
{
    BCL_CMD_TYPE_NORMAL,//常规指令
    BCL_CMD_TYPE_SET_NO_PARAM,//设置无参数指令
    BCL_CMD_TYPE_SET_WITH_PARAM,//设置带参数指令
    BCL_CMD_TYPE_DATA,//数据读取指令
}BCL_CMD_TYPE;

typedef enum
{
    BC_TYPE_IFT,//2/5隔行扫描码
    BC_TYPE_39,//39码
    BC_TYPE_32,//32码
    BC_TYPE_UPC = 6,//UPC-A/UPC-E
    BC_TYPE_EAN,//EAN8
    BC_TYE_128,//128码
    BC_TYPE_Pharmacode,//Pharmacode码
    BC_TYPE_EAN_SUFFIX,//EAN码附录
    BC_TYPE_CODEBAR,//Codebar
    BC_TYPE_93,//93码
}BARCODE_TYPE;


typedef enum
{
    BCL_STATE_OK,//有效传输
    BCL_STATE_INVALID_INFO,//无效的信息
    BCL_STATE_INVALID_INFO_LEN,//无效的信息长度
    BCL_STATE_INVALID_BLOCK_CRC_TYPE,//无效的块校验类型
    BCL_STATE_INVALID_BLOCK_CRC,//无效的块校验和
    BCL_STATE_INVALID_DATA_LEN,//数据长度无效
    BCL_STATE_INVALID_INFO_DATA,//无效的信息数据
    BCL_STATE_INVALID_START_ADDR,//无效的起始地址
    BCL_STATE_INVALID_PARAMS,//无效参数集
    BCL_STATE_INVALID_PARAM_TYPE,//无效的参数集类型
}BCL_CMD_STATE;

typedef struct{
    uint8_t head;
    uint8_t data[0];
}bcl_frame;

typedef struct
{
    const char *cmd;//指令数据
    BCL_CMD_TYPE cmdType;//指令类型
    uint16_t timeout;//指令执行超时时间s
    bool bHaveResponse;//是否有结果
}bclItem_t;//条码指令结构

const uint8_t kFramePrefixLength = 1;
const uint8_t kFrameSuffixLength = 2;
/*
 Scanner Packet Format
 | Prefix1 STX(0x02) | Data ... | Suffix 1(0x0D) | Suffix 2(0x0A)
    > Prefix1: default prefix 1 is ASCII STX(0x02)
    > Data: variable number of bytes
    > Suffix: 2 bytes, Suffix1 is 0x0D, Suffix2 is 0x0A
*/
enum Stat
{
    ePrefix,
    eBody,
    eSuffix
};

class BCL
{
public:
    BCL();
    void init();//init the barcode scanner
    //获取特定指令完整协议数据
    bool getCmdData(uint8_t cmdId, char *cmd, uint8_t& cmdLen, uint8_t settingParam/* = 0*/);
    bool isCmdHaveResponse(uint8_t cmdId);
    //获取指令超时时间
    uint16_t getCmdTimeOutValue(uint8_t cmdId);
    void reset();
    void timeoutDetect();
    bool dataIn(uint8_t rx);
    bool parse(QByteArray data);
    void setContinueScan(bool bContinueScan);
    QVector<QString> getAllBarcode();
    void clearAllBarcode();
    QString getLatestResult();
    int getBarCodeSize();
    bool IsDataCmdType(int cmdId);
private:
    bool _detected;//是否接收到响应指令
    uint16_t _maxSize;//支持的最大数据长度
    uint64_t _latestRecvTime;//上个字符的接收时间
    uint8_t *_bufPrefix;
    uint8_t *_bufTail;
    uint8_t *_bufSuffix;
    Stat _stat;
    bool _bContinueScan;
    QString _latestResult;
    QVector<QString> _barcodeVect;

};

#endif // DSBCL95_H
