﻿#include "cprojectdb.h"

#include <QVector>
#include <QDebug>
#include "cmainwindow.h"
#include "cglobalconfig.h"
#include <QMutexLocker>

CProjectInformation::CProjectInformation(QObject* parent)
    : QObject(parent)
    , m_dbImpl(CProjectDBImpl::getInstance())
{
    initDataBase();
}

CProjectInformation& CProjectInformation::getInstance()
{
    static CProjectInformation instance;
    return instance;
}

void CProjectInformation::initDataBase()
{
    m_dbImpl.initDataBase();
    this->initData();
}

void CProjectInformation::updateDBFile(const QString& strNewFilePath)
{
    m_dbImpl.updateDBFile(strNewFilePath);
    loadAllProjects();
}

void CProjectInformation::loadAllProjects()
{
    QMutexLocker locker(&m_mutex);
    m_mapProjects.clear();
    m_qProjectNameInformationMap.clear();
    m_strProjectNameList.clear();
    m_strProjectLotList.clear();

    QStringList projectLots = m_dbImpl.getAllProjectLots();
    for (const QString& lot : projectLots) {
        SProjectInformationStruct info = SProjectInformationStruct::fromStringList(m_dbImpl.getProjectInfoFromProjectLot(lot), m_dbImpl.m_strFieldNameProjectList);
        m_mapProjects[lot] = info;
        m_qProjectNameInformationMap[info.projectName] = info;
        m_strProjectNameList << info.projectName;
        m_strProjectLotList << info.projectLot;
    }
}

void CProjectInformation::initData()
{
    loadAllProjects();
}

SProjectInformationStruct CProjectInformation::getInfoFromLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot, SProjectInformationStruct());
}

SProjectInformationStruct CProjectInformation::getInfoFromName(const QString& kstrProjectName)
{
    QMutexLocker locker(&m_mutex);
    return m_qProjectNameInformationMap.value(kstrProjectName, SProjectInformationStruct());
}

QString CProjectInformation::getProjectLotFromProjectName(const QString& kstrProjectName)
{
    QMutexLocker locker(&m_mutex);
    QString strReturn = m_qProjectNameInformationMap.value(kstrProjectName, SProjectInformationStruct{}).projectLot;
    DEBUG_LOG << kstrProjectName << strReturn;
    return strReturn;
}

QString CProjectInformation::getProjectTypeFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    QString strReturn = m_mapProjects.value(kstrProjectLot, SProjectInformationStruct{}).projectType;
    return strReturn;
}

QString CProjectInformation::getProjectNameFromLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    QString strReturn = m_mapProjects.value(kstrProjectLot, SProjectInformationStruct{}).projectName;
    return strReturn;
}

QString CProjectInformation::getProjectLotFromQCCode(const QString& kstrWFQCCode)
{
    QString strReturn = "";
    if (kstrWFQCCode.contains(gk_WFQC))
    {
        QStringList strSampleIDList = kstrWFQCCode.split("+");
        if (strSampleIDList.length() >= 2)
        {
            strReturn = strSampleIDList[1].left(4);// 当前默认批号为4位，后期根据后面日期去掉
        }
    }
    return strReturn;
}

QString CProjectInformation::getProjectLotFromISCode(const QString& kstrWFQCCode)
{
    QString strReturn = "";
    if (kstrWFQCCode.contains(gk_WFIS))
    {
        QStringList strSampleIDList = kstrWFQCCode.split("+");
        if (strSampleIDList.length() >= 2)
        {
            strReturn = strSampleIDList[1].left(4);// 当前默认批号为4位，后期根据后面日期去掉
        }
    }
    return strReturn;
}

QString CProjectInformation::getProjectLotFromFLID(const QString& kstrFLID)
{
    QString strProjectLot = "";
    QStringList strFLIDList = kstrFLID.split("_");
    if (strFLIDList.length() >= 4)
    {
        strProjectLot = strFLIDList.at(strFLIDList.length() - 3);
    }
    return strProjectLot;
}

QStringList CProjectInformation::getAllProjectNames()
{
    QMutexLocker locker(&m_mutex);
    return m_strProjectNameList;
}

QStringList CProjectInformation::getAllProjectLots()
{
    QMutexLocker locker(&m_mutex);
    return m_strProjectLotList;
}

std::tuple<int, int> CProjectInformation::getCTThresholdValueRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.ctThresholdValue.contains("-"))
    {
        QStringList strCTThresholdValueList = sProjectInfoStruct.ctThresholdValue.split("-");
        if (strCTThresholdValueList.length() >= 2)
        {
            iMin = strCTThresholdValueList[0].toInt();
            iMax = strCTThresholdValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getISThresholdValueRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.ISThresholdValue.contains("-"))
    {
        QStringList strISThresholdValueList = sProjectInfoStruct.ISThresholdValue.split("-");
        if (strISThresholdValueList.length() >= 2)
        {
            iMin = strISThresholdValueList[0].toInt();
            iMax = strISThresholdValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getISConcentrationValueRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.ISConcentrationValue.contains("-"))
    {
        QStringList strISConcentrationValueList = sProjectInfoStruct.ISConcentrationValue.split("-");
        if (strISConcentrationValueList.length() >= 2)
        {
            iMin = strISConcentrationValueList[0].toInt();
            iMax = strISConcentrationValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getQCTValueRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 28; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.QCTValueRange.contains("-"))
    {
        QStringList strQCTValueRangeList = sProjectInfoStruct.QCTValueRange.split("-");
        if (strQCTValueRangeList.length() >= 2)
        {
            iMin = strQCTValueRangeList[0].toInt();
            iMax = strQCTValueRangeList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getQConcentrationRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.QConcentrationRange.contains("-"))
    {
        QStringList strQConcentrationRangeList = sProjectInfoStruct.QConcentrationRange.split("-");
        if (strQConcentrationRangeList.length() >= 2)
        {
            iMin = strQConcentrationRangeList[0].toInt();
            iMax = strQConcentrationRangeList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getPCTValueRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 25; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.PCTValueRange.contains("-"))
    {
        QStringList strPCTValueRangeList = sProjectInfoStruct.PCTValueRange.split("-");
        if (strPCTValueRangeList.length() >= 2)
        {
            iMin = strPCTValueRangeList[0].toInt();
            iMax = strPCTValueRangeList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getPConcentrationRangeFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.PConcentrationRange.contains("-"))
    {
        QStringList strPConcentrationRangeList = sProjectInfoStruct.PConcentrationRange.split("-");
        if (strPConcentrationRangeList.length() >= 2)
        {
            iMin = strPConcentrationRangeList[0].toInt();
            iMax = strPConcentrationRangeList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getQCNegativeCTValueFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.QCNegativeCTValue.contains("-"))
    {
        QStringList strQCNegativeCTValueList = sProjectInfoStruct.QCNegativeCTValue.split("-");
        if (strQCNegativeCTValueList.length() >= 2)
        {
            iMin = strQCNegativeCTValueList[0].toInt();
            iMax = strQCNegativeCTValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getQCInternalCtCTValueFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.QCInternalCtCTValue.contains("-"))
    {
        QStringList strQCInternalCtCTValueList = sProjectInfoStruct.QCInternalCtCTValue.split("-");
        if (strQCInternalCtCTValueList.length() >= 2)
        {
            iMin = strQCInternalCtCTValueList[0].toInt();
            iMax = strQCInternalCtCTValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

std::tuple<int, int> CProjectInformation::getQCInternalCtConcentrationValueFromProjectLot(const QString& kstrProjectLot)
{
    int iMin = 10; // 默认最小值
    int iMax = 40; // 默认最大值
    SProjectInformationStruct sProjectInfoStruct = this->getInfoFromLot(kstrProjectLot);
    if (sProjectInfoStruct.QCInternalCtConcentrationValue.contains("-"))
    {
        QStringList strQCInternalCtConcentrationValueList = sProjectInfoStruct.QCInternalCtConcentrationValue.split("-");
        if (strQCInternalCtConcentrationValueList.length() >= 2)
        {
            iMin = strQCInternalCtConcentrationValueList[0].toInt();
            iMax = strQCInternalCtConcentrationValueList[1].toInt();
        }
    }
    return std::make_tuple(iMin, iMax);
}

// 将QStringList中的内容顺序赋值给结构体
void CProjectInformation::_assignValuesToStruct(SProjectInformationStruct& projectInfo, const QStringList& strValuesList)
{
    if (strValuesList.size() != m_dbImpl.getFieldLength()) {
        DEBUG_LOG << "Error: QStringList size does not match the number of fields in the structure.";
        return;
    }

    projectInfo.projectLot = strValuesList[0];
    projectInfo.projectName = strValuesList[1];
    projectInfo.projectType = strValuesList[2];
    projectInfo.ablationExpiration = strValuesList[3];
    projectInfo.sampleCapacity = strValuesList[4];
    projectInfo.internalStandardCapacity = strValuesList[5];
    projectInfo.extractCapacity = strValuesList[6];
    projectInfo.pcrReagentCapacity = strValuesList[7];
    projectInfo.complexSolutionCapacity = strValuesList[8];
    projectInfo.extractName = strValuesList[9];
    projectInfo.tecName = strValuesList[10];
    projectInfo.tubeCount = strValuesList[11];
    projectInfo.tubeInfo = strValuesList[12];
    projectInfo.calculation = strValuesList[13];
    projectInfo.autoThreshold = strValuesList[14];
    projectInfo.thresholdValue = strValuesList[15];
    projectInfo.peakHeightValue = strValuesList[16];
    projectInfo.meltingValue = strValuesList[17];
    projectInfo.crossInterference = strValuesList[18];
    projectInfo.ctThresholdValue = strValuesList[19];
    projectInfo.ISThresholdValue = strValuesList[20];
    projectInfo.ISConcentrationValue = strValuesList[21];
    projectInfo.liftValue = strValuesList[22];
    projectInfo.ISCapacity = strValuesList[23];
    projectInfo.QCCapacity = strValuesList[24];
    projectInfo.QCTValueRange = strValuesList[25];
    projectInfo.QConcentrationRange = strValuesList[26];
    projectInfo.PCTValueRange = strValuesList[27];
    projectInfo.PConcentrationRange = strValuesList[28];
    projectInfo.QCNegativeCTValue = strValuesList[29];
    projectInfo.QCInternalCtCTValue = strValuesList[30];
    projectInfo.QCInternalCtConcentrationValue = strValuesList[31];
    projectInfo.addTime = strValuesList[32];
    projectInfo.sampleCalibrationCapacity = strValuesList[33];
    projectInfo.internalStandardCalibrationCapacity = strValuesList[34];
    projectInfo.extractCalibrationCapacity = strValuesList[35];
    projectInfo.pcrReagentCalibrationCapacity = strValuesList[36];
    projectInfo.complexSolutionCalibrationCapacity = strValuesList[37];
    projectInfo.remark = strValuesList[38];
    projectInfo.remarks1 = strValuesList[39];
    projectInfo.remarks2 = strValuesList[40];
    projectInfo.remarks3 = strValuesList[41];
}

int CProjectInformation::getFieldLength()
{
    return m_dbImpl.getFieldLength();
}


QStringList CProjectInformation::getProjectInfoStringFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).toStringList(m_dbImpl.m_strFieldNameProjectList);
}




QString CProjectInformation::getTubeInfoFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).tubeInfo;
}

QString CProjectInformation::getTecNameFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).tecName;
}

int CProjectInformation::getTubeCountFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).tubeCount.toInt();
}

QString CProjectInformation::getTecNameFromProjectName(const QString& kstrProjectName)
{
    QMutexLocker locker(&m_mutex);
    for (const auto& info : m_mapProjects) {
        if (info.projectName == kstrProjectName) {
            return info.tecName;
        }
    }
    return QString();
}

QString CProjectInformation::getExtractNameFromProjectName(const QString& kstrProjectName)
{
    QMutexLocker locker(&m_mutex);
    for (const auto& info : m_mapProjects) {
        if (info.projectName == kstrProjectName) {
            return info.extractName;
        }
    }
    return QString();
}

QString CProjectInformation::getAblationExpirationFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).ablationExpiration;
}

QString CProjectInformation::getSampleCapacityFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).sampleCapacity;
}

QString CProjectInformation::getInternalStandardCapacityFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).internalStandardCapacity;
}

QString CProjectInformation::getExtractCapacityFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).extractCapacity;
}

QString CProjectInformation::getPcrReagentCapacityFromProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    return m_mapProjects.value(kstrProjectLot).pcrReagentCapacity;
}

std::tuple<int, QString, QString> CProjectInformation::getHistoryShowDateFromeProjectLot(const QString& kstrProjectLot)
{
    QMutexLocker locker(&m_mutex);
    const SProjectInformationStruct& info = m_mapProjects.value(kstrProjectLot);
    return std::make_tuple(info.projectType.toInt(), info.projectName, info.tubeInfo);
}

float CProjectInformation::getFloatFiledFromProjectLot(const QString& kstrProjectLot, const QString& kstrField)
{
    QMutexLocker locker(&m_mutex);
    const SProjectInformationStruct& info = m_mapProjects.value(kstrProjectLot);

    if (kstrField == m_dbImpl.FILED_ProjectInfo.autoThreshold) return info.autoThreshold.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.thresholdValue) return info.thresholdValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.peakHeightValue) return info.peakHeightValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.meltingValue) return info.meltingValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.crossInterference) return info.crossInterference.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ctThresholdValue) return info.ctThresholdValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ISThresholdValue) return info.ISThresholdValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ISConcentrationValue) return info.ISConcentrationValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCNegativeCTValue) return info.QCNegativeCTValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCInternalCtCTValue) return info.QCInternalCtCTValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCInternalCtConcentrationValue) return info.QCInternalCtConcentrationValue.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.sampleCapacity) return info.sampleCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.pcrReagentCapacity) return info.pcrReagentCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.extractCapacity) return info.extractCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.complexSolutionCapacity) return info.complexSolutionCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.sampleCalibrationCapacity) return info.sampleCalibrationCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.internalStandardCalibrationCapacity) return info.internalStandardCalibrationCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.extractCalibrationCapacity) return info.extractCalibrationCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.pcrReagentCalibrationCapacity) return info.pcrReagentCalibrationCapacity.toFloat();
    if (kstrField == m_dbImpl.FILED_ProjectInfo.complexSolutionCalibrationCapacity) return info.complexSolutionCalibrationCapacity.toFloat();

    return 0.0f;
}

QString CProjectInformation::getStringFiledFromProjectLot(const QString& kstrProjectLot, const QString& kstrField)
{
    QMutexLocker locker(&m_mutex);
    const SProjectInformationStruct& info = m_mapProjects.value(kstrProjectLot);

    if (kstrField == m_dbImpl.FILED_ProjectInfo.projectLot) return info.projectLot;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.projectName) return info.projectName;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ablationExpiration) return info.ablationExpiration;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.sampleCapacity) return info.sampleCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.internalStandardCapacity) return info.internalStandardCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.extractCapacity) return info.extractCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.pcrReagentCapacity) return info.pcrReagentCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.complexSolutionCapacity) return info.complexSolutionCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.extractName) return info.extractName;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.tecName) return info.tecName;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.tubeInfo) return info.tubeInfo;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.calculation) return info.calculation;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.autoThreshold) return info.autoThreshold;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.thresholdValue) return info.thresholdValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.peakHeightValue) return info.peakHeightValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.meltingValue) return info.meltingValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.crossInterference) return info.crossInterference;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ctThresholdValue) return info.ctThresholdValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ISThresholdValue) return info.ISThresholdValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ISConcentrationValue) return info.ISConcentrationValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.liftValue) return info.liftValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.ISCapacity) return info.ISCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCCapacity) return info.QCCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCTValueRange) return info.QCTValueRange;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QConcentrationRange) return info.QConcentrationRange;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.PCTValueRange) return info.PCTValueRange;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.PConcentrationRange) return info.PConcentrationRange;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCNegativeCTValue) return info.QCNegativeCTValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCInternalCtCTValue) return info.QCInternalCtCTValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.QCInternalCtConcentrationValue) return info.QCInternalCtConcentrationValue;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.addTime) return info.addTime;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.sampleCalibrationCapacity) return info.sampleCalibrationCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.internalStandardCalibrationCapacity) return info.internalStandardCalibrationCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.extractCalibrationCapacity) return info.extractCalibrationCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.pcrReagentCalibrationCapacity) return info.pcrReagentCalibrationCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.complexSolutionCalibrationCapacity) return info.complexSolutionCalibrationCapacity;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.remark) return info.remark;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.remarks1) return info.remarks1;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.remarks2) return info.remarks2;
    if (kstrField == m_dbImpl.FILED_ProjectInfo.remarks3) return info.remarks3;

    return QString();
}

bool CProjectInformation::addOneProjectInfo(const QStringList& kstrDataList)
{
    bool result = m_dbImpl.addOneProjectInfo(kstrDataList);
    if (result) {
        QMutexLocker locker(&m_mutex);
        m_mapProjects[kstrDataList[0]] = SProjectInformationStruct::fromStringList(kstrDataList, m_dbImpl.m_strFieldNameProjectList);
    }
    return result;
}

bool CProjectInformation::updateTubeCountFromProjectLot(const QString& kstrProjectLot, const QString& kstrTubeCount)
{
    bool result = m_dbImpl.updateTubeCountFromProjectLot(kstrProjectLot, kstrTubeCount);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].tubeCount = kstrTubeCount.toInt();
        }
    }
    return result;
}

bool CProjectInformation::updateTubeInfoFromProjectLot(const QString& kstrProjectLot, const QString& kstrTubeInfo)
{
    bool result = m_dbImpl.updateTubeInfoFromProjectLot(kstrProjectLot, kstrTubeInfo);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].tubeInfo = kstrTubeInfo;
        }
    }
    return result;
}

bool CProjectInformation::updateProjTypeFromProjectLot(const QString& kstrProjectLot, const QString& kstrProjType)
{
    bool result = m_dbImpl.updateProjTypeFromProjectLot(kstrProjectLot, kstrProjType);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].projectType = kstrProjType.toInt();
        }
    }
    return result;
}

bool CProjectInformation::updateProjNameFromProjectLot(const QString& kstrProjectLot, const QString& kstrProjName)
{
    bool result = m_dbImpl.updateProjNameFromProjectLot(kstrProjectLot, kstrProjName);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].projectName = kstrProjName;
        }
    }
    return result;
}

bool CProjectInformation::updateCalculationFromProjectLot(const QString& kstrProjectLot, const QString& kstrCalculation)
{
    bool result = m_dbImpl.updateCalculationFromProjectLot(kstrProjectLot, kstrCalculation);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].calculation = kstrCalculation;
        }
    }
    return result;
}

bool CProjectInformation::updateAblationExpirationFromProjectLot(const QString& kstrProjectLot, const QString& kstrAblationExpiration)
{
    bool result = m_dbImpl.updateAblationExpirationFromProjectLot(kstrProjectLot, kstrAblationExpiration);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].ablationExpiration = kstrAblationExpiration;
        }
    }
    return result;
}

bool CProjectInformation::updateSampleCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrSampleCapacity)
{
    bool result = m_dbImpl.updateSampleCapacityFromProjectLot(kstrProjectLot, kstrSampleCapacity);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].sampleCapacity = kstrSampleCapacity;
        }
    }
    return result;
}

bool CProjectInformation::updateInternalStandardCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrInternalStandardCapacity)
{
    bool result = m_dbImpl.updateInternalStandardCapacityFromProjectLot(kstrProjectLot, kstrInternalStandardCapacity);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].internalStandardCapacity = kstrInternalStandardCapacity;
        }
    }
    return result;
}

bool CProjectInformation::updateExtractCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrExtractCapacity)
{
    bool result = m_dbImpl.updateExtractCapacityFromProjectLot(kstrProjectLot, kstrExtractCapacity);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].extractCapacity = kstrExtractCapacity;
        }
    }
    return result;
}

bool CProjectInformation::updatePcrReagentCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrPcrReagentCapacity)
{
    bool result = m_dbImpl.updatePcrReagentCapacityFromProjectLot(kstrProjectLot, kstrPcrReagentCapacity);
    if (result) {
        QMutexLocker locker(&m_mutex);
        if (m_mapProjects.contains(kstrProjectLot)) {
            m_mapProjects[kstrProjectLot].pcrReagentCapacity = kstrPcrReagentCapacity;
        }
    }
    return result;
}

bool CProjectInformation::deleteProjInfoFromProjectLot(const QString& kstrProjectLot)
{
    bool result = m_dbImpl.deleteProjInfoFromProjectLot(kstrProjectLot);
    if (result) {
        QMutexLocker locker(&m_mutex);
        m_mapProjects.remove(kstrProjectLot);
    }
    return result;
}

CProjectDBImpl::CProjectDBImpl(QObject* parent) : CDBObject(parent)
{
    m_strTableProjectInfoName = "projectInfo";
    m_strFieldNameProjectList << FILED_ProjectInfo.projectLot << FILED_ProjectInfo.projectName << FILED_ProjectInfo.projectType
        << FILED_ProjectInfo.ablationExpiration << FILED_ProjectInfo.sampleCapacity << FILED_ProjectInfo.internalStandardCapacity
        << FILED_ProjectInfo.extractCapacity << FILED_ProjectInfo.pcrReagentCapacity
        << FILED_ProjectInfo.complexSolutionCapacity
        << FILED_ProjectInfo.extractName
        << FILED_ProjectInfo.tecName << FILED_ProjectInfo.tubeCount << FILED_ProjectInfo.tubeInfo << FILED_ProjectInfo.calculation
        << FILED_ProjectInfo.autoThreshold << FILED_ProjectInfo.thresholdValue << FILED_ProjectInfo.peakHeightValue
        << FILED_ProjectInfo.meltingValue << FILED_ProjectInfo.crossInterference << FILED_ProjectInfo.ctThresholdValue
        << FILED_ProjectInfo.ISThresholdValue << FILED_ProjectInfo.ISConcentrationValue
        << FILED_ProjectInfo.liftValue
        << FILED_ProjectInfo.ISCapacity << FILED_ProjectInfo.QCCapacity << FILED_ProjectInfo.QCTValueRange
        << FILED_ProjectInfo.QConcentrationRange << FILED_ProjectInfo.PCTValueRange << FILED_ProjectInfo.PConcentrationRange
        << FILED_ProjectInfo.QCNegativeCTValue << FILED_ProjectInfo.QCInternalCtCTValue << FILED_ProjectInfo.QCInternalCtConcentrationValue
        << FILED_ProjectInfo.addTime << FILED_ProjectInfo.sampleCalibrationCapacity << FILED_ProjectInfo.internalStandardCalibrationCapacity
        << FILED_ProjectInfo.extractCalibrationCapacity << FILED_ProjectInfo.pcrReagentCalibrationCapacity
        << FILED_ProjectInfo.complexSolutionCalibrationCapacity << FILED_ProjectInfo.remark << FILED_ProjectInfo.remarks1 << FILED_ProjectInfo.remarks2
        << FILED_ProjectInfo.remarks3;
}

CProjectDBImpl& CProjectDBImpl::getInstance()
{
    static CProjectDBImpl instance;
    return instance;
}

void CProjectDBImpl::initDataBase()
{
    QList<QStringList> strFieldNameLists;
    strFieldNameLists << m_strFieldNameProjectList;
    QStringList strTableNameList;
    strTableNameList << m_strTableProjectInfoName;

    QStringList strFieldNameList;
    for (int i = 0; i != strFieldNameLists.length(); ++i)
    {
        strFieldNameList = strFieldNameLists.at(i);
        if (this->addFieldToTable(strTableNameList.at(i), strFieldNameList, false))
        {
            DEBUG_LOG << strTableNameList.at(i) << "Table created successfully";
        }
        else
        {
            DEBUG_LOG << strTableNameList.at(i) << "Failed to create table";
        }
    }
}

void CProjectDBImpl::updateDBFile(const QString& strNewFilePath)
{
    QString oldDbPath = CGlobalConfig::getInstance().getProjectDBDir();

    try
    {
        closeAllConnections();

        if (QFile::exists(strNewFilePath))
        {
            if (QFile::exists(oldDbPath))
            {
                QFile::remove(oldDbPath);
            }
            QFile::copy(strNewFilePath, oldDbPath);
            #if Q_OS_QML
            CMainWindow::getInstance().showMessageDlgToUI("数据库文件导入成功", Pop_SuccessWindow);
            #endif
        }
        else
        {
            #if Q_OS_QML
            CMainWindow::getInstance().showMessageDlgToUI("数据库文件导入失败", Pop_ErrorWindow);
            #endif
        }
        initDataBase();
    }
    catch (const std::exception& e)
    {
        #if Q_OS_QML
        CMainWindow::getInstance().showMessageDlgToUI("数据库文件连接失败", Pop_ErrorWindow);
        #endif
        qWarning() << "Failed to verify new database connection";
    }
}

int CProjectDBImpl::getFieldLength()
{
    return m_strFieldNameProjectList.length();
}

// get 方法实现

QStringList CProjectDBImpl::getAllProjectLots()
{
    return this->getMoreRecordsOneField(m_strTableProjectInfoName, FILED_ProjectInfo.projectLot);
}

QStringList CProjectDBImpl::getProjectInfoFromProjectLot(const QString& kstrProjectLot)
{
    QMap<QString, QString> strConditionMap = { {FILED_ProjectInfo.projectLot, kstrProjectLot} };
    return this->getOneRecordMoreFields(m_strTableProjectInfoName, m_strFieldNameProjectList, strConditionMap);
}



// update 方法实现
bool CProjectDBImpl::addOneProjectInfo(const QStringList& strDataList)
{
    if (strDataList.length() != m_strFieldNameProjectList.length()) {
        DEBUG_LOG << "dataLength error" << strDataList.length() << m_strFieldNameProjectList.length();
        return false;
    }
    return this->addOneDBRecord(m_strTableProjectInfoName, m_strFieldNameProjectList, strDataList, FILED_ProjectInfo.projectLot);
}

bool CProjectDBImpl::updateTubeCountFromProjectLot(const QString& kstrProjectLot, const QString& kstrTubeCount)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.tubeCount, kstrTubeCount,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateTubeInfoFromProjectLot(const QString& kstrProjectLot, const QString& kstrTubeInfo)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.tubeInfo, kstrTubeInfo,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateProjTypeFromProjectLot(const QString& kstrProjectLot, const QString& kstrProjType)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.projectType, kstrProjType,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateProjNameFromProjectLot(const QString& kstrProjectLot, const QString& kstrProjName)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.projectName, kstrProjName,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateCalculationFromProjectLot(const QString& kstrProjectLot, const QString& kstrCalculation)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.calculation, kstrCalculation,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateAblationExpirationFromProjectLot(const QString& kstrProjectLot, const QString& kstrAblationExpiration)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.ablationExpiration, kstrAblationExpiration,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateSampleCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrSampleCapacity)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.sampleCapacity, kstrSampleCapacity,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateInternalStandardCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrInternalStandardCapacity)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.internalStandardCapacity, kstrInternalStandardCapacity,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updateExtractCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrExtractCapacity)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.extractCapacity, kstrExtractCapacity,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

bool CProjectDBImpl::updatePcrReagentCapacityFromProjectLot(const QString& kstrProjectLot, const QString& kstrPcrReagentCapacity)
{
    return updateValueFromConditionFieldName(m_strTableProjectInfoName, FILED_ProjectInfo.pcrReagentCapacity, kstrPcrReagentCapacity,
        FILED_ProjectInfo.projectLot, kstrProjectLot);
}

// delete 方法实现
bool CProjectDBImpl::deleteProjInfoFromProjectLot(const QString& kstrProjectLot)
{
    return this->deleteDBRecord(m_strTableProjectInfoName, FILED_ProjectInfo.projectLot, kstrProjectLot);
}
