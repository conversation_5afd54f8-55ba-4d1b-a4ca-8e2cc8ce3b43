#ifndef SAMPLEMODULE_H
#define SAMPLEMODULE_H
#include "devicemodule.h"
#include "SampleControl/samplecontrol.h"

enum SampleTaskID
{
     //--------------------------周期时序动作-----------------------------
    STI_CATCH_SAMPLE = 0,
    STI_CATCH_CHECK_EXIST,     // 检测样本有无
    STI_CATCH_CHECK_EXIST_LEFT,// 检测样本有无(左)
    STI_CATCH_INIT,
    STI_CATCH_CLEAR,
    STI_CATCH_OPEN_CAP,//开盖
    STI_CATCH_CLOSE_CAP,//关盖
    STI_CATCH_BACK_HOME,//抓样本回样本架
     //--------------------------复位清理动作-------------------------------------
};

enum SampleCatchType
{
    SCT_RIGHT,//抓取右边
    SCT_LEFT,//抓取左边
    SCT_DOUBLE,//全部抓取
};

class SampleModule : public DeviceModule {
    Q_OBJECT

public:
    SampleModule(bool bUseThread = false, quint8 quCatchType = DEVICE_CATCH_TYPE); // 添加默认值
    void SetCatchType(quint8 quCatchType); // 新增设置 m_quCatchType 变量的函数
    void SetSampleCatchType(quint8 uiSampleCatchType);
public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr) override;
    void SlotAddTask(const CmdTask& task) override;

private:
    void _ProcessSubTask() override;

private:
    quint8 m_uiCatchType; // 添加成员变量
    quint8 m_uiSampleCatchType;//样本抓取类型
    QVector<SampleInfo> m_curExecSampleVect;
    QVector<SampleInfo> m_nextExecSampleVect;
};

#endif // SAMPLEMODULE_H
