#include<qdebug.h>
#include<QTime>
#include"CRFIDCtrl.h"
#include "control/coperationunit.h"
#include"CRFIDWaitRsThread.h"
#include"consumables/consumables.h"
#include"consumables/reagent.h"
#include"./ccommunicationobject.h"
#include"CRFIDMotionTask.h"
CRFIDCtrl::CRFIDCtrl(QObject *parent) : QObject(parent)
{
    m_ReagentBoxMap.clear();
    m_ConsumableBoxMap.clear();
    //  connect(this, &CRFIDCtrl::sigError,
    //          [](ErrorID errorID, QString strExtraInfo)
    //  {CCommunicationObject::getInstance().handleError(strExtraInfo, Mid_Sub_RFID, errorID);}); //0715
    connect(this, SIGNAL(sigError(QString , MidMachineSubmodule , ErrorID)),
            &CCommunicationObject::getInstance(),SLOT(handleError(QString , MidMachineSubmodule , ErrorID)));
    connect(this,SIGNAL(SignWaitFeedBack(int,int)),&CRFIDWaitRsThread::getInstance(),SLOT(SlotWaitStatusFeedBack(int,int)));
    connect(this,SIGNAL(SignResetStatus()),&CRFIDMotionTask::getInstance(),SLOT(SlotResetStatua()));
   // connect(&CRFIDWaitRsThread::getInstance(),SIGNAL(SignReadRFIDData(int,QString)),this,SLOT(SlotRFIDReadData(int ,QString)));
}

CRFIDCtrl &CRFIDCtrl::getInstance()
{
    static CRFIDCtrl cCRFIDCtrl;
    return cCRFIDCtrl;
}

int CRFIDCtrl::SendMotion(int iMachineID,int iMethod,QString strInputParam,int iType)
{
    QString strAddInfoHead ="<MachineID:"+QString::number(iMachineID)+",iMethod:"+QString::number(iMethod)+">";
    quint8 quiSync =1;  ////1是并行标志位
    COperationUnit::getInstance().sendStringData(iMethod,strInputParam,iMachineID,quiSync);
    qDebug()<<strAddInfoHead<<",iType="<<iType<<",Send Cmd Time"<<QTime::currentTime();
    emit SignWaitFeedBack(iMethod,iType);
    return 0;
}


int  CRFIDCtrl::_converBoxTypeToIdx(RFIDConsumableType cType, int &iIdx)
{
    switch (cType)
    {
    case RFID_ConsumableType_Reagent1:
    case RFID_ConsumableType_Reagent2:
    case RFID_ConsumableType_Reagent3:
    case RFID_ConsumableType_Reagent4:
    {
        iIdx = cType - 1;
        break;
    }
    case RFID_ConsumableType_Tip1:
    case RFID_ConsumableType_TubeCap1:
    {
        iIdx = 0;
        break;
    }
    case RFID_ConsumableType_Tip2:
    case  RFID_ConsumableType_TubeCap2:
    {
        iIdx = 1;
        break;
    }
    default:
    {
        return -1;
    }
    }
    return 0;
}

RFIDConsumableType  CRFIDCtrl::ConverIdxToConsumableType(quint8 uiType, quint8 uiIndex)
{
    RFIDConsumableType cType = RFID_ConsumableType_Tip1;

    switch (uiType)
    {
    case CT_TIP:
    {
        cType = static_cast<RFIDConsumableType>(RFID_ConsumableType_Tip1 + uiIndex);
        break;
    }
    case CT_TUBE:
    case CT_CAP:
    {
        cType = static_cast<RFIDConsumableType>(RFID_ConsumableType_TubeCap1 + uiIndex);
        break;
    }
    case CT_REAGENT:
    {
        cType = static_cast<RFIDConsumableType>(RFID_ConsumableType_Reagent1 + uiIndex);
        break;
    }
    }
    qDebug()<<"_converIdxToConsumableType"<<uiType<<uiIndex<<cType;
    return cType;
}

int CRFIDCtrl::_getMachineIDFromBoxType(RFIDConsumableType cType,int &iMachineID)
{
    switch (cType)
    {
    case RFID_ConsumableType_Reagent1:
    case RFID_ConsumableType_Reagent2:
    case RFID_ConsumableType_Reagent3:
    case RFID_ConsumableType_Reagent4:
    {
        iMachineID = Machine_RFID_1;
        break;
    }
    case RFID_ConsumableType_Tip1:
    case RFID_ConsumableType_Tip2:
    {
        iMachineID = Machine_RFID_2;
        break;
    }
    case RFID_ConsumableType_TubeCap1:
    case  RFID_ConsumableType_TubeCap2:
    {
        iMachineID = Machine_RFID_3;
        break;
    }
    default:
    {
        return 1;
    }
    }
    return 0;
}

bool CRFIDCtrl:: _processRFIDTipData(QStringList strItem, RFIDConsumableType cType)
{
    qDebug()<<"_processRFIDTipData:"<< cType<<strItem;
    QString strLog;
    int iIdx;
    ConsumableBox cbox;
    cbox.strWireID  = strItem[0];
    cbox.uiType = strItem[1].toInt();   //读出来的是1～8
    if(cbox.uiType!= RFID_ConsumableType_Tip1 && cbox.uiType!=RFID_ConsumableType_Tip2 )
    {
        strLog= QString(__func__)+"Type error,cType ="+QString::number(cType)+",DataType ="+QString::number(cbox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_TypeMismatch);
        return false;
    }
    cbox.uiState  = strItem[2].toInt();
    cbox.strBatchNo = strItem[3];
    if(strItem[4].split("-").size()!=3)
    {
        strLog= QString(__func__)+"error!!!!,qExpDate type error ,qExpDate ="+strItem[4];
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_DataFormatError);
        return  false;
    }
    cbox.qExpDate  =QDate::fromString(strItem[4], "yyyy-MM-dd");
    cbox.uiCapacity =strItem[5].toInt();
    cbox.iRemain =strItem[6].toInt();
    cbox.uiNextSingleAvrPos  =strItem[7].toInt();
    cbox.uiNextDoubleAvrPos  =strItem[8].toInt();

    int iNeedWriteRFID = 0;
    if (cbox.uiType != cType)//不相等，需要回写RFID信息
    {
        cbox.uiType = static_cast<int>(cType);
        iNeedWriteRFID = 1;
    }
    else// 需要重新更新到上位机
    {
        WriteTipDataToRFIDConsumableBox(cbox,false);
    }

    int iStatus= _converBoxTypeToIdx(RFIDConsumableType(cbox.uiType),iIdx);
    if(iStatus ==-1)
    {
        strLog= QString(__func__)+"_converBoxTypeToIdx error,uiType = "+QString::number(cbox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_ConverBoxTypeError);
        return false;
    }

    QString strKey = cType ==RFID_ConsumableType_Tip1?"Tip1":"Tip2";
    m_ConsumableBoxMap.insert(strKey,cbox);//   这个的idx1～8的
    //卡盒类型要修改为非1～8
    cbox.uiType = CT_TIP;
    Consumables::getInstance().AddConsumable(CT_TIP, iIdx,cbox,iNeedWriteRFID);
    return true;
}

bool CRFIDCtrl:: _processRFIDTubeCapData(QStringList strItem, RFIDConsumableType cType)
{
    qDebug()<<"_processRFIDTubeCapData:"<< cType<<strItem;
    QString strLog;
    int iIdx;
    ConsumableBox cbox_Tube,cbox_Cap;
    cbox_Tube.strWireID  = cbox_Cap.strWireID= strItem[0];
    int iType = strItem[1].toInt();   //读出来的是1～8;   
    cbox_Tube.uiType = cbox_Tube.uiType=iType;
    if( iType!=RFID_ConsumableType_TubeCap1 &&  iType!=RFID_ConsumableType_TubeCap2)
    {
        strLog= QString(__func__)+"Type error,cType ="+QString::number(cType)+",DataType ="+QString::number(iType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_TypeMismatch);
        return false;
    }
    cbox_Tube.uiState  = strItem[2].toInt();
    cbox_Cap.uiState  = strItem[3].toInt();

    cbox_Tube.strBatchNo =strItem[4];
     cbox_Cap.strBatchNo =strItem[5];
    if(strItem[6] .split("-").size()!=3)
    {
        strLog= QString(__func__)+"error!!!!,qExpDate type error ,qExpDate ="+strItem[6];
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_DataFormatError);
        return  false;
    }
    if(strItem[7] .split("-").size()!=3)
    {
        strLog= QString(__func__)+"error!!!!,qExpDate type error ,qExpDate ="+strItem[7];
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_DataFormatError);
        return  false;
    }
    cbox_Tube.qExpDate  =QDate::fromString(strItem[6], "yyyy-MM-dd");
    cbox_Cap.qExpDate = QDate::fromString(strItem[7], "yyyy-MM-dd");
    cbox_Tube.uiCapacity =strItem[8].toInt();
    cbox_Cap.uiCapacity =strItem[9].toInt();
    cbox_Tube.iRemain=strItem[10].toInt();
    cbox_Cap.iRemain=strItem[11].toInt();

    cbox_Tube.uiNextSingleAvrPos  =strItem[12].toInt();
    cbox_Cap.uiNextSingleAvrPos  =strItem[13].toInt();
    cbox_Tube.uiNextDoubleAvrPos  =strItem[14].toInt();
    cbox_Cap.uiNextDoubleAvrPos  =strItem[15].toInt();

    int iNeedWriteRFID = 0;
    if (iType != cType)//不相等，需要回写RFID信息
    {
        iType = static_cast<int>(cType);
        iNeedWriteRFID = 1;
    }
    int iStatus= _converBoxTypeToIdx(RFIDConsumableType(iType),iIdx);
    if(iStatus ==-1)
    {
        strLog= QString(__func__)+"_converBoxTypeToIdx error,uiType = "+QString::number(iType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_ConverBoxTypeError);
        return false;
    }
    QString strKey;
    if(iType == RFID_ConsumableType_TubeCap1)
    {
        //压入两个
        strKey = "Tube1";
        m_ConsumableBoxMap.insert(strKey,cbox_Tube);//   这个的idx还是1～8的
        cbox_Tube.uiType = CT_TUBE;  //修改为非1～8
        Consumables::getInstance().AddConsumable(CT_TUBE, iIdx,cbox_Tube,0);
        strKey = "Cap1";
        m_ConsumableBoxMap.insert(strKey,cbox_Cap);//   这个的idx还是1～8的
        cbox_Cap.uiType = CT_CAP;  //修改为非1～8
        Consumables::getInstance().AddConsumable(CT_CAP, iIdx,cbox_Cap,iNeedWriteRFID);
    }
    if(iType == RFID_ConsumableType_TubeCap2)
    {
        //压入两个
        strKey = "Tube2";
        m_ConsumableBoxMap.insert(strKey,cbox_Tube);//   这个的idx还是1～8的
        cbox_Tube.uiType = CT_TUBE;  //修改为非1～8
        Consumables::getInstance().AddConsumable(CT_TUBE, iIdx,cbox_Tube,0);
        strKey = "Cap2";
        m_ConsumableBoxMap.insert(strKey,cbox_Cap);//   这个的idx还是1～8的
        cbox_Cap.uiType = CT_CAP;  //修改为非1～8
        Consumables::getInstance().AddConsumable(CT_CAP, iIdx,cbox_Cap,iNeedWriteRFID);
    }

    if(iNeedWriteRFID == 0)// 需要重新更新到上位机(需要前面步骤更新 m_ConsumableBoxMap)
    {
        cbox_Tube.uiType = iType;
        WriteTubeDataToRFIDConsumableBox(cbox_Tube,false);//更新一个即可
        // WriteCapDataToRFIDConsumableBox(cbox_Cap,false);
    }

    return true;
}

bool CRFIDCtrl::_processRFIDReagentData(QStringList strItem, RFIDConsumableType cType)
{
    qDebug()<<"_processRFIDReagentData: "<<cType<<strItem;
    QStringList strsubItem;
    QString strLog;
    ReagentBox rbox;
    int iIdx;
    rbox.strWireID = strItem[0];
    rbox.uiType = strItem[1].toInt();//读出来的是1～8
    if(rbox.uiType!= RFID_ConsumableType_Reagent1 && rbox.uiType != RFID_ConsumableType_Reagent2 &&
       rbox.uiType!= RFID_ConsumableType_Reagent3 && rbox.uiType != RFID_ConsumableType_Reagent4 )
    {
        strLog= QString(__func__)+"Type error,cType ="+QString::number(cType)+",DataType ="+QString::number(rbox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_TypeMismatch);
        return false;
    }
    rbox.uiState =  strItem[2].toInt();
    rbox.strProjID =  strItem[3];
    rbox.strBatchNo = strItem[4];
    if(strItem[5] .split("-").size()!=3)
    {
        strLog= QString(__func__)+"error!!!!,qExpDate type error ,qExpDate = "+strItem[5];
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_DataFormatError);
        return  false;
    }
    rbox.qExpDate  =QDate::fromString(strItem[5], "yyyy-MM-dd");
    rbox.uiCapacity =strItem[6].toInt();
    rbox.uiCompNum =strItem[7].toInt();
    rbox.strCompInfo =strItem[8];
    strsubItem    = rbox.strCompInfo .split("&");
    if(strsubItem.size() != rbox.uiCompNum)
    {
        strLog= QString(__func__)+"error!!!!,strCompInfo noEqu rbox.uiCompNum,strCompInfo="+rbox.strCompInfo+ ",uiCompNum="+QString::number(rbox.uiCompNum);
        qDebug()<<strLog;
        //emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_GroupCountMismatch);
       // return  false;
    }
    rbox.uiSingleHoleCapacity  = strItem[9].toInt();
    rbox.uiRemain= strItem[10].toInt();
    rbox.uiNextAvrRowPos = strItem[11].toInt();
    // strItem[12]是开封日期，新卡应该没有开封日期时间的，应该非空的时候才需要判断
    if(!strItem[12].isEmpty() && strItem[12].split("-").size()!=3)
    {
        strLog= QString(__func__)+"error!!!!,qFirstUsedTime type error ,qFirstUsedTime = "+strItem[12];
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_DataFormatError);
        return  false;
    }
    rbox.qFirstUsedTime  =QDate::fromString(strItem[12], "yyyy-MM-dd");
    rbox.uiExpDays =strItem[13].toInt();
    rbox.uiHandleResultType = strItem[14].toInt();
    rbox.uiThreshold =strItem[15].toFloat();
    rbox.fInnerDensity = strItem[16].toFloat();

    // 中位机使用数据(新的数据长度大于19)，同时需要兼容旧rfid
    if (strItem.size()>19)
    {
         rbox.qPunchTime  =QDate::fromString(strItem[17], "yyyy-MM-dd");
         rbox.uiPunchHole =strItem[18].toInt();
         rbox.uiPackHole = strItem[19].toInt();
    }
    
    int iNeedWriteRFID = 0;
    if (rbox.uiType != cType)//不相等，需要回写RFID信息
    {
        rbox.uiType = static_cast<int>(cType);
        iNeedWriteRFID = 1;
    }
    else// 需要重新更新到上位机
    {
        WriteDataToRFIDReagentBox(rbox,false);
    }

    //BoxType转uiIdx;
    int iStatus= _converBoxTypeToIdx(RFIDConsumableType(rbox.uiType),iIdx);
    if(iStatus ==-1 )
    {
        strLog= QString(__func__)+"error!!!!,_converBoxTypeToIdx error ,uiType = "+QString::number(rbox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_ConverBoxTypeError);
        return false;
    }

    QString strKey ;
    if(cType ==RFID_ConsumableType_Reagent1 )   {strKey="Reagent1";}
    if(cType ==RFID_ConsumableType_Reagent2)   {strKey="Reagent2";}
    if(cType ==RFID_ConsumableType_Reagent3)  {strKey="Reagent3";}
    if(cType ==RFID_ConsumableType_Reagent4)   {strKey="Reagent4";}

    m_ReagentBoxMap.insert(strKey,rbox);//   这个的idx1～8的
    
    //卡盒类型在Reagent中位机没用到，不修改
    rbox.uiColumnIndex = iIdx;          //列信息
    Reagent::getInstance().AddReagentBox(iIdx,rbox,iNeedWriteRFID);
    return true;
}

int CRFIDCtrl::_converRFIDDataAndUpdate(QString strData, RFIDConsumableType cType)
{
    qDebug()<<"_converRFIDDataAndUpdate: "<<cType;
    QString strLog;
    QStringList strItem;
    bool bStatus;
    int iok=0;  //主要为防止接收到其他消息
    if(cType <0   ||  cType >8)
    {
        strLog= QString(__func__)+"Type error,Type ="+QString::number(cType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_TypeMismatch);
        return  1;
    }

    strItem = strData.split(",");
    qDebug()<<"_converRFIDDataAndUpdate strItem:"<<strItem.size();
    if(strItem.size() == gk_iConsumableStructSize_Tip &&
            (cType ==RFID_ConsumableType_Tip1 ||
             cType ==RFID_ConsumableType_Tip2 ))  //管帽
    {
        bStatus = _processRFIDTipData(strItem,cType);
        if(bStatus == false)
        {
            return  1;
        }
        iok =1;
    }
    if(strItem.size() == gk_iConsumableStructSize_TubeCap &&
            (cType ==RFID_ConsumableType_TubeCap1 ||
             cType ==RFID_ConsumableType_TubeCap2 ))  //管帽
    {
        bStatus = _processRFIDTubeCapData(strItem,cType);
        if(bStatus == false)
        {
            return  1;
        }
        iok =1;
    }
    if(strItem.size() >= gk_iReagentStructSize &&
            (cType ==RFID_ConsumableType_Reagent1 ||
             cType ==RFID_ConsumableType_Reagent2 ||
             cType ==RFID_ConsumableType_Reagent3||
             cType ==RFID_ConsumableType_Reagent4)) //试剂
    {
        bStatus =_processRFIDReagentData(strItem,cType);
        if(bStatus == false)
        {
            return  1;
        }
        iok =1;
    }
    if(iok ==0)
    {
        qDebug()<<"ConverRFIDDataAndUpDate_Consumable error,cType="<<cType<<",strData="<<strData;
        return  1;
    }
    return  0;
}


void CRFIDCtrl::SlotRFIDReadData(int iType,QString strData)   //这个iType是植工定义的1～8
{
    qDebug()<<"SlotRFIDReadData in";
    QString strLog;
    int  iStatus = _converRFIDDataAndUpdate(strData,RFIDConsumableType(iType));
    if(iStatus!=0)
    {
        qDebug()<<QString(__func__)+",error";
        emit SignResetStatus();
    }
}

int CRFIDCtrl::RefreshDataFromRFID(RFIDConsumableType cType)
{
    qDebug()<<"RefreshDataFromRFID in";
    QString strLog;
    if(cType <0   ||  cType >8)
    {
        strLog= QString(__func__)+"Type error,Type ="+QString::number(cType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataWriteError_TypeMismatch);
        return  1;
    }
    QString strPayLoad;
    int iMachineID,iStatus;
    iStatus  = _getMachineIDFromBoxType(cType,iMachineID);
    if(iStatus!=0)
    {
        strLog= QString(__func__)+"GetMachineIDFromBoxType error,cType ="+QString::number(cType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataParseError_ConverBoxTypeError);
        emit SignResetStatus();
        return 1;
    }

    strPayLoad = QString::number(cType);
    iStatus= CRFIDCtrl::getInstance().SendMotion(iMachineID,Method_rfid_read,strPayLoad,cType);
    if(iStatus!=0)
    {
        qDebug()<<__FUNCTION__<<"Send Motion error";
        emit SignResetStatus();
        return 1;
    }
    //receive wait rs 0715
    return 0;
}

int CRFIDCtrl::WriteTipDataToRFIDConsumableBox(ConsumableBox box,bool bSendToRFID)
{
    QString strLog;
    int iMachineID;
    QString strInPutParam, strWireID="NULL";   //天线ID不用写
    strInPutParam =QString("%1,%2,").arg(box.uiType).arg(box.uiState)
            +box.strBatchNo+","
            +box.qExpDate.toString("yyyy-MM-dd")+","
            +QString::number(box.uiCapacity)+","
            +QString::number(box.iRemain)+","
            +QString::number(box.uiNextSingleAvrPos)+","
            +QString::number(box.uiNextDoubleAvrPos);

    int iStatus  = _getMachineIDFromBoxType((RFIDConsumableType)box.uiType,iMachineID);
    if(iStatus!=0)
    {
        strLog= QString(__func__)+"GetMachineIDFromBoxType error,cType ="+QString::number(box.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataWriteError_TypeMismatch);
        emit SignResetStatus();
        return 1;
    }
    
    if (bSendToRFID)
    {
        iStatus= SendMotion(iMachineID,Method_rfid_write,strInPutParam,box.uiType);
        qDebug()<<__FUNCTION__<<"strInPutParam ="<<strInPutParam;
        if(iStatus!=0)
        {
            qDebug()<<"WriteTipDataToRFIDConsumableBox error,strInPutParam ="<<strInPutParam;
            emit SignResetStatus();
            return  1;
        }
    }

    QString strUpdateParam = QString("%1,").arg(box.strWireID) + strInPutParam;
    COperationUnit::getInstance().sendStringResult(Method_update_consumable, strUpdateParam, Machine_UpperHost);
    qDebug()<<"WriteTipDataToRFIDConsumableBox update_consumable: "<<strUpdateParam<<bSendToRFID;    
    return 0 ;
}

int CRFIDCtrl::WriteTubeDataToRFIDConsumableBox(ConsumableBox Tubebox,bool bSendToRFID)   //需要取出帽数据整合
{
    // 取出帽子数据,并更新相应链表数据
    ConsumableBox  CapBox;
    if(Tubebox.uiType ==RFID_ConsumableType_TubeCap1 )
    {
        CapBox=m_ConsumableBoxMap.value("Cap1"); //取
        m_ConsumableBoxMap.insert("Tube1",Tubebox); //写
    }
    if(Tubebox.uiType ==RFID_ConsumableType_TubeCap2 )
    {
        CapBox=m_ConsumableBoxMap.value("Cap2");//取
        m_ConsumableBoxMap.insert("Tube2",Tubebox); //写
    }
    qDebug()<<"WriteTubeDataToRFIDConsumableBox: "<<m_ConsumableBoxMap.keys()<<","<<CapBox.qExpDate;
    QString strLog;
    int iMachineID;
    QString strInPutParam, strWireID="NULL";   //天线ID不用写，不发送的
    strInPutParam =QString("%1,%2,%3,").arg(Tubebox.uiType).arg(Tubebox.uiState).arg(CapBox.uiState)
            +Tubebox.strBatchNo+","
            +CapBox.strBatchNo+","
            +Tubebox.qExpDate.toString("yyyy-MM-dd")+","
            +CapBox.qExpDate.toString("yyyy-MM-dd")+","
            +QString::number(Tubebox.uiCapacity)+","
            +QString::number(CapBox.uiCapacity)+","
            +QString::number(Tubebox.iRemain)+","
            +QString::number(CapBox.iRemain)+","
            +QString::number(Tubebox.uiNextSingleAvrPos)+","
            +QString::number(CapBox.uiNextSingleAvrPos)+","
            +QString::number(Tubebox.uiNextDoubleAvrPos)+","
            +QString::number(CapBox.uiNextDoubleAvrPos);

    int iStatus  = _getMachineIDFromBoxType((RFIDConsumableType)Tubebox.uiType,iMachineID);
    if(iStatus!=0)
    {
        strLog= QString(__func__)+"GetMachineIDFromBoxType error,cType ="+QString::number(Tubebox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataWriteError_TypeMismatch);
        emit SignResetStatus();
        return 1;
    }
    
    if (bSendToRFID)
    {
        iStatus= SendMotion(iMachineID,Method_rfid_write,strInPutParam,Tubebox.uiType);
         qDebug()<<__FUNCTION__<<"strInPutParam ="<<strInPutParam;
        if(iStatus!=0)
        {
            qDebug()<<"WriteTubeDataToRFIDConsumableBox error,strInPutParam ="<<strInPutParam;
            emit SignResetStatus();
            return  1;
        }
    }
    
    QString strUpdateParam = QString("%1,").arg(CapBox.strWireID) + strInPutParam;
    COperationUnit::getInstance().sendStringResult(Method_update_consumable, strUpdateParam, Machine_UpperHost);
    qDebug()<<"WriteTubeDataToRFIDConsumableBox update_consumable: "<<strUpdateParam<<bSendToRFID;
    return 0 ;
}

int CRFIDCtrl::WriteCapDataToRFIDConsumableBox(ConsumableBox Capbox,bool bSendToRFID)  //需要取出管数据整合
{
    // 取出帽子数据,并更新相应链表数据
    ConsumableBox  TubeBox;
    if(Capbox.uiType ==RFID_ConsumableType_TubeCap1 )
    {
        TubeBox=m_ConsumableBoxMap.value("Tube1");  //取
        m_ConsumableBoxMap.insert("Cap1",Capbox);  //写
    }
    if(Capbox.uiType ==RFID_ConsumableType_TubeCap2 )
    {
        TubeBox=m_ConsumableBoxMap.value("Tube2");      //取
        m_ConsumableBoxMap.insert("Cap2",Capbox);  //写
    }

    QString strLog;
    int iMachineID;
    QString strInPutParam, strWireID="NULL";   //天线ID不用写，不发送的
    strInPutParam =QString("%1,%2,%3,").arg(Capbox.uiType).arg(TubeBox.uiState).arg(Capbox.uiState)
            +TubeBox.strBatchNo+","
            +Capbox.strBatchNo+","
            +TubeBox.qExpDate.toString("yyyy-MM-dd")+","
            +Capbox.qExpDate.toString("yyyy-MM-dd")+","
            +QString::number(TubeBox.uiCapacity)+","
            +QString::number(Capbox.uiCapacity)+","
            +QString::number(TubeBox.iRemain)+","
            +QString::number(Capbox.iRemain)+","
            +QString::number(TubeBox.uiNextSingleAvrPos)+","
            +QString::number(Capbox.uiNextSingleAvrPos)+","
            +QString::number(TubeBox.uiNextDoubleAvrPos)+","
            +QString::number(Capbox.uiNextDoubleAvrPos);

    int iStatus  = _getMachineIDFromBoxType((RFIDConsumableType)Capbox.uiType,iMachineID);
    if(iStatus!=0)
    {
        strLog= QString(__func__)+",GetMachineIDFromBoxType error,cType ="+QString::number(Capbox.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataWriteError_TypeMismatch);
        emit SignResetStatus();
        return 1;
    }

    if (bSendToRFID)
    {
        iStatus= SendMotion(iMachineID,Method_rfid_write,strInPutParam,Capbox.uiType);
         qDebug()<<__FUNCTION__<<"strInPutParam ="<<strInPutParam;
        if(iStatus!=0)
        {
            qDebug()<<"WriteCapDataToRFIDConsumableBox error,strInPutParam ="<<strInPutParam;
            emit SignResetStatus();
            return  1;
        }
    }

    QString strUpdateParam = QString("%1,").arg(TubeBox.strWireID) + strInPutParam;
    COperationUnit::getInstance().sendStringResult(Method_update_consumable, strUpdateParam, Machine_UpperHost);
    qDebug()<<"WriteCapDataToRFIDConsumableBox update_consumable: "<<strUpdateParam<<bSendToRFID;
    return 0 ;
}


int CRFIDCtrl::WriteDataToRFIDReagentBox(ReagentBox box,bool bSendToRFID)
{
    //更新链表
    if(box.uiType ==RFID_ConsumableType_Reagent1 ){m_ReagentBoxMap.insert("Reagent1",box);}
    if(box.uiType ==RFID_ConsumableType_Reagent2 ){m_ReagentBoxMap.insert("Reagent2",box);}
    if(box.uiType ==RFID_ConsumableType_Reagent3 ){m_ReagentBoxMap.insert("Reagent3",box);}
    if(box.uiType ==RFID_ConsumableType_Reagent4 ){m_ReagentBoxMap.insert("Reagent4",box);}

    if (box.strProjID.isEmpty() && !box.qExpDate.isValid())
    {
        qDebug()<<"WriteDataToRFIDReagentBox is error";
        emit SignResetStatus();
        return 1;
    }
    
    QString strLog;
    QString strInPutParam,strWireID="NULL";   //天线ID不用写，不发送的
    strInPutParam =QString("%1,%2,").arg(box.uiType).arg(box.uiState)
            +box.strProjID+","
            +box.strBatchNo+","
            +box.qExpDate.toString("yyyy-MM-dd")+","
            +QString::number(box.uiCapacity)+","
            +QString::number(box.uiCompNum)+","
            +box.strCompInfo+","
            +QString::number(box.uiSingleHoleCapacity)+","
            +QString::number(box.uiRemain)+","
            +QString::number(box.uiNextAvrRowPos)+","
            +box.qFirstUsedTime.toString("yyyy-MM-dd")+","
            +QString::number(box.uiExpDays)+","
            +QString::number(box.uiHandleResultType)+","
            +QString::number(box.uiThreshold)+","
            +QString::number(box.fInnerDensity)+","
            +box.qPunchTime.toString("yyyy-MM-dd")+","
            +QString::number(box.uiPunchHole)+","
            +QString::number(box.uiPackHole);

    int iMachineID ;
    int iStatus  = _getMachineIDFromBoxType((RFIDConsumableType)box.uiType,iMachineID);
    if(iStatus!=0)
    {
        strLog= QString(__func__)+",GetMachineIDFromBoxType error,cType ="+QString::number((RFIDConsumableType)box.uiType);
        qDebug()<<strLog;
        emit sigError(strLog,Mid_Sub_RFID,FT_RFIDDataWriteError_TypeMismatch);
        emit SignResetStatus();
        return 1;
    }
    
    if (bSendToRFID)
    {
        iStatus= SendMotion(iMachineID,Method_rfid_write,strInPutParam,box.uiType);
        qDebug()<<__FUNCTION__<<"strInPutParam ="<<strInPutParam;
        if(iStatus!=0)
        {
            qDebug()<<"WriteDataToRFID_ReagentBox error,strInPutParam ="<<strInPutParam;
            emit SignResetStatus();
            return  1;
        }
    }

    QString strUpdateParam = QString("%1,").arg(box.strWireID) + strInPutParam;
    COperationUnit::getInstance().sendStringResult(Method_update_consumable, strUpdateParam, Machine_UpperHost);
    qDebug()<<"WriteDataToRFIDReagentBox update_consumable: "<<strUpdateParam<<bSendToRFID;
    return 0 ;
}
