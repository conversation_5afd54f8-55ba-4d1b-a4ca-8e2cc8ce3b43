#include<qdebug.h>
#include<QTime>
#include"HalSubSystem.h"
#include "../affair/caffair.h"

class HalSubSystemPrivate
{
public:
  HalSubSystemPrivate(HalSubSystem *parent)
    : q_ptr(parent)
  {
  }

public:
  ElecMagneticLock m_elecMagneticLock;
  Led m_LedTrigger;
  RunStat m_runStatus;
private:
  HalSubSystem * const q_ptr;
  Q_DECLARE_PUBLIC(HalSubSystem);
};

HalSubSystem::HalSubSystem(): 
  d_ptr(new HalSubSystemPrivate(this))
{

}

HalSubSystem::~HalSubSystem()
{
    Q_D(HalSubSystem);
    delete d;
}

HalSubSystem &HalSubSystem::getInstance()
{
    static HalSubSystem obj;
    return obj;
}

void HalSubSystem::SetRFIDElecMagneticLock(ElecMagneticLock::EnumLockState state, quint16 uiLockType, quint16 uiIndex,EnumMachineID machineID)
{
    Q_D(HalSubSystem);
    if(RST_WAIT_RUN != d->m_runStatus && RST_RUN != d->m_runStatus)
    {
       qDebug() << "HalSubSystem::SetRFIDElecMagneticLock: Not in wait run or run state";
       return;
    }
    d->m_elecMagneticLock.SetRFIDElecMagneticLock(state, uiLockType, uiIndex,machineID);
}

void HalSubSystem::SetRunStatus(quint16 state)
{
    Q_D(HalSubSystem);
    d->m_runStatus = static_cast<RunStat>(state);
    switch (d->m_runStatus)
    {
    case RunStat::RST_WAIT_RUN:
    // case RunStat::RST_RUN: // fix: 防止空耗材解锁后，再次进入运行状态，导致空耗材被再次上锁
      d->m_elecMagneticLock.SetAllElecMagneticLock();
      break;
    case RunStat::RST_STOP:
    case RunStat::RST_WAIT_IDLE:
    case RunStat::RST_IDLE:
      d->m_elecMagneticLock.SetAllElecMagneticUnlock();
      break;
    default:
      break;
    }
    qDebug() << "HalSubSystem::SetRunStatus" << state;
}

void HalSubSystem::SetAllElecMagneticUnlock()
{
    Q_D(HalSubSystem);
    d->m_elecMagneticLock.SetAllElecMagneticUnlock();  
}

void HalSubSystem::SetLedTrigger(Led::EnumTriggerState state, Led::EnumMoudle moudle,EnumMachineID machineID)
{
    Q_D(HalSubSystem);
    d->m_LedTrigger.Trigger(state,moudle,machineID);
}

Led::EnumTriggerState HalSubSystem::GetLedMoudleStatus(Led::EnumMoudle moudle)
{
    Q_D(HalSubSystem);
    return d->m_LedTrigger.GetMoudleStatus(moudle);
}