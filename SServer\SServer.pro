QT += core network core-private sql serialport serialbus
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle
QMAKE_CXXFLAGS += -g
QMAKE_LFLAGS += -rdynamic
QMAKE_CXXFLAGS += -Wall

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS
MOC_DIR = temp/moc
RCC_DIR = temp/rcc
UI_DIR = temp/ui
OBJECTS_DIR = temp/objB
# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

#DEFINES += Send_By_Virtual # 虚拟调试通讯


SOURCES += \
        CodeScan/CCodeScannerAnalyze.cpp \
        CodeScan/CSampleScannerCtrl.cpp \
        CodeScan/CZebraScannerCtrl.cpp \
        CodeScan/DSBCL95.cpp \
        CodeScan/cextractscannerthread.cpp \
        HalSubSystem/Hal/ElecMagneticLock.cpp \
        HalSubSystem/Hal/Led.cpp \
        HalSubSystem/HalSubSystem.cpp \
        Maintain/AgingTest/AgingConfigMgr.cpp \
        Maintain/AgingTest/AgingTest.cpp \
        RFID/CRFIDCtrl.cpp \
        RFID/CRFIDWaitRsThread.cpp \
        RFID/CRFIDMotionTask.cpp \
        SampleControl/samplecontrol.cpp \
        SampleControl/SampleAmplificate/SampleAmplificate.cpp \
        Maintain/MaintainSubSystem.cpp \
        Maintain/MotorDebug/MotorDebug.cpp \
        Maintain/MotorDebug/MotorConfigMgr.cpp \  
	    Upgrade/UpgradeCtrl.cpp \
        Maintain/SelfTest/SelfTest.cpp \
        Maintain/ResetTest/ResetTest.cpp \
        affair/caffair.cpp \
        affair/caffairbase.cpp \
        affair/caffaircomplexcompose.cpp \
        affair/caffairobject.cpp \
        affair/cdevstatus.cpp \
        affair/cextractparse.cpp \
        affair/cmachineinitseq.cpp \
        affair/cmoduleseqmapper.cpp \
        affair/testpreprocessing.cpp \
        ccommunicationobject.cpp \
        cglobalconfig.cpp \
        cmainwindow.cpp \
        consumables/cconsumablereservation.cpp \
        consumables/consumables.cpp \
        consumables/cpcrrespersister.cpp \
        consumables/crecyclebin.cpp \
        consumables/cstrip.cpp \
        consumables/pcrresource.cpp \
        consumables/reagent.cpp \
        control/ccananalyzethread.cpp \
        control/ccanbusdevicethread.cpp \
        control/cftpserverthread.cpp \
        control/coperationunit.cpp \
        control/cserialdevicethread.cpp \
        control/ctcpanalyzethread.cpp \
        control/ctcpserverthread.cpp \
        control/cudpserverthread.cpp \
        datacontrol/CSystemDB.cpp \
        datacontrol/cdbobject.cpp \
        datacontrol/cprojectdb.cpp \
        datacontrol/ctiminginfodb.cpp \
        datacontrol/errorcodedb.cpp \
        error/cerrorhandler.cpp \
        error/cerrornotify.cpp \
        error/errorconfig.cpp \
        log/cspdlogger.cpp \
        main.cpp \
        module/devicemodule.cpp \
        module/extractmodule.cpp \
        module/gantrymodule.cpp \
        module/pcrcatchmodule.cpp \
        module/pcrmodule.cpp \
        module/samplemodule.cpp \
        module/switchmixmodule.cpp \
        publicconfig.cpp \
        publicfunction.cpp \
        SystemConfig/SystemConfig.cpp \
        SystemConfig/CpuMonitor.cpp \
        window/ccan0window.cpp \
        window/ccan1window.cpp \
        window/cclientwindow.cpp \
        window/cpcrwindow.cpp \
        window/cserialwindow.cpp \
        window/cvirtualwindow.cpp \
        window/cwindowobject.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

HEADERS += \
    CodeScan/CCodeScannerAnalyze.h \
    CodeScan/CSampleScannerCtrl.h \
    CodeScan/CZebraScannerCtrl.h \
    CodeScan/DSBCL95.h \
    CodeScan/cextractscannerthread.h \
    HalSubSystem/Hal/ElecMagneticLock.h \
    HalSubSystem/Hal/Led.h \
    HalSubSystem/HalSubSystem.h \
    Maintain/AgingTest/AgingConfigMgr.h \
    Maintain/AgingTest/AgingTest.h \
    RFID/CRFIDCtrl.h \
    RFID/CRFIDWaitRsThread.h \
    RFID/CRFIDMotionTask.h \
    SampleControl/samplecontrol.h \
    SampleControl/SampleAmplificate/SampleAmplificate.h \
    Maintain/MaintainSubSystem.h \
    Maintain/MotorDebug/MotorDebug.h \   
    Upgrade/UpgradeCtrl.h \
    Maintain/MotorDebug/MotorConfigMgr.h \  
    Maintain/SelfTest/SelfTest.h \
    Maintain/ResetTest/ResetTest.h \
    affair/caffair.h \
    affair/caffairbase.h \
    affair/caffaircomplexcompose.h \
    affair/caffairobject.h \
    affair/cdevStatus.h \
    affair/cextractparse.h \
    affair/cmachineinitseq.h \
    affair/cmoduleseqmapper.h \
    affair/testpreprocessing.h \
    ccommunicationobject.h \
    cglobalconfig.h \
    cmainwindow.h \
    consumables/cconsumablereservation.h \
    consumables/consumables.h \
    consumables/cpcrrespersister.h \
    consumables/crecyclebin.h \
    consumables/cstrip.h \
    consumables/pcrresource.h \
    consumables/reagent.h \
    control/ccananalyzethread.h \
    control/ccanbusdevicethread.h \
    control/cftpserverthread.h \
    control/coperationunit.h \
    control/cserialdevicethread.h \
    control/ctcpanalyzethread.h \
    control/ctcpserverthread.h \
    control/cudpserverthread.h \
    datacontrol/CSystemDB.h \
    datacontrol/cdbobject.h \
    datacontrol/cprojectdb.h \
    datacontrol/ctiminginfodb.h \
    datacontrol/errorcodedb.h \
    error/cerrorhandler.h \
    error/cerrornotify.h \
    error/errorconfig.h \
    log/compressed_daily_file_sink.h \
    log/cspdlogger.h \
    module/devicemodule.h \
    module/extractmodule.h \
    module/gantrymodule.h \
    module/pcrcatchmodule.h \
    module/pcrmodule.h \
    module/samplemodule.h \
    module/switchmixmodule.h \
    publicconfig.h \
    publicfunction.h \
    SystemConfig/SystemConfig.h \
    SystemConfig/CpuMonitor.h \
    window/ccan0window.h \
    window/ccan1window.h \
    window/cclientwindow.h \
    window/cpcrwindow.h \
    window/cserialwindow.h \
    window/cvirtualwindow.h \
    window/cwindowobject.h


INCLUDEPATH += $$PWD/include
DEPENDPATH += $$PWD/include
INCLUDEPATH += $$PWD/include/libzip

win32 {
    # 对于 Windows 平台
    DEFINES += Q_OS_WIN32
    LIBS += $$PWD/lib/win32/libspdlog.a
    LIBS += $$PWD/lib/win32/libdbghelp.a
}
unix {
     contains(QT_ARCH, arm64) {
     DEFINES += Q_OS_ARM
     LIBS += $$PWD/lib/arm/libspdlog.a
     LIBS += $$PWD/lib/arm/libzip.a
     LIBS +=  -lz
     } else {
     DEFINES += Q_OS_UNIX
     LIBS += $$PWD/lib/unix/libspdlog.a 
     LIBS += $$PWD/lib/unix/libzip.a
     LIBS += -L$$PWD/lib/unix/ -lz
     }
 }

exists($$PWD/SServer.pri) {
    #生成版本信息相关定义
    DEFINES += SServer_Version 
    include($$PWD/SServer.pri)#获取git信息并生成版本信息
    DISTFILES += ($$PWD/SServer.pri)
}

# 禁用-Wunknown-pragmas报警
CONFIG += warn_off
QMAKE_CXXFLAGS += -Wno-unknown-pragmas