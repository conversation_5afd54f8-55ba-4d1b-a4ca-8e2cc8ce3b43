/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-11-11
  * Description: spdlog日志封装二次开发，字需要在main.cpp中添加头文件，及CSpdLogger::init("log_config.ini");
  * 1. 新增对日志文件的配置信息，配置文件严格按照【key:value；】方式编写，否则可能会读写错误
  *     默认会生成配置文件
  * 2. 日志每日轮巡只保存最新的配置的最大文件数
  * 3. 默认日志每天压缩过往的日志文件
  * 4. 日志等级为4类，debug，info，warning，cirtical+
  * 5.日志存储格式总是 level_YYYY-MM-dd.log_index
  * 6.删除N天之前的日志（默认90天）
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CSPDLOGGER_H
#define CSPDLOGGER_H

#include <QString>
#include <QDate>
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <pthread.h>
#include <mutex>
#include <condition_variable>
#include <QMap>
#include "./include/concurrentqueue/concurrentqueue.h"

//#include <zip.h>
// 设计高性能日志，使用static比单例更加合适，无实例化和访问开销
//
class CSpdLogger
{
public:
    static void init(const QString &strConfigFile = "");
    static void shutdown();
    static void modifyConfig(const QString &strKey, const QString &strValue);
private:
    static void initLogger();
    static void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg);    
    static int findMaxIndexForDate(const QString& strBase, const QString& strDate);
    static void* checkLogFileSize(void* args);// 间隔检查日志文件是否超过设定大小
    static void* dailyMonitoring(void* args);// 日监控，清除过期日志及压缩昨天之前日志

    static void updateLogger_debug(const QString& strNewLogFile);
    static void updateLogger_info(const QString& strNewLogFile);
    static void updateLogger_warn(const QString& strNewLogFile);
    static void updateLogger_critical(const QString& strNewLogFile);

    static void initLogger_debug(const QString& strNewLogFile);
    static void initLogger_info(const QString& strNewLogFile);
    static void initLogger_warn(const QString& strNewLogFile);
    static void initLogger_critical(const QString& strNewLogFile);
    // 配置
    static void saveDefaultConfig(const QString& strConfigFile, const QMap<QString, QString>& strConfigMap);
    static void loadConfig(const QString& strConfigFile);
    static QMap<QString, QString> parseConfig(const QString& strConfigText);
    // 压缩与清除
    static void compressLAndClearogs(bool bCompress = true);
    static void compressLogsForLevel(const QString& strLogDirectory, const QString& strLogLevel);
    static void compressFilesToZip(const QStringList& strFilesList, const QString& strZipFilePath);
    static void deleteOldLogsForLevel(const QString& strLogDirectory, const QString& strLogLevel);

private:
    static bool m_bThreadExit;
    static std::shared_ptr<spdlog::logger> async_logger_d;// debug
    static std::shared_ptr<spdlog::logger> async_logger_i;// info
    static std::shared_ptr<spdlog::logger> async_logger_w;// warn
    static std::shared_ptr<spdlog::logger> async_logger_cf;// critical,fatal
    static pthread_t log_file_check_thread;
    static pthread_t log_daily_monitoring_thread;
    // 一天最多多少个文件,累加递增，比如三个，文件会从log_0,log_1,log_2变成log_1,log_2,log_3，数字最大为最新
    static quint32 m_iMaxFileCount;
    static quint32 m_iMaxFileSize;// 单个文件最大大小，单位MB
    // 是否输出控制台,不建议使用，消耗系统资源
    // 建议使用 tail -n 100 -f logs/debug.log_0 查看最近100行即可
    static bool m_bConsole;
    static QString m_strLogLevel; // 日志级别，默认debug
    static quint32 m_iThreadPoolSize; // 日志队列大小，默认8192
    static quint32 m_iThreadPoolThreads; // 线程池线程数，默认2
    static quint32 m_iFlushIntervalMs; // 日志刷新的频率，单位ms，默认100ms
    static QString m_strLogDirectory; // 日志保存的目录
    static quint32 m_iSaveDays; // 日志保存的时间，日期之前会被清除
    static moodycamel::ConcurrentQueue<QString> m_qZipPath;//需要压缩和删除的文件
    static std::mutex m_mtxZip;// zip压缩
    static std::condition_variable m_cvZip; // zip压缩 
    static bool m_bReadyZip;  
    static QString m_strConfigFile;
};

#endif // CSPDLOGGER_H
