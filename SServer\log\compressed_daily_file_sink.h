#ifndef COMPRESSED_DAILY_FILE_SINK_H
#define COMPRESSED_DAILY_FILE_SINK_H

#include <QDebug>
#include <QDateTime> 
#include <QTime> 
#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include "zip.h"

namespace spdlog {
namespace sinks {

/*
 * Rotating file sink based on date.
 * If truncate != false , the created file will be truncated.
 * If max_files > 0, retain only the last max_files and delete previous.
 */
template <typename Mutex, typename FileNameCalc = daily_filename_calculator>
class compressed_daily_file_sink final : public base_sink<Mutex> {
public:
    // create daily file sink which rotates on given time
    compressed_daily_file_sink(filename_t base_filename,
                    int rotation_hour,
                    int rotation_minute,
                    bool truncate = false,
                    uint16_t max_files = 0,
                    bool delete_file = false,
                    const file_event_handlers &event_handlers = {})
        : base_filename_(std::move(base_filename)),
          rotation_h_(rotation_hour),
          rotation_m_(rotation_minute),
          file_helper_{event_handlers},
          truncate_(truncate),
          max_files_(max_files),
          delete_old_file_(delete_file),
          filenames_q_() {
        if (rotation_hour < 0 || rotation_hour > 23 || rotation_minute < 0 ||
            rotation_minute > 59) {
            throw_spdlog_ex("daily_file_sink: Invalid rotation time in ctor");
        }

        auto now = log_clock::now();
        auto filename = FileNameCalc::calc_filename(base_filename_, now_tm(now));
        file_helper_.open(filename, truncate_);
        rotation_tp_ = next_rotation_tp_();
        if (max_files_ > 0) {
            init_filenames_q_();
        }
    }

    filename_t filename() {
        std::lock_guard<Mutex> lock(base_sink<Mutex>::mutex_);
        return file_helper_.filename();
    }

protected:
    void sink_it_(const details::log_msg &msg) override {
        auto time = msg.time;
        bool should_rotate = time >= rotation_tp_;
        if (should_rotate) {
            auto filename = FileNameCalc::calc_filename(base_filename_, now_tm(time));
            file_helper_.open(filename, truncate_);
            rotation_tp_ = next_rotation_tp_();
        }
        memory_buf_t formatted;
        base_sink<Mutex>::formatter_->format(msg, formatted);
        file_helper_.write(formatted);

        // Do the cleaning only at the end because it might throw on failure.
        if (should_rotate && max_files_ > 0) {
            delete_old_();
        }
    }

    void flush_() override { file_helper_.flush(); }

private:
    void init_filenames_q_() {
        using details::os::path_exists;

        filenames_q_ = details::circular_q<filename_t>(static_cast<size_t>(max_files_));
        std::vector<filename_t> filenames;
        auto now = log_clock::now();
        while (filenames.size() < max_files_) {
            auto filename = FileNameCalc::calc_filename(base_filename_, now_tm(now));
            if (!path_exists(filename)) {
                break;
            }
            filenames.emplace_back(filename);
            now -= std::chrono::hours(24);
        }
        for (auto iter = filenames.rbegin(); iter != filenames.rend(); ++iter) {
            filenames_q_.push_back(std::move(*iter));
        }
    }

    tm now_tm(log_clock::time_point tp) {
        time_t tnow = log_clock::to_time_t(tp);
        return spdlog::details::os::localtime(tnow);
    }

    log_clock::time_point next_rotation_tp_() {
        auto now = log_clock::now();
        tm date = now_tm(now);
        date.tm_hour = rotation_h_;
        date.tm_min = rotation_m_;
        date.tm_sec = 0;
        auto rotation_time = log_clock::from_time_t(std::mktime(&date));
        if (rotation_time > now) {
            return rotation_time;
        }
        return {rotation_time + std::chrono::hours(24)};
    }

    // Delete the file N rotations ago.
    // Throw spdlog_ex on failure to delete the old file.
    void delete_old_() {
        using details::os::filename_to_str;
        using details::os::remove_if_exists;
 
        if (!delete_old_file_)//跨天或者超出当天最大文件数时，是否删除旧文件
        {
          return;
        }
        
        filename_t current_file = file_helper_.filename();
        if (filenames_q_.full()) {
            auto old_filename = std::move(filenames_q_.front());
#if 0      // 是否压缩日志文件
            QString strZipDate = QDateTime::currentDateTime().toString("yyyyMMdd") +"_"+QTime::currentTime().toString("_HHmmss");
            filename_t zip_filename_ = base_filename_ + "_" + strDate.toStdString() + ".zip";
            qDebug()<<"delete_old_: "<<old_filename.c_str()<<base_filename_.c_str()<<zip_filename_.c_str();
            compressFilesToZip(old_filename, zip_filename_);
#endif            
            filenames_q_.pop_front();
            bool ok = remove_if_exists(old_filename) == 0;
            if (!ok) {
                filenames_q_.push_back(std::move(current_file));
                throw_spdlog_ex("Failed removing daily file " + filename_to_str(old_filename),
                                errno);
            }
        }
        filenames_q_.push_back(std::move(current_file));
    }

    void compressFilesToZip(const filename_t& strFilePath, const filename_t& strZipFilePath){
       int iError = 0;
       zip_t* pZip = zip_open(strZipFilePath.c_str(), ZIP_CREATE | ZIP_TRUNCATE, &iError);
       if (!pZip)
       {
           qCritical() << "Failed to open zip file:" << strZipFilePath.c_str();
           return;
       }
    
       zip_source_t* pZipSource = zip_source_file(pZip, strFilePath.c_str(), 0, 0);
       if (!pZipSource)
       {
           qCritical() << "Failed to create zip source for file:" << strFilePath.c_str();
           zip_close(pZip);
           return;
       }
       if (zip_file_add(pZip, strFilePath.c_str(), pZipSource, ZIP_FL_OVERWRITE) < 0)
       {
           qCritical() << "Failed to add file to zip:" << strFilePath.c_str();
           zip_source_free(pZipSource);
           zip_close(pZip);
           return;
       }

       if (zip_close(pZip) < 0)
       {
           qCritical() << "Failed to close zip file:" << strZipFilePath.c_str();
       }
    }

    filename_t base_filename_;
    int rotation_h_;
    int rotation_m_;
    log_clock::time_point rotation_tp_;
    details::file_helper file_helper_;
    bool truncate_;
    uint16_t max_files_;
    details::circular_q<filename_t> filenames_q_;
    bool delete_old_file_;
};
using compressed_daily_file_sink_mt = compressed_daily_file_sink<std::mutex>;
}  // namespace sinks
}  // namespace spdlog

#endif
