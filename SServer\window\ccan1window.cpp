#include "ccan1window.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
#include "datacontrol/ctiminginfodb.h"
#include"control/coperationunit.h"
CCan1Window::CCan1Window(QObject *parent) : CWindowObject(parent)
{

}

CCan1Window::~CCan1Window()
{
    m_bThreadExit = true;
}

void CCan1Window::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            m_pFramePos = qMessage.data() + gk_iLengthPos;
            m_iReadPayloadLength = GetByte2Int(m_pFramePos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");

            // CGlobalConfig::getInstance().printMessageInfo(qMessage,
            //                                               "[can1->server] ");

            bool bUpperHost=true;
            if (Method_rfid_read == m_iMethodID || Method_upgrade_req ==m_iMethodID ||
                      Method_upgrade_data ==m_iMethodID )
            {
                bUpperHost = false;
            }

            if (bUpperHost)
            {
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_UpperHost);
            }

            #ifndef ShortOutPutLog
            // qDebug()<<"CCan1Window::_HandleReceiveList"<<m_iMethodID;
            #endif
            switch (m_iMethodID)
            {
            //            case Method_read_comp_cmd:
            //            {//
            //                qDebug() << "Method_read_comp_cmd "  << m_qPayloadString;
            //                int iNameIndex = m_qPayloadString.indexOf(';');
            //                if(iNameIndex > 1 && iNameIndex<m_qPayloadString.size())
            //                {
            //                    QString strIDName = m_qPayloadString.left(iNameIndex);
            //                    QStringList strIDNameList = strIDName.split(",");
            //                    QString strContent = m_qPayloadString.right(m_qPayloadString.size()-iNameIndex-1);
            //                    if(strIDNameList.length() >= 3)
            //                    {
            //                        CTimingInfoDB::getInstance().addComplexTiming(QString::number(m_iSourceID), strIDNameList[2],
            //                                strIDNameList[1], strIDNameList[0], strContent);
            //                    }
            //                    else if(strIDNameList.length() >= 2)
            //                    {
            //                        CTimingInfoDB::getInstance().addComplexTiming(QString::number(m_iSourceID), "",
            //                                                                      strIDNameList[1], strIDNameList[0], strContent);
            //                    }
            //                }
            //                break;
            //            }
            //            case Method_comp_cmd_st:
            //            case Method_comp_cmd_exec_cond:
            //            case Method_comp_cmd:
            //            case Method_stop:
            //            case Method_pause:
            //            case Method_resume:
            //            case Method_dev_comp_status:
            //            {
            //                qDebug()<<"Can1window Deal with Method_comp cmd:"<<m_iMethodID<<m_qPayloadString;
            //                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
            //                break;
            //            }
            case Method_board_info:
            {
                qDebug()<<"Can0window Deal with Method_comp cmd:"<<m_iMethodID<<m_qPayloadString;
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_extract_heater_start:
            case Method_extract_heater_stop:
            {
                qDebug()<<"Can0window Deal with Method_extract_heater_start or stop cmd:"<<m_iMethodID<<m_qPayloadString;
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_rfid_read:  //LXJ
            case Method_rfid_write:  //LXJ
            {
                qDebug()<<"Method_rfid_read or write to MiddleWare??? "<<m_iDestinationID;
                if(m_iDestinationID == 0x00)  //0x00中位机
                {
                    qDebug()<<"sigRFIDRsMsg to MiddleWare";
                    emit sigRFIDRsMsg(qMessage);
                }
                else// 非中位机
                {
                    CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_UpperHost);
                }
                break;
            }
            case Method_PoweCtrl_UV:   //Method_uv_open
            {
                qDebug()<<__FUNCTION__<<"Enter uv Reply,payload ="<<m_qPayloadString;
                COperationUnit::getInstance().sendStringResult(Method_uv_open, "", Machine_UpperHost, m_qPayloadString.toInt());
                break;
            }
            case Method_upgrade_end:
            {
                qDebug()<<"m_iSourceID="<<m_iSourceID<<",m_iResult"<<m_iResult;
                emit sigUpgradeEndMsg(EnumMachineID(m_iSourceID),m_iResult);
                QString strStatus = QString("%1,%2").arg("0").arg("");//状态IDLE目前暂用0替代,information为空
                 COperationUnit::getInstance().sendStringData(Method_unit_status, strStatus, m_iSourceID);
                break;
            }
            case Method_upgrade_data:
            {
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
				break;
            }
            case Method_FeatMngBoard_ReportCurrentTemp:
            {
                QStringList strList = m_qPayloadString.split(",");
                if(strList.size()!=2)
                {
                    qDebug()<<"ReportCurrentTemp  error ,m_qPayloadString="<<m_qPayloadString;
                }
                else
                {
                    int iType = strList[0].toInt();
                    double dRs = strList[1].toInt()/100.0;
                    int iChangeUpLoadType =-1;
                    if(iType ==1) { iChangeUpLoadType = DailyMonitor_SplitArea;}  //裂解   上位机定义与中位机定义不一样
                    if(iType ==2) { iChangeUpLoadType = DailyMonitor_ElutionArea;}  //洗脱
                    if(iType ==4) { iChangeUpLoadType = DailyMonitor_FreezeArea;}
                    if(iChangeUpLoadType ==-1)
                    {
                        qDebug()<<"ReportCurrentTemp  error ,iType="<<iType;
                    }
                    else
                    {
                        QString strInPutParam=QString("%1,%2").arg(iChangeUpLoadType).arg(dRs);
                        qDebug()<<"Upload Temp str ="<<strInPutParam;
                        COperationUnit::getInstance().sendStringResult(Method_daily_monitor, strInPutParam, Machine_UpperHost);
                    }
                }
                break;
            }
            case Method_error_info:
            {
                //错误处理
                qDebug()<<"Can1window Deal with Method_error_info :"<<m_iMethodID<<m_qPayloadString;
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_FeatMngBoard_NotifyLockStateChange:
            case Method_FeatMngBoard_TipTrayLockSensorStatus:
            case Method_FeatMngBoard_PCRTrayLockSensorStatus:
            case Method_FeatMngBoard_ReagentBarLockSensorStatus:
            case Method_FeatMngBoard_ExtractModuleLockSensorStatus:
            case Method_FeatMngBoard_ExtractBarPositionStatusCheck:
            {
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            default:
                break;
            }//end of switch

        }//end of if
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }//end of while
}
