#include "reagent.h"
#include <QDebug>
#include <cmath>
#include "cglobalconfig.h"
#include "error/cerrornotify.h"
#include"./RFID/CRFIDMotionTask.h"
#include"./RFID/CRFIDCtrl.h"
#include "HalSubSystem/HalSubSystem.h"

#define REAGENT_SUB_CAPACITY            12  // 单孔容量
#define REAGENT_LYOPHILIZED_BALLS_SIZE  8   // 冻干球数量
#define REAGENT_DUILENT_SIZE            2   // 稀释液数量
#define DUILENT_CAPACITY                48  // 稀释液容量
#define REAGENT_CAPACITY                4   // 试剂条容量

Reagent &Reagent::getInstance()
{
    static Reagent instance;
    return instance;
}


Reagent::Reagent()
{

}

quint8 Reagent::_GetNextAvrPos(ReagentBox& reagentBox)
{
    //    quint8 uiNextAvrPos = (REAGENT_LYOPHILIZED_BALLS_SIZE - std::ceil(1.0*reagentBox.uiRemain/REAGENT_SUB_CAPACITY*reagentBox.uiDivCompNum));
    quint8 uiNextAvrPos = REAGENT_LYOPHILIZED_BALLS_SIZE - _GetCeilIndex(reagentBox.uiRemain, reagentBox.uiSingleHoleCapacity);
    qDebug()<<"Reagent Remain:"<<reagentBox.uiRemain<<"Next AvrPos"<<uiNextAvrPos<<"Hole Capacity:"<<reagentBox.uiSingleHoleCapacity;
    return uiNextAvrPos;
}

bool Reagent::GetNextAvrReagent(QString strProjID, quint8 &uiRowIndex, quint8 &uiColumnIndex,quint8& uiAmplifyCompIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bFind = false;
    //根据项目ID查找当前所需的试剂位置
    QMap<QString, QList<quint8> >::iterator itor = m_qRelationshipMap.find(strProjID);
    qDebug()<<"Reagent::GetNextAvrReagent keys"<<m_qRelationshipMap.keys()<<m_qReagentMap.keys();

    if(itor != m_qRelationshipMap.end())
    {
        QList<quint8>* qList = &m_qRelationshipMap[strProjID];
        if(!qList)
        {
            qDebug()<<"Reagent::GetNextAvrReagent qList is null";
        }
        else if(qList->size()>0)
        {
            uiColumnIndex = qList->at(0);//获取排序后的第一个
            ReagentBox* reagentBox = &m_qReagentMap[uiColumnIndex];
            if(reagentBox)
            {
                if(reagentBox->uiRemain>0)
                {
                    bFind = _GetNextAvrRowPos(*reagentBox,uiRowIndex,uiAmplifyCompIndex);
                }
                qDebug()<<"Reagent::GetNextAvrReagent"<<"uiRemain: "<<reagentBox->uiRemain;
            }
        }
    }
    if(!bFind)
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_InsufficientMaterial, QString("ProjectID %1 is insufficient.").arg(strProjID));
        // 如果获取失败，需要将uiRowIndex、uiColumnIndex、uiAmplifyCompIndex设置为1，防止后续位置出错
        uiRowIndex = 0;
        uiColumnIndex = 0;
        uiAmplifyCompIndex = 1;
    }
    qDebug()<<"Reagent::GetNextAvrReagent strProjID: "<<strProjID<<"uiRowIndex: "<<uiRowIndex<<"uiColumnIndex: "<<uiColumnIndex<<"bFind: "<<bFind<<"uiAmplifyCompIndex: "<<uiAmplifyCompIndex;
    return bFind;
}

bool Reagent::GetNextAvrReagent(SubPackData& data1, SubPackData& data2)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bFind = false;

    // 有两种情况
    // 1、同一个项目需要判断1+n的情况(即跨条获取)，不能在一条试剂获取(考虑到组分不是1的时候，比如组分5)
    // 2、不同项目，直接获取即可
    if (data1.strProjID == data2.strProjID)
    {
        //根据项目ID查找当前所需的试剂位置
        QMap<QString, QList<quint8> >::iterator itor = m_qRelationshipMap.find(data1.strProjID);
        if(itor != m_qRelationshipMap.end())
        {
            QList<quint8>* qList = &m_qRelationshipMap[data1.strProjID];
            if(qList && qList->size()>0)
            {
                data1.uiColumnIndex = qList->at(0);//获取排序后的第一个
                ReagentBox* reagentBox = &m_qReagentMap[data1.uiColumnIndex];
                if(reagentBox && reagentBox->uiRemain>0)
                {
                    //大于等于组分的两倍，可以使用同一条试剂
                    if(reagentBox->uiRemain >= (reagentBox->uiCompNum - data1.uiAmplifyCompIndex) * 2)
                    {
                        bFind = _GetNextAvrRowPos(*reagentBox,data1.uiRowIndex,data1.uiAmplifyCompIndex);
                        data2 = data1;
                    }
                }

                // 不满足，需要使用两条试剂条
                if (!bFind && qList->size()>1)
                {
                    data2.uiColumnIndex = qList->at(1);//获取排序后的第二个
                    ReagentBox* reagentBox2 = &m_qReagentMap[data2.uiColumnIndex];
                    bFind &= _GetNextAvrRowPos(*reagentBox, data1.uiRowIndex,data1.uiAmplifyCompIndex);
                    if(reagentBox2 && reagentBox2->uiRemain>0)
                    {
                        bFind &= _GetNextAvrRowPos(*reagentBox2,data2.uiRowIndex,data2.uiAmplifyCompIndex);
                    }
                    else//可能存在不满足uiRemain大于0的情况
                    {
                        bFind = false;
                    }
                    qDebug()<<"GetNextAvrReagent reagentBox2"<<reagentBox2->uiRemain;
                }
                qDebug()<<"GetNextAvrReagent project"<<data1.strProjID<<reagentBox->uiRemain<<reagentBox->uiCompNum;
            }
        }        
    }
    else
    {
        bFind &= GetNextAvrReagent(data1.strProjID,data1.uiRowIndex,data1.uiColumnIndex,data1.uiAmplifyCompIndex);
        bFind &= GetNextAvrReagent(data2.strProjID,data2.uiRowIndex,data2.uiColumnIndex,data2.uiAmplifyCompIndex);
    }

    qDebug() << "Reagent::GetNextAvrReagent result:" << bFind 
             << "data1:" << data1.uiColumnIndex << data1.uiRowIndex 
             << "data2:" << data2.uiColumnIndex << data2.uiRowIndex
             << "m_qRelationshipMap:" << m_qRelationshipMap.keys() << m_qRelationshipMap.values()
             << "m_qReagentMap:" << m_qReagentMap.keys();
    return bFind;
}

bool Reagent::_GetNextAvrRowPos(ReagentBox reagentBox, quint8& uiNextAvrRowPos, quint8 uiSampleCompNum)
{
    if (reagentBox.uiRemain <= 0)
    {
        QDFUN_LINE << "Error reagentBox.uiRemain" << reagentBox.uiRemain;
        return false;
    }

    // 单孔人份数（12）
    quint8 uiSingleHoleCapacity = reagentBox.uiSingleHoleCapacity;
    if (reagentBox.uiSingleHoleCapacity == 0) {
        uiSingleHoleCapacity = REAGENT_SUB_CAPACITY;
    }
    
    // 组分数量（1~8）
    quint8 uiCompNum = reagentBox.uiCompNum;
    if (uiCompNum == 0) {
        uiCompNum = 1;
    }

    // 计算剩余量对应的孔位索引（从高位开始使用）（0~7）
    quint8 uiNextAvrPos = (reagentBox.uiCapacity / uiSingleHoleCapacity) - _GetCeilIndex(reagentBox.uiRemain, uiSingleHoleCapacity);
    
    // 孔位映射规则
    uiNextAvrRowPos = (uiNextAvrPos / uiCompNum) * uiCompNum + uiSampleCompNum;

    qDebug() << "_GetNextAvrRowPos: compNum=" << uiCompNum 
             << "remain=" << reagentBox.uiRemain
             << "nextAvrPos=" << uiNextAvrPos
             << "uiSampleCompNum" << uiSampleCompNum
             << "finalPos=" << uiNextAvrRowPos;
    
    if (uiNextAvrRowPos < 0 || uiNextAvrRowPos >= REAGENT_LYOPHILIZED_BALLS_SIZE)
    {
        QDFUN_LINE << "Error" << reagentBox.uiCapacity << uiSingleHoleCapacity;
        return false;
    }

    return true;
}

void Reagent::_UpdateStatus(REAGENT_STATE status,RFIDConsumableType cType)
{
    // switch (status)
    // {
    // case REGST_VALID://试剂可用
    //     HalSubSystem::getInstance().SetRFIDElecMagneticLock(ElecMagneticLock::Lock,ElecMagneticLock::Reagent,cType);
    //     break;
    // case REGST_EMPTY://试剂空
    // case REGST_INVALID://试剂不可用(过效期，过开瓶有效期)
    //     HalSubSystem::getInstance().SetRFIDElecMagneticLock(ElecMagneticLock::Unlock,ElecMagneticLock::Reagent,cType);
    //     break;            
    // default:
    //     break;
    // }
    // qDebug()<<"Reagent::_UpdateStatus"<<status<<cType;
}

REAGENT_STATE Reagent::_CheckReagentEnough(quint16 uiIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    REAGENT_STATE state = REGST_EMPTY;

    if(m_qReagentMap.count(uiIndex) == 0)
    {
        qWarning()<<"Reagent::_CheckReagentEnough error"<<" index:"<<uiIndex;
        return state;
    }
    ReagentBox* pBox = &m_qReagentMap[uiIndex];
    if(pBox && _IsReagentValid(*pBox))
    {
        if(pBox->uiRemain>0 && pBox->uiRemain<=pBox->uiCapacity)//剩余数量大于0,
        {
            state = REGST_VALID;
        }
    }    
    qDebug()<<"Reagent::_CheckReagentEnough"<<" uiIndex:"<<uiIndex<<" remain:"<<pBox->uiRemain<<"state: "<<state;
    return state;
}

void Reagent::CheckReagentStatus()
{
    qDebug()<<"Reagent::CheckReagentStatus"<<m_qReagentMap.keys()<<m_qReagentMap.size();
    // 需要根据组分判断当前试剂条能否继续使用(如果过期，需要删除这部分，否则会引起刺破和分装的异常)
    for (auto it = m_qReagentMap.begin(); it != m_qReagentMap.end(); ++it)
    {
        quint8 key = it.key();          // 获取当前元素的 key
        ReagentBox& reagent = it.value(); // 获取当前元素的 value

        // 不判断已超过有效期、剩余量为0的试剂条
        if (_IsReagentValid(reagent) || reagent.uiRemain == 0)
        {
            continue;
        }
        
        //判断复溶有效期(删除已过期组分)
        if (reagent.qFirstUsedTime.isValid() && 
            reagent.qFirstUsedTime.addDays(reagent.uiExpDays) > QDate::currentDate())
        {
            const quint8 groupSize = qMax<quint8>(1, REAGENT_LYOPHILIZED_BALLS_SIZE / reagent.uiCompNum);
            const quint8 groupCapacity = groupSize * reagent.uiSingleHoleCapacity;
            
            if (groupCapacity > 0) {
                const quint8 usedCapacity = reagent.uiCapacity - reagent.uiRemain;
                // 修正：使用向上取整计算过期组数
                const quint8 expiredGroups = static_cast<quint8>(
                    std::ceil(static_cast<float>(usedCapacity) / groupCapacity)
                );
                
                // 计算需要清除的总容量（整组清除）
                const quint8 expiredCapacity = expiredGroups * groupCapacity;
                
                // 更新剩余量（清除过期组的全部容量）
                if (reagent.uiRemain > expiredCapacity) {
                    // reagent.uiRemain -= expiredCapacity;
                    ConsumeAction(reagent, expiredCapacity,key);
                } else {
                    // reagent.uiRemain = 0;
                    ConsumeAction(reagent, reagent.uiRemain,key);
                }
                
                qDebug() << "CheckReagentStatus uiCompNum" << reagent.uiCompNum 
                         << "usedCapacity:"    << usedCapacity
                         << "expiredGroups:"   << expiredGroups
                         << "expiredCapacity:" << expiredCapacity
                         << "new uiRemain:"    << reagent.uiRemain;
            }
        }
    }   
    for (size_t i = 0; i < REAGENT_CAPACITY; i++)
    {
       _UpdateStatus(_CheckReagentEnough(i),CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, i));        
    }
}

bool Reagent::_IsReagentValid(ReagentBox &reagentBox)
{
    bool bValid = true;
    //进行有效期及开瓶有效期判断
    
    if(reagentBox.qExpDate.isValid() && 
       reagentBox.qExpDate < QDate::currentDate())
    {
        bValid = false;
    }
    
    // 不判断复溶有效期，开始测试前检查，使用函数(CheckReagentStatus)
    // if(reagentBox.qFirstUsedTime.isValid() && 
    //    reagentBox.qFirstUsedTime.addDays(reagentBox.uiExpDays)<QDate::currentDate())
    // {
    //     bValid = false;
    // }
    qDebug()<<"Reagent::IsReagentValid"<<bValid<<reagentBox.qExpDate<<reagentBox.qFirstUsedTime<<QDate::currentDate();
    return bValid;
}

void Reagent::AddReagentBox(quint8 uiColumnIndex, ReagentBox reagentBox,int iNeedWriteRFID)
{
    // 在系统构建时，如果试剂位已有试剂盒，则不添加
    QMap<quint8, ReagentBox>::iterator itor = m_qReagentMap.find(uiColumnIndex);
    if(m_bSystemBuildBusyStatus && itor != m_qReagentMap.end())
    {
        qDebug() << "Reagent::AddReagentBox uiColumnIndex" << uiColumnIndex 
                 << "uiRemain" << reagentBox.uiRemain << m_qReagentMap[uiColumnIndex].uiRemain;
        return;
    }

    QMutexLocker qLocker(&m_qMutex);
    qDebug()<<"AddReagentBox: "<< uiColumnIndex<<m_qRelationshipMap.keys()<<m_qReagentMap.keys();
    //如果当前还在效期内
    if(_IsReagentValid(reagentBox))
    {
        reagentBox.uiRemain -= reagentBox.uiRemain % reagentBox.uiCompNum; // 解决余量异常
        if(reagentBox.uiRemain>0 && reagentBox.uiRemain<=reagentBox.uiCapacity)//剩余数量大于0,
        {
            m_qReagentMap[uiColumnIndex] = reagentBox;
            //更新关联信息
            QMap<QString, QList<quint8> >::iterator itor = m_qRelationshipMap.find(reagentBox.strProjID);
            if(itor != m_qRelationshipMap.end()){
                QList<quint8>* qList;
                qList = &m_qRelationshipMap[reagentBox.strProjID];
                if (!qList->toSet().contains(uiColumnIndex))// 不存在则添加
                {
                    qList->push_back(uiColumnIndex);
                }
                _SortReagentLists(reagentBox.strProjID, qList);//试剂排序
            }
            else
            {
                QList<quint8> qList;
                qList.push_back(uiColumnIndex);
                m_qRelationshipMap[reagentBox.strProjID] = qList;
            }
            // CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Loaded, QString("reagentbox %1 loaded.").arg(uiColumnIndex));
            //FIXME 需要判断耗材是否充足，控制灯和锁的状态(开/关)
            _UpdateStatus(REGST_VALID,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex));             
        }
        else if(reagentBox.uiRemain>reagentBox.uiCapacity)
        {
            _UpdateStatus(REGST_INVALID,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex));             
             CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_StockOverflow, QString("reagentbox %1 remain %2 big than capacity %3.").
                                                          arg(uiColumnIndex).arg(reagentBox.uiRemain).arg(reagentBox.uiCapacity));
        }
        else if(reagentBox.uiRemain == 0)
        {
            _UpdateStatus(REGST_EMPTY,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex));             
            reagentBox.uiState = REGST_EMPTY;
            m_qReagentMap[uiColumnIndex] = reagentBox;
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Empty, QString("reagentbox %1 is empty").arg(uiColumnIndex));
        }
    }
    else
    {
        _UpdateStatus(REGST_INVALID,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex));             
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Expired, QString("reagentbox %1 is expired").arg(uiColumnIndex));
    }

    if(iNeedWriteRFID ==1)
    {
        //       0715
        CRFIDTask task;
        RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex);
        task.iType = RFIDConsumableType(cType);
        reagentBox.uiType = task.iType;
        task.iMethod = Method_rfid_write;
        task.rBox = reagentBox;
        qDebug()<<"AddReagentBox:"<<reagentBox.strProjID<<"BatchNo"<<reagentBox.strBatchNo<<"ExpDate:"<<reagentBox.qExpDate
                <<"Capacity:"<<reagentBox.uiCapacity <<"CompNum"<<reagentBox.uiCompNum<<"NextAvrRowPos:"<<reagentBox.uiNextAvrRowPos
                <<"FirstUsedTime"<<reagentBox.qFirstUsedTime <<"ExpDays:"<<reagentBox.uiExpDays<<"Type:"<<reagentBox.uiType;
        CRFIDMotionTask::getInstance().AddTask(task);
    }

    qDebug()<<"AddReagentBox finished: "<< uiColumnIndex<<m_qRelationshipMap.keys()<<m_qReagentMap.keys();
}

void Reagent::AddVirtualReagentBox(quint8 uiColumnIndex, QString strProjID, quint8 uiCompNum, quint8 uiRemain)
{
    qDebug()<<"AddVirtual ReagentBox uiColumnIndex:"<<uiColumnIndex<<"Project:"<<strProjID<<" compNum"<<uiCompNum<< "remain:"<<uiRemain;
    if (uiCompNum <= 0) 
    {
        uiCompNum = 1;
    }
    if (uiCompNum > REAGENT_LYOPHILIZED_BALLS_SIZE) 
    {
        uiCompNum = REAGENT_LYOPHILIZED_BALLS_SIZE;
    }
    ReagentBox reagentBox = {};
    reagentBox.strProjID = strProjID;
    reagentBox.uiState = REGST_VALID;
    reagentBox.uiCompNum = uiCompNum;
    reagentBox.uiCapacity = std::floor(REAGENT_LYOPHILIZED_BALLS_SIZE / uiCompNum) * (REAGENT_SUB_CAPACITY * uiCompNum);
    reagentBox.uiRemain = uiRemain > reagentBox.uiCapacity ? reagentBox.uiCapacity : uiRemain;
    reagentBox.uiNextAvrRowPos = _GetNextAvrPos(reagentBox);
    reagentBox.uiExpDays = 14;
    reagentBox.uiSingleHoleCapacity = REAGENT_SUB_CAPACITY;
    reagentBox.qExpDate = QDate::currentDate().addDays(365);
    reagentBox.qFirstUsedTime = QDate::currentDate();
    reagentBox.uiColumnIndex = uiColumnIndex;
    AddReagentBox(uiColumnIndex, reagentBox);
}

void Reagent::RemoveReagentBox(quint8 uiColumnIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    qDebug()<<"Reagent::RemoveReagentBox"<<uiColumnIndex;

    QMap<quint8, ReagentBox>::iterator itor = m_qReagentMap.find(uiColumnIndex);
    if(itor != m_qReagentMap.end())
    {
        ReagentBox reagentBox = m_qReagentMap[uiColumnIndex];

        // 在系统构建时，如果试剂条还有剩余量，则不删除，防止试剂条因为光耦状态变化被卸载
        if (m_bSystemBuildBusyStatus && reagentBox.uiRemain > 0)
        {
            qDebug()<<"Reagent::RemoveReagentBox"<<uiColumnIndex<<"busy"<<reagentBox.uiRemain;
            return;
        }

        m_qReagentMap.remove(uiColumnIndex);
        // CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Unloaded,QString("reagent box %1 is unloaded.").arg(uiColumnIndex));
        QMap<QString, QList<quint8>>::iterator iter = m_qRelationshipMap.find(reagentBox.strProjID);
        if(iter != m_qRelationshipMap.end())
        {
            QList<quint8>* qList = &m_qRelationshipMap[reagentBox.strProjID];
            if(qList)
            {
                for(int i=0; i< qList->size();i++)
                {
                    if(qList->at(i) == uiColumnIndex)
                    {
                        qList->removeAt(i);
                        // break;// 防止有同样的项目没有被删除
                    }
                }
                if (qList->isEmpty())// 如果没有元素了，移除该项目
                {
                    m_qRelationshipMap.remove(reagentBox.strProjID);
                }
                
                _SortReagentLists(reagentBox.strProjID, qList);//试剂排序
            }
        }
    }
    qDebug()<<"Reagent::RemoveReagentBox keys"<<m_qReagentMap.keys()<<m_qRelationshipMap.keys();
}

quint8 Reagent::_GetCeilIndex(quint8 uiNum, quint8 uiSingleHoleCapacity)
{
    if (uiSingleHoleCapacity == 0)
    {
        return 0;
    }    
    // quint8 uiIndex = std::ceil(1.0*uiNum/REAGENT_SUB_CAPACITY)*uiCompNum;
    quint8 uiIndex = std::ceil(1.0*uiNum/uiSingleHoleCapacity);
    qDebug()<<"Reagent::_GetCeilIndex"<<uiNum<<"uiCompNum:"<<uiSingleHoleCapacity<<"uiIndex:"<<uiIndex;
    return uiIndex;
}

quint8 Reagent::_GetFloorIndex(quint8 uiNum, quint8 uiSingleHoleCapacity)
{
    if (uiSingleHoleCapacity == 0)
    {
        return 0;
    }
    // quint8 uiIndex = std::floor(1.0*uiNum/REAGENT_SUB_CAPACITY)*uiCompNum;
    quint8 uiIndex = std::floor(1.0*uiNum/uiSingleHoleCapacity);// 组分数不影响孔位索引计算
    qDebug()<<"Reagent::_GetFloorIndex"<<uiNum<<"uiCompNum:"<<uiSingleHoleCapacity<<"uiIndex:"<<uiIndex;
    return uiIndex;
}

void Reagent::_GetFloorSection(ReagentBox* pReagentBox,quint8& subPackStart,quint8& subPackEnd,quint8 uiTotalUseSize)
{
    if (pReagentBox == nullptr) return;
    
    const quint8 uiCompNum = qMin(pReagentBox->uiCompNum, (quint8)REAGENT_LYOPHILIZED_BALLS_SIZE);
    const quint8 uiRemain = pReagentBox->uiRemain;
    const quint8 uiSingleHoleCapacity = pReagentBox->uiSingleHoleCapacity;
    const quint8 totalCapacity = pReagentBox->uiCapacity;

    // 新增分组计算逻辑
    const quint8 groupCount = (uiCompNum > 0) ? std::ceil(1.0 * REAGENT_LYOPHILIZED_BALLS_SIZE / uiCompNum) : 0;
    const quint8 groupCapacity = uiCompNum * uiSingleHoleCapacity;
    QDFUN_LINE << uiTotalUseSize << uiCompNum << uiRemain << uiSingleHoleCapacity << totalCapacity << groupCount << groupCapacity;
    if (0 == groupCount)
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_ReadError, QString("Reagent ColumnIndex %1 info faild").arg(pReagentBox->uiColumnIndex));
        QDFUN_LINE << "Error" << QString("Reagent ColumnIndex %1 info faild").arg(pReagentBox->uiColumnIndex) << uiCompNum;
        return;
    }

    // 已穿刺的量
    const quint8 uiDiff = uiRemain % groupCapacity;
    if (uiDiff >= uiTotalUseSize) 
    {
        QDFUN_LINE << "uiDiff" << uiDiff << uiTotalUseSize;
        subPackStart = 0;
        subPackEnd = 0;
        return;
    }

    // 需要再穿刺的量
    uiTotalUseSize -= uiDiff;
    QDFUN_LINE << "uiTotalUseSize" << uiTotalUseSize << "uiDiff" << uiDiff;

    // 计算实际需要的完整组数（规格×数量）
    quint8 neededGroups = 0;
    if(groupCapacity > 0) {
        neededGroups = std::ceil(1.0 * uiTotalUseSize / groupCapacity);
        neededGroups = qMin(neededGroups, groupCount);
    }
    QDFUN_LINE << "neededGroups" << neededGroups;

    // 计算起始组（考虑当前剩余和完整组需求）
    quint8 startGroup = 0;
    if(groupCapacity > 0 && totalCapacity > 0) {
        const quint8 consumed = totalCapacity - uiRemain;
        startGroup = std::ceil(1.0 * consumed / groupCapacity) * uiCompNum;
        startGroup = qMin(startGroup, static_cast<quint8>(groupCount - neededGroups));
    }
    QDFUN_LINE << "startGroup" << startGroup;

    subPackStart = startGroup * uiCompNum;
    subPackEnd = subPackStart + uiCompNum * neededGroups;
    QDFUN_LINE << "subPackStart" << subPackStart << "subPackEnd" << subPackEnd;

    // 边界保护
    subPackEnd = qMin(subPackEnd, (quint8)REAGENT_LYOPHILIZED_BALLS_SIZE);
    QDFUN_LINE << "subPackEnd" << subPackEnd;
}


bool Reagent::_NeedSwitchGroup(ReagentBox* pBox, quint8 uiTotalUseSize)
{
    if (!pBox) return false;
    
    const quint8 groupCapacity = pBox->uiCompNum * pBox->uiSingleHoleCapacity;
    if (groupCapacity == 0) return false;

    // 计算当前组剩余容量
    const quint8 currentGroupRemain = pBox->uiRemain % groupCapacity;
    
    // 两种情况需要切换组：
    // 1. 当前组剩余量不足且本次用量会耗尽当前组
    // 2. 本次用量超过当前组剩余容量
    bool bSwitch = (currentGroupRemain > 0 && currentGroupRemain <= uiTotalUseSize) || 
                   (uiTotalUseSize > currentGroupRemain);

    qDebug() << "_NeedSwitchGroup uiCompNum" << pBox->uiCompNum 
             << "uiRemain" << pBox->uiRemain
             << "uiTotalUseSize" << uiTotalUseSize
             << "currentGroupRemain" << currentGroupRemain
             << "bSwitch" << bSwitch;

    return bSwitch;
}

bool Reagent::IsReagentEnough(QString strProjID, quint8 uiTotalUseSize)
{
    QMutexLocker qLocker(&m_qMutex);
    _ClearSubPackDatas();
    //根据项目ID查找当前所有满足需求的试剂,确认总量是否满足预期
    bool bResult = false;
    bool bCountTotalUseSize = false;

    //1、根据项目ID查找当前所需的试剂位置,并排查总体数量确认是否满足预期
    //2、再判断是否存在跨试剂条

    QMap<QString, QList<quint8> >::iterator itor = m_qRelationshipMap.find(strProjID);
    if(itor != m_qRelationshipMap.end())
    {
        QList<quint8>* qList = &m_qRelationshipMap[strProjID];
        if(qList)
        {
            // 1、判断试剂是否充足，计算对应项目所有试剂条的数量(考虑多组分情况)
            quint16 u16AllReagentCount = 0;
            quint8  uiCompNum = 0;
            for(int i= 0;i<qList->size();i++)
            {
                quint8 uiColumnIndex = qList->at(i);
                ReagentBox* reagentBox = &m_qReagentMap[uiColumnIndex];
                if(reagentBox)
                {
                    u16AllReagentCount += reagentBox->uiRemain;
                    uiCompNum = reagentBox->uiCompNum;
                }
            }

            quint8 uiUseSize = uiTotalUseSize*uiCompNum;
            qDebug()<<"Reagent::IsReagentEnough u16AllReagentCount:"<<u16AllReagentCount<<uiUseSize<<uiCompNum;
            if (u16AllReagentCount == 0 || uiUseSize == 0)// 提前检查结束，试剂已经不足
            {
                return bResult;
            }

            if (u16AllReagentCount >= uiUseSize)
            {
                bResult = true;
            }

            //2、剔除已经全部刺破试剂条(根据剩余量判断)
            QList<quint8> listPunchColumnIndex;//需要刺破
            QList<quint8> listColumnIndex;//不需要刺破
            for(int i= 0;i<qList->size();i++)
            {
                quint8 uiColumnIndex = qList->at(i);
                ReagentBox* reagentBox = &m_qReagentMap[uiColumnIndex];
                if(reagentBox)
                {
                    // 计算孔组参数
                    quint8 holesPerGroup = qMin(reagentBox->uiCompNum, static_cast<quint8>(REAGENT_LYOPHILIZED_BALLS_SIZE));
                    quint8 totalGroups = (holesPerGroup > 0) ? (REAGENT_LYOPHILIZED_BALLS_SIZE / holesPerGroup) : 0;
                    quint8 groupCapacity = holesPerGroup * reagentBox->uiSingleHoleCapacity;
                    
                    // 计算已使用的完整孔组数量
                    quint8 usedGroups = (reagentBox->uiCapacity - reagentBox->uiRemain) / groupCapacity;
                    
                    // 判断是否还有可用孔组
                    bool bFullyUsed = (usedGroups >= totalGroups) || 
                                     (reagentBox->uiRemain < groupCapacity && usedGroups == (totalGroups - 1));
                    
                    // 仅保留未完全使用的试剂条
                    if (!bFullyUsed) {
                        listPunchColumnIndex.append(uiColumnIndex);
                    }
                    else {
                        listColumnIndex.append(uiColumnIndex);
                        qDebug() << "Reagent box" << uiColumnIndex << "fully used, compNum:" 
                                << reagentBox->uiCompNum << "remain:" << reagentBox->uiRemain;
                    }
                }
            }
            qDebug()<<"Reagent::IsReagentEnough listPunchColumnIndex:"<<qList->size()<<listPunchColumnIndex<<"listColumnIndex:"<<listColumnIndex;

            //3、判断不需要刺破的试剂条是否满足(判断是否已经有刺破)
            quint16 uiCountTotalNoPunchSize = 0;
            for (auto it = listColumnIndex.begin(); it != listColumnIndex.end();++it)
            {
                quint8 uiColumnIndex = *it;
                ReagentBox* reagentBox = &m_qReagentMap[uiColumnIndex];
                if(reagentBox)
                {   
                    uiCountTotalNoPunchSize += reagentBox->uiRemain;
                } 
            }
            if (uiCountTotalNoPunchSize >= uiUseSize)// 不需要刺破的试剂条且数量满足
            {
                qDebug()<<"IsReagentEnough proj:"<< strProjID<<"totalUseSize"<<uiTotalUseSize<<m_qRelationshipMap.keys()<<bResult<<uiCountTotalNoPunchSize<<uiUseSize;
                return bResult;
            }

            //4、判断需要刺破的试剂条数量和孔位
            quint16 uiRemainNeedPunch = uiUseSize - uiCountTotalNoPunchSize;
            quint16 uiCountTotalPunchSize = 0;
            
            for (auto it = listPunchColumnIndex.begin(); it != listPunchColumnIndex.end(); ++it)
            {
                quint8 uiColumnIndex = *it;
                ReagentBox* reagentBox = &m_qReagentMap[uiColumnIndex];
                if(reagentBox && uiRemainNeedPunch > 0)
                {   
                    // 计算当前试剂条可用量
                    quint16 uiAvailable = qMin((int)reagentBox->uiRemain, (int)uiRemainNeedPunch);
                    
                    // 添加刺破数据
                    quint8 subPackStart = 0;
                    quint8 subPackEnd = 0;                            
                    _GetFloorSection(reagentBox,subPackStart,subPackEnd,uiAvailable);
                    _AddReagentBallSubPackDataToVect(uiColumnIndex, subPackStart, subPackEnd, strProjID, *reagentBox);// 在函数中判断刺破和复溶孔位
                    qDebug()<<"IsReagentEnough uiPackHole: "<<reagentBox->uiPackHole<<reagentBox->uiPunchHole<<reagentBox->qFirstUsedTime;
                    // 更新用量
                    uiCountTotalPunchSize += uiAvailable;
                    uiRemainNeedPunch -= uiAvailable;
                    if(uiRemainNeedPunch == 0) break;
                }                 
            }
            
            // 5、最终校验
            if(uiRemainNeedPunch > 0){
                qDebug() << "Insufficient punch capacity! Need:" << uiUseSize 
                        << " NoPunch:" << uiCountTotalNoPunchSize
                        << " PunchProvided:" << uiCountTotalPunchSize;
            }
            
        }
    }
    else
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_InsufficientMaterial, QString("Project Name %1 is no exist,please check reagent strip.").arg(strProjID));
    }
    qDebug()<<"IsReagentEnough proj:"<< strProjID<<"totalUseSize"<<uiTotalUseSize<<m_qRelationshipMap.keys()<<bResult;
    return bResult;
}

void Reagent::ConsumeAction(ReagentBox reagentBox, quint8 uiUseSize, quint8 uiColumnIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    qDebug()<<reagentBox.strProjID<<" _ConsumeAction columnIndex:"<< uiColumnIndex<<" CurRemain:"<<reagentBox.uiRemain<<" ConsumeSize:"<<uiUseSize;
    reagentBox.uiRemain -= uiUseSize;
    //如果当前试剂已耗尽则清除关联信息
    if(reagentBox.uiRemain<=0)
    {
        QMap<QString, QList<quint8>>::iterator iter = m_qRelationshipMap.find(reagentBox.strProjID);
        if(iter != m_qRelationshipMap.end())
        {
            QList<quint8>* qList = &m_qRelationshipMap[reagentBox.strProjID];
            if(qList&&qList->size()>0)
            {
                qList->removeAt(0);
            }
        }
        //FIXME 需要判断耗材是否充足，控制灯和锁的状态(开/关)
        _UpdateStatus(REGST_EMPTY,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex));         
    }
    reagentBox.uiNextAvrRowPos = _GetNextAvrPos(reagentBox);
    m_qReagentMap[uiColumnIndex] = reagentBox;

    CRFIDTask task;
    RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, uiColumnIndex);
    task.iType = RFIDConsumableType(cType);
    task.iMethod = Method_rfid_write;
    reagentBox.uiType = task.iType;
    task.rBox = reagentBox;
    qDebug()<<"ConsumeAction:"<<reagentBox.strProjID<<"BatchNo"<<reagentBox.strBatchNo<<"ExpDate:"<<reagentBox.qExpDate
            <<"Capacity:"<<reagentBox.uiCapacity <<"CompNum"<<reagentBox.uiCompNum<<"NextAvrRowPos:"<<reagentBox.uiNextAvrRowPos
            <<"FirstUsedTime"<<reagentBox.qFirstUsedTime <<"ExpDays:"<<reagentBox.uiExpDays<<"Type:"<<reagentBox.uiType;
    CRFIDMotionTask::getInstance().AddTask(task);     
}

void Reagent::ConsumeReagent(quint8 uiColumnIndex, quint8 uiUseSize/* = 1*/)
{
    //消耗特定位的试剂
Consume:
    m_qMutex.lock();
    ReagentBox reagentBox = m_qReagentMap[uiColumnIndex];
    m_qMutex.unlock();
    if(reagentBox.uiRemain >= uiUseSize)
    {
        ConsumeAction(reagentBox, uiUseSize, uiColumnIndex);  //里面写rfid
    }
    else
    {
        ConsumeAction(reagentBox, reagentBox.uiRemain, uiColumnIndex);  //里面写rfid
        if(CGlobalConfig::getInstance().getAgingMode() && reagentBox.uiRemain<=0)
        {
            RemoveReagentBox(reagentBox.uiColumnIndex);
            reagentBox.uiRemain = reagentBox.uiCapacity;
            AddReagentBox(reagentBox.uiColumnIndex, reagentBox);
            qDebug()<<reagentBox.strProjID<<"column:"<<reagentBox.uiColumnIndex
                   <<"Aging Mode reset reagentBox remain:"<<reagentBox.uiRemain;
        }
        quint8 uiRowIndex = 0;
        bool bGet = GetNextAvrReagent(reagentBox.strProjID, uiRowIndex, uiColumnIndex,uiRowIndex);//最后uiRowIndex不起作用，默认使用0
        if(bGet)
            goto Consume;
        else
             return;
    }   
}

void Reagent::_AddReagentBallSubPackDataToVect(quint8 uiColumnIndex, quint8 uiSubPackStartIndex,
                                              quint8 uiSubPackEndIndex, QString& strProjID, ReagentBox reagentBox)
{
    SubPackData data;
    data.strProjID = strProjID;
    data.uiColumnIndex = uiColumnIndex;
    quint8 uiTotalSize = REAGENT_DUILENT_SIZE + REAGENT_LYOPHILIZED_BALLS_SIZE;

    for(quint8 i = uiSubPackStartIndex; i < uiSubPackEndIndex; i++)
    {
        data.uiRowIndex = i;
        if(data.uiRowIndex < REAGENT_LYOPHILIZED_BALLS_SIZE / 2)
            data.uiDuilentRowIndex = uiTotalSize - 2;//右边4个冻干球用把手从左到右第2个稀释液
        else
            data.uiDuilentRowIndex = uiTotalSize - 1;//左边4个冻干球用把手从左到右第1个

        if (reagentBox.uiSingleHoleCapacity != 0)
        {
            if((REAGENT_LYOPHILIZED_BALLS_SIZE-i)%(DUILENT_CAPACITY/reagentBox.uiSingleHoleCapacity) == 0)
                data.bNeedPunchDuilent = true;
            else
                data.bNeedPunchDuilent = false;
        }

        quint8 uiCompIndex = (data.uiRowIndex)%reagentBox.uiCompNum;
        QMap<quint8, QVector<SubPackData>> &innerMap = m_qReagentSubPackDataMap[strProjID];
        QVector<SubPackData> &vector = innerMap[uiCompIndex];// 然后，根据uiCompIndex获取或创建内层QMap中的QVector

        {
            if (vector.isEmpty()) {
                vector = QVector<SubPackData>();
            }
            // 最后，将SubPackData对象添加到QVector中
            vector.push_back(data);
            std::sort(vector.begin(), vector.end(),[](SubPackData a, SubPackData b) { return a.uiRowIndex < b.uiRowIndex; });
        }

        {
            QDFUN_LINE << data.printInfo();
            m_qReagentBallSubPackDataVect.push_back(data);
            std::sort(m_qReagentBallSubPackDataVect.begin(), m_qReagentBallSubPackDataVect.end(),[](SubPackData a, SubPackData b) { return a.uiRowIndex < b.uiRowIndex; });
        }

        qDebug()<<"Reagent sub pack rowIndex: "<<data.uiRowIndex<<"columnIndex:"<<data.uiColumnIndex
                <<"duilent rowIndex:"<<data.uiDuilentRowIndex << "NeedPunchD"<<data.bNeedPunchDuilent
                <<"uiCompIndex:"<<uiCompIndex
                <<"m_qReagentSubPackDataMap: "<<m_qReagentSubPackDataMap.keys()
                <<"m_qReagentBallSubPackDataVect:"<<m_qReagentBallSubPackDataVect.size()  
                <<"vector:"<<vector.size()
                <<"uiPunchHole:"<<reagentBox.uiPunchHole<<reagentBox.uiPackHole;     
     }
}

QMap<QString, QMap<quint8, QVector<SubPackData>>> Reagent::GetReagentBallSubPackDatas()
{
    QMutexLocker qLocker(&m_qMutex);
    return m_qReagentSubPackDataMap;
}

QVector<SubPackData> Reagent::GetReagentBallSubPackDatasVect()
{
    QMutexLocker qLocker(&m_qMutex);
    return  m_qReagentBallSubPackDataVect;
}

void Reagent::_ClearSubPackDatas()
{
    m_qReagentSubPackDataMap.clear();
    m_qReagentBallSubPackDataVect.clear();
}

void Reagent::UpdateReagentStatus(SubPackData data)
{
    QMutexLocker qLocker(&m_qMutex);
    if(!m_qReagentMap.contains(data.uiColumnIndex))
    {
        qDebug()<<"UpdateReagentStatus error: "<<data.uiColumnIndex;
        return;
    }
    auto& reagentBox = m_qReagentMap[data.uiColumnIndex];
    reagentBox.qFirstUsedTime = QDate::currentDate();
    reagentBox.uiPackHole = data.uiRowIndex + 1;//有复溶uiPackHole就不为0
    CRFIDTask task;
    RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, data.uiColumnIndex);
    task.iType = RFIDConsumableType(cType);
    task.iMethod = Method_rfid_write;
    task.rBox = reagentBox;
    qDebug()<<"UpdateReagentStatus:"<<reagentBox.strProjID<<"BatchNo"<<reagentBox.strBatchNo<<"ExpDate:"<<reagentBox.qExpDate
            <<"Capacity:"<<reagentBox.uiCapacity <<"CompNum"<<reagentBox.uiCompNum<<"NextAvrRowPos:"<<reagentBox.uiNextAvrRowPos
            <<"FirstUsedTime"<<reagentBox.qFirstUsedTime <<"ExpDays:"<<reagentBox.uiExpDays<<"Type:"<<reagentBox.uiType<<data.uiRowIndex;
    CRFIDMotionTask::getInstance().AddTask(task); 
}

void Reagent::UpdateReagentPunchInfo(SubPackData data)
{
    QMutexLocker qLocker(&m_qMutex);
    if(!m_qReagentMap.contains(data.uiColumnIndex))
    {
        qDebug()<<"UpdateReagentPunchInfo error: "<<data.uiColumnIndex;
        return;
    }
    auto& reagentBox = m_qReagentMap[data.uiColumnIndex];
    reagentBox.qPunchTime = QDate::currentDate();
    reagentBox.uiPunchHole = data.uiRowIndex + 1;// 有刺破uiPunchHole就不为0
    CRFIDTask task;
    RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_REAGENT, data.uiColumnIndex);
    task.iType = RFIDConsumableType(cType);
    task.iMethod = Method_rfid_write;
    task.rBox = reagentBox;
    qDebug()<<"UpdateReagentPunchInfo:"<<reagentBox.strProjID<<"BatchNo"<<reagentBox.strBatchNo<<"ExpDate:"<<reagentBox.qExpDate
            <<"Capacity:"<<reagentBox.uiCapacity <<"CompNum"<<reagentBox.uiCompNum<<"NextAvrRowPos:"<<reagentBox.uiNextAvrRowPos
            <<"FirstUsedTime"<<reagentBox.qFirstUsedTime <<"ExpDays:"<<reagentBox.uiExpDays<<"Type:"<<reagentBox.uiType<<data.uiRowIndex;
    CRFIDMotionTask::getInstance().AddTask(task); 
}

void Reagent::SetSystemBuildBusyStatus(bool bIsBusy)
{
    m_bSystemBuildBusyStatus = bIsBusy;
    qDebug()<<"Reagent::SetSystemBuildBusyStatus"<<bIsBusy;
}

void Reagent::_SortReagentLists(const QString& strProjID,QList<quint8>* pList)
{
    if(!m_qRelationshipMap.contains(strProjID))
    {
        qDebug()<<"Reagent::_SortReagentLists error: "<<strProjID;
        return;
    }

    if (pList == nullptr)
    {
        qDebug()<<"Reagent::_SortReagentLists pList null";
        return;
    }

    QList<quint8> qList = m_qRelationshipMap[strProjID];//获取同一个项目的试剂列表
    QList<ReagentBox> qListReagent;
    for(auto index : qList)
    {
        auto box = m_qReagentMap[index];
        qListReagent.push_back(box);
    }

    // 对列表进行排序(同一批次时优先使用快过期和余量少的试剂)
    std::sort(qListReagent.begin(), qListReagent.end(), [](const ReagentBox &a, const ReagentBox &b) {
        if (a.qFirstUsedTime < b.qFirstUsedTime) {
            return true;
        } else if (a.qExpDate == b.qExpDate) {
            return a.uiRemain < b.uiRemain;
        } else {
            return false;
        }
    });

    qDebug()<<"Reagent::_SortReagentLists list: "<<*pList; 
    pList->clear();
    // 获取排序后的列表
    for (auto& reagent:qListReagent)
    {
        pList->push_back(reagent.uiColumnIndex);
    }

    qDebug()<<"Reagent::_SortReagentLists sort: "<<strProjID<<" list:"<<*pList; 
    qListReagent.clear();
}
