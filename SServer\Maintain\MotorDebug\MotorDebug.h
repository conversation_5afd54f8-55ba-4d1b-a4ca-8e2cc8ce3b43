#ifndef MotorDebug_H
#define MotorDebug_H

#include<QObject>
#include "MotorConfigMgr.h"
#include"publicconfig.h"

class MotorDebug : public QObject
{
    Q_OBJECT
public:

//    enum MotorDebugOperation// 电机调试操作
//    {
//        set,   // 设置(参数微调使用)
//        get,   // 获取(参数微调使用)
//        start, // 开始
//        stop,  // 结束
//        save,  // 保存
//        prev,  // 上一个
//        next,  // 下一个
//    };
//    Q_ENUM( MotorDebugOperation)

    enum MotorDebugCmd // 电机命令
    {
        increase, // 步数+
        decrease, // 步数-
        unload,   // 卸载
        recovery, // 恢复
        verify,   // 验证
        IsNeedSave, // 是否让UI显示保存 1 ，是  0 否
        debug,    // 调试值  
    };
    Q_ENUM( MotorDebugCmd) 

    struct MotorDebugParam // 电机参数
    {
        QString strName;      // 电机名称
        QString strCmd;       // 执行命令(由枚举MotorDebugCmd转字符串填充)
        QString strValue;     // 参数
    };
public:
    /**
     * @brief DebugOperation 电机调试操作
     * @param strParams 参数
     * @return  
     */
    void DebugOperation(const QString strParams); 

    /**
     * @brief StartDebug 开始调试(执行初始化)
     * @param strParams 参数
     * @return  
     */
    void StartDebug(const QString strParams); 

    /**
     * @brief SetMotorPos 设置电机位置参数(微调)
     * @param strParams 设置电机参数
     * @return  
     */
    void SetMotorPos(const QString strParams); 

    /**
     * @brief GetMotorPos 设置电机位置参数(上位机获取)
     * @param strParams 获取电机参数
     * @return  
     */
    void GetMotorPos(const QString strParams); 

    /**
     * @brief HandleCmdReply 命令执行结果返回
     * @param uiComplexID 执行命令
     * @param uiResult    执行结果
     * @return  
     */
    void HandleCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult);


    void AskDebugPosActionList(QString strInputParam);
    void AskDebugPosDeviceList(QString strInputParam);
    void SaveMotorParam(QString strInputParam);  //单个
    //如果准备动作涉及跨板子的，则initcmd 的形式 BoardIDX &MethodIDX | BoardIDY &MethodIDY,验证类似，如果没有&链接，则使用common 的boardIdx
    //param init与verify的用$隔开，init或者verfiy里面有多个动作的参数用|隔开，参数不止一个的用&链接 ，
    //如  10|11$12|13&14   (10属于init1的，11属于init2,12属于verify1，13，14属于verify2)
    //如果是UI传入参数的，目前initcmd及vefifycmd只能是一个指令，不能多个
    int DoPrepareAction(QString strInputParam,QString strUIPayload="",int iRlyMethood=-1); //非-1如果回复前的id则执行同级的下一个动作 ,-1默认执行第一个，  0当前已经发出 1全部发出   -1失败
    int DoVerifyAction(QString strInputParam,QString strUIPayload="",int iRlyMethood=-1);  //非-1 回复前的id则执行同级的下一个动作   ,-1默认执行第一个， 0当前已经发出 1全部发出   -1失败
    void DoLiquidTest(QString strInputParam);
    void DoLiquidCalSuck(QString strInputParam);

    void SaveSuckZParam(QString strZPos,QString strZMaxPos);
    void SaveSplitZParam(QString strSplitZPos);


private:
    /**
     * @brief _SaveParam 保存参数
     * @param 
     * @return  
     */
    void _SaveParam();  

    /**
     * @brief _Prev 上一个调试项目
     * @param 
     * @return  
     */

    void _Prev(); 

    /**
     * @brief _Next 下一个调试项目
     * @param 
     * @return  
     */
    void _Next();  

     QString  _tranModuleEnumNunToString(int iModuleNum);
private:
    QString m_strCurrentDebugProject;    // 当前调试项目 
    int m_iCurrentMachineID;             // 当前板卡       
    MotorConfigMgr* m_pMotorConfigMgr;   // 电机配置管理器
signals:

public slots:

public:
    MotorDebug();
    ~MotorDebug();
private:
    Q_DISABLE_COPY(MotorDebug);
    QString m_strCurDebugEnumNumModuleName;   //枚举模块 的数字 //由于调试的时候是一个接一个，先这样写 ，用于处理回复
    QString m_strCurDebugEnumStrSubActionName;  //枚举字符串，非数字
    QString m_strCurDebugMotorName ;
    //strParam配置档的参数 传入的是已经切割好的输入init或者verify
    //strRsList ， 0是boardidx， 1 methodID   strParam strPaylodParam
    //iRlyMethod=-1 默认找第一个，非-1 的要真实找
     int findNextCmdBoardMethodAndParam(QString strInputCmdParam,QString strParam,int iCommonBoardIdx ,
                                        int &iNextBoardIdx,int &iNextMethodID,QString &strPaylodParam,int iRlyMethod); //-1:fail 0ok  1finial  iRlyMethod是最后一个
};


#endif // MotorDebug_H
