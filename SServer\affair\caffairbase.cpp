#include "caffairbase.h"
#include <QDebug>


CAffairBase::CAffairBase(QObject *parent)
    : QThread(parent)
  , m_bThreadExit(false)
{


}

CAffairBase::~CAffairBase()
{
    m_bThreadExit = true;
}

void CAffairBase::slotAddReciveMsg(QByteArray qMsgBtyeArray)
{
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qSendMessageInfoList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
    m_conditionVariable.notify_one();// 唤醒
}

void CAffairBase::run()
{
    qDebug() << "Starting _createConcurrentThread in" << this;
    std::unique_lock<std::mutex> uniqueLock(this->m_mutex);
    while(!this->m_bThreadExit)
    {
        this->m_conditionVariable.wait(uniqueLock, [this]
        {
            return this->m_iReadIndex.load() != this->m_iWriteIndex.load()
                    || this->m_bThreadExit;
        });
        if (this->m_bThreadExit)
        {
            break;
        }
        this->_HandleReceiveList();
    }
}

void CAffairBase::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];


        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}

