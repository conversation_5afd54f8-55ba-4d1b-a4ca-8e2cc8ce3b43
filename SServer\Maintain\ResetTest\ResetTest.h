#ifndef CRESETTEST_H
#define CRESETTEST_H

#include<QObject>
#include<QHash>
#include<QList>
#include<QSet>
#include<QQueue>
#include <functional>

class CResetTest : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief  StartResetTest 开始复位测试
     * @param  uiSeqType 时序类型
     * @return  
     */
    void StartResetTest(quint16 uiSeqType); 

    /**
     * @brief  SendGainOptoStatusCommand 下发命令获取所有光耦当前状态
     * @param 
     * @return  
     */
    void SendGainOptoStatusCommand();   
    
    /**
     * @brief  SendElecMagneticLockCommand 下发组件上锁
     * @param 
     * @return  
     */
    void SendElecMagneticLockCommand();   

    /**
     * @brief  HandleTimeseqReply 接收时序执行结果
     * @param  uiComplexID 时序执行的id
     * @param  uiResult    时序结果
     * @return  
     */
    void HandleTimeseqReply(quint16 uiComplexID, quint16 uiResult);   
    
signals:

public slots:

public:
  CResetTest();
  ~CResetTest();
private:
    /**
     * @brief  _init 初始化
     * @param 
     * @return  
     */
    void _init(); 

    /**
     * @brief  _resetInitHash 初始化hash结构体
     * @param strName 名称
     * @param hash    初始化对象
     * @param list    hash的key
     * @return  
     */
    void _initHash(QString strName,QHash<quint16, bool>& hash,QList<quint16> list);

    /**
     * @brief  _resetInitHash 初始化hash结构体
     * @param strName 名称
     * @param hash    初始化对象
     * @return  
     */
    void _resetInitHash(QString strName,QHash<quint16, bool>& hash);

    /**
     * @brief  _resetStep 重置步骤
     * @param  
     * @return  
     */
    void _resetStep(); 

    /**
     * @brief  _checkBoard 检查板卡条件
     * @param  str  执行名称
     * @param  hash 验证对象
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkBoard(QString str, QHash<quint16, bool>& hash, quint16 uiComplexID);

    /**
     * @brief  _checkAllBoardZInit 检查所有板卡的Z轴复位成功
     * @param uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardZInit(quint16 uiComplexID); 

    /**
     * @brief  _checkAllBoardXYInit1 检查非Z轴板卡复位成功
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardXYInit(quint16 uiComplexID); 

    /**
     * @brief  _clearRemainingSteps 清空步骤
     * @param 
     * @return  
     */    
    void _clearRemainingSteps();

    /**
     * @brief  _executeCurrentStage 执行最新步骤
     * @param 
     * @return  
     */    
    void _executeCurrentStage();   
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param  uiComplexID     时序id
     * @param  strParam        时序参数
     * @return  
     */    
    void _onBoardAction(const QString strName,quint16 uiComplexID,QString strParam); 

    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard1ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard2ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard3ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard4ZInit();     

    /**
     * @brief  _onBoard1RemainInit  板卡1执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard1RemainInit();

    /**
     * @brief  _onBoard2RemainInit  板卡2执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard2RemainInit();

    /**
     * @brief  _onBoard3RemainInit  板卡3执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard3RemainInit();

    /**
     * @brief  _onBoard4RemainInit  板卡4执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard4RemainInit();

    /**
     * @brief _sendSelfResetResult 发送复位结果
     * @param uiResult 结果
     */    
    void _sendSelfResetResult(quint16 uiResult); 

    /**
     * @brief _onUpdateMiddleHostStatus 更新中位机状态
     */    
    void _onUpdateMiddleHostStatus();  
              
private: 
    quint16 m_uiSeqType = 0;                                      // 自检的时序类型索引
    QHash<quint16, std::function<bool(quint16)>> m_stepValidators;// 步骤检验
    QHash<quint16, std::function<void()>> m_stepExecutors;        // 步骤执行
    QList<QList<quint16>> m_listRunningStep;                      // 运行步骤
    QSet<quint16> m_allStepIds;                                   // 所有的步骤
    bool m_isAborted;                                             // 异常停止标记
    quint16 m_uiCurrentStageIndex = 0;                            // 当前执行步骤
    QHash<quint16, bool> m_hashZInit;                             // 板卡Z轴复位状态(判断Z轴复位成功)
    QHash<quint16, bool> m_hashXYInit;                           // 非Z轴板卡复位状态(判断X、Y轴复位成功)
};

#endif // CRESETTEST_H