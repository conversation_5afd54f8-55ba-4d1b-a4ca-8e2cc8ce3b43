#ifndef CCONSUMABLERESERVATION_H
#define CCONSUMABLERESERVATION_H
#include <QObject>
#include <QVector>
#include "publicconfig.h"
#include "reagent.h"
#include "consumables.h"

/*
 * @author:mflin
 * @created:2024-7-18
 *
 */

struct SimpleSysBuildInfo
{
    quint8 uiStripIndex;//卡条位置
    quint8 uiCompIndex;//构建的项目组分索引
    QString strProj;//构建的项目名称
};

struct ReservateData
{
    quint8 uiType;//耗材类型 Tip/Tube/Cap/Reagent
    bool bEnough;//耗材是否充足
    QString strName;//耗材名
    QList<RangePos> qPosList;//预定位置信息
};

class CConsumableReservation
{
public:
    CConsumableReservation();

    /**
     * @brief calcConsumableSupplyPos 计算批次测试所需试剂、Tip、PCR管、PCR管盖等是否充足，
     * 并提供充足情况下的锁定位置信息
     * @param qSampleInfoList  所有测试样本的测试项目列表信息
     * @param uiCatchType  泵操作类型，单个/双个
     * @return
     */
    QList<ReservateData> calcConsumableSupplyPos(QList<QString> qSampleInfoList, quint8 uiCatchType = CT_DOUBLE);

    void test();

     /**
     * @brief getReservateResult 获取计算批次信息
     * @param qSampleInfoList  所有测试样本的测试项目列表信息
     * @return 计算结果
     */   
    QString getReservateResult(const QList<QString>& qSampleInfoList);

private:
    /**
     * @brief _parseSampleInfos 解析，并重新组织获取每个系统构建测试的简单信息，获取不同项目测试的样本个数信息
     * @param qSampleInfoList 所有测试样本的测试项目列表信息，每个样本作为一个Item，单样本多项目的项目之间用逗号分隔
     * @param qProjTestCounts 获取不同项目所需测试的样本数
     * @param qSimpleSysBuildInfoVect 获取简化版系统构建信息
     */
    void _parseSampleInfos(QList<QString> qSampleInfoList,
                          QMap<QString, int> &qProjTestCounts,
                          QVector<SimpleSysBuildInfo>& qSimpleSysBuildInfoVect);

    /**
     * @brief _getSysBuildReserveInfo 获取系统构建操作顺序
     * @param uiSingleOpCount 单次操作总数
     * @param uiDoubleOpCount 双次操作总数
     * @param qConsumeList 构建顺序列表
     * @param qSimpleSysBuildInfoVect
     * @param uiCatchType 泵操作类型，单个/双个
     */
    void _getSysBuildReserveInfo(quint8 &uiSingleOpCount, quint8 &uiDoubleOpCount,
                            QList<quint8> &qConsumeList,
                            QVector<SimpleSysBuildInfo> qSimpleSysBuildInfoVect,
                            quint8 uiCatchType = CT_DOUBLE);

    /**
     * @brief _getReagentReserveInfo 获取系统构建过程所需试剂信息，TIP数量信息，并更新耗材锁定
     * @param qProjTestCounts 不同项目测试数量信息
     * @param qReservateList 耗材锁定信息列表
     * @return 试剂分装所需tip数量
     */
    quint8 _getReagentReserveInfo(const QMap<QString, int> &qProjTestCounts,
                                  QList<ReservateData> &qReservateList);
};

#endif // CCONSUMABLERESERVATION_H
