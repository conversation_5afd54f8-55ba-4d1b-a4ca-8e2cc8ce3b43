#include "caffair.h"
#include <QDebug>
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include "publicfunction.h"
#include "affair/cextractparse.h"
#include "cglobalconfig.h"
#include "cdevStatus.h"
#include "error/cerrorhandler.h"
#include "error/cerrornotify.h"
#include"ccommunicationobject.h"
#include "HalSubSystem/HalSubSystem.h"
#include <QCryptographicHash>
#include "SystemConfig/SystemConfig.h"
#include <QRandomGenerator>
#include "datacontrol/cprojectdb.h"
#include "Maintain/MaintainSubSystem.h"
#define MAGIC_ENUM_RANGE_MAX 2550
#include "magic_enum/magic_enum.hpp"

#define STRIP_SIZE 16
#define Send_By_Virtual 0
#define TEST_DISABLE_PCR_HOLE 1 // 是否启动禁孔功能
#define PCR_MODULE_MAIN_INDEX 0 //PCR模块主索引


int iTolScanStripNum =16;
int iTolCryScanCnt =0;
int iTolFainCnt =0;
int iTolNotSame =0;

CAffair::CAffair(QObject *parent) : QThread(parent)
  ,m_sampleModule(true, DEVICE_CATCH_TYPE)
  ,m_extractModule(true)
  ,m_gantryModule(true, DEVICE_CATCH_TYPE)
  ,m_switchMixModule(true, DEVICE_CATCH_TYPE)
  ,m_pcrCatchModule(true, DEVICE_CATCH_TYPE)
  ,m_bPeriodicInit(false)
  ,m_bReconstitutionWait(false)
{
    m_bExtractScanMode =false;
    m_bThreadAlive = true;
    m_sharedVariable = QAtomicInt(RST_IDLE);
    m_bRunSTChanged = false;
    m_bStartZebraScan=false;
    m_bZebraScanAging =false;
    m_pSampleControl = &SampleControl::getInstance();
    m_pSampleControl->SetCatchType(DEVICE_CATCH_TYPE);
    m_pStrip = &CStrip::getInstance();
    m_pStrip->SetStripSize(STRIP_SIZE);
    m_pPCRResource = &PCRResource::getInstance();
    m_pReagent = &Reagent::getInstance();
    m_pFrontBin = &CRecycleBin::frontBin();
    m_pBackBin = &CRecycleBin::backBin();
    m_pTestPre = new TestPreprocessing();
    for(int i =0; i<=PCR_MODULE_SIZE;i++)
    {
        m_pcrModule[i].SetPCRModuleIndex(i);
    }
    m_bWaitForStopResult.store(false);
    m_bManualStop.store(false);
    m_deviceCmdExecState = {false,false,false,false,false,false,false,false};
    m_seqType = ST_UNDEFINE;
    m_curBatchPCREndDone.store(false);
    connect(&CErrorHandler::getInstance(), &CErrorHandler::sigStopProcess, this, &CAffair::StopProcess);
    for(int i=0;i<16;i++)
    {
        m_vecScanConutDiff.push_back(0);
    }
    listScanRsMsg.clear();
}

CAffair::~CAffair()
{
    m_bThreadAlive = false;
}

CAffair &CAffair::getInstance()
{
    static CAffair instance;
    return instance;
}


void CAffair::_SetRunST(RunStat runSate, QString strFunc)
{
    auto state = magic_enum::enum_cast<RunStat>(runSate);
    if (state.has_value())
    {
        auto state_name = magic_enum::enum_name(state.value());
        qDebug() << strFunc << "runSate" << runSate << state_name.data();
    }
    else
    {
        qDebug() << strFunc << "runSate" << runSate;
    }
    HalSubSystem::getInstance().SetRunStatus(runSate);
    m_sharedVariable.storeRelease(runSate);
    m_bRunSTChanged = true;
    m_conditionVariable.notify_all();
}

void CAffair::_SetSeqType(SeqType seqType)
{
    m_seqType.store(seqType);
    qDebug()<<"_SetSeqType:"<<seqType;
}

SeqType CAffair::_GetSeqType()
{
    return static_cast<SeqType>(m_seqType.load());
}

int CAffair::SetPosDebugStatus() //设置设备状态为位置调试模式  0 成功：1 失败
{
    RunStat eRunST = GetRunST();
    if(eRunST!=RST_IDLE)
    {
        qDebug()<<"Status not idle,can't in PosDebug";
        return 1;
    }
    _SetRunST(RST_POSDEBUG, __FUNCTION__);
    return 0 ;
}

int CAffair::StopPosDebugStatus()//设置设备状态为位置idle 0 成功：1 失败
{
    RunStat eRunST = GetRunST();
    if(eRunST!=RST_POSDEBUG)
    {
        qDebug()<<"Status not RST_POSDEBUG,can't out";
        return 1;
    }
    _SetRunST(RST_IDLE, __FUNCTION__);
    return 0 ;
}

void CAffair::PreProcess(QString strParams)
{
    if (!strParams.isEmpty() && !CCommunicationObject::getInstance().GetSampleCodeScanInitStatus())//获取初始化状态
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Sample_Barcode, FT_Comm_OpenFail, "start sample scan failed");
        return;
    }

    _SetSeqType(static_cast<SeqType>(ST_TEST_PRE)); 
    m_pTestPre->start(strParams);
}

void CAffair::ResetResult(int iResult)
{
    emit sigResetFinished(0 == iResult ? true : false, "");
}

void CAffair::SampleExistProcess(QString strParams)
{
    qint8 iRow = MAX_SACN_SAMPLE_ROW; 
    if(!strParams.isEmpty())
    {
        auto list = strParams.split(",");
        int iIndex = 0;
        if(16 == list.size())
        {
            for (int i = 0; i + 1 < list.size();)
            {
                if ("1" == list[i] || "1" == list[i+1])
                {
                    iIndex = i + 1;
                }
                i += 2;
            }
            iRow = (iIndex + 1) / 2;
        }
    }
    CCommunicationObject::getInstance().SetSampleCodeMaxSampleRow(iRow);
    ActionAddSampleScanTubeExistTask();
}

void CAffair::SampleScanProcess()
{
    SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_START);//开始扫码
    ActionAddSampleScanCodeStartTask(CCommunicationObject::getInstance().GetSamplerScanCurPos());
    ExtractScanTest();//开启提取条扫码
}

void CAffair::StartProcess(QString strParams)
{
    quint8 uiIndex = strParams.indexOf(':');
    if(uiIndex>0)
    {
        quint8 uiSeqType = strParams.left(uiIndex).toUInt();
        RunStat eRunST = GetRunST();
        qDebug()<<"Start process uiSeqType:"<<uiSeqType<<"index"<<uiIndex<<"params:"<<strParams<<eRunST;  
        
        // if(eRunST == RST_IDLE)
        if(1)
        {
            // 重置停止标记
            m_deviceCmdExecState.bMotorBoard1Recv = false;
            m_deviceCmdExecState.bMotorBoard2Recv = false;
            m_deviceCmdExecState.bMotorBoard3Recv = false;
            m_deviceCmdExecState.bPCRBoardRecv = false; 
            
            _SetSeqType(static_cast<SeqType>(uiSeqType)); 
            
            auto state = HalSubSystem::getInstance().GetLedMoudleStatus(Led::EnumMoudle::Strip_Box);
            if (state == Led::EnumTriggerState::OFF)// 提取盒打开，不执行测试或者自检动作
            {
                CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStartFailed, "extract module isn't ready.");
                _sendRunSTErrorNotify();
                qDebug() << "Error CAffair::StartProcess extract module isn't ready.";
                return;
            }            
            if(uiSeqType ==ST_PERIODIC)
            {
                m_strPeriodicParams = strParams;
                PeriodicProcess();
            }
            else if(uiSeqType == ST_SELF_TEST)
            {
                _SetRunST(RST_WAIT_RUN, __FUNCTION__);
                m_pcrCatchModule.SetIsBusy(false);//出现异常复位处理后，清空繁忙状态
                m_pcrCatchModule.SetCurSubTaskID();//出现异常后，重置默认id
                MaintainSubSystem::getInstance().SelfTestSendGainOptoStatusCommand();//先获取光耦状态(获取成功后启动自检)
            }
            else if(uiSeqType == ST_RESET)
            {
                ResetProcess();
            }            
            else if(uiSeqType == ST_SAMPLE_SCAN)//旋转扫码单元
            {  
                // 提取条扫码和样本扫码同时进行
                // 1、提取条扫码
                // ExtractScanTest();
                // 2、样本扫码
                if (!CCommunicationObject::getInstance().GetSampleCodeScanInitStatus())//获取初始化状态
                {
                    const QString strErr = "start sample scan failed";
                    qDebug()<<strErr;
                    CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Sample_Barcode, FT_Comm_OpenFail, strErr);
                    // 结束扫码，发送到上位机
                    SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_END,1,strErr);//扫码结束
                    return;
                }
                 
                _SetRunST(RST_WAIT_RUN, __FUNCTION__);
                qDebug()<<"start scan code";
                QString str = strParams.right(strParams.length() - uiIndex -1);
                if(!str.isEmpty())
                {
                    auto list = str.split(",");
                    int iCount = 0;
                    qint8 iRow = 0; 
                    if(list.size() > 1)// 不选中项目，默认全选
                    {
                        for(QString str : list) {
                            if(str == "1") {
                                iCount++;
                            }
                        }
                        iRow = (iCount+1)/2;//+1防止出现1的类似基数情况
                    }
                    else
                    {
                        iRow = MAX_SACN_SAMPLE_ROW;
                    }

                    CCommunicationObject::getInstance().SetSampleCodeMaxSampleRow(iRow);
                }
                
                SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_START);//开始扫码
                ActionAddSampleScanTubeExistTask();

                ExtractScanTest();//开启提取条扫码
                qDebug() << "end scan code";
                // return; // 扫码中位机不改变状态，由上位机控制
            }
            else if (uiSeqType == ST_AMPLIFICATION)//单扩增
            {
                m_curBatchPCREndDone.store(false);
                PCRResource::getInstance().LoadValidPcrArea();// 开始单扩增前，检测禁用区域(单扩增逻辑与正常测试不同，需要提前设置)
                m_pSampleControl->m_sampleAmplificate.ParseSampleInfo(strParams);
                QString m_strBatchNo = m_pSampleControl->m_sampleAmplificate.GetBatchNo();
                SendPeroidStateToUpperHost(PEST_SEQ_START);
                m_pSampleControl->SetCurBatchSystemBuildSize(m_pSampleControl->m_sampleAmplificate.GetAmplificateSampleInfoSize());
                SendPeroidStateToUpperHost(PEST_SYSTEM_BUILD_START);
                // 解析完成后启动测试
                ActionAddAmplTransTubeTask();
            }
            else if (uiSeqType == ST_EXTRACTION)//单提取
            {
            }
            else if (uiSeqType == ST_PCR_CLEAN)//PCR清理
            {
                // 获取板卡3全部光耦状态
                COperationUnit::getInstance().sendStringData(Method_AllOptoStatus, "", Machine_Motor_3); 
                // SetMotor3AllOptoStatus();//在收到光耦信息函数启动
            }
            else if (uiSeqType == ST_MOTOR_DEBUG)//电机调试            
            {
                // 保存、上一个、下一个需要关闭调试状态
                MaintainSubSystem::getInstance().MotorPosDebug(strParams);
            }
            else if (uiSeqType == ST_AGING_TEST)//老化测试
            {
                MaintainSubSystem::getInstance().AgingTestDebug(strParams);
            }
            else if (uiSeqType == ST_SAMPLE_EXIST)
            {
                SampleExistProcess("");
            }
            _SetRunST(RST_RUN, __FUNCTION__);
        }
        else
        {
            qDebug()<<"start seqType " << m_seqType<< "process failed, for runST isn't idle.";
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStartFailed, QString("seqType:%1 start failed.").arg(uiSeqType));
        }
    }
    else
    {
        QDFUN_LINE<<"start process failed. invalid params, can't find seqType";
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceCmdParamError, QString("start process failed. invalid params, can't find seqType."));
    }
}

void CAffair::ResetProcess()
{
    _SetRunST(RST_WAIT_RUN, __FUNCTION__);
    m_pcrCatchModule.SetIsBusy(false);//出现异常复位处理后，清空繁忙状态
    m_pcrCatchModule.SetCurSubTaskID();//出现异常后，重置默认id
    MaintainSubSystem::getInstance().ResetTestStart(_GetSeqType());
}

void CAffair::PeriodicProcess()
{
    _SetSeqType(static_cast<SeqType>(ST_PERIODIC)); 
    quint8 uiIndex = m_strPeriodicParams.indexOf(':');
    CDevStatus::getInstance().setUpdateFlag(true);
    m_pSampleControl->SetCurBatchSamples(m_strPeriodicParams.right(m_strPeriodicParams.length() - uiIndex -1));
    _SetRunST(RST_WAIT_RUN, __FUNCTION__);//设置等待运行状态
    Consumables::getInstance().CheckConsumableBoxStatus();//检查耗材，准备上锁和灯
    Reagent::getInstance().SetSystemBuildBusyStatus(true);//设置系统构建状态
    m_extractModule.SetExtractClevageHoleStatus(Method_extract_heater_start);
    SendSimulateExtractScanResult();
    m_curBatchPCREndDone.store(false);
    // 重置单扩增状态(确保不会影响测试流程)
    m_pSampleControl->m_sampleAmplificate.ResetRunStatus();    
    
    if (!m_pSampleControl->AddCurBatch())
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Test_Process, FT_AddBatchFailed, "AddCurBatch failed.");
        _sendRunSTErrorNotify();
        qDebug()<<"Error CAffair::StartProcess AddCurBatch failed.";
        return; 
    }

    if(!m_pPCRResource->IsPCRResourceEnough(m_pSampleControl->GetCurBatchSystemBuildSize()))
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Empty, "PCRResource size is not enough.");
        // _sendRunSTErrorNotify();
        // qDebug() << "Error CAffair::StartProcess PCRResource size is not enough.";
        // return;  
    }
    
    // m_pStrip->SetStripSize(STRIP_SIZE);// 重置提取条数量(内标需要提前使用)
    m_eInternalStandardStatus = IS_NONE;//重置状态

    quint8 quWaitExecSampleSize = 0;
    //Run New Batch
    if(m_pSampleControl->IsCurBatchSampleStandardAvailable())//先加内标
    {
        quWaitExecSampleSize = m_pSampleControl->GetNextInternalStandardCatchAndMixSize();
        m_eInternalStandardStatus = IS_STEP_WAIT;
    }
    else
    {
        quWaitExecSampleSize = m_pSampleControl->GetNextSampleCatchAndMixSize();
    }
    
    // if(CSystemDB::getInstance().getBoolValueFromKey("Extract_Info_Count"))
    {
        // 判断提取条是否足够、连续
        if(m_pSampleControl->GetCurBatchSampleSize() > CStrip::getInstance().GetStripValidSize())
        {
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStartFailed, "strip size is not enough.");
            _sendRunSTErrorNotify();
            qDebug() << "Error CAffair::StartProcess strip size is not enough." 
                        << m_pSampleControl->GetCurBatchSampleSize()
                        << CStrip::getInstance().GetStripValidSize();  
            return;              			
        }

        if(!CStrip::getInstance().CheckStripInOrder())//检查是否连续
        {
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStartFailed, "strip is not in order.");
            _sendRunSTErrorNotify();
            qDebug() << "Error CAffair::StartProcess strip is not in order.";
            return;              			
        }
    }

    qDebug()<<"************quWaitExecSampleSize"<<quWaitExecSampleSize;
    if(quWaitExecSampleSize>0)
    {
        _BeginPeriodicProcessActions(quWaitExecSampleSize);
    }
    else
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_NoSample, "no sample.");
        _sendRunSTErrorNotify();
        qDebug() << "no sample." << quWaitExecSampleSize;
    }

    // _SetRunST(RST_RUN);
}

void CAffair::PauseProcess()
{
    RunStat eRunST = GetRunST();
    if(eRunST < RST_WAIT_PAUSE)
    {
        m_deviceCmdExecState = {false, false, false, false, false, false,false,false};
        _SetRunST(RST_WAIT_PAUSE, __FUNCTION__);
    }
    else
    {
        qDebug()<<"pause process failed.";
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequencePauseFailed, QString("pause process failed."));
    }

}

void CAffair::StopProcess(bool bManualStop)
{
    m_bManualStop.store(bManualStop);
    RunStat eRunST = GetRunST();
    qDebug()<<"StopProcess runST:"<<eRunST;
    if(eRunST <RST_WAIT_STOP)
    {
        memset(&m_deviceCmdExecState, 0, sizeof(m_deviceCmdExecState));
        m_bWaitForStopResult.store(false);// 如果待停止，主动发起的stop需要等待其他板下位机板和PCR主控板的停止
        StopExtractScan();// 提取条停止扫码
        CCommunicationObject::getInstance().ResetSampleScanPos();//样本停止扫码
        SendStopCmdToDevice();
        _SetRunST(RST_WAIT_STOP, __FUNCTION__);
        m_pcrModule[PCR_MODULE_MAIN_INDEX].SetFLLedStatus(false); // 灯源关闭(异常)
        Reagent::getInstance().SetSystemBuildBusyStatus(false);        
        m_extractModule.SetExtractClevageHoleStatus(Method_extract_heater_stop);
    }
    else if(eRunST == RST_STOP)
    {
        memset(&m_deviceCmdExecState, 0, sizeof(m_deviceCmdExecState));
        m_bWaitForStopResult.store(false);//false为不等待其他板的停止
        StopExtractScan();// 提取条停止扫码
        CCommunicationObject::getInstance().ResetSampleScanPos();//样本停止扫码
        SendStopCmdToDevice();
        _SetRunST(RST_STOP, __FUNCTION__);
        m_pcrModule[PCR_MODULE_MAIN_INDEX].SetFLLedStatus(false); // 灯源关闭(异常)
        Reagent::getInstance().SetSystemBuildBusyStatus(false);        
        m_extractModule.SetExtractClevageHoleStatus(Method_extract_heater_stop);
    }
    else
    {
        qDebug()<<"stop process failed. runST:"<<eRunST;
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStopFailed, QString("stop process failed."));
    }
    if (RST_IDLE == eRunST)
    {
        UpdateRunStat(RST_IDLE);
    }
}

void CAffair::_ClearData()
{
    listScanRsMsg.clear();
    m_bStartZebraScan = false;
    m_bZebraScanAging =false;
    // m_bManualStop.store(false); // 不能更新，停止需要等待，要用到这个信息
    m_bPunchDone = false;
    m_bWaitPunchDone = false;
    m_eInternalStandardStatus = IS_NONE;

    m_iWriteIndex_Condition = 0;
    m_iReadIndex_Condition = 0;
    m_iNextWriteIndex_Condition = 0;
    m_iCurrentWriteIndex_Condition = 0;

    m_iCmdWriteIndex_Condition = 0;
    m_iCmdReadIndex_Condition = 0;
    m_iCmdNextWriteIndex_Condition = 0;
    m_iCmdCurrentWriteIndex_Condition = 0;

    m_pSampleControl->ClearCurBatchData();

    m_sampleModule.InitData();
    m_gantryModule.InitData();
    m_extractModule.InitData();
    m_pcrCatchModule.InitData();
    m_switchMixModule.InitData();
    for(int i=0;i<=PCR_MODULE_SIZE;i++)
    {
        m_pcrModule[i].InitData();
    }
    m_pPCRResource->FreeAllPCR();
    m_pStrip->SetStripSize(STRIP_SIZE);
    m_bPeriodicInit = false;

    m_bTransReagentFinishToWaitExtract = false;
    m_bWaitExtractFinishToTransReagent = false;
    m_strPeriodicParams = "";
    m_bReconstitutionWait = false;
    m_bGetTip = false;
    qDebug() << "_ClearData";
}

void CAffair::ResumeProcess()
{
    RunStat eRunST = GetRunST();
    if(eRunST < RST_WAIT_STOP)
    {
        _SetRunST(RST_WAIT_RESUME, __FUNCTION__);
    }
    else
        qDebug()<<"resume process failed.";
}

RunStat CAffair::GetRunST()
{
    int value = m_sharedVariable.loadAcquire();
    return (RunStat)value;
}

void CAffair::SetExtractScanMode(bool bStatus)
{
    qDebug()<<"CAffair::SetExtractScanMode bStatus: "<<bStatus;
    m_bExtractScanMode = bStatus;
}

QString CAffair::_getCurBatchNo(quint8 uiCurExecST)
{
    // 获取对应阶段样本信息中的批次号
    QString strBatchNo = "";
    QVector<SampleInfo> qVectSampleInfo;
    QVector<SystemBuildInfo> qVectSystemBuildInfo;
    bool bGet = m_pSampleControl->GetCurExecSampleInfo(uiCurExecST, qVectSampleInfo);
    if (!bGet && qVectSampleInfo.isEmpty())
    {
        // 有些过程状态存在多批次信息，需要获取最新样本信息才能得到正确批次号
        bGet = m_pSampleControl->GetLatestExecSystemBuildInfo(uiCurExecST, qVectSystemBuildInfo);
        if (bGet && !qVectSystemBuildInfo.isEmpty())
        {
            strBatchNo = qVectSystemBuildInfo[0].strBatchNo;
        }
    }
    else
    {
        if (!qVectSampleInfo.isEmpty())
        {
            strBatchNo = qVectSampleInfo[0].strBatchNo;
        }
    }

    qDebug()<<"_getCurBatchNo:"<<qVectSampleInfo.size()<<qVectSystemBuildInfo.size()<<strBatchNo<<uiCurExecST;
    return strBatchNo;
}

void CAffair::SendPeroidStateToUpperHost(quint8 uiTestState, quint8 uiCurExecST/* = 0*/, quint8 uiResult/* = 0*/,QString strBatchNoSpecial/* = ""*/)
{
    static quint8 uiLastTestState = 0;
    static QString strLastBatchNo = "";
    QString strBatchNo = _getCurBatchNo(uiCurExecST);
    if (!strBatchNoSpecial.isEmpty())
    {
        strBatchNo = strBatchNoSpecial;
    }
    
    QString strParams = QString("%1:%2,%3").arg(ST_PERIODIC).arg(strBatchNo).arg(uiTestState);
    if (strBatchNo.isEmpty() || (uiLastTestState == uiTestState && strLastBatchNo == strBatchNo))
    {
        qDebug()<<"SendPeroidStateToUpperHost strBatchNo or uiTestState repeat"<<strBatchNo<<uiTestState<<uiCurExecST<<uiResult;
        return;
    }
    
    COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost, uiResult);
    uiLastTestState = uiTestState;
    strLastBatchNo = strBatchNo;
    qDebug()<<"SendPeroidStateToUpperHost "<<strParams<<strBatchNo<<uiTestState<<uiCurExecST<<uiResult;
}

void CAffair::SendSampeScanStateToUpperHost(quint8 uiTestState, quint8 uiResult, QString strResult)
{
    QString strParams = QString("%1:%2;%3").arg(ST_SAMPLE_SCAN).arg(uiTestState).arg(strResult);
    COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost, uiResult);
    qDebug()<<"CAffair::SendSampeScanStateToUpperHost "<<strParams<<uiResult;
}

void CAffair::slotAddReciveSTCondition(quint16 uiUnit, quint16 uiCondition)
{
    m_iCurrentWriteIndex_Condition= m_iWriteIndex_Condition.load();
    m_iNextWriteIndex_Condition= (m_iCurrentWriteIndex_Condition+ 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex_Condition== m_iReadIndex_Condition.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CAffairObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    SCondition cond = {uiUnit, uiCondition};
    m_uiConditionInfoList[m_iCurrentWriteIndex_Condition] = cond;
    qDebug()<<"Receive Condition unitID 1"<<cond.uiUnitID<<" condID:" <<cond.uiConditionID
           << " m_iCurrentWriteIndex_Condition"<<m_iCurrentWriteIndex_Condition;
    m_iWriteIndex_Condition.store(m_iNextWriteIndex_Condition);
    m_conditionVariable.notify_all();
}

void CAffair::slotAddActionCmdReply(quint16 uiComplexID, quint16 uiResult)
{
    RunStat eRunST = GetRunST();
    auto action = magic_enum::enum_cast<EnumAffairAction>(uiComplexID);
    if (action.has_value())
    {
        auto action_name = magic_enum::enum_name(action.value());
        qDebug() << "slotAddActionCmdReply:" << action_name.data() << uiComplexID << uiResult << eRunST;
    }
    else
    {
        qDebug() << "slotAddActionCmdReply:" << uiComplexID << uiResult << eRunST;
    }
    if(eRunST>RST_IDLE && eRunST<RST_STOP)
    {
        m_iCmdCurrentWriteIndex_Condition= m_iCmdWriteIndex_Condition.load();
        m_iCmdNextWriteIndex_Condition= (m_iCmdCurrentWriteIndex_Condition+ 1) % BUFFER_SIZE;

        if (m_iCmdNextWriteIndex_Condition== m_iCmdReadIndex_Condition.load())
        { // 原则上不可能有65535个重发存在，故而不做考虑
            qDebug() << "CAffairObject^^^^Add Receive Exec Cond^^^^^^ERROR-^^^^^^^^^^^^^";
            return;
        }
        ActionCmdReply reply = {uiComplexID, uiResult};
        m_uiComplexCmdInfoList[m_iCmdCurrentWriteIndex_Condition] = reply;
        qDebug()<<"Receive Condition complexCmd "<<uiComplexID
               << " m_iCmdCurrentWriteIndex_Condition"<<m_iCmdCurrentWriteIndex_Condition;
        m_iCmdWriteIndex_Condition.store(m_iCmdNextWriteIndex_Condition);
        m_conditionVariable.notify_all();
    }
}

void CAffair::slotPCRCmdReply(quint16 iSourceID,quint16 uiCmdID, quint16 uiResult, QString strParam)
{
    RunStat eRunST = GetRunST();
    if((eRunST>RST_IDLE && eRunST<RST_STOP) || eRunST == RST_PCR_RUN)
    {
        m_iPCRCmdCurrentWriteIndex_Condition= m_iPCRCmdWriteIndex_Condition.load();
        m_iPCRCmdNextWriteIndex_Condition= (m_iPCRCmdCurrentWriteIndex_Condition+ 1) % BUFFER_SIZE;

        if (m_iPCRCmdNextWriteIndex_Condition== m_iPCRCmdReadIndex_Condition.load())
        { // 原则上不可能有65535个重发存在，故而不做考虑
            qDebug() << "CAffairObject^^^^Add Receive PCR Cmd Reply^^^^^^ERROR-^^^^^^^^^^^^^";
            return;
        }
        PCRCmdReply reply = {iSourceID, uiCmdID, uiResult, strParam};
        m_pcrCmdReplyList[m_iPCRCmdCurrentWriteIndex_Condition] = reply;
        qDebug()<<"Receive PCR Cmd Reply "<<uiCmdID
               << " m_iPCRCmdCurrentWriteIndex_Condition"<<m_iPCRCmdCurrentWriteIndex_Condition;
        m_iPCRCmdWriteIndex_Condition.store(m_iPCRCmdNextWriteIndex_Condition);
        m_conditionVariable.notify_all();
    }
}

void CAffair::slotFLDataCmdReply(const QByteArray &qByteArray)
{
    RunStat eRunST = GetRunST();
    if(eRunST>RST_IDLE && eRunST<RST_STOP)
    {
        m_iFLDataCmdCurrentWriteIndex_Condition= m_iFLDataCmdWriteIndex_Condition.load();
        m_iFLDataCmdNextWriteIndex_Condition= (m_iFLDataCmdCurrentWriteIndex_Condition+ 1) % MIDDLE_BUFFER_SIZE;

        if (m_iFLDataCmdNextWriteIndex_Condition== m_iFLDataCmdReadIndex_Condition.load())
        { // 有256个重发存在，故而不做考虑
            qDebug() << "CAffairObject^^^^Add Receive FLData Cmd Reply^^^^^^ERROR-^^^^^^^^^^^^^";
            return;
        }

        m_flDataList[m_iFLDataCmdCurrentWriteIndex_Condition] = qByteArray;
        qDebug()<<"Receive FLData Cmd Reply "<< " m_iFLDataCmdCurrentWriteIndex_Condition"
               <<m_iFLDataCmdCurrentWriteIndex_Condition;
        m_iFLDataCmdWriteIndex_Condition.store(m_iFLDataCmdNextWriteIndex_Condition);
        m_conditionVariable.notify_all();
    }
}

void CAffair::updateDeviceCmdExecState(quint8 iSourceID, quint8 iResult)
{
    if(iSourceID == Machine_Motor_1)
    {
        m_deviceCmdExecState.bMotorBoard1Recv = true;
        m_deviceCmdExecState.bMotorBoard1Result = iResult;
    }
    else if(iSourceID == Machine_Motor_2)
    {
        m_deviceCmdExecState.bMotorBoard2Recv = true;
        m_deviceCmdExecState.bMotorBoard2Result = iResult;
    }
    else if(iSourceID == Machine_Motor_3)
    {
        m_deviceCmdExecState.bMotorBoard3Recv = true;
        m_deviceCmdExecState.bMotorBoard3Result = iResult;
    }
    else if(iSourceID == Machine_Motor_4)
    {
        m_deviceCmdExecState.bMotorBoard4Recv = true;
        m_deviceCmdExecState.bMotorBoard4Result = iResult;
    }    
    else if(iSourceID == Machine_PCR_Ctrl)
    {
        m_deviceCmdExecState.bPCRBoardRecv = true;
        m_deviceCmdExecState.bPCRBoardResult = iResult;
    }

    if(m_bManualStop.load())//主动停止的
    {
        bool bStopRecvFlag = m_deviceCmdExecState.bMotorBoard1Recv &
                             m_deviceCmdExecState.bMotorBoard2Recv & 
                             m_deviceCmdExecState.bMotorBoard3Recv & 
                             m_deviceCmdExecState.bMotorBoard4Recv &
                             m_deviceCmdExecState.bPCRBoardRecv;
        if (bStopRecvFlag)// 收到所有板的停止结果
        {
            // 汇总结果
            bool bStopResult = m_deviceCmdExecState.bMotorBoard1Result | 
                               m_deviceCmdExecState.bMotorBoard2Result | 
                               m_deviceCmdExecState.bMotorBoard3Result |
                               m_deviceCmdExecState.bMotorBoard4Result |
                               m_deviceCmdExecState.bPCRBoardResult ;
            COperationUnit::getInstance().sendResult(Method_stop, Machine_UpperHost, bStopResult);          
            qDebug()<<"updateDeviceCmdExecState stop finished"<<bStopResult;
        }
    }
    qDebug()<<"iSourceID"<<iSourceID<<"iResult"<<iResult;
}


void CAffair::SendStopCmdToDevice()
{
    COperationUnit::getInstance().sendCmd(Method_stop,  Machine_Motor_1);
    COperationUnit::getInstance().sendCmd(Method_stop,  Machine_Motor_2);
    COperationUnit::getInstance().sendCmd(Method_stop,  Machine_Motor_3);
    COperationUnit::getInstance().sendCmd(Method_stop,  Machine_Motor_4);
    COperationUnit::getInstance().sendCmd(Method_stop, Machine_PCR_MainCtrl);
}

void CAffair::SendResumeCmdToDevice()
{
    COperationUnit::getInstance().sendCmd(Method_resume,  Machine_Motor_1);
    COperationUnit::getInstance().sendCmd(Method_resume,  Machine_Motor_2);
    COperationUnit::getInstance().sendCmd(Method_resume,  Machine_Motor_3);
    COperationUnit::getInstance().sendCmd(Method_resume,  Machine_Motor_4);
    COperationUnit::getInstance().sendCmd(Method_resume, Machine_PCR_MainCtrl);
}

void CAffair::SendPauseCmdToDevice()
{
    COperationUnit::getInstance().sendCmd(Method_pause,  Machine_Motor_1);
    COperationUnit::getInstance().sendCmd(Method_pause,  Machine_Motor_2);
    COperationUnit::getInstance().sendCmd(Method_pause,  Machine_Motor_3);
    COperationUnit::getInstance().sendCmd(Method_pause,  Machine_Motor_4);
    COperationUnit::getInstance().sendCmd(Method_pause, Machine_PCR_MainCtrl);
}

void CAffair::run()
{
    std::unique_lock<std::mutex> uniqueLock(m_mutex);
    while(m_bThreadAlive)
    {
        m_conditionVariable.wait(uniqueLock, [this] {
            return !m_bThreadAlive ||
                    m_bRunSTChanged || // 只要状态发生改变就唤醒线程
                    (this->m_iReadIndex_Condition.load() != this->m_iWriteIndex_Condition.load() ||
                    this->m_iCmdReadIndex_Condition.load() != this->m_iCmdWriteIndex_Condition.load() ||
                    this->m_iPCRCmdReadIndex_Condition.load() != this->m_iPCRCmdWriteIndex_Condition.load() ||
                    this->m_iFLDataCmdReadIndex_Condition.load() != this->m_iFLDataCmdWriteIndex_Condition.load());
        });

        if (!m_bThreadAlive)
        {
            break;
        }
        m_bRunSTChanged = false;
        RunStat eRunST = GetRunST();
        qDebug() << "CAffair::run"<<eRunST<<m_seqType;

        switch (eRunST)
        {
        case RST_RUN:
        {
            if(this->m_iReadIndex_Condition.load() != this->m_iWriteIndex_Condition.load())
            {
                this->_HandleReceiveSTCondList();
            }
            //复合动作
            if(this->m_iCmdReadIndex_Condition.load() != this->m_iCmdWriteIndex_Condition.load())
            {
                this->_HandleReceiveCmdResultList();
            }
            if(this->m_iPCRCmdReadIndex_Condition.load() != this->m_iPCRCmdWriteIndex_Condition.load())
            {
                this->_HandleReceivePCRCmdReplyList();
            }
            if(this->m_iFLDataCmdReadIndex_Condition.load() != this->m_iFLDataCmdWriteIndex_Condition.load())
            {
                this->_HandleReceiveFLDataList();
            }
        }        
            break;
        case RST_WAIT_STOP:
        {
            //收到下位机各个板的停止指令后转到停止状态
            if(m_bWaitForStopResult.load()/* && m_deviceCmdExecState.bPCRBoardRecv*/
                    &&m_deviceCmdExecState.bMotorBoard1Recv
                    && m_deviceCmdExecState.bMotorBoard2Recv
                    && m_deviceCmdExecState.bMotorBoard3Recv)
            {
                qDebug()<<"Receive device stop cmd result, pcr:"<<m_deviceCmdExecState.bPCRBoardResult << "board1"<<m_deviceCmdExecState.bMotorBoard1Result
                       << "board2"<<m_deviceCmdExecState.bMotorBoard2Result<< "board3"<<m_deviceCmdExecState.bMotorBoard3Result;
                _SetRunST(RST_STOP, __FUNCTION__);
            }
            else if(!m_bWaitForStopResult.load())
            {
                _SetRunST(RST_STOP, __FUNCTION__);
            }
        }        
            break;
        case RST_WAIT_PAUSE:
        {
            SendPauseCmdToDevice();
            _SetRunST(RST_PAUSE, __FUNCTION__);
        }        
            break;
        case RST_WAIT_RESUME:
        {
            SendResumeCmdToDevice();
            _SetRunST(RST_RESUME, __FUNCTION__);
        }        
            break;
        case RST_STOP:
        {
            // 1、需要清空pcr区域信息
            // 2、需要清空样本信息            
            _ClearData();
            _SetRunST(RST_IDLE, __FUNCTION__);
        }        
            break;
        case RST_WAIT_IDLE:
        {
            _ClearData();
            _SetRunST(RST_IDLE, __FUNCTION__);
        }        
            break;
        case RST_PCR_RUN:
        {
            // 只透传PCR相关命令和数据
            if(this->m_iPCRCmdReadIndex_Condition.load() != this->m_iPCRCmdWriteIndex_Condition.load())
            {
                this->_HandleReceivePCRCmdReplyList();
            }
            if(this->m_iFLDataCmdReadIndex_Condition.load() != this->m_iFLDataCmdWriteIndex_Condition.load())
            {
                this->_HandleReceiveFLDataList();
            }            
        }          
            break;            
        default:
            break;
        }
    }

}

void CAffair::_HandleReceiveSTCondList()
{
    while (m_iReadIndex_Condition.load() != m_iWriteIndex_Condition.load())
    {
        SCondition sCond = m_uiConditionInfoList[m_iReadIndex_Condition.load()];
        qDebug()<<"Receive Condition unitID 2"<<sCond.uiUnitID<<" condID:" <<sCond.uiConditionID
               << " m_iCurrentWriteIndex_Condition"<<m_iReadIndex_Condition.load();
        // TO DO
        if(sCond.uiConditionID >= 0 && sCond.uiConditionID < CONDITION_SIZE)
        {
            // 处理条件信息
        }

        // 环形队列
        m_iReadIndex_Condition.store((m_iReadIndex_Condition.load() + 1) % BUFFER_SIZE);
    }
}


void CAffair::_HandleReceivePCRCmdReplyList()
{
    while (m_iPCRCmdReadIndex_Condition.load() != m_iPCRCmdWriteIndex_Condition.load())
    {
        PCRCmdReply reply = m_pcrCmdReplyList[m_iPCRCmdReadIndex_Condition.load()];
        const quint8 uiPCRModuleMainStartID = 0x08;
        const quint8 uiPCRModuleMainEndID = 0x0C;

        // 默认是0x08的PCR索引和名称
        quint16 uiPCRIndex = 0;// SourceID对应的PCR索引
        QString strPCRName = "PCR_Ctrl";// SourceID对应的名称
        if (reply.uiSourceID > uiPCRModuleMainStartID && reply.uiSourceID<=uiPCRModuleMainEndID)
        {
            uiPCRIndex = reply.uiSourceID - uiPCRModuleMainStartID - 1;//索引需要从0开始
            strPCRName = strPCRName+"_"+QString::number(uiPCRIndex+1);//名称需要加1
        }        
        
        QString strBatchNo = PCRResource::getInstance().GetRecordPCRAreaBatchInfo(uiPCRIndex);
        qDebug()<<"Receive PCR cmd"<<reply.uiCmdId<<" result:" <<reply.uiResult <<"param"<< reply.strParam
                << "m_iPCRCmdReadIndex_Condition"<<m_iPCRCmdReadIndex_Condition.load()
                <<"strPCRName "<<strPCRName<<"uiPCRIndex "<<uiPCRIndex<<strBatchNo;
        // TO DO
        switch (reply.uiCmdId)
        {
        case Method_TEC_PCR_StartOrStop:
        {
            QStringList qStrParamList = reply.strParam.split(",");
            if(qStrParamList.size()>0 && ST_PERIODIC == _GetSeqType())// 周期执行
            {
                int iParam = qStrParamList.at(0).toInt();
                qDebug()<<"-----Receive Method_TEC_PCR_StartOrStop param:"<<iParam<<" result:"<<reply.uiResult;
                m_pcrModule[PCR_MODULE_MAIN_INDEX].UpdateRunStatus(iParam,reply.uiResult);
                if(reply.uiResult != 0)//启动失败，重发启动
                {
                    m_pcrModule[PCR_MODULE_MAIN_INDEX].ResetStartMultiPCRStatus(m_pcrModule,m_pcrModule[PCR_MODULE_MAIN_INDEX].GetCommandStr());
                    m_pcrModule[PCR_MODULE_MAIN_INDEX].StartMultiPCR(m_pcrModule);
                }
            }           
            break;
        }
        case Method_TEC_PCR_SignalReport:
        {
            QStringList qStrParamList = reply.strParam.split(",");
            if(qStrParamList.size()>2)
            {                
                int iType = qStrParamList.at(0).toInt();
                int iCycle = qStrParamList.at(1).toInt();
                int iTemp = qStrParamList.at(2).toInt();
                qDebug()<<"-----Receive Method_TEC_PCR_SignalReport type:"<<iType<<" cycle"<<iCycle <<"temp:"<<iTemp;
                if(iType == TECST_SEQ_END)//PCR时序结束,执行PCR结束相关处理动作
                {
                    // 需要找出相同批次的pcr区域，把对应PCR区域的优先级提高
                    HandlePCREndReply(uiPCRIndex);// 注意顺序，m_bStartMELT的值变化
                    if(m_bStartMELT)
                    {
                       SendPeroidStateToUpperHost(PEST_MELT_END,SEST_WAIT_PCR_AMPLIFY,0,strBatchNo);
                       m_bStartMELT = false;
                    }                    
                }
                else if (iType == TECST_MELT_START)// 溶解开始
                {
                    SendPeroidStateToUpperHost(PEST_AMPLIFY_END,SEST_WAIT_PCR_AMPLIFY,0,strBatchNo);//有熔解就扩增结束
                    QThread::msleep(10);//延时10ms，防止状态更新太快导致的状态更新错误
                    m_bStartMELT = true;
                    SendPeroidStateToUpperHost(PEST_MELT_START,SEST_WAIT_PCR_AMPLIFY);
                }
            }
            break;
        }
        case Method_TEC_RequestTransmitTimingTable:
        {
            if(reply.uiResult == 1)
            {
                qDebug()<<"TEC Timing transmit table error........";
                m_pcrModule[PCR_MODULE_MAIN_INDEX].StopTimingTransmit();// 在PCRModule模块已经做了重发
            }
            // else
            // {
            //     // 开始发送pcr时序数据
            //     m_pcrModule[uiPCRModuleIndex].TransTecTimeSeq();
            // }
            break;
        }           
        case Method_TEC_TransmitTimingData:
        {
            if(reply.uiResult == 1)
            {
                qDebug()<<"TEC Timing transmit data error........";
                m_pcrModule[PCR_MODULE_MAIN_INDEX].StopTimingTransmit();// 在PCRModule模块已经做了重发
            }
            break;
        }        
        case Method_TEC_TransmitTimingEnd:
        {
            if(reply.uiResult == 1)
            {
                qDebug()<<"TEC Timing transmit end error........";
                m_pcrModule[PCR_MODULE_MAIN_INDEX].StopTimingTransmit();// 在PCRModule模块已经做了重发
                // 已经重发还是失败，直接启动
                if(m_pcrModule[PCR_MODULE_MAIN_INDEX].CheckReTransTecTimeSeq())
                {
                    m_pcrModule[PCR_MODULE_MAIN_INDEX].StartMultiPCR(m_pcrModule);
                }                
            }
            else
            {
                m_pcrModule[PCR_MODULE_MAIN_INDEX].StartMultiPCR(m_pcrModule);
            }
            break;
        }
        case Method_FLCYEND:
        {
            int iMethodID = Method_test_result;//测试结果
            if (m_bStartMELT)//熔解结果
            {
                iMethodID = Method_melt_result;
            }   
            //m_pcrModule[0]中的0不能改，与调用接收函数m_pcrModule[0].AddFLData(strFLData);有关联         
            m_pcrModule[PCR_MODULE_MAIN_INDEX].SendFLDataResultToUpperHost(iMethodID);
            break;
        }
        case Method_FLGAIN:
        {
            SendPeroidStateToUpperHost(PEST_GAIN_START,SEST_WAIT_PCR_AMPLIFY);
            break;
        }        
        default:
            break;
        }
        // 环形队列
        m_iPCRCmdReadIndex_Condition.store((m_iPCRCmdReadIndex_Condition.load() + 1) % BUFFER_SIZE);
    }
}

void CAffair::_HandleReceiveFLDataList()
{
    while (m_iFLDataCmdReadIndex_Condition.load() != m_iFLDataCmdWriteIndex_Condition.load())
    {
        QByteArray qByteArray = m_flDataList[m_iFLDataCmdReadIndex_Condition.load()];
        qDebug()<<"Receive FLData cmd"<<qByteArray
               << " m_iFLDataCmdReadIndex_Condition"<<m_iFLDataCmdReadIndex_Condition.load();
        if (qByteArray.size()>=50)
        {
            //PCR返回数据格式 前20个数16位，后4个数16位( 循环数 )
            //[1.index：扫描起始孔位索引(孔位索引由0开始)  2.H1_B    3.H2_B   4.H3_B  5.H4_B
            //6.index：扫描起始孔位索引(孔位索引由0开始)  7.H1_G  8.H2_G  9.H3_G   10.H4_G
            //11.index：扫描起始孔位索引(孔位索引由0开始)  12.H1_O  13.H2_O  14.H3_O  15.H4_O
            //16.index：扫描起始孔位索引(孔位索引由0开始)  17.H1_R   18.H2_R  19.H3_R  20.H4_R
            //21.cycle1 22.cycle2 23.cycle3 24.cycle4]
            int iByteGap = 5*2;//一组5个数据，每个数据2个字节
            int iStartIndex = 1;//第一位是"["符号
            quint16 uiGStartHole = GetByte2Int(qByteArray.data()+iStartIndex);
            quint16 uiBStartHole = GetByte2Int(qByteArray.data()+iStartIndex+iByteGap);
            quint16 uiOStartHole = GetByte2Int(qByteArray.data()+iStartIndex+iByteGap*2);
            quint16 uiRStartHole = GetByte2Int(qByteArray.data()+iStartIndex+iByteGap*3);
            qDebug()<<"uiGStartHole"<<uiGStartHole<<"uiBStartHole"<<uiBStartHole
                   <<"uiOStartHole"<<uiOStartHole<<"uiRStartHole"<<uiRStartHole;
            if(uiGStartHole != 0 || uiBStartHole != 0 || uiOStartHole != 0 || uiRStartHole !=0 )
            {
                QString strFLData = "";
                int iValNum = 26;//总共26个参数
                for(int j =0; j<iValNum; j++)
                {
                    int iVal = GetByte2Int(qByteArray.data()+iStartIndex+j*2);
                    strFLData += QString("%1,").arg(iVal);
                }

                qDebug()<<"---------strFLData:"<<strFLData;
                m_pcrModule[PCR_MODULE_MAIN_INDEX].AddFLData(strFLData);
            }
        }
        // 环形队列
        m_iFLDataCmdReadIndex_Condition.store((m_iFLDataCmdReadIndex_Condition.load() + 1) % MIDDLE_BUFFER_SIZE);
    }
}



void CAffair::HandleCatchSampleToOpenCapReply()
{
    qDebug()<<"<<<<<<---------HandleCatchSampleToOpenCapReply------->>>>>>";
    m_sampleModule.SetState(false);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SAMPLE_CATCH, SEST_WAIT_OPEN_SAMPLE_CAP);//开盖
    ActionAddOpenSampleCapTask();
}

void CAffair::HandleOpenCapReply()
{
    qDebug()<<"<<<<<<---------HandleOpenCapReply------->>>>>>";
    m_sampleModule.SetState(false);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_OPEN_SAMPLE_CAP, SEST_WAIT_SAMPLING);
    
    if(m_bPunchDone)
    {
        // 需要判断提取条位置，如果最左侧有提取条，则移液泵需要复位或者后退一个提取条，防止与样本开盖单元碰撞
        // 获取提取条光耦位置和当前样本是不是最后抓取的样本
        bool bStrip =  CStrip::getInstance().CheckLastStripInLeftNext();
        if(bStrip)
        {
            ActionAddSample1000Tip(SEST_WAIT_SAMPLING);
        }
        qDebug()<<"HandleCatchSampleToOpenCapReply bStrip"<<bStrip;        
        ActionAddSampingTask();  //吸样
    }
    else
    {
        m_bWaitPunchDone = true;
    }
}

void CAffair::HandleCloseCapReply()
{
    qDebug()<<"<<<<<<---------HandleCloseCapReply------->>>>>>";
    m_sampleModule.SetState(false);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_CLOSE_SAMPLE_CAP, SEST_WAIT_SAMPLE_BACK_HOME);
    ActionAddSampleBackHomeTask();    //样本归位
}

void CAffair::HandleSampleBackHomeReply()
{
    qDebug()<<"<<<<<<---------HandleSampleBackHomeReply------->>>>>>";
    m_sampleModule.SetState(false);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SAMPLE_BACK_HOME, SEST_SAMPLE_FINISH);

    qDebug()<<"HandleSampleBackHomeReply m_eInternalStandardStatus:"<<m_eInternalStandardStatus;
    quint8 uiNextExecSampleSize = 0;
    if (m_eInternalStandardStatus == IS_STEP_ABANDON)
    {
        ActionAddCloseSampleCapTask();// 内标样本盖关闭   
        m_eInternalStandardStatus = IS_STEP_DONE;
    }
    else if(m_eInternalStandardStatus == IS_STEP_DONE)
    {
        m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SPIT_SAMPLING, SEST_SAMPLE_FINISH);
        m_pSampleControl->AddSampleStandardActionCount();
        uiNextExecSampleSize = m_pSampleControl->GetNextInternalStandardCatchAndMixSize();// 调用此函数，有可能会清空GetNextWaitCatchAndMixSampleInfos()内容
        if(!m_pSampleControl->IsCurBatchSampleStandardAvailable())
        {
            qWarning()<<"interal standard and sample count is not available";
        }
        
        qDebug()<<"Next Exec Internal Standard done size:"<<uiNextExecSampleSize; 
        if (uiNextExecSampleSize>0)
        {
            m_bInternalStandardRun = true;
            m_eInternalStandardStatus = IS_STEP_WAIT;
        }
        else
        {
            m_eInternalStandardStatus = IS_FINISH;//内标加样全部完成
            m_bInternalStandardRun = false;
            m_pSampleControl->ResetCurBatchStatus();//内标加样已经完成，开始添加样本
            uiNextExecSampleSize = m_pSampleControl->GetNextSampleCatchAndMixSize();
            qDebug()<<"Internal Standard Finish and Next Exec Sample size:"<<uiNextExecSampleSize;
        }
    }
    else
    {
        // 判断是否需要继续添加内标
        if(m_eInternalStandardStatus != IS_NONE &&
           m_eInternalStandardStatus != IS_FINISH)
        {
            //先添加内标
            uiNextExecSampleSize = m_pSampleControl->GetNextInternalStandardCatchAndMixSize();
            if (uiNextExecSampleSize>0)// 需要判断内标和当前样本项目是否一致
            {
                m_eInternalStandardStatus = IS_STEP_WAIT;
                qDebug()<<"Next Exec Internal Standard size:"<<uiNextExecSampleSize; 
            }
        }
        else
        {
            // m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SPIT_SAMPLING, SEST_WAIT_TRANS_CLEVAGE);
            uiNextExecSampleSize = m_pSampleControl->GetNextSampleCatchAndMixSize();
            qDebug()<<"Next Exec Sample size:"<<uiNextExecSampleSize;
        }
    }
    
    if(uiNextExecSampleSize>0)
    {
        ActionAddCatchSampleTask();
        if(m_eInternalStandardStatus == IS_NONE ||
           m_eInternalStandardStatus == IS_FINISH)// 非内标或者内标添加完成后才需要取Tip1000
        {
            // 需要判断提取条位置，如果最左侧有提取条，则移液泵需要复位或者后退一个提取条，防止与样本开盖单元碰撞
            // 获取提取条光耦位置和当前样本是不是最后抓取的样本
            bool bStrip =  CStrip::getInstance().CheckLastStripInLeftNext();
            // 不能关盖，只允许丢弃TIP1000
            if(!bStrip)
            {
                ActionAddSample1000Tip(SEST_WAIT_SAMPLE_CATCH);
            }
            qDebug()<<"HandleSampleBackHomeReply bCatch"<<bStrip;
        }
    }
    else
    {
        //整個批次样本都已归位后即可開始试剂分裝
        // SendPeroidStateToUpperHost(PEST_SAMPLE_PROCESS_END);//开始提取的时间早于样本处理的时间
    }
}


void CAffair::HandlePunchReply()
{
    m_bPunchDone = true;
    qDebug()<<"<<<<<<---------HandlePunchReply------->>>>>>";
    if(m_bWaitPunchDone)
    {
        if(m_eInternalStandardStatus == IS_NONE ||
           m_eInternalStandardStatus == IS_FINISH)// 非内标或者内标添加完成后才需要取Tip1000
        {
            ActionAddSample1000Tip();
        }
        ActionAddSampingTask();
    }
    else
    {
        if(m_eInternalStandardStatus == IS_NONE ||
           m_eInternalStandardStatus == IS_FINISH)// 非内标或者内标添加完成后才需要取Tip1000
        {
            bool bStrip =  CStrip::getInstance().CheckLastStripInLeftNext();
            if(!bStrip)
            {
                ActionAddSample1000Tip(SEST_WAIT_OPEN_SAMPLE_CAP);
            }
        }
    }
    m_extractModule.SetState(false);
}

void CAffair::slotZebraScannerRs(QString dataRs,int iStatus)
{
    QString strMsg;
    int iStripIdx;
    iTolCryScanCnt++;
    if(iStatus ==1)   //1成功  0 超时
    {
        //更新信息，继续压入扫码任务，直到全部扫码完成
        m_extractModule.UpdataScanCodeInfo(dataRs,Status_ScanFinish);
        m_extractModule.FindNextScanStripIdxList(iStripIdx);
        if(iTolCryScanCnt<=iTolScanStripNum && listScanRsMsg.size()<iTolScanStripNum)
        {
            listScanRsMsg.append(dataRs);
        }

        if(iStripIdx !=-1)
        {
            SendExtractScanCodeMotorMove(iStripIdx);
        }
        else
        {
            //已经全部扫码完成
            //格式（同一个试剂条“，”，不同的“|”）：试剂条序号，扫码状态（2：Status_ScanFinish   3：Status_ScanFail），条码信息|
            m_extractModule. PackageScanStripScanRsInfo(strMsg);
            SetExtractScanMode(false);
            emit sigExtractScanRs(strMsg);
            //ActionAddExtractScanMotorInit();  //   //增加复位    该复位动作增加到卡盒复位
            qDebug()<<"scan finish!";
            if(m_bZebraScanAging ==true)
            {
                int iNotSame =   m_extractModule.JudgeScanRs(listScanRsMsg,m_vecScanConutDiff);
                iTolNotSame=iTolNotSame+iNotSame;
                QString strMsg ;
                //qDebug()<<"Toltal Scan num="<<QString::number(iTolCryScanCnt)<<",Fail Num="<<QString::number(iTolFainCnt)<<",iTolNotSame="<<iTolNotSame;
                strMsg = "Toltal Scan num="+QString::number(iTolCryScanCnt)+",Fail Num="+QString::number(iTolFainCnt)+",iTolNotSame="+QString::number(iTolNotSame)+" ";
                for(int i=0;i<m_vecScanConutDiff.size();i++)
                {
                    if(m_vecScanConutDiff[i]!=0)
                    {
                        // qDebug()<<__FUNCTION__<<"not same idx ="<<i<<",cnt="<<m_vecScanConutDiff[i];
                        strMsg = strMsg +"not same idx ="+QString::number(i)+",cnt="+QString::number(m_vecScanConutDiff[i]);
                    }
                }
                qDebug()<<"send MSg="<<strMsg;
                // COperationUnit::getInstance().sendStringResult(Method_strip_codescan_test, strMsg, Machine_UpperHost);
                COperationUnit::getInstance().sendStringData(Method_strip_codescan_test, strMsg, Machine_UpperHost);
                ExtractScanTest_use();
            }
        }
    }
    else
    {
        qDebug()<<"@@@@@ZebraScanner Scan error!!!!!!!!!";
        //关闭扫码
        CCommunicationObject::getInstance().closeZebraScanner();
        iTolFainCnt++;
        //当前扫码失败，扫码下一个
        m_extractModule.UpdataScanCodeInfo(dataRs,Status_ScanFail);
        m_extractModule.FindNextScanStripIdxList(iStripIdx);
        if(iStripIdx !=-1)
        {
            SendExtractScanCodeMotorMove(iStripIdx);
        }
        else
        {
            //已经全部扫码完成,但有错误
            //格式（同一个试剂条“，”，不同的“|”）：试剂条序号，扫码状态（2：Status_ScanFinish   3：Status_ScanFail），条码信息|
            m_extractModule. PackageScanStripScanRsInfo(strMsg);
            SetExtractScanMode(false);
            emit sigExtractScanRs(strMsg);
            //ActionAddExtractScanMotorInit();     //增加复位   该复位动作增加到卡盒复位
            if(m_bZebraScanAging==true)
            {
                int iNotSame =   m_extractModule.JudgeScanRs(listScanRsMsg,m_vecScanConutDiff);
                iTolNotSame =iTolNotSame+iNotSame;
                QString strMsg ;
                qDebug()<<"Toltal Scan num="<<QString::number(iTolCryScanCnt)<<",Fail Num="<<QString::number(iTolFainCnt)<<",iTolNotSame="<<iTolNotSame;
                strMsg = "Toltal Scan num="+QString::number(iTolCryScanCnt)+",Fail Num="+QString::number(iTolFainCnt)+",iTolNotSame="+QString::number(iTolNotSame)+" ";
                for(int i=0;i<m_vecScanConutDiff.size();i++)
                {
                    if(m_vecScanConutDiff[i]!=0)
                    {
                        qDebug()<<__FUNCTION__<<"not same idx ="<<i<<",cnt="<<m_vecScanConutDiff[i];
                        strMsg =strMsg+ "not same idx ="+QString::number(i)+",cnt="+QString::number(m_vecScanConutDiff[i]);
                    }
                }
                qDebug()<<"send MSg="<<strMsg;
                // COperationUnit::getInstance().sendStringResult(Method_strip_codescan_test, strMsg, Machine_UpperHost);
                COperationUnit::getInstance().sendStringData(Method_strip_codescan_test, strMsg, Machine_UpperHost);
                ExtractScanTest_use();
            }
        }
    }
}

void CAffair::ExtractScanCodeMotorMoveReply()  ////回复扫码移动完成
{
    qDebug()<<"<<<<<<---------ExtractScanCodeMotorMoveReply------->>>>>>";
    //移动到位，开始启动扫码;
    if(m_bStartZebraScan==true)  //移动过程中急停
    {
        CCommunicationObject::getInstance().sendStartScanCmdToZebraScanner();
    }
}

void CAffair::StripCarMovePosReply()
{
    qDebug()<<"<<<<<<---------StripCarMovePosReply------->>>>>>";
    int iStripIdx;
    m_extractModule.FindNextScanStripIdxList(iStripIdx);
    if(iStripIdx !=-1)
    {
        SendExtractScanCodeMotorMove(iStripIdx);
    }
}

void CAffair::ExtractScanMotorInitReply()
{
    qDebug()<<"<<<<<<---------ExtractScanMotorInitReply------->>>>>>";
    ActionAddStripPutDownPos();
}

void CAffair::HandleExtractReply()
{
    m_bWaitExtractFinishToTransReagent = true;
    qDebug()<<"<<<<<<---------HandleExtractReply------->>>>>>";
    QString strBatchNo = m_pSampleControl->GetCurBatchNo();// 获取当前批次号，提取完成后，有可能还在转移试剂，需要使用最新批次号
    SendPeroidStateToUpperHost(PEST_EXTRACT_END,SEST_WAIT_EXTRACT,0,strBatchNo);
    m_extractModule.SetState(false);
    SendPeroidStateToUpperHost(PEST_SYSTEM_BUILD_START,SEST_WAIT_TRANS_PURIFY,0,strBatchNo);
    _TransPurify();
}

void CAffair::_TransPurify()
{
    qDebug() << "m_bTransReagentFinishToWaitExtract" << m_bTransReagentFinishToWaitExtract
             << "m_bWaitExtractFinishToTransReagent" << m_bWaitExtractFinishToTransReagent;
    if (m_bTransReagentFinishToWaitExtract && m_bWaitExtractFinishToTransReagent)
    {
        ActionAddTransPurifyTask();
    }
}

void CAffair::HandleSample1000TipReply()
{
    qDebug()<<"<<<<<<---------HandleSample1000TipReply------->>>>>>";
    m_gantryModule.SetState(false);
}

void CAffair::HandleSamplingReply()
{
    qDebug()<<"<<<<<<---------HandleSamplingReply------->>>>>>";
    m_gantryModule.SetState(false);
    //    QVector<quint8> qNextSTVect;
    //    qNextSTVect.push_back(SEST_WAIT_SPIT_SAMPLING);
    //    qNextSTVect.push_back(SEST_WAIT_CLOSE_SAMPLE_CAP);
    //    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SAMPLING, qNextSTVect);

    qDebug()<<"Internal Standard Status"<<m_eInternalStandardStatus;
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SAMPLING, SEST_WAIT_SPIT_SAMPLING);//
    //step1.获取当前吸完样的样本的匹配卡条位置信息, 添加吐样动作
    ActionAddSpitSampleTask();//吐样
}

void CAffair::HandleSpitSampleReply()
{
    qDebug()<<"<<<<<<---------HandleSpitSampleReply------->>>>>>";
    m_gantryModule.SetState(false);

    if (m_eInternalStandardStatus == IS_STEP_ACTION)
    {
        QString strProjID;
        quint8 uiStripIndex = 0;
        quint8 uiColumnIndex = 0;
        bool bSearch = m_pSampleControl->GetNextWaitSampleInfo(strProjID,uiStripIndex,uiColumnIndex);
        qDebug()<<"SpitSampleReply Next Exec Internal Standard size:"<<bSearch<<uiStripIndex;
        if (!bSearch)
        {
            ActionAddStandardAbandonTip200Task();
            m_eInternalStandardStatus = IS_STEP_DONE;
        }
        else
        {
            ActionAddSampingTask();
        }
        qDebug()<<"HandleSpitSampleReply m_eInternalStandardStatus: "<<m_eInternalStandardStatus;
        return;
    }
    
    QVector<quint8> qNextSTVect;
    qNextSTVect.push_back(SEST_WAIT_TRANS_CLEVAGE);
    qNextSTVect.push_back(SEST_WAIT_CLOSE_SAMPLE_CAP);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_SPIT_SAMPLING, qNextSTVect);//
    
    // 需要判断提取条位置，如果最左侧有提取条，则移液泵需要复位或者后退一个提取条，防止与样本开盖单元碰撞
    // 获取提取条光耦位置和当前样本是不是最后抓取的样本
    bool bStrip =  CStrip::getInstance().CheckLastStripInLeft(); 
    ActionAddTransClevageTask(bStrip);//转移裂解液(修改了时序，只丢弃TIP1000)
    if (!bStrip)
    {
        ActionAddCloseSampleCapTask();//关盖
    }
}

void CAffair::HandleTransCleavageReply()
{
    qDebug()<<"<<<<<<---------HandleTransCleavageReply------->>>>>>";
    m_gantryModule.SetState(false); // 不用转移裂解液，(修改了时序，只丢弃TIP1000)
    // QVector<quint8> qNextSTVect;
    // qNextSTVect.push_back(SEST_WAIT_EXTRACT);
    // qNextSTVect.push_back(SEST_WAIT_SUB_PACK_REAGENT);
    m_pSampleControl->UpdateSampleExecSTToNext(SEST_WAIT_TRANS_CLEVAGE, SEST_WAIT_EXTRACT);

    //整個批次都在等待提取即可開始提取及試劑分裝
    if(m_pSampleControl->IsCurBatchSampleSpecificActionDone(SEST_WAIT_EXTRACT) && 
       m_pSampleControl->IsCurBatchSampleStandardActionDone())
    {
        m_bTransReagentFinishToWaitExtract = false;
        m_bWaitExtractFinishToTransReagent = false;
        qDebug()<<"CurBatch Wait ExecTract is true";
        ActionAddExtractTask();
        m_pSampleControl->RearraySystemBuildRegentInfo();
        ActionAddSubpackReagentTask();
        bool bStrip =  CStrip::getInstance().CheckLastStripInLeftNext();// 特殊逻辑，解决样本和移液泵模块干涉问题，延后关盖
        if (bStrip)
        {
            ActionAddCloseSampleCapTask();//关盖
        }
    }
}

void CAffair::HandleStandardAbandonTip200Reply()
{
    qDebug()<<"<<<<<<---------HandleStandardAbandonTip200Reply------->>>>>>";
    m_gantryModule.SetState(false);
    m_eInternalStandardStatus = IS_STEP_ABANDON;
    HandleSampleBackHomeReply();
}

void CAffair::HandleSubpackReagentReply()
{
    qDebug()<<"<<<<<<---------HandleSubpackReagentReply------->>>>>>";
    m_gantryModule.SetState(false);
    ActionAddSubpackReagentTask();
}

void CAffair::HandleSubPackPunchReply()
{
    qDebug()<<"<<<<<<---------HandleSubPackPunchReply------->>>>>>";
    m_extractModule.SetState(false);
}

void CAffair::HandleGetTipReply()
{
    qDebug()<<"<<<<<<---------HandleGetTipReply------->>>>>>";
    m_gantryModule.SetState(false);
}

void CAffair::HandleAbandonTipReply()
{
    qDebug()<<"<<<<<<---------HandleAbandonTipReply------->>>>>>";
    m_gantryModule.SetState(false);
}

void CAffair::HandleTransReagentReply()
{
    qDebug()<<"<<<<<<---------HandleTransReagentReply------->>>>>>";
    m_gantryModule.SetState(false);
    //已做完转移试剂，转移到下一个状态等待提存液和石蜡油
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_TRANS_REAGENT, SEST_WAIT_TRANS_PURIFY);  

    ActionAddTransReagentTask();  
}

void CAffair::HandleTransPurifyReply()
{
    qDebug()<<"<<<<<<---------HandleTransPurifyReply------->>>>>>";
    m_gantryModule.SetState(false);
    //已做完转移试剂、提存液和石蜡油部分转移到下一个状态等待盖帽及转移
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_TRANS_PURIFY, SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE);  
    ActionAddTransPurifyTask();   
}

void CAffair::HandleCapAndTransTubeReply()
{
    qDebug()<<"<<<<<<---------HandleCapAndTransTubeReply------->>>>>>";
    m_gantryModule.SetState(false);
    //已做完盖帽及转移切換到到下一个状态等待轉移及混勻
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE,SEST_WAIT_PCR_SWITCH);
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA, qVect);
    if(!bGet && qVect.size() == 0)//不需要转移到pcr区域才能转移tube
    {
        bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_PCR_MIX, qVect);
        if (!bGet && qVect.size() == 0)// 需要判断上一个pcr管动作是否正在离心(判断pcr抓手是否空闲)
        {
            if (m_pcrCatchModule.GetCurSubTaskID() != PCTI_SWITCH_TUBE)// 不能连续相同两个转移pcr任务
            {
                //转移tube到离心模块旁
                ActionAddSwitchTubeTask(); 
            }            
        }
    }    
}

void CAffair::HandleSwitchTubeReply()
{
    qDebug()<<"<<<<<<---------HandleSwitchTubeReply------->>>>>>";
    m_pcrCatchModule.SetState(false);  
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_PCR_SWITCH,SEST_WAIT_PCR_MIX); 
    // 混匀
    ActionAddCentrifugeTubeTask();

    //处在扩增单例程，只执行转移
    if (m_pSampleControl->m_sampleAmplificate.GetRunStatus())
    {
        ActionAddAmplTransTubeTask();
        return;
    }
    // //驅動下一個蓋帽+轉移
    // ActionAddCapAndTransTubeTask();
}

void CAffair::HandleCentrifugeTubeReply()
{
    qDebug()<<"<<<<<<---------HandleCentrifugeTubeReply------->>>>>>";
    m_pcrCatchModule.SetState(false);
    // m_switchMixModule.SetState(false); // 提速替换
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_PCR_MIX,SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA);
    // 转移PCR到扩增区
    ActionAddTransToPCRAmplifyAreaTask();
}

void CAffair::HandleOpenPCRReply()
{
    qDebug()<<"<<<<<<---------HandleOpenPCRReply------->>>>>>";
    //PCR区域开盖完成
    m_pcrCatchModule.SetState(false);
}

void CAffair::HandleTransPCRTubeToAmplifyAreaReply()
{
    qDebug()<<"<<<<<<---------HandleTransPCRTubeToAmplifyAreaReply------->>>>>>";
    m_pcrCatchModule.SetState(false);
    //转移完PCR管到擴增區
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA, SEST_WAIT_PCR_AMPLIFY);
    //已批量完成轉移到PCR擴增區並關蓋
    QString strBatchNo  = _getCurBatchNo(SEST_WAIT_PCR_AMPLIFY);
    if(m_pSampleControl->IsCurBatchSystemBuildSpecificActionDone(SEST_WAIT_PCR_AMPLIFY,strBatchNo))//最新批次都在等待PCR擴增即可開始PCR擴增
    {
        ActionAddMotorBoard2InitTask();//前面的提取體系構建處理完成相應模塊可恢復初始狀態
        qDebug()<<strBatchNo<<"Batch Wait PCR Amplify is true";
        //執行PCR擴增過程
        ActionAddStartPCRTask();
    }
    else
    {
        QVector<SystemBuildInfo> qVect;
        bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_PCR_SWITCH, qVect);
        if(bGet && !qVect.isEmpty())
        {
            if (m_pcrCatchModule.GetCurSubTaskID() != PCTI_SWITCH_TUBE)// 不能连续相同两个转移pcr任务
            {
                ActionAddSwitchTubeTask();
            }
        }        
    }
}

//关PCR区域盖子 有两种情况(但是pcr抓手组件已经空闲)
// 1、已批量完成PCR管转移 HandleClosePCRReply
// 2、已批量完成PCR管丟棄 HandleAbandonClosePCRReply
void CAffair::HandleClosePCRReply()
{
    qDebug()<<"<<<<<<---------HandleClosePCRReply------->>>>>>";
    m_pcrCatchModule.SetState(false);
    // 1、已批量完成PCR管转移
    QString strBatchNoAmplify  = _getCurBatchNo(SEST_WAIT_PCR_AMPLIFY);
    if(m_pSampleControl->IsCurBatchSystemBuildSpecificActionDone(SEST_WAIT_PCR_AMPLIFY,strBatchNoAmplify))//多批次存在状态时间空隙
    {
        SendPeroidStateToUpperHost(PEST_SYSTEM_BUILD_END,SEST_WAIT_PCR_AMPLIFY);
        QThread::msleep(10);//延时10ms，防止状态更新太快导致的状态更新错误
        SendPeroidStateToUpperHost(PEST_AMPLIFY_START,SEST_WAIT_PCR_AMPLIFY);  
        m_pcrCatchModule.SetIsBusy(false);// 转移pcr管完成，pcr抓手就认为空闲状态(需要判断是否需要丢弃pcr管) 
        ActionAddAbandonPCRTask();// 是否需要丢弃pcr管
    }
}

void CAffair::HandleAbandonClosePCRReply()
{
    qDebug()<<"<<<<<<---------HandleAbandonClosePCRReply------->>>>>>";
    m_pcrCatchModule.SetState(false);
    // 2、已批量完成PCR管丟棄
    QString strBatchNoFinish  = _getCurBatchNo(SEST_FINISH);
    if(!strBatchNoFinish.isEmpty() && 
       m_pSampleControl->IsCurBatchSystemBuildSpecificActionDone(strBatchNoFinish) &&//当前批次都已完成PCR並丟棄
       PCRResource::getInstance().CheckRecordPCRAreaDuration(strBatchNoFinish))// 当前批次是否还在pcr区域
    {
        qDebug()<<strBatchNoFinish<<"Batch test finish.";
        SendPeroidStateToUpperHost(PEST_SEQ_END,SEST_FINISH);
        // 单扩增结束，重置状态
        m_pSampleControl->m_sampleAmplificate.ResetRunStatus();
        PCRResource::getInstance().DelRecordPCRAreaDuration(strBatchNoFinish);
        m_pSampleControl->ClearFinishSystemBuildInfo(strBatchNoFinish);//清空已完成SEST_FINISH状态样本(注意状态更新到上位机的顺序)

        QString strBatchNoTubeCap  = _getCurBatchNo(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE);
        bool bTransTubeCap = m_pSampleControl->IsCurBatchSystemBuildSpecificActionDone(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE,strBatchNoTubeCap);
        // 多批次有可能存在体系构建时间短于PCR扩增时间的情况，需要判断pcr区域是否有足够的资源
        if(m_pSampleControl->GetCurBatchSystemBuildSize()>0 &&  bTransTubeCap && // bTransTubeCap 确保提纯液已经转移完成
           m_pPCRResource->IsPCRResourceEnough(m_pSampleControl->GetCurBatchSystemBuildSize()))
        {
            m_pcrCatchModule.SetIsBusy(true);//开始转移pcr管，pcr抓手就认为进入繁忙状态
            _calcArraySystemBuildInfo();// 需要重新计算
            //添加盖PCR管业务
            ActionAddCapAndTransTubeTask();
        }
        else
        {
            m_pcrCatchModule.SetIsBusy(false);// 丢弃pcr管完成，pcr抓手就认为空闲状态
        }
    }

    // 全部批次执行完成后，更新运行状态为空闲
    if (m_seqType != ST_SAMPLE_SCAN &&    // 是否在样本扫码
        m_pSampleControl->IsAllBatchSystemBuildSpecificActionDone(strBatchNoFinish))// 所有批次都已完成
    {
        _SetRunST(RST_WAIT_IDLE, __FUNCTION__);
        m_pcrModule[PCR_MODULE_MAIN_INDEX].SetFLLedStatus(false); // 灯源关闭(正常状态)
        qDebug()<<"All Batch test finish.";
    }
}

void CAffair::HandlePCREndReply(quint16 uiPCRIndex)
{
    qDebug()<<"<<<<<<---------HandlePCREndReply------->>>>>>";
    if(!m_curBatchPCREndDone.load())
    {
        qDebug()<<"<<<<<<---------HandlePCREndReplyAction------>>>>>>";
        // m_curBatchPCREndDone.store(true);// 多批次
        PCRResource::getInstance().SetPCRST(uiPCRIndex,PCRST_IDLE);
        if(!m_bStartMELT)// 有熔解不上报扩增结束
        {
            QString strBatchNo = PCRResource::getInstance().GetRecordPCRAreaBatchInfo(uiPCRIndex);
            SendPeroidStateToUpperHost(PEST_AMPLIFY_END,SEST_WAIT_PCR_AMPLIFY,0,strBatchNo);
        }
        m_pSampleControl->RearraySystemBuildInfoBeforePCRAbandon(uiPCRIndex);//PCR結束後重新整理SystemBuildInfo信息
        m_pSampleControl->ShowSTMapInfo();

        // 需要判断是否需要透传PCR相关命令和数据状态(即异常状态，不能丢弃pcr管，直接结束测试)
        qDebug()<<"HandlePCREndReply GetRunST:"<<GetRunST()<<m_pSampleControl->IsAllBatchSystemBuildSpecificActionDone("");
        if(GetRunST() == RST_PCR_RUN)
        {
            QString strBatchNo = PCRResource::getInstance().GetRecordPCRAreaBatchInfo(uiPCRIndex);
            qDebug()<<strBatchNo<<"Batch test finish.";
            SendPeroidStateToUpperHost(PEST_SEQ_END,SEST_FINISH,0,strBatchNo);  
            PCRResource::getInstance().DelRecordPCRAreaDuration(strBatchNo);
            m_pSampleControl->ClearFinishSystemBuildInfo(strBatchNo);//清空已完成SEST_FINISH状态样本(注意状态更新到上位机的顺序)                   
            if(m_pSampleControl->IsAllBatchSystemBuildSpecificActionDone(strBatchNo))
            {
                _SetRunST(RST_IDLE, __FUNCTION__);
                _SetSeqType(ST_UNDEFINE);
            }
            return;
        }

        if (!m_pcrCatchModule.GetIsBusy())// 判断是否进入丢弃PCR管流程(如果pcr抓手繁忙，在转移完成时重新进入丢弃pcr管状态HandleClosePCRReply函数处理)
        {
            m_pcrCatchModule.SetIsBusy(true);//pcr扩增完成，准备丢弃pcr管
            ActionAddAbandonPCRTask();
        }
    }
}

void CAffair::HandleAbandonPCRReply()
{
    qDebug()<<"<<<<<<---------HandleAbandonPCRReply------->>>>>>";
    //丢弃PCR管
    m_pcrCatchModule.SetState(false);
    m_pSampleControl->UpdateSystemBuildExecSTToNext(SEST_WAIT_ABANDON, SEST_FINISH);
    //如果还有未完成的丢弃的PCR扩增管，继续丢弃
    ActionAddAbandonPCRTask();
}

void CAffair::GetPCRResource(quint8 uiPCRSize, PosInfo& areaPos, PosInfo& subAreaPos,
                             bool& bNeedOpenCap, QString& strTecName)
{
    quint8 uiAreaRowIndex = 0;
    quint8 uiAreaColumnIndex = 0;
    quint8 uiSubAreaRowIndex = 0;
    quint8 uiSubAreaColumnIndex = 0;
    if(uiPCRSize == 1)
    {
        //        PCRResource::getInstance().GetNextSingleIdlePCRPos(uiAreaRowIndex, uiAreaColumnIndex, uiSubAreaRowIndex, uiSubAreaColumnIndex, bNeedOpenCap);
        m_pPCRResource->GetNextDoubleIdlePCRPos(uiAreaRowIndex, uiAreaColumnIndex,
                                                uiSubAreaRowIndex, uiSubAreaColumnIndex, bNeedOpenCap, strTecName);
    }
    else
    {
        m_pPCRResource->GetNextDoubleIdlePCRPos(uiAreaRowIndex, uiAreaColumnIndex,
                                                uiSubAreaRowIndex, uiSubAreaColumnIndex, bNeedOpenCap, strTecName);
    }
    areaPos = {uiAreaRowIndex, uiAreaColumnIndex, 0};
    subAreaPos = {uiSubAreaRowIndex, uiSubAreaColumnIndex, 0};
    qDebug()<<"GetPCRResource:"<<uiAreaRowIndex<<uiAreaColumnIndex<<uiSubAreaRowIndex<<uiSubAreaColumnIndex<<bNeedOpenCap<<strTecName;
}

void CAffair::scanCodeHandleCmdReply(quint16 uiComplexID,quint16 uiResult)
{
    qDebug()<<"scanCodeHandleCmdReply: "<<uiComplexID<<uiResult;
    switch (uiComplexID)
    {
    case Action_StripCarMoveToScanPos:// 提取条卡盒到位
    {
        StripCarMovePosReply();     
        break;
    }
    case Action_ExtractScanMotorMove:// 提取条扫码电机
    {
        ExtractScanCodeMotorMoveReply();      
        break;
    }  
    case Action_ExtractScanMotorInit:// 扫码电机复位
    {
        ExtractScanMotorInitReply();      
        break;
    }               
    case Action_SampleCodeScanStepStart:// 单个样本扫码开始
    {
        // 需要判断样本有无
        _checkSampleScanTubeAndAction();
        break;
    }

    case Action_SampleCodeScanRotate:// 单个样本扫码抓手旋转
    {
        qDebug()<<"Action_SampleCodeScanRotate"<<CCommunicationObject::getInstance().GetSamplerScanCurPos();
        //1、关闭扫码器
        CCommunicationObject::getInstance().StopSamplerCodeScanner();
        //2、放回样本
        ActionAddSampleScanCodeEndTask(CCommunicationObject::getInstance().GetSamplerScanCurPos());
        break;
    }  
    case Action_SampleCodeScanStepEnd:// 单个样本扫码结束
    {
        //1、判断是否还有样本
        if(!CCommunicationObject::getInstance().IsSamplerScanFinish())
        {
            ActionAddSampleScanCodeStartTask(CCommunicationObject::getInstance().GetSamplerScanNextPos());
        }
        else
        {
            // 需要复位(可以使用板1复位)
            ActionAddSampleScanCodeInitTask();
        }         
        break;
    }
    case Action_SampleCodeScanInit://  样本扫码全部结束
    {
        // 获取结果
        QStringList resultLeft, resultRight;
        CCommunicationObject::getInstance().GetSampleCodeScanLeftResult(resultLeft);
        CCommunicationObject::getInstance().GetSampleCodeScanRightResult(resultRight);                            
        QStringList resultAll = CCommunicationObject::getInstance().GetSampleCodeScanAllResult(resultLeft,resultRight);
        qDebug() << "SampleCodeScan resultAll: "<<resultAll.size()<<resultAll;
        SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_END,0,resultAll.join(","));//上报扫码结果                    
        CCommunicationObject::getInstance().ResetSampleScanPos();      
        if (GetRunST() != RST_RUN)// 不能改变运行状态，可能还在测试状态
        {
            _SetSeqType(ST_UNDEFINE);
            _SetRunST(RST_IDLE, __FUNCTION__);
        }
        emit sigSampleScanFinished(true, "");
        // 测试使用(老化样本扫码测试)
        // m_pAffair->SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_START,0);//开始扫码
        // m_pAffair->ActionAddSampleScanCodeStartTask(CCommunicationObject::getInstance().GetSamplerScanCurPos());
        // qDebug() << "SampleCodeScan Action_SampleCodeScanInit: repeat";
        break;
    }      
    default:
        break;    
    }
}

void CAffair::periodicHandleCmdReply(quint16 uiComplexID)
{
    m_pSampleControl->ShowSTMapInfo();
    switch (uiComplexID)
    {
    case Action_Board1Init:
    {
        if(GetRunST() != RST_RUN)
            ActionAddMotorBoard2InitTask();
        break;
    }
    case Action_SampleToOpenCap://抓取样本到开盖位结束
    case Action_SampleToOpenCapLeft:
    case Action_SampleToOpenCapDouble:
    {
        HandleCatchSampleToOpenCapReply();
        break;
    }
    case Action_OpenCap://开盖
    case Action_OpenCapLeft:
    case Action_OpenCapDouble:
    {
        HandleOpenCapReply();
        break;
    }
    case Action_Sampling://吸样
    case Action_SamplingDouble:
    {
        HandleSamplingReply();
        break;
    }    
    case Action_SingleSample1000Tip://吸样本前取tip1000
    case Action_DoubleSample1000Tip:
    {
        HandleSample1000TipReply();
        break;
    }
    case Action_CloseCap://关盖
    case Action_CloseCapLeft:
    case Action_CloseCapDouble:
    {
        HandleCloseCapReply();
        break;
    }
    case Action_SampleBackHome://样本归位
    case Action_SampleBackHomeLeft:
    case Action_SampleBackHomeDouble:
    {
        HandleSampleBackHomeReply();
        break;
    }
    case Action_Punch:
    {
        HandlePunchReply();
        break;
    }       
    case Action_SpitSample://吐样
    case Action_SpitSampleDouble:
    {
        HandleSpitSampleReply();
        break;
    }
    case Action_AbandonStandardTip200:
    {
        HandleStandardAbandonTip200Reply();
        break;
    }    
    case Action_TransClevage:
    case Action_TransClevageDouble:
    {
        HandleTransCleavageReply();
        break;
    }
    case Action_SubpackFinish:
    {
        // Handle Subpack Reagent action
        HandleSubpackReagentReply();
        break;
    }
    case Action_SSwReagent:
    case Action_DSwReagent:
    case Action_SwReagentSpit:
    {
        HandleTransReagentReply();
        break;
    }
    case Action_SSwPurifyOraffin:
    case Action_DSwPurifyOraffin:
    case Action_TransPurify:
    case Action_TransPurifyDouble:
    case Action_SwOilSpit:
    {
        HandleTransPurifyReply();
        break;
    }
    case Action_CapAndTransTube:
    case Action_CapAndTransTubeDouble:
    case Action_AmplTransTube:
    case Action_AmplTransTubeDouble:
    case Action_TransTubeCap:
    {
        HandleCapAndTransTubeReply();
        break;
    }   
    case Action_SwitchTube:
    {
        HandleSwitchTubeReply();
        break;
    }
    case Action_CentrifugeTube:
    {
        HandleCentrifugeTubeReply();
        break;
    }    
    case Action_OpenPCRCap:
    case Action_AbandonOpenPCRCap:
    {
        // Handle Open PCR action
        HandleOpenPCRReply();
        break;
    }
    case Action_TransPCRTube:
    {
        // Handle Switch PCR Cube action
        HandleTransPCRTubeToAmplifyAreaReply();
        break;
    }
    case Action_ClosePCRCap:
    {
        // Handle Close PCR action
        HandleClosePCRReply();
        break;
    }
    case Action_AbandonClosePCRCap:
    {
        // Handle Close PCR action
        HandleAbandonClosePCRReply();
        break;
    }    
    case Action_AbandonPCR:
    {
        // Handle Abandon PCR action
        HandleAbandonPCRReply();
        break;
    }
    case Action_Extract:
    case Action_Extract_End:
    {
        // Handle Extract action
        HandleExtractReply();
        break;
    }
    case Action_Extract_Start:
    case Action_Extract_Run:    
    {
        m_extractModule.SetState(false);
        break;
    }    
    case Action_SubPackPunch1:
    case Action_SubPackPunch2:
    case Action_GetTip:
    case Action_AbandonTip:
    case Action_GetStandardTip200:
    case Action_GetTip200:
    case Action_AbandonTip200:
    case Action_GetCrossTip200:
    case Action_SwReagentSuck:
    case Action_SwPurifySuck:
    case Action_SwOilSuck:
    case Action_SwPurifySpit:
    case Action_GetPcrCap:
    case Action_CapToTube:
    case Action_SubpackReagent:
    {
        m_gantryModule.SetState(false);
        break;
    }
    case Action_SampleScanTubeExist:
    {
        if(ST_SAMPLE_SCAN == _GetSeqType())
        {
            ActionAddSampleScanCodeStartTask(CCommunicationObject::getInstance().GetSamplerScanCurPos());
        }
        else if(ST_SAMPLE_EXIST == _GetSeqType() || ST_TEST_PRE == _GetSeqType())
        {
            QString strParams = QString("%1:%2").arg(ST_SAMPLE_EXIST).arg(CCommunicationObject::getInstance().GetSampleCodeScanResult());
            QDFUN_LINE << "strParams" << strParams;
            COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost);
            emit sigSampleExistFinished(true, "");
        }
        else
        {
            QDFUN_LINE << "Error:Action_SampleScanTubeExist seqType:" << _GetSeqType();
        }
        break;
    }
    default:
        break;
    }
}

void CAffair::periodicErrorHandleCmdReply(quint16 uiComplexID,quint16 uiResult)
{
    // 测试或者扫码过程中异常，需要判断是否需要透传PCR相关命令和数据
    bool bIsNeedTransPCR = false;
    if(!m_pSampleControl->IsAllSystemBuildSpecificActionDone(SEST_WAIT_PCR_AMPLIFY))//改为判断PCR扩增是否完成，而不是判断批次是否完成
    {
        _SetRunST(RST_PCR_RUN, __FUNCTION__);
        bIsNeedTransPCR = true;
    }
    
    // 以PCR扩增为基准，PCR扩增中出现其他故障(需要保持PCR扩增状态)，扩增完成后(在丢弃PCR或者管盖出错时，需要发送到上位机PCR当前批次测试完成)
    if (uiResult != 0 && (!m_pSampleControl->IsAllSystemBuildSpecificActionDone(SEST_WAIT_ABANDON) || uiComplexID == Action_AbandonClosePCRCap))
    {
        // 获取当前丢弃PCR的批次信息
        QVector<SystemBuildInfo> qVect;        
        if(uiComplexID == Action_AbandonClosePCRCap)
        {
            m_pSampleControl->GetCurExecSystemBuildInfo(SEST_FINISH,qVect);
        }
        else
        {
            m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_ABANDON,qVect);
        }
        
        if(qVect.size()>0)
        {
            // 获取当前丢弃PCR的批次信息
            QString strBatchNo = qVect[0].strBatchNo;
            SendPeroidStateToUpperHost(PEST_SEQ_END,SEST_WAIT_ABANDON,0,strBatchNo);
            qDebug()<<strBatchNo<<"Batch test abandon pcr finish.";
        }
    }

    RunStat runStatus = this->GetRunST();
    if(RunStat::RST_STOP == runStatus|| RunStat::RST_IDLE == runStatus)//停止和空闲状态下，直接返回(例：点击停止后)
    {
        if (m_seqType == ST_SAMPLE_SCAN)
        {
            qDebug()<<"scan code --error"<<runStatus;
            SendSampeScanStateToUpperHost(PEST_SAMPLE_SCAN_END,0,"");//上报扫码结果                    
            CCommunicationObject::getInstance().ResetSampleScanPos();   
        }        
        if(!bIsNeedTransPCR)
        {
            _SetSeqType(ST_UNDEFINE);//更新状态
            _SetRunST(RST_IDLE, __FUNCTION__);
        }
    }     
    qDebug()<<"periodicErrorHandleCmdReply: "<<uiComplexID<<uiResult;
}

void CAffair::_HandleReceiveCmdResultList()
{
    while (m_iCmdReadIndex_Condition.load() != m_iCmdWriteIndex_Condition.load())
    {
        ActionCmdReply reply = m_uiComplexCmdInfoList[m_iCmdReadIndex_Condition.load()];
        quint16 uiComplexID = reply.uiCmdId;
        auto action = magic_enum::enum_cast<EnumAffairAction>(uiComplexID);
        if (action.has_value()) {
            auto action_name = magic_enum::enum_name(action.value());
            qDebug()<<"Deal with ComplexCmd "<<uiComplexID<<":"<<action_name.data()
                    << " m_iCmdReadIndex_Condition"<<m_iCmdReadIndex_Condition.load()
                    << "m_iCmdWriteIndex_Condition"<<m_iCmdWriteIndex_Condition.load()
                    <<m_seqType;
        }
        else
        {
            qDebug()<<"Deal with ComplexCmd uiComplexID over range"<<uiComplexID<<":"
                      << " m_iCmdReadIndex_Condition"<<m_iCmdReadIndex_Condition.load()
                      << "m_iCmdWriteIndex_Condition"<<m_iCmdWriteIndex_Condition.load()
                      <<m_seqType;
        }

        // 环形队列
        m_iCmdReadIndex_Condition.store((m_iCmdReadIndex_Condition.load() + 1) % BUFFER_SIZE);

        if(reply.uiResult>0)
        {
            qDebug()<<"ComplexCmd failed:"<<uiComplexID<<"Result:"<<reply.uiResult;
            if(m_seqType == ST_PERIODIC || m_seqType == ST_SAMPLE_SCAN)
            {
                periodicErrorHandleCmdReply(uiComplexID,reply.uiResult);          
            }
			else if(m_seqType == ST_MOTOR_DEBUG)// 位置调试
            {
               // MaintainSubSystem::getInstance().HandleCmdReply(uiComplexID,reply.uiResult);
            }
            else if(m_seqType == ST_SELF_TEST)
            {               
                MaintainSubSystem::getInstance().SelfTestHandleTimeseqReply(uiComplexID,reply.uiResult);
            }
			else if(m_seqType == ST_AGING_TEST)// 老化测试
            {
               MaintainSubSystem::getInstance().HandleAgingTestCmdReply(uiComplexID,"",reply.uiResult);
            }
            else if(m_seqType == ST_RESET)
            {
                MaintainSubSystem::getInstance().ResetTestHandleTimeseqReply(uiComplexID,reply.uiResult);
            }
            else
            {
                CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceActionFailed, QString("seqType:%1 ComplexID %2 failed.").arg(m_seqType).arg(uiComplexID));
                QString strParams = QString("%1:").arg(m_seqType);
                COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost, reply.uiResult);
                _SetSeqType(ST_UNDEFINE);
                _SetRunST(RST_IDLE, __FUNCTION__);

            }
        }
        else
        {
            switch(m_seqType)
            {
            case ST_PERIODIC:
            case ST_EXTRACTION:
            case ST_AMPLIFICATION:
            case ST_SAMPLE_SCAN:
            case ST_SAMPLE_EXIST:
            case ST_TEST_PRE:
            {
                periodicHandleCmdReply(uiComplexID);
                scanCodeHandleCmdReply(uiComplexID);
                break;
            }
            case ST_RESET:
            {
                MaintainSubSystem::getInstance().ResetTestHandleTimeseqReply(uiComplexID,reply.uiResult);
                break;
            }
            case ST_SELF_TEST:
            case ST_PCR_CLEAN:
            {
                MaintainSubSystem::getInstance().SelfTestHandleTimeseqReply(uiComplexID,reply.uiResult);
                break;
            }
            case ST_MOTOR_DEBUG:
            {
               // MaintainSubSystem::getInstance().HandleCmdReply(uiComplexID,reply.uiResult);
                break;
            } 
			case ST_AGING_TEST:
            {
                
                //MaintainSubSystem::getInstance().HandleAgingTestCmdReply(uiComplexID,reply.uiResult);
                break; 
                
            }                     
            case ST_UNDEFINE:
                break;
            }
        }
    }
}



void CAffair::_BeginPeriodicProcessActions(quint8 quWaitExecSampleSize )
{
    //检查耗材信息
    //
    //    m_sampleModule.SetCatchType(quWaitExecSampleSize);
    SendPeroidStateToUpperHost(PEST_SEQ_START);
    if(quWaitExecSampleSize>0)
    {     
        ActionAddCatchSampleTask();
        ActionAddPunchStripTask();
        SendPeroidStateToUpperHost(PEST_SAMPLE_PROCESS_START);
    }
}

void CAffair::_addSampleTubeExistFalgToParamStr(QString& strParam)
{
    if(CGlobalConfig::getInstance().getSampleTubeExistFlag())
    {
        strParam += ",2";
    }
    else
    {
        strParam += ",3";
    }
}

void CAffair::_addPCRTubeExistFalgToParamStr(QString& strParam)
{
    if(CGlobalConfig::getInstance().getPCRTubeExistFlag())
    {
        strParam += ",2";
    }
    else
    {
        strParam += ",3";
    }
}

void CAffair::ActionAddCatchSampleTask()
{
    qDebug()<<"-------ActionAddCatchSampleTask---------";
    QString strParam = "";
    QVector<SampleInfo> infoVect =  m_pSampleControl->GetNextWaitCatchAndMixSampleInfos();
    if(infoVect.size()>0)
    {

#ifdef M1
        strParam += QString(",%1,%2").arg(infoVect.at(0).uiRowIndex).arg(infoVect.at(0).uiColumnIndex);
#else
        strParam += QString(",%1").arg(infoVect.at(0).uiRowIndex);
#endif

        switch(infoVect.size())
        {
        case 1:
        {
            m_sampleModule.SetSampleCatchType(infoVect[0].uiColumnIndex);
            QDFUN_LINE << infoVect[0].printInfo();
        }
            break;
        case 2:
        {
            m_sampleModule.SetSampleCatchType(SCT_DOUBLE);
            QDFUN_LINE << infoVect[0].printInfo();
            QDFUN_LINE << infoVect[1].printInfo();
        }
            break;
        default:
        {
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Test_Process, FT_SampleSizeOut, QString("Sample size is error! %1").arg(infoVect.size()));
            QDFUN_LINE << "Sample size is error!" << infoVect.size();
        }
            break;
        }
        strParam += QString(",%1").arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
        _addSampleTubeExistFalgToParamStr(strParam);
        m_sampleModule.SlotAddSubTask(STI_CATCH_SAMPLE, "", strParam);
    }
}


void CAffair::_addIsCapToParam(QString& strParam)
{
    //是否开关盖动作，需要执行以下动作
    //1.获取样本信息,如果单个样本操作，提供有无盖信息，如果是两个样本需要对四种有无盖组合情况依次分析
    QVector<SampleInfo> infoVect =  m_pSampleControl->GetNextWaitCatchAndMixSampleInfos();
    if(infoVect.size()>0)
    {
        switch(infoVect.size())
        {
        case 1:
            //单样本只需补充参数该样本是否盖盖
            strParam += QString(",%1").arg(infoVect[0].bIsCaped);
            break;
        case 2:
            //两个样本的情况需要补充进行4种情况应对：左无盖*右无盖，左有盖*右无盖，左无盖*右有盖，左有盖*右有盖
            if(infoVect[0].bIsCaped == infoVect[1].bIsCaped )
            {
                strParam += QString(",%1").arg(infoVect[0].bIsCaped);
                m_sampleModule.SetSampleCatchType(SCT_DOUBLE);
            }
            else if (infoVect[0].bIsCaped) //只第一个元素有盖
            {
                strParam += QString(",%1").arg(infoVect[0].bIsCaped);
                m_sampleModule.SetSampleCatchType(infoVect[0].uiColumnIndex);

            }
            else if (infoVect[1].bIsCaped)//只第二个元素有盖
            {
                strParam += QString(",%1").arg(infoVect[1].bIsCaped);
                m_sampleModule.SetSampleCatchType(infoVect[1].uiColumnIndex);
            }
            break;
        }
    }
    else
    {
        strParam += QString(",%1").arg(true);
    }
}

void CAffair::ActionAddOpenSampleCapTask()
{
    qDebug()<<"-------ActionAddOpenSampleCapTask---------";
    QString strParam = "";
    strParam += QString(",%1").arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
    strParam += ",3";//状态类型：0为运动中，1为到达目标位置，2为旋转堵转，3为到达目标位置或旋转堵转（即是状态1或2）
    // _addSampleTubeExistFalgToParamStr(strParam);
    _addSampleHeightStateToParam(strParam);
    _addIsCapToParam(strParam);//添加是否盖盖信息
    m_sampleModule.SlotAddSubTask(STI_CATCH_OPEN_CAP,"",strParam);
}

void CAffair::_addSampleHeightStateToParam(QString& strParam)
{
    //高低管的判断由下位机根据光耦判断
    quint8 uileftSampleTubeST = 0;
    quint8 uiRightSampleTubeST = 0;
    QVector<SampleInfo> infoVect =  m_pSampleControl->GetNextWaitCatchAndMixSampleInfos();
    if(infoVect.size()>0)
    {
        uileftSampleTubeST = infoVect.at(0).uiRowIndex;
        uiRightSampleTubeST = infoVect.at(0).uiRowIndex;
    }
    strParam += QString(",%1,%2").arg(uileftSampleTubeST).arg(uiRightSampleTubeST);
}

void CAffair::ActionAddCloseSampleCapTask()
{
    qDebug()<<"-------ActionAddCloseSampleCapTask---------";
    QString strParam = QString(",%1").arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
    strParam += ",3";//状态类型：0为运动中，1为到达目标位置，2为旋转堵转，3为到达目标位置或旋转堵转（即是状态1或2）
    // _addSampleTubeExistFalgToParamStr(strParam);
    _addSampleHeightStateToParam(strParam);
    _addIsCapToParam(strParam);//添加是否盖盖信息
    m_sampleModule.SlotAddSubTask(STI_CATCH_CLOSE_CAP,"",strParam);
}

void CAffair::ActionAddSampleBackHomeTask()
{
    qDebug()<<"-------ActionAddSampleBackHomeTask---------";

    QString strParam = "";
    QVector<SampleInfo> infoVect =  m_pSampleControl->GetNextWaitCatchAndMixSampleInfos();
    if(infoVect.size()>0)
    {
        switch(infoVect.size())
        {
        case 1:
            m_sampleModule.SetSampleCatchType(infoVect[0].uiColumnIndex);
            break;
        case 2:
            m_sampleModule.SetSampleCatchType(SCT_DOUBLE);
            break;
        }
        strParam += QString(",%1").arg(infoVect.at(0).uiRowIndex);
        strParam += QString(",%1").arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
        _addSampleTubeExistFalgToParamStr(strParam);
        bool bCatch = m_pSampleControl->HasNextSampleCatchSize();//bCatch 还有样本
        strParam += QString(",%1").arg(!bCatch);
        m_sampleModule.SlotAddSubTask(STI_CATCH_BACK_HOME, "", strParam);
    }

#if 0
    QVector<SampleInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSampleInfo(SEST_WAIT_SAMPLE_BACK_HOME, qVect);
    if(bGet && qVect.size()>0)
    {
        strParam += QString(",%1,%2").arg(qVect[0].uiRowIndex)
                .arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
        _addSampleTubeExistFalgToParamStr(strParam);
        m_sampleModule.SlotAddSubTask(STI_CATCH_BACK_HOME,"",strParam);
    }
#endif
}

void CAffair::ActionAddSample1000Tip(const SeqExecST seq)
{
    qDebug()<<"-------ActionAddSample1000Tip---------";
    QString strParam = "";
    quint8 uiSize =1;
    quint8 uiColumnIndex = 0;
    bool bCurOpSamplesSingleRight = false;
    
    QVector<SampleInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSampleInfo(seq, qVect);
    if(bGet || qVect.size()>0)
    {
        QVector<quint8> qStripIndexVect = CStrip::getInstance().GetNextConsume(qVect.size()); 
        for(int i=0;i<qVect.size();i++)
        {
            SampleInfo info = qVect.at(i);
            info.uiStripIndex = qStripIndexVect.at(i);
            strParam += QString(",%1,%2").arg(info.uiStripIndex).arg(info.uiColumnIndex);
            uiColumnIndex = info.uiColumnIndex;
            QDFUN_LINE << info.printInfo();
            break;
        } 
        uiSize = qVect.size();
        m_pSampleControl->UpdateCurExecStripAndSamplingSTIndex(uiSize);// 用于判断isCurOpSamplesSingleRight
    }       
    
    if(uiSize<1)
    {
        uiSize = 1;
    }
    m_gantryModule.SetCatchType(uiSize-1);
    if (qVect.size() == 1 && uiColumnIndex == 0)// 只有一个样本，列位置在右
    {
        bCurOpSamplesSingleRight = true;
    }
    
    strParam += QString (",%1").arg(bCurOpSamplesSingleRight);   // 是否z1(右侧)扎Tip
    strParam += QString (",%1").arg(!bCurOpSamplesSingleRight);  // 是否z2扎Tip
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());

    m_gantryModule.SlotAddSubTask(GTI_GET_TIP1000,"",strParam);
}

void CAffair::ActionAddSampingTask()
{
    qDebug()<<"-------ActionAddSampingTask---------";
    QString strParam = "";
    quint8 uiSize =1;

    if (m_eInternalStandardStatus == IS_NONE || 
        m_eInternalStandardStatus == IS_FINISH)
    {
        m_pSampleControl->UpdateCurExecStripAndSamplingSTIndex(strParam, uiSize);
    }
    else
    {
        if (m_eInternalStandardStatus == IS_STEP_WAIT)
        {
            m_pSampleControl->UpdateCurExecStripAndSamplingSTIndex(uiSize);// 用于判断isCurOpSamplesSingleRight
        }
        strParam += QString (",%1,%2").arg(0).arg(0);
    }    

    if(uiSize<1)
    {
        uiSize = 1;
    }
    m_gantryModule.SetCatchType(uiSize-1);
    strParam += QString (",%1").arg(CSystemDB::getInstance().getIntValueFromKey("SamplePosition"));
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());

    //如果是加内标，其他步骤不用扎Tip头
    if (m_eInternalStandardStatus == IS_NONE || 
        m_eInternalStandardStatus == IS_FINISH)// 原来的p4参数分开三个参数控制
    {
       strParam += QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight());   // 是否z1(右侧)扎Tip
       strParam += QString (",%1").arg(!m_pSampleControl->isCurOpSamplesSingleRight());  // 是否z2扎Tip
       strParam += QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight());   // 是否z1取样本
    }
    else
    {
        //在添加内标样本由第5个参数控制是否需要扎tip，单个移液泵不用z1扎Tip，两个移液泵不需要z1、z2扎Tip
        strParam += QString (",%1").arg(0);                                              // 第5个参数，不用z1扎Tip
        strParam += QString (",%1").arg(0);                                              // 第6个参数，不用z2扎Tip
        strParam += QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight());  // 第7个参数，是否z1取样本
    }
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag());//第8个参数，空吸/堵针检测
    
    // 样本容量
    qint32 iSampleCapacity =  m_pSampleControl->GetCurExecSampleCapacity();
    strParam += QString (",%1").arg(iSampleCapacity);//第9个参数，样本容量

    if (m_eInternalStandardStatus == IS_STEP_WAIT)
    {
        m_eInternalStandardStatus = IS_STEP_ACTION;
        m_gantryModule.SlotAddSubTask(GTI_GET_STANDARD_TIP200,"",QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight()));
    }

    QTimer::singleShot(50, this, [=]() {
        m_gantryModule.SlotAddSubTask(GTI_SAMPLING,"",strParam);
    });
    qDebug()<<"m_eInternalStandardStatus:"<<m_eInternalStandardStatus;
}

void CAffair::ActionAddSpitSampleTask()
{
    qDebug()<<"-------ActionAddSpitSampleTask---------";
    QString strParam;
    quint8 uiSize = 1;

    // 获取需要添加内标的卡条位置信息
    if (m_eInternalStandardStatus == IS_STEP_ACTION)
    {
        // 获取对应项目信息，根据项目信息获取对应的卡条位置信息
        QString strProjID;
        quint8 uiStripIndex = 0;
        quint8 uiColumnIndex = 0;
        m_pSampleControl->GetNextWaitSampleInfo(strProjID,uiStripIndex,uiColumnIndex);
        strParam += QString (",%1").arg(uiStripIndex); 
        m_pSampleControl->UpdateProjectStripIndex(strProjID,uiStripIndex);   
    }
    else
    {
        m_pSampleControl->GetStripIndexParamStr(SEST_WAIT_SPIT_SAMPLING, strParam,uiSize);
    } 
    
    if(uiSize<1)
    {
        qDebug()<<"........ActionAddSpitSampleTask error.......";
        uiSize = 1;
    }

    m_gantryModule.SetCatchType(uiSize-1);
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
    strParam += QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight());
    m_gantryModule.SlotAddSubTask(GTI_SPIT_SAMPLE, "", strParam);
}

void CAffair::ActionAddTransClevageTask(bool bRightMove)
{
    qDebug()<<"-------ActionAddTransClevageTask---------";
    QString strParam;
    quint8 uiSize = 1;
    m_pSampleControl->GetStripIndexParamStr(SEST_WAIT_TRANS_CLEVAGE, strParam, uiSize);
    if(uiSize<1)
    {
        qDebug()<<"........HandleSpitSampleReply error.......";
        uiSize =1;
    }
    m_gantryModule.SetCatchType(uiSize-1);
    strParam += QString (",%1").arg(CSystemDB::getInstance().getIntValueFromKey("LyseLiquidLevel"));
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
    strParam += QString (",%1").arg(m_pSampleControl->isCurOpSamplesSingleRight());
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag());
    strParam += QString (",%1").arg(bRightMove);// 是否往右移动一个提取条的位置
    m_gantryModule.SlotAddSubTask(GTI_TRANS_CLEVAGE_REAGENT,"",strParam);
}

void CAffair::ActionAddPunchStripTask()
{
    qDebug()<<"-------ActionAddPunchStripTask---------";
    m_bPunchDone = false;
    m_bWaitPunchDone = false;
    m_extractModule.SlotAddSubTask(ETI_PUNCH,"","");
}

void CAffair::ExtractScanTest()
{
#if 0// 2621指令有问题
    // // 开始扫码
    // QList<int> listPosition;
    // CStrip::getInstance().GetMagneticRodSleevePositionAll(listPosition);// 获取提取条光耦信息
    // ActionBeginExtractScanTask(listPosition);    
#else
    CmdTask  task;
    task.bSync=false;
    task.strParamStr="";
    task.strCommandStr = QString::number(Action_StripCarMoveToScanPos);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
#endif
}

void CAffair::ExtractScanTest_use()
{
    m_bZebraScanAging =true ;
    _SetRunST(RST_RUN, __FUNCTION__);
    _SetSeqType(static_cast<SeqType>(ST_SAMPLE_SCAN));
    // 开始扫码
    QList<int> listPosition;
    for (int i = 0; i < STRIP_MAX_SIZE; i++)
    {
        if(i<iTolScanStripNum)
        {
            listPosition.append(1);
        }
        else
        {
            listPosition.append(0);
        }
    }
    ActionBeginExtractScanTask(listPosition);
}


void CAffair::StopExtractScanTest_use()
{
    m_bZebraScanAging =false;
    iTolCryScanCnt =0;
    iTolFainCnt =0;
    iTolNotSame =0;
    StopExtractScan();
    ActionAddStripPutDownPos();
    listScanRsMsg.clear();
    for(int i=0;i<m_vecScanConutDiff.size();i++)
    {
        m_vecScanConutDiff[i]=0;
    }
}

void CAffair::UVLightCtrl(int iType,int iSet) //消毒灯控制     0:提取 1PCR  2:all      iset:0 关 1开
{
    //植工那边定义为：0-all    1---PCR   2---提取    iType 转换
    quint8 quiSync =0;
    int iChangeType;
    if(iType==0) {iChangeType =2;}
    if(iType==1) {iChangeType =1;}
    if(iType==2) {iChangeType =0;}
    QString strInPutParam=QString("%1,%2").arg(iChangeType).arg(iSet);
    COperationUnit::getInstance().sendStringData(Method_PoweCtrl_UV,strInPutParam,Machine_Power_Ctrl,quiSync);
    qDebug()<<__FUNCTION__<<"strInPutParam="<<strInPutParam<<",Send Cmd Time"<<QTime::currentTime();
}

void CAffair::StartDailyMonitor(QString strParam)
{
    QStringList strDataList = strParam.split(",");
    int iType, iPower;
    QString strInPutParam="";
    int iFreq=0;
    if(strDataList.size()!=2)
    {
        return ;
    }
    iType = strDataList[0].toInt();
    QStringList strSubDataList= strDataList[1].split("|");
    if(strSubDataList.size()>2)
    {
        return ;
    }
    iPower = strSubDataList[0].toInt();
    if(strSubDataList.size()==2)   { iFreq= strSubDataList[1].toInt();  }

    quint8 quiSync =0;
    if(iType ==DailyMonitor_FreezeArea)     ////1裂解  2洗脱  3冷藏盖  4冷藏仓10毫秒单位
    {
        strInPutParam=QString("%1,%2").arg(4).arg(iFreq*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetTempReportFreq,strInPutParam,Machine_Function_manager_Ctrl,quiSync);
    };
    if(iType ==DailyMonitor_SplitArea)
    {
        strInPutParam=QString("%1,%2").arg(1).arg(iFreq*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetTempReportFreq,strInPutParam,Machine_Function_manager_Ctrl,quiSync);
    };
    if(iType ==DailyMonitor_ElutionArea)
    {
        strInPutParam=QString("%1,%2").arg(2).arg(iFreq*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetTempReportFreq,strInPutParam,Machine_Function_manager_Ctrl,quiSync);
    };
    if(iType ==DailyMonitor_FanArea)
    {
        COperationUnit::getInstance().sendStringData(Method_PoweCtrl_DisplayFanParam,QString::number(iPower),Machine_Power_Ctrl,quiSync);  //1打开  暂时接口无频率
    };

}

void CAffair::DailyMonitorWarnSet(QString strParam)  //日常监控异常上下限设置
{
    QStringList strDataList = strParam.split(",");
    int iType;
    QString strInPutParam="";
    if(strDataList.size()!=2)
    {
        return ;
    }
    iType = strDataList[0].toInt();
    QStringList strSubDataList= strDataList[1].split("|");
    if(strSubDataList.size()!=2)
    {
        return ;
    }
    double dLowLimit,dHighLimit;
    dLowLimit  = strSubDataList[0].toDouble();
    dHighLimit = strSubDataList[1].toDouble();

    QString strMsg ;
    if(iType ==DailyMonitorWarnSetType_FreezeArea)
    {
        strMsg =QString::number(4)+","+QString::number(dHighLimit*100)+","+QString::number(dLowLimit*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetAlarmTemp,strMsg,Machine_Function_manager_Ctrl);
    };
    if(iType ==DailyMonitorWarnSetType_SplitArea)
    {
        strMsg =QString::number(1)+","+QString::number(dHighLimit*100)+","+QString::number(dLowLimit*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetAlarmTemp,strMsg,Machine_Function_manager_Ctrl);
    };
    if(iType ==DailyMonitorWarnSetType_ElutionArea)
    {
        strMsg =QString::number(2)+","+QString::number(dHighLimit*100)+","+QString::number(dLowLimit*100);
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_SetAlarmTemp,strMsg,Machine_Function_manager_Ctrl);
    };
    if(iType ==DailyMonitorWarnSetType_FanArea)
    {
        double dFullParam = 2500.0;
        dHighLimit =dHighLimit / dFullParam*100;
        dLowLimit = dLowLimit / dFullParam*100;
        strMsg =QString::number(1)+","+QString::number(dHighLimit)+","+QString::number(dLowLimit);
        COperationUnit::getInstance().sendStringData(Method_PoweCtrl_FanMonitorSet,strMsg,Machine_Power_Ctrl);
    };

}

void CAffair::ReportMonitorWarnError(QString strParam)  //上报温度异常
{
    qDebug()<<"@@@@@@@@ReportMonitorWarnError"<<strParam;
    QString strMsg="";
    if(strParam=="01030903") //冷藏过高
    { strMsg=QString("%1,%2").arg(0).arg(1);}
    if(strParam=="01030904") //冷藏过低;
    { strMsg=QString("%1,%2").arg(0).arg(0);}
    if(strParam=="01000903") //裂解过高
    { strMsg=QString("%1,%2").arg(1).arg(1); }
    if(strParam=="01000904") //裂解过低;
    { strMsg=QString("%1,%2").arg(1).arg(0);}
    if(strParam=="01010903")//洗脱过高
    { strMsg=QString("%1,%2").arg(2).arg(1);}
    if(strParam=="01010904")//洗脱过低
    { strMsg=QString("%1,%2").arg(2).arg(0);}
    if(strParam=="061411000")//风扇上限
    {strMsg=QString("%1,%2").arg(3).arg(1);}
    if(strParam=="061411001")//风扇下限
    {strMsg=QString("%1,%2").arg(3).arg(0);}

    if(strMsg !="")
    {
        COperationUnit::getInstance().sendStringData(Method_daily_monitor_warn,strMsg,Machine_UpperHost);
    }
}

void CAffair::StopExtractScan()
{
    m_bStartZebraScan =false;
    CCommunicationObject::getInstance().closeZebraScanner();
}

void CAffair::ActionBeginExtractScanTask(QList<int> ScanStripList)  // 16个 :   1 是需要扫码， 0 是不用
{
    if(ScanStripList.size()!=16)
    {
        qDebug()<<__FUNCTION__<<"size not fit,ScanStripList="<<ScanStripList.size();
        return;
    }
    bool bScanStripTask =false;
    for(int i=0;i<ScanStripList.size();i++)
    {
        if(ScanStripList[i]==1)
        {
            bScanStripTask = true;
            break;
        }
    }
    if(bScanStripTask ==true)
    {
        m_bStartZebraScan =true;//用于停止
        SetExtractScanMode(true);//
        m_extractModule.AddScanCodeStripIdx(ScanStripList);
        qDebug()<<"-------ActionAddStripCarMove";
        CmdTask  task;
        task.bSync=false;
        task.strParamStr="";
        task.strCommandStr = QString::number(Action_StripCarMoveToScanPos);
        int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
        COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
    }
}


void CAffair::SendExtractScanCodeMotorMove(int iStripIdx)   //这个是最原始的部分
{
    //这个iStripIdx 应该在回复的时候 遍历 ExtractModule 中的 ScanCodeInfoList得到
    QString strParam = ","+QString::number(iStripIdx);
    qDebug()<<"-------ActionAddExtractScanCodeTask---------strParam="<<strParam;
    m_extractModule.SetExtractScanStatus(iStripIdx,Status_Scanning);

    CmdTask  task;
    task.bSync=false;
    task.strParamStr=","+QString::number(iStripIdx);
    task.strCommandStr = QString::number(Action_ExtractScanMotorMove);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddStripPutDownPos()
{
    qDebug()<<"-------ActionAddStripPutDownPos---------";
    CmdTask  task;
    task.bSync=false;
    task.strCommandStr = QString::number(Action_StripPutDownPos);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddExtractScanMotorInit()
{
    if(m_bZebraScanAging==true)
    {
        return;
    }
    qDebug()<<"-------ActionAddExtractScanMotorInit---------";
    CmdTask  task;
    task.bSync=false;
    task.strCommandStr = QString::number(Action_ExtractScanMotorInit);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddSampleScanTubeExistTask() 
{
    qDebug()<<"-------ActionAddSampleScanTubeExistTask ";
    CmdTask  task;
    task.bSync=false;
    task.strCommandStr = QString::number(Action_SampleScanTubeExist);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);    
}

void CAffair::ActionAddSampleScanCodeStartTask(quint8 uiSampleIndex) 
{
    qDebug()<<"-------ActionAddSampleScanCodeTask "<<uiSampleIndex;
    CmdTask  task;
    task.bSync=false;
    QString strParam = ","+QString::number(uiSampleIndex)+",0,3";
    task.strParamStr = strParam;
    task.strCommandStr = QString::number(Action_SampleCodeScanStepStart);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    qDebug()<<"-------ActionAddSampleScanCodeTask iMachineID"<<iMachineID;
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}
void CAffair::ActionAddSampleScanCodeRotateTask(bool bLeft,bool bRight,quint8 uiSampleIndex) 
{
    qDebug()<<"-------ActionAddSampleScanCodeRotateTask";
    CmdTask  task;
    task.bSync=false;
    QString strParam = ","+QString::number(bRight)+","+QString::number(bLeft)+","+QString::number(uiSampleIndex);
    task.strParamStr = strParam;
    task.strCommandStr = QString::number(Action_SampleCodeScanRotate);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}
void CAffair::ActionAddSampleScanCodeEndTask(quint8 uiSampleIndex) 
{
    qDebug()<<"-------ActionAddSampleScanCodeEndTask"<<uiSampleIndex;
    CmdTask  task;
    task.bSync=false;
    QString strParam = ","+QString::number(uiSampleIndex)+",0";
    task.strParamStr = strParam;
    task.strCommandStr = QString::number(Action_SampleCodeScanStepEnd);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddSampleScanCodeInitTask()
{
    qDebug()<<"-------ActionAddSampleScanCodeInitTask";
    CmdTask  task;
    task.bSync=false;
    QString strParam = "";
    task.strParamStr = strParam;
    task.strCommandStr = QString::number(Action_SampleCodeScanInit);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddSampleScanCodeTubeCheck(quint16 uiType)
{
    qDebug()<<"-------ActionAddSampleScanCodeTubeCheck";
    CmdTask  task;
    task.bSync=false;
    QString strParam = "";
    task.strParamStr = strParam;
    task.strCommandStr = QString::number(uiType);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
}

void CAffair::ActionAddExtractTask()
{
    SendPeroidStateToUpperHost(PEST_SAMPLE_PROCESS_END,SEST_WAIT_EXTRACT);//
    qDebug()<<"-------ActionAddExtractTask---------";   
    m_extractModule.SetState(false);// 防止单独执行提取模块引起异常
    //获取提取时序,转换并下发
    QString strLastStepParams = "";
    QStringList strParamList;
    QString strExtractProgram = m_pSampleControl->getCurBatchExtractProgram();
    QString strExtractUIContent = CTimingDB::getInstance().getExtractTimingUIContentFromName(strExtractProgram);
    CExtractParse::getInstance().getExtractMotorContentFromUIContent(strExtractUIContent,strParamList,strLastStepParams);
    qDebug() << "ActionAddExtractTask Extract"<<strExtractProgram<<strExtractUIContent<<strLastStepParams;

    m_extractModule.SlotAddSubTask(ETI_EXTRACT_START,"","");
    for(int i=0;i<strParamList.size();i++)
    {
        m_extractModule.SlotAddSubTask(ETI_EXTRACT_RUN,"",strParamList[i]);
    }
    m_extractModule.SlotAddSubTask(ETI_EXTRACT_END,"",strLastStepParams);  

    SendPeroidStateToUpperHost(PEST_EXTRACT_START,SEST_WAIT_EXTRACT);
}

void CAffair::ActionAddStandardAbandonTip200Task()
{
    qDebug()<<"-------ActionAddStandardAbandonTip200Task---------";
    m_gantryModule.SlotAddSubTask(GTI_ABANDON_STANDARD_TIP200,"","");    
}

void CAffair::ActionAddSubpackReagentTask()
{
    qDebug()<<"-------ActionAddSubpackReagentTask---------";
    //确认所有待做样本所需扩增试剂是否需要分装，如果需要则添加分装任务
    //根据不同的组分将需要进行分装的试剂进行分类，每个类别一个Tip枪头操作

    QString strProjName = "";
    quint8 uiSize = 0;
    bool bExist = m_pSampleControl->DequeueNextSystemBuildReagentInfo(strProjName, uiSize);
    QVector<SubPackData> subPackVect = Reagent::getInstance().GetReagentBallSubPackDatasVect();
    QMap<QString, QMap<quint8, QVector<SubPackData>>> subPackMap = Reagent::getInstance().GetReagentBallSubPackDatas();
    if(bExist && subPackVect.size() > 0 && subPackMap.size() > 0)
    {
        bool bEnough = Reagent::getInstance().IsReagentEnough(strProjName, uiSize);
        if(!bEnough)
        {
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Empty, QString("Reagent %1 size is not enough.").arg(strProjName));
            qDebug()<<"Reagent:"<<strProjName<< "use "<< uiSize<<" aren't enough";
        }
        m_bReconstitutionWait = true;
        m_gantryModule.SetCatchType(CT_SINGLE);
        m_gantryModule.SlotAddSubTask(GTI_PUNCH_AND_SUB_PACK_REAGENT,"","");
        qDebug()<<"-------ActionAddSubpackReagentTask reagentName:"<< strProjName<<" size:"<<uiSize;
    }
    else
    {
        // 复溶完成后，开始转移试剂
        m_pSampleControl->RearraySampleInfoToSystemBuildInfo();//該批次提取部分信息重排並轉換到下一個狀態
        m_pSampleControl->ClearSampleSTMap();

        m_strCurProjID = "";
        m_iCurAmplifyCompIndex = -1;
        m_iSystemBuildInfoSize = 0;
        m_bGetTip = false;

        int waitTime = CSystemDB::getInstance().getIntValueFromKey("Reconstitution_WaitTime_S");
        if (waitTime > 0 && m_bReconstitutionWait)
        {
            qDebug()<<"<<<<<<---------ActionAddSubpackReagentTask 2------->>>>>>" << waitTime << m_bReconstitutionWait;
            QTimer::singleShot(waitTime * 1000, this, [this]() {
                if (m_bReconstitutionWait)
                {
                    m_bReconstitutionWait = false;
                    ActionAddTransReagentTask();
                }
            });
        }
        else
        {
            qDebug()<<"<<<<<<---------ActionAddSubpackReagentTask 3------->>>>>>" << waitTime << m_bReconstitutionWait;
            ActionAddTransReagentTask();
        }
    }
}

void CAffair::ActionAddTransReagentTask()
{
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_TRANS_REAGENT, qVect);
    m_pSampleControl->ShowSystemBuildSTMap();
    QString strParam = "";
    const quint8 kCapacity = 100;//容量系数
    qDebug()<<"-------ActionAddTransReagentTask size:"<<qVect.size();
    if(bGet && qVect.size()>0)//存在待转移样本
    {
        // if (m_bGetTip && (m_strCurProjID != qVect[0].strProjID || m_iCurAmplifyCompIndex != qVect[0].uiAmplifyCompIndex || m_iSystemBuildInfoSize != qVect.size()))
        // {
                // // 丢弃Tip200参数
                // QString strParamAbandonTip = QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());
        //     m_gantryModule.SlotAddSubTask(GTI_ABANDON_TIP200, "", "");
        //     m_bGetTip = false;
        // }
        m_strCurProjID = qVect[0].strProjID;
        m_iCurAmplifyCompIndex = qVect[0].uiAmplifyCompIndex;
        m_iSystemBuildInfoSize = qVect.size();
        if(qVect.size()==1)//单次转移一个试剂
        {
            SystemBuildInfo info = qVect.at(0);

            quint8 uiTipRowIndex = 0;
            quint8 uiTipColumnIndex = 0;
            m_gantryModule.SetCatchType(CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_TIP, CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_TUBE, CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_CAP, CT_SINGLE);
            
            Consumables::getInstance().GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
            quint8 uiTipAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TIP);
            //轉移試劑並更新樣本對應的PCR管位置信息
            quint8 uiReagentRowIndex = 0;
            quint8 uiReagentColumnIndex = 0;
            Reagent::getInstance().GetNextAvrReagent(info.strProjID, uiReagentRowIndex, uiReagentColumnIndex,info.uiAmplifyCompIndex);
            // uiReagentRowIndex += info.uiAmplifyCompIndex;
            Reagent::getInstance().ConsumeReagent(uiReagentColumnIndex,1);

            quint8 uiTubeRowIndex = 0, uiCapRowIndex = 0;
            quint8 uiTubeColumnIndex = 0, uiCapColumnIndex = 0;
            bool bRet = Consumables::getInstance().GetNextAvrConsumable(CT_TUBE, uiTubeRowIndex, uiTubeColumnIndex);
            qDebug()<<"GetNextAvrConsumable CT_TUBE"<<bRet;
            bRet = Consumables::getInstance().GetNextAvrConsumable(CT_CAP, uiCapRowIndex, uiCapColumnIndex);
            qDebug()<<"GetNextAvrConsumable CT_CAP"<<bRet;
            quint8 uiTubeAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TUBE);
            qint32 iExtractCapacity    = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::extractCalibrationCapacity)*kCapacity;
            qint32 iPcrReagentCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::pcrReagentCalibrationCapacity)*kCapacity;

            QString strParamTip = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(1).arg(uiTipAreaIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex)
                                .arg(0)
                                .arg(0).arg(0).arg(0).arg(0);
            // if (!m_bGetTip)
            {
                m_gantryModule.SlotAddSubTask(GTI_GET_CROSS_TIP200, "", strParamTip);
                Consumables::getInstance().Consume(CT_TIP);
                m_bGetTip = true;
            }

            QString strParamSuck = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(uiReagentColumnIndex).arg(uiReagentRowIndex)
                                .arg(CSystemDB::getInstance().getIntValueFromKey("MixingReagentPosition"))
                                .arg(iPcrReagentCapacity).arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag())
                                .arg(0).arg(0).arg(0).arg(0);
            m_gantryModule.SlotAddSubTask(GTI_SUCK_REAGENT, "", strParamSuck);

            QString strParamSpit = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(1).arg(uiTubeAreaIndex).arg(uiTubeColumnIndex).arg(uiTubeRowIndex)
                                .arg(0)
                                .arg(0).arg(0).arg(0).arg(0);
            m_gantryModule.SlotAddSubTask(GTI_SPIT_REAGENT, "", strParamSpit);
            Consumables::getInstance().Consume(CT_TUBE);
            Consumables::getInstance().Consume(CT_CAP);

            // 丢弃Tip200参数
            QString strParamAbandonTip = QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());
            m_gantryModule.SlotAddSubTask(GTI_ABANDON_TIP200, "", strParamAbandonTip);  // 丢弃TIP

            info.tubePos = {uiTubeRowIndex,uiTubeColumnIndex,uiTubeAreaIndex};
            info.tubeCapPos = {uiTubeRowIndex, uiTubeColumnIndex, uiTubeAreaIndex};
            info.reagentPos ={uiReagentRowIndex,uiReagentColumnIndex,0};
            info.tipPos = {uiTipRowIndex,uiTipColumnIndex,uiTipAreaIndex};
            qVect[0] = info;
            QDFUN_LINE << qVect[0].printInfo();
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_TRANS_REAGENT, qVect);

            qDebug()<<"ActionAddTransReagentTask reagent single:"<<info.strSampleID<<info.tubePos.uiAreaIndex<<info.tubePos.uiRowIndex<<info.tubePos.uiColumnIndex<<info.uiAmplifyCompIndex;
        }
        else if(qVect.size()>1)//单次转移两个试剂
        {
            quint8 uiTipRowIndex = 0;
            quint8 uiTipColumnIndex = 0;
            quint8 uiTipAreaIndex = 0;
            quint8 uiTipRowIndex1 = 0;
            quint8 uiTipColumnIndex1 = 0;
            quint8 uiTipAreaIndex1 = 0;

            m_gantryModule.SetCatchType(CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_TIP, CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_TUBE, CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_CAP, CT_DOUBLE);

            bool bCrossTip = Consumables::getInstance().GetNextCrossConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex, uiTipAreaIndex
                , uiTipRowIndex1, uiTipColumnIndex1, uiTipAreaIndex1);
            if (!bCrossTip)
            {
                Consumables::getInstance().GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
                uiTipAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TIP);
            }
            
            //轉移試劑並更新樣本對應的PCR管位置信息
            SystemBuildInfo info = qVect.at(0);
            SubPackData data;
            data.strProjID = info.strProjID;
            data.uiAmplifyCompIndex = info.uiAmplifyCompIndex;

            SystemBuildInfo info1 = qVect.at(1);
            SubPackData data1;
            data1.strProjID = info1.strProjID;
            data1.uiAmplifyCompIndex = info1.uiAmplifyCompIndex;            

            if (!Reagent::getInstance().GetNextAvrReagent(data,data1))
            {
                CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Empty, QString("reagentbox %1 is empty").arg(data.strProjID));
                QDFUN_LINE << "Error GetNextAvrReagent false";
            }
            qDebug()<<"info.strProjID "<<info.strProjID <<data.uiRowIndex <<data.uiColumnIndex <<info.strSampleID;
            qDebug()<<"info1.strProjID"<<info1.strProjID<<data1.uiRowIndex<<data1.uiColumnIndex<<info1.strSampleID;

            Reagent::getInstance().ConsumeReagent(data.uiColumnIndex,1);
            Reagent::getInstance().ConsumeReagent(data1.uiColumnIndex,1);

            quint8 uiTubeRowIndex = 0;
            quint8 uiTubeColumnIndex = 0;
            quint8 uiTubeAreaIndex = 0;
            quint8 uiCapRowIndex = 0;
            quint8 uiCapColumnIndex = 0;
            quint8 uiCapAreaIndex = 0;
            quint8 uiTubeRowIndex1 = 0;
            quint8 uiTubeColumnIndex1 = 0;
            quint8 uiTubeAreaIndex1 = 0;
            quint8 uiCapRowIndex1 = 0;
            quint8 uiCapColumnIndex1 = 0;
            quint8 uiCapAreaIndex1 = 0;
            bool bCrossTube = Consumables::getInstance().GetNextCrossConsumable(CT_TUBE, uiTubeRowIndex, uiTubeColumnIndex, uiTubeAreaIndex
                , uiTubeRowIndex1, uiTubeColumnIndex1, uiTubeAreaIndex1);
            if (!bCrossTube)
            {
                Consumables::getInstance().GetNextAvrConsumable(CT_TUBE, uiTubeRowIndex, uiTubeColumnIndex);
                uiTubeAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TUBE);
            }
            qDebug() << "GetNextAvrConsumable CT_TUBE" << uiTubeRowIndex << uiTubeColumnIndex << uiTubeAreaIndex;

            bool bCrossCap = Consumables::getInstance().GetNextCrossConsumable(CT_CAP, uiCapRowIndex, uiCapColumnIndex, uiCapAreaIndex
                , uiCapRowIndex1, uiCapColumnIndex1, uiCapAreaIndex1);
            if (!bCrossCap)
            {
                Consumables::getInstance().GetNextAvrConsumable(CT_CAP, uiCapRowIndex, uiCapColumnIndex);
                uiCapAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_CAP);
            }
            qDebug() << "GetNextAvrConsumable CT_CAP" << uiCapRowIndex << uiCapColumnIndex << uiCapAreaIndex;
            
            qint32 iExtractCapacity    = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::extractCalibrationCapacity)*kCapacity;
            qint32 iPcrReagentCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::pcrReagentCalibrationCapacity)*kCapacity;
            qint32 iPcrReagentCapacity1 = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info1.strProjID,CProjectDBImpl::FieldName_ProjectInfo::pcrReagentCalibrationCapacity)*kCapacity;

            QString strParamTip = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(bCrossTip).arg(uiTipAreaIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex)
                                .arg(!bCrossTip)
                                .arg(bCrossTip).arg(uiTipAreaIndex1).arg(uiTipColumnIndex1).arg(uiTipRowIndex1);
            // if (!m_bGetTip)
            {
                m_gantryModule.SlotAddSubTask(GTI_GET_CROSS_TIP200, "", strParamTip);
                Consumables::getInstance().Consume(CT_TIP);
                m_bGetTip = true;
            }

            QString strParamSuck = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(data.uiColumnIndex).arg(data.uiRowIndex)
                                .arg(CSystemDB::getInstance().getIntValueFromKey("MixingReagentPosition"))
                                .arg(iPcrReagentCapacity).arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag())
                                .arg(1).arg(data1.uiColumnIndex).arg(data1.uiRowIndex).arg(iPcrReagentCapacity1);
            m_gantryModule.SlotAddSubTask(GTI_SUCK_REAGENT, "", strParamSuck);

            QString strParamSpit = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(bCrossTube).arg(uiTubeAreaIndex).arg(uiTubeColumnIndex).arg(uiTubeRowIndex)
                                .arg(!bCrossTube)
                                .arg(bCrossTube).arg(uiTubeAreaIndex1).arg(uiTubeColumnIndex1).arg(uiTubeRowIndex1);
            m_gantryModule.SlotAddSubTask(GTI_SPIT_REAGENT, "", strParamSpit);
            Consumables::getInstance().Consume(CT_TUBE);
            Consumables::getInstance().Consume(CT_CAP);

            // 丢弃Tip200参数
            QString strParamAbandonTip = QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());
            m_gantryModule.SlotAddSubTask(GTI_ABANDON_TIP200, "", strParamAbandonTip);  // 丢弃TIP

            info.tubePos = {uiTubeRowIndex,uiTubeColumnIndex,uiTubeAreaIndex};
            info.tubeCapPos = {uiCapRowIndex, uiCapColumnIndex, uiTubeAreaIndex};
            info.reagentPos ={data.uiRowIndex,data.uiColumnIndex,0};
            info.tipPos = {uiTipRowIndex,uiTipColumnIndex,uiTipAreaIndex};
            qVect[0] = info;
            if (!bCrossTube)
            {
                info1.tubePos = {uiTubeRowIndex,++uiTubeColumnIndex,uiTubeAreaIndex};
                info1.tubeCapPos = {uiCapRowIndex, ++uiCapColumnIndex, uiCapAreaIndex};
                info1.reagentPos ={data1.uiRowIndex,data1.uiColumnIndex,0};
                info1.tipPos = {uiTipRowIndex,++uiTipColumnIndex,uiTipAreaIndex};
                qVect[1] = info1;
            }
            else
            {
                info1.tubePos = {uiTubeRowIndex1,uiTubeColumnIndex1,uiTubeAreaIndex1};
                info1.tubeCapPos = {uiCapRowIndex1, uiCapColumnIndex1, uiCapAreaIndex1};
                info1.reagentPos ={data1.uiRowIndex,data1.uiColumnIndex,0};
                info1.tipPos = {uiTipRowIndex1,uiTipColumnIndex1,uiTipAreaIndex1};
                qVect[1] = info1;
            }
            QDFUN_LINE << qVect[0].printInfo();
            QDFUN_LINE << qVect[1].printInfo();
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_TRANS_REAGENT, qVect);

            // qDebug()<<"ActionAddTransReagentTask reagent double1:"<<info.strSampleID<<uiTubeAreaIndex<<uiTubeRowIndex<<uiTubeColumnIndex<<info.uiAmplifyCompIndex;
            // qDebug()<<"ActionAddTransReagentTask reagent double2:"<<info1.strSampleID<<uiTubeAreaIndex1<<uiTubeRowIndex1<<uiTubeColumnIndex1<<info1.uiAmplifyCompIndex;
        }
    }
    else //无待转移样本，该批次做完转移
    {
        // // 丢弃Tip200参数
        // QString strParamAbandonTip = QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());
        // m_gantryModule.SlotAddSubTask(GTI_ABANDON_TIP200, "", "");
        m_bTransReagentFinishToWaitExtract = true;
        _TransPurify();
    }
}

void CAffair::ActionAddTransPurifyTask()
{
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_TRANS_PURIFY, qVect);
    m_pSampleControl->ShowSystemBuildSTMap();
    QString strParam = "";
    const quint8 kCapacity = 100;//容量系数
    qDebug()<<"-------ActionAddTransPurifyTask size:"<<qVect.size();
    if(bGet && qVect.size()>0)//存在待转移样本
    {
        if(qVect.size()==1)//单次转移一个提存液
        {
            SystemBuildInfo info = qVect.at(0);

            quint8 uiTipRowIndex = 0;
            quint8 uiTipColumnIndex = 0;
            m_gantryModule.SetCatchType(CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_TIP, CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_TUBE, CT_SINGLE);
            Consumables::getInstance().SetCatchType(CT_CAP, CT_SINGLE);
            Consumables::getInstance().GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
            quint8 uiTipAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TIP);

            //轉移試劑並更新樣本對應的PCR管位置信息
            quint8 uiReagentRowIndex = info.reagentPos.uiRowIndex;
            quint8 uiReagentColumnIndex = info.reagentPos.uiColumnIndex;

            quint8 uiTubeRowIndex = info.tubePos.uiRowIndex, uiCapRowIndex = info.tubeCapPos.uiRowIndex;
            quint8 uiTubeColumnIndex = info.tubePos.uiColumnIndex, uiCapColumnIndex = info.tubeCapPos.uiColumnIndex;
            quint8 uiTubeAreaIndex = info.tubePos.uiAreaIndex;
            qint32 iExtractCapacity    = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::extractCalibrationCapacity)*kCapacity;
            qint32 iPcrReagentCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::pcrReagentCalibrationCapacity)*kCapacity;
#ifdef M1
            //轉移試劑需要7個參數，試劑列,试剂行位置，PCR管列，PCR行位置，卡条位置, TIP列，Tip行位置信息
            strParam = QString(",%1,%2,%3,%4,%5,%6,%7").arg(uiReagentColumnIndex).arg(uiReagentRowIndex).
                    arg(uiTubeColumnIndex). arg(uiTubeRowIndex).arg(info.uiStripIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex);
#else
            //轉移試劑需要9個參數: 試劑列,试剂行位置,PCR管区域, PCR管列，PCR行位置，卡条位置, TIP区域, TIP列，Tip行位置信息
            strParam = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9,%10").arg(uiReagentColumnIndex).arg(uiReagentRowIndex)
                    .arg(uiTubeAreaIndex).arg(uiTubeColumnIndex). arg(uiTubeRowIndex).arg(info.uiStripIndex)
                    .arg(uiTipAreaIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex)
                    .arg(CSystemDB::getInstance().getIntValueFromKey("PurificationLiquidLevel"));
            strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
            strParam += QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());//添加耗材丢弃位参数
            strParam += QString (",%1").arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag());
            strParam += QString (",%1,%2").arg(iExtractCapacity).arg(iPcrReagentCapacity);//参数1-纯化物量(提取完成后的提纯液) 参数2-扩增容量(对应项目的试剂) 13 14
            strParam += QString (",%1").arg(CSystemDB::getInstance().getIntValueFromKey("ParaffinOilLevel"));
#endif
            qDebug()<<"GetNextAvrConsumable CT_SINGLE"<<strParam<<info.strProjID<<iExtractCapacity<<iPcrReagentCapacity<<info.uiAmplifyCompIndex;
            QString strParamTip = QString(",%1,%2,%3,%4,%5,%6").arg(uiTipAreaIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex)
                    .arg(CGlobalConfig::getInstance().getPumpDetectFlag()).arg(1).arg(0);
            qDebug() << "strParamTip" << strParamTip;
            m_gantryModule.SlotAddSubTask(GTI_GET_TIP200, "", strParamTip);
            Consumables::getInstance().Consume(CT_TIP);
            m_gantryModule.SlotAddSubTask(GTI_TRANS_PURIFY, "", strParam );

            qDebug()<<"ActionAddTransPurifyTask purify single:"<<info.strSampleID<<info.tubePos.uiAreaIndex<<info.tubePos.uiRowIndex<<info.tubePos.uiColumnIndex<<info.uiAmplifyCompIndex;
        }
        else if(qVect.size()>1)//单次转移两个提存液
        {
            quint8 uiTipRowIndex = 0;
            quint8 uiTipColumnIndex = 0;
            quint8 uiTipAreaIndex = 0;
            quint8 uiTipRowIndex1 = 0;
            quint8 uiTipColumnIndex1 = 0;
            quint8 uiTipAreaIndex1 = 0;

            m_gantryModule.SetCatchType(CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_TIP, CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_TUBE, CT_DOUBLE);
            Consumables::getInstance().SetCatchType(CT_CAP, CT_DOUBLE);

            bool bCrossTip = Consumables::getInstance().GetNextCrossConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex, uiTipAreaIndex
                , uiTipRowIndex1, uiTipColumnIndex1, uiTipAreaIndex1);
            if (!bCrossTip)
            {
                Consumables::getInstance().GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
                uiTipAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TIP);
            }

            SystemBuildInfo info = qVect.at(0);
            SystemBuildInfo info1 = qVect.at(1);

            quint8 uiTubeRowIndex = info.tubePos.uiRowIndex;
            quint8 uiTubeColumnIndex = info.tubePos.uiColumnIndex;
            quint8 uiTubeAreaIndex = info.tubePos.uiAreaIndex;
            quint8 uiCapRowIndex = info.tubeCapPos.uiRowIndex;
            quint8 uiCapColumnIndex = info.tubeCapPos.uiColumnIndex;
            quint8 uiCapAreaIndex = info.tubePos.uiAreaIndex;

            quint8 uiTubeRowIndex1 = info1.tubePos.uiRowIndex;
            quint8 uiTubeColumnIndex1 = info1.tubePos.uiColumnIndex;
            quint8 uiTubeAreaIndex1 = info1.tubePos.uiAreaIndex;
            quint8 uiCapRowIndex1 = info1.tubeCapPos.uiRowIndex;
            quint8 uiCapColumnIndex1 = info1.tubeCapPos.uiColumnIndex;
            quint8 uiCapAreaIndex1 = info1.tubePos.uiAreaIndex;

            bool bCrossTube = true;
            if (uiTubeAreaIndex == uiTubeAreaIndex1 && uiTubeRowIndex == uiTubeRowIndex1 && (uiTubeColumnIndex + 1) == uiTubeColumnIndex1)
            {
                bCrossTube = false;
            }
            
            bool bCrossCap = Consumables::getInstance().GetNextCrossConsumable(CT_CAP, uiCapRowIndex, uiCapColumnIndex, uiCapAreaIndex
                , uiCapRowIndex1, uiCapColumnIndex1, uiCapAreaIndex1);
            if (!bCrossCap)
            {
                Consumables::getInstance().GetNextAvrConsumable(CT_CAP, uiCapRowIndex, uiCapColumnIndex);
                uiCapAreaIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_CAP);
            }
            qDebug() << "GetNextAvrConsumable CT_CAP" << uiCapRowIndex << uiCapColumnIndex << uiCapAreaIndex;

            qint32 iExtractCapacity    = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::extractCalibrationCapacity)*kCapacity;
            qint32 iPcrReagentCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(info.strProjID,CProjectDBImpl::FieldName_ProjectInfo::pcrReagentCalibrationCapacity)*kCapacity;

            qDebug()<<"GetNextAvrConsumable CT_DOUBLE"<<strParam<<info.strProjID<<iExtractCapacity<<iPcrReagentCapacity;
            
            QString strParamTip = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(bCrossTip).arg(uiTipAreaIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex)
                                .arg(!bCrossTip)
                                .arg(bCrossTip).arg(uiTipAreaIndex1).arg(uiTipColumnIndex1).arg(uiTipRowIndex1);
            m_gantryModule.SlotAddSubTask(GTI_GET_CROSS_TIP200, "", strParamTip);
            Consumables::getInstance().Consume(CT_TIP);
            
            qDebug() << "info.uiStripIndex" << info.uiStripIndex << "info1.uiStripIndex" << info1.uiStripIndex;
            QString strParamSuck = QString(",%1,%2,%3,%4,%5,%6").arg(info1.uiStripIndex).arg(6).arg(2).arg(20000)
                                .arg(CSystemDB::getInstance().getIntValueFromKey("PurificationLiquidLevel")).arg(iExtractCapacity);
            m_gantryModule.SlotAddSubTask(GTI_SUCK_PURIFY_OIL, "", strParamSuck);

            QString strParamSpit = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(bCrossTube).arg(uiTubeAreaIndex).arg(uiTubeColumnIndex).arg(uiTubeRowIndex)
                                .arg(!bCrossTube)
                                .arg(bCrossTube).arg(uiTubeAreaIndex1).arg(uiTubeColumnIndex1).arg(uiTubeRowIndex1);
            m_gantryModule.SlotAddSubTask(GTI_SPIT_PURIFY, "", strParamSpit);

            QString strParamSuck2 = QString(",%1,%2,%3,%4,%5,%6").arg(info1.uiStripIndex).arg(1).arg(3).arg(10000)
                                .arg(CSystemDB::getInstance().getIntValueFromKey("ParaffinOilLevel")).arg(4000);

            // 丢弃Tip200参数
            QString strParamAbandonTip = QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());

            m_gantryModule.SlotAddSubTask(GTI_SUCK_PURIFY_OIL, "", strParamSuck2);

            m_gantryModule.SlotAddSubTask(GTI_SPIT_OIL, "", strParamSpit);

            m_gantryModule.SlotAddSubTask(GTI_ABANDON_TIP200, "", strParamAbandonTip);

            qDebug()<<"ActionAddTransPurifyTask purify double1:"<<info.strSampleID<<info.tubePos.uiAreaIndex<<info.tubePos.uiRowIndex<<info.tubePos.uiColumnIndex<<info.uiAmplifyCompIndex;
            qDebug()<<"ActionAddTransPurifyTask purify double2:"<<info1.strSampleID<<info1.tubePos.uiAreaIndex<<info1.tubePos.uiRowIndex<<info1.tubePos.uiColumnIndex<<info1.uiAmplifyCompIndex;
        }
    }
    else //无待转移样本，该批次做完转移
    {
        //需要判断pcr区域是否空闲，根据当前样本数量
        if(m_pPCRResource->IsPCRResourceEnough(m_pSampleControl->GetCurBatchSystemBuildSize()) && 
          !m_pcrCatchModule.GetIsBusy())
        {
            m_pcrCatchModule.SetIsBusy(true);//开始转移pcr管，pcr抓手就认为进去繁忙状态
            _calcArraySystemBuildInfo();// pcr区域充足，可以转移pcr管，但是pcr抓手在繁忙状态
            //添加盖PCR管业务
            ActionAddCapAndTransTubeTask();
        }     
        // 卡盒复位和解锁
        ActionAddStripPutDownPos();//复位
    }
}

void CAffair::ActionAddAmplTransTubeTask()
{
    qDebug()<<"-------ActionAddAmplTransTubeTask---------";

    SampleAmplificate::AmplificateSampleInfo asInfo;
    SampleAmplificate& sampleAmplificate = m_pSampleControl->m_sampleAmplificate;
    if(sampleAmplificate.GetAmplificateSampleInfoSize()>0)// 还没测试的样本数量
    {
        sampleAmplificate.GetNextAmplificateSampleInfo(asInfo);

        //更新PCR信息
        QVector<SystemBuildInfo> qVect;
        SystemBuildInfo info;
        PosInfo pcrAreaPos = {};
        PosInfo pcrSubAreaPos = {};    
        pcrAreaPos.uiRowIndex = asInfo.uiPcrAreaPosRowIndex;
        pcrAreaPos.uiColumnIndex = asInfo.uiPcrAreaPosColumnIndex;
        pcrAreaPos.uiAreaIndex = 0;
    
        pcrSubAreaPos.uiRowIndex =  asInfo.uiPcrPosRowIndex;   
        pcrSubAreaPos.uiColumnIndex =  asInfo.uiPcrPosColumnIndex;
        pcrSubAreaPos.uiAreaIndex = 0;
    
        info.pcrPos = pcrSubAreaPos;
        info.pcrAreaPos = pcrAreaPos;
        info.bNeedOpenCap = asInfo.bNeedOpenCap;//判断开盖
        info.bNeedCloseCap = asInfo.bNeedCloseCap;//判断关盖
        info.bLastOneInCurTecBatch = asInfo.bLastOneInCurTecBatch;// 需要计算是否是当前Tec的最后一个样本 
        info.strTecName = sampleAmplificate.GetTecName();
        info.strSampleID = asInfo.strSampleID;
        info.strProjID = asInfo.strProjectID;
        // FIXME组分需要变更
        info.uiAmplifyCompIndex = 0;//组分

        qVect.push_back(info);
        qDebug()<<"Add TransTube param:"<<info.bNeedOpenCap<<info.bNeedCloseCap<<info.bLastOneInCurTecBatch<<info.strTecName
                                        <<pcrSubAreaPos.uiRowIndex<<pcrSubAreaPos.uiColumnIndex
                                        <<info.strSampleID<<info.strProjID<<info.uiAmplifyCompIndex;
        
        QString strParam = "";
        //3个参数：PCR管区域,PCR管X位置,PCR管Y位置
        strParam = QString(",%1,%2,%3").arg(asInfo.uiTubeAreaIndex).arg(asInfo.uiTubeColumnIndex).arg(asInfo.uiTubeRowIndex);
        qDebug()<<"Add TransTube Task:"<<strParam;
        strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
        m_gantryModule.SetCatchType(qVect.size()-1);
        m_gantryModule.SlotAddSubTask(GTI_TRANS_TUBE,"",strParam);
    

        bool bResult = false;
        m_pSampleControl->AddSystemBuildToNextState(qVect,SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE,bResult,true);
        sampleAmplificate.UpdateCurAmplificateSampleInfo(asInfo);
    }
    else //蓋PCR擴增管帽及轉移已做完
    {
        SendPeroidStateToUpperHost(PEST_SYSTEM_BUILD_END,SEST_WAIT_PCR_MIX);
        ActionAddMotorBoard2InitTask();//前面的提取體系構建處理完成相應模塊可恢復初始狀態
    }
}

void CAffair::ActionAddCapAndTransTubeTask()
{
    qDebug()<<"-------ActionAddCapAndTransTubeTask---------";
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE, qVect);
    if(bGet && qVect.size()>0)
    {
//        PosInfo pcrAreaPos = {};
//        PosInfo pcrSubAreaPos = {};
//        bool bNeedOpenCap = false;
        SystemBuildInfo info = qVect.at(0);
#if !TEST_DISABLE_PCR_HOLE
        GetPCRResource(qVect.size(), pcrAreaPos, pcrSubAreaPos, bNeedOpenCap, info.strTecName);
#endif        
        // 转移两个样本先判断孔位是否连续，不连续，可以分开两次转移，转移完成才更新为下一个状态(拆成两个单管任务)
        // 连续两个样本，但是孔位不连续，需要改成一个转移(单个样本使用左移液泵)
        if(qVect.size()==1)//一次盖一个PCR帽
        {
            QString strParam = "";

            //6个参数：PCR管帽区域,管帽X位置,管帽Y位置,PCR管区域,PCR管X位置,PCR管Y位置
            strParam = QString(",%1,%2,%3,%4,%5,%6").
                    arg(info.tubeCapPos.uiAreaIndex).arg(info.tubeCapPos.uiColumnIndex).arg(info.tubeCapPos.uiRowIndex).
                    arg(info.tubePos.uiAreaIndex).arg(info.tubePos.uiColumnIndex).arg(info.tubePos.uiRowIndex);
            qDebug()<<"Add Cap and TransTube Task:"<<strParam;
            strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
            m_gantryModule.SetCatchType(qVect.size()-1);
            m_gantryModule.SlotAddSubTask(GTI_CAP_AND_TRANS_TUBE,"",strParam);

#if !TEST_DISABLE_PCR_HOLE
            //更新PCR盖信息
            info.pcrPos = pcrSubAreaPos;
            info.pcrAreaPos = pcrAreaPos;
            info.bNeedOpenCap = bNeedOpenCap;
#endif
            qVect[0] = info;
            QDFUN_LINE << qVect[0].printInfo();
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE, qVect);
            qDebug()<<"ActionAddCapAndTransTubeTask captube single:"<<info.strSampleID<<info.tubeCapPos.uiAreaIndex<<info.tubeCapPos.uiRowIndex<<info.tubeCapPos.uiColumnIndex<<info.uiAmplifyCompIndex;
        }
        else //單次蓋兩個PCR帽
        {
            qDebug()<<"Cap two PCR tube per action.";
            QString strParam = "";
            SystemBuildInfo info1 = qVect.at(1);

            quint8 uiTubeRowIndex = info.tubePos.uiRowIndex;
            quint8 uiTubeColumnIndex = info.tubePos.uiColumnIndex;
            quint8 uiTubeAreaIndex = info.tubePos.uiAreaIndex;
            quint8 uiCapRowIndex = info.tubeCapPos.uiRowIndex;
            quint8 uiCapColumnIndex = info.tubeCapPos.uiColumnIndex;
            quint8 uiCapAreaIndex = info.tubePos.uiAreaIndex;

            quint8 uiTubeRowIndex1 = info1.tubePos.uiRowIndex;
            quint8 uiTubeColumnIndex1 = info1.tubePos.uiColumnIndex;
            quint8 uiTubeAreaIndex1 = info1.tubePos.uiAreaIndex;
            quint8 uiCapRowIndex1 = info1.tubeCapPos.uiRowIndex;
            quint8 uiCapColumnIndex1 = info1.tubeCapPos.uiColumnIndex;
            quint8 uiCapAreaIndex1 = info1.tubePos.uiAreaIndex;

            bool bCrossCap = true;
            if (uiCapAreaIndex == uiCapAreaIndex1 && uiCapRowIndex == uiCapRowIndex1 && (uiCapColumnIndex + 1) == uiCapColumnIndex1)
            {
                bCrossCap = false;
            }
            qDebug() << "GetNextAvrConsumable CT_CAP" << uiCapRowIndex << uiCapColumnIndex << uiCapAreaIndex;

            //10个参数：PCR管帽1区域,管帽1X位置,管帽1Y位置,PCR管1区域,PCR管1X位置,PCR管1Y位置
            strParam = QString(",%1,%2,%3,%4,%5,%6").
                    arg(info.tubeCapPos.uiAreaIndex).arg(info.tubeCapPos.uiColumnIndex).arg(info.tubeCapPos.uiRowIndex).
                    arg(info1.tubeCapPos.uiAreaIndex).arg(info1.tubeCapPos.uiColumnIndex).arg(info1.tubeCapPos.uiRowIndex);
            strParam += QString(",%1,%2,%3,%4,%5,%6").
                    arg(info.tubePos.uiAreaIndex).arg(info.tubePos.uiColumnIndex).arg(info.tubePos.uiRowIndex).
                    arg(info1.tubePos.uiAreaIndex).arg(info1.tubePos.uiColumnIndex).arg(info1.tubePos.uiRowIndex);

            strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
            qDebug()<<"Add Cap and TransTube Task:"<<strParam<<info.strSampleID<<info1.strSampleID;
            m_gantryModule.SetCatchType(qVect.size()-1);

            // m_gantryModule.SlotAddSubTask(GTI_CAP_AND_TRANS_TUBE,"",strParam);

            QString strParamCap = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(!bCrossCap).arg(uiCapAreaIndex).arg(uiCapColumnIndex).arg(uiCapRowIndex)
                               .arg(bCrossCap)
                               .arg(bCrossCap).arg(uiCapAreaIndex1).arg(uiCapColumnIndex1).arg(uiCapRowIndex1);
            m_gantryModule.SlotAddSubTask(GTI_GET_PCR_CAP, "", strParamCap);

            bool bCrossTube = true;
            if (uiTubeAreaIndex == uiTubeAreaIndex1 && uiTubeRowIndex == uiTubeRowIndex1 && (uiTubeColumnIndex + 1) == uiTubeColumnIndex1)
            {
                bCrossTube = false;
            }
            QString strParamTube = QString(",%1,%2,%3,%4,%5,%6,%7,%8,%9").arg(!bCrossTube).arg(uiTubeAreaIndex).arg(uiTubeColumnIndex).arg(uiTubeRowIndex)
                               .arg(bCrossTube)
                               .arg(bCrossTube).arg(uiTubeAreaIndex1).arg(uiTubeColumnIndex1).arg(uiTubeRowIndex1);
            m_gantryModule.SlotAddSubTask(GTI_CAP_TO_TUBE, "", strParamTube);

            m_gantryModule.SlotAddSubTask(GTI_TRANS_TUBE_CAP, "", "");

#if !TEST_DISABLE_PCR_HOLE
            //更新PCR盖信息
            info.pcrPos = info1.pcrPos = pcrSubAreaPos;
            info1.pcrPos.uiColumnIndex++;
            info.pcrAreaPos = info1.pcrAreaPos = pcrAreaPos;
            info.bNeedOpenCap = bNeedOpenCap;
            info1.bNeedOpenCap = false;
#endif
            qVect[0] = info;
            qVect[1] = info1;
            QDFUN_LINE << qVect[0].printInfo();
            QDFUN_LINE << qVect[1].printInfo();
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE, qVect);

            qDebug()<<"ActionAddCapAndTransTubeTask captube double1:"<<info.strSampleID<<uiTubeAreaIndex<<uiTubeRowIndex<<uiTubeColumnIndex<<info.uiAmplifyCompIndex;
            qDebug()<<"ActionAddCapAndTransTubeTask captube double2:"<<info1.strSampleID<<uiTubeAreaIndex1<<uiTubeRowIndex1<<uiTubeColumnIndex1<<info1.uiAmplifyCompIndex;
        }
    }
    else //蓋PCR擴增管帽及轉移已做完
    {
        // SendPeroidStateToUpperHost(PEST_SYSTEM_BUILD_END,SEST_WAIT_PCR_MIX);
        //前面的提取體系構建處理完成相應模塊可恢復初始狀態
        // 可以解锁耗材、试剂、提取、样本架
        HalSubSystem::getInstance().SetAllElecMagneticUnlock();
        Reagent::getInstance().SetSystemBuildBusyStatus(false);
    }
}

void CAffair::ActionAddSwitchTubeTask()
{
    qDebug()<<"-------ActionAddSwitchTubeTask---------";
    //转移模块移动到离心旁
    QString  strParam = QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
    _addPCRTubeExistFalgToParamStr(strParam);
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_PCR_SWITCH, qVect);
    if(bGet && qVect.size()>0)
    {
        if(qVect.size()==1)
        {
            strParam  += QString(",%1,%2,%3").arg(false).arg(true).arg(CGlobalConfig::getInstance().getCentrifugeOptDetectFlag());
        }
        else
        {
            strParam  += QString(",%1,%2,%3").arg(true).arg(true).arg(CGlobalConfig::getInstance().getCentrifugeOptDetectFlag());
        } 
        m_pcrCatchModule.SlotAddSubTask(PCTI_SWITCH_TUBE, "",  strParam);

        //驅動下一個蓋帽+轉移
        ActionAddCapAndTransTubeTask();
    }
    qDebug()<<"ActionAddSwitchTubeTask: "<<strParam<<qVect.size();
}

void CAffair::ActionAddCentrifugeTubeTask()
{
    qDebug()<<"-------ActionAddCentrifugeTubeTask---------";
    //离心
    QString  strParam = QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_PCR_MIX, qVect);
    if(bGet && qVect.size()>0)
    {
        if(qVect.size()==1)
        {
            strParam  += QString(",%1,%2,%3").arg(false).arg(true).arg(CGlobalConfig::getInstance().getCentrifugeOptDetectFlag());
        }
        else
        {
            strParam  += QString(",%1,%2,%3").arg(true).arg(true).arg(CGlobalConfig::getInstance().getCentrifugeOptDetectFlag());
        }
        // 提前更新使用PCR
        SystemBuildInfo info = qVect.at(0);
        bool bNeedCloseCap = false;
#if TEST_DISABLE_PCR_HOLE
        PCRResource::getInstance().UsePCR(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, bNeedCloseCap,qVect);        
#else
        PCRResource::getInstance().UseDoublePCR(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, bNeedCloseCap);  
#endif  
        m_pcrCatchModule.SlotAddSubTask(PCTI_MIX_TUBE, "",  strParam);
        // m_switchMixModule.SlotAddSubTask(SMTI_SWITCH_AND_MIX, "",  strParam); // 提速替换
        QString strAreaIndexParam = "";
        ActionAddOpenPCRCapTask(info.bNeedOpenCap, info.pcrAreaPos.uiRowIndex,
                                    info.pcrAreaPos.uiColumnIndex,  strAreaIndexParam);
    }
    qDebug()<<"ActionAddCentrifugeTubeTask: "<<strParam<<qVect.size();
    
    // qVect.clear();
    // bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE, qVect);
    // if(!bGet && qVect.isEmpty())
    // {
    //     ActionAddMotorBoard2InitTask();//前面的提取體系構建處理完成相應模塊可恢復初始狀態
    // }
    // qDebug()<<"ActionAddCentrifugeTubeTask Board2Init: "<<bGet<<qVect.size();
}

void CAffair::ActionAddOpenPCRCapTask(bool bNeedOpenCap, quint8 uiRowIndex,
                                      quint8 uiColumnIndex, QString& strAreaIndexParam,PCRCatchTaskID taskID)
{
    qDebug()<<"-------ActionAddOpenPCRCapTask---------";
    quint8 uiAreaIndex = GetPCRAreaIndexString(uiRowIndex, uiColumnIndex, strAreaIndexParam);
    qDebug()<<"ActionAddOpenPCRCapTask param: "<<bNeedOpenCap<<uiRowIndex<<uiColumnIndex<<strAreaIndexParam<<uiAreaIndex<<taskID;    
    //如果需要打開PCR區域蓋子
    if(bNeedOpenCap)
    {
        qDebug()<<"Open PCR Area cap"<<strAreaIndexParam;
        QString strParam  = QString(",%1").arg(strAreaIndexParam) + QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
        _addPCRTubeExistFalgToParamStr(strParam);
        strParam += QString(",%1,%2").arg(uiAreaIndex+3).arg(CGlobalConfig::getInstance().getPCRAreaCapDetectFlag());
        m_pcrCatchModule.SlotAddSubTask(taskID, "", strParam);
    }
}

void CAffair::ActionAddClosePCRCapTask(bool bNeedCloseCap, const QString& strAreaIndexParam,PCRCatchTaskID taskID)
{
    qDebug()<<"-------ActionAddClosePCRCapTask---------";
    qDebug()<<"ActionAddClosePCRCapTask param: "<<bNeedCloseCap<<strAreaIndexParam<<taskID;
    if(bNeedCloseCap)
    {
        qDebug()<<"Close PCR Area cap"<<strAreaIndexParam;
        QString strParam  = QString(",%1").arg(strAreaIndexParam) + QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
        _addPCRTubeExistFalgToParamStr(strParam);
        strParam += QString(",%1,%2").arg(strAreaIndexParam.toInt()+3).arg(CGlobalConfig::getInstance().getPCRAreaCapDetectFlag());
        m_pcrCatchModule.SlotAddSubTask(taskID,"",strParam);                
    }
}

void CAffair::ActionAddTransToAmplifyAreaTask(SystemBuildInfo& info,const QString& strAreaIndexParam, bool bLeft, bool bRight)
{
    qDebug()<<"-------ActionAddTransToAmplifyAreaTask---------";
    QString strParam = QString(",%1,%2").arg(m_pPCRResource->GetPCRSizeType()).arg(strAreaIndexParam);
    // 双个pcr管，以Z1轴为起点，单个pcr管，以Z2轴为起点
    quint8 uiColumnIndex = (info.pcrPos.uiColumnIndex % 2 == 1) ? (info.pcrPos.uiColumnIndex - 1) : info.pcrPos.uiColumnIndex;
    if (bRight)// 单个pcr管，以Z2轴为起点
    {
        uiColumnIndex = info.pcrPos.uiColumnIndex;
    }
    
    strParam += QString(",%1,%2").arg(uiColumnIndex).arg(info.pcrPos.uiRowIndex);
    strParam  += QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
    _addPCRTubeExistFalgToParamStr(strParam);
    strParam  += QString(",%1,%2,%3").arg(bLeft).arg(bRight).arg(CGlobalConfig::getInstance().getCentrifugeOptDetectFlag());
    qDebug()<<"ActionAddTransToAmplifyAreaTask: "<<strParam<<info.pcrAreaPos.uiRowIndex<<info.pcrPos.uiRowIndex;
    m_pcrCatchModule.SlotAddSubTask(PCTI_TRANS_TO_PCR_AMPLIFY_AREA,"",strParam);

    // 计算孔位(孔位范围是0-63)
    quint8 uiAreaIndex = 0;
    if(!strAreaIndexParam.isEmpty())
    {
        uiAreaIndex = strAreaIndexParam.toInt();
    }
    quint8 uiHoleIndex = (info.pcrPos.uiRowIndex * PCR_SUB_AREA_COLUMN_SIZE) + uiColumnIndex;// 这个是在区域内的孔位
    uiHoleIndex += uiAreaIndex*PCRResource::getInstance().GetPCRSubAreaSize(); // 这个是根据不同的区域计算最终孔位
    info.uiHoleIndex = uiHoleIndex;
    qDebug()<<"ActionAddTransToAmplifyAreaTask uiHoleIndex:"<<info.printInfo();
}

void CAffair::ActionAddTransToPCRAmplifyAreaTask()
{
    qDebug()<<"-------ActionAddTransToPCRAmplifyAreaTask---------";
    QVector<SystemBuildInfo> qVect;
    bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA, qVect);
    if(bGet && qVect.size()>0)
    {
        QString strAreaIndexParam = "";
        GetPCRAreaIndexString(qVect.at(0).pcrAreaPos.uiRowIndex, qVect.at(0).pcrAreaPos.uiColumnIndex, strAreaIndexParam);
        if(qVect.size()==1)//單次轉移一個PCR管
        {
            SystemBuildInfo info = qVect.at(0);
            // ActionAddOpenPCRCapTask(info.bNeedOpenCap, info.pcrAreaPos.uiRowIndex,
            //                         info.pcrAreaPos.uiColumnIndex,  strAreaIndexParam);
            //轉移到PCR擴增區域
            ActionAddTransToAmplifyAreaTask(info, strAreaIndexParam, false, true);
            //更新信息
            bool bNeedCloseCap = false;
            PCRResource::getInstance().GetNeedCloseCap(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, bNeedCloseCap);
            //处在扩增单例程，只执行转移
            if (!m_pSampleControl->m_sampleAmplificate.GetRunStatus())
            {
                info.bNeedCloseCap = bNeedCloseCap;
            }            
            if(!info.bNeedCloseCap)
            {
                //如果当前操作的是当前批次特定TEC的最后一个也需要执行关盖
                if(info.bLastOneInCurTecBatch)
                {
                    info.bNeedCloseCap = true;
                }
            }
            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask single: "<<info.bNeedCloseCap<<info.bLastOneInCurTecBatch<<info.bNeedCloseCap<<info.uiHoleIndex;

            // 1.单管转移，孔位是第一个管的孔位+1(使用Z1轴转移，会造成pcr区域跳一个孔位，有空隙，所以测试信息要跳一个孔位)
            // 2.PCR分配是正确的，代码中PCR分配+1是不起作用
            // info.uiHoleIndex += 1;
            qVect[0] = info;
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA, qVect);
            //如果需要给PCR区域关盖
            ActionAddClosePCRCapTask(info.bNeedCloseCap, strAreaIndexParam);

            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask pcr single:"<<info.strSampleID<<strAreaIndexParam<<info.pcrPos.uiRowIndex<<info.pcrPos.uiColumnIndex<<info.uiAmplifyCompIndex<<info.uiHoleIndex;
            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask pcr single11:"<<info.strSampleID<<info.tubeCapPos.uiAreaIndex<<info.tubeCapPos.uiRowIndex<<info.tubeCapPos.uiColumnIndex;
        }
        else if(qVect.size()==2)//單次轉移兩個PCR管到擴增區
        {
            SystemBuildInfo info = qVect.at(0);
            SystemBuildInfo lastInfo = qVect.at(1);
            // ActionAddOpenPCRCapTask(info.bNeedOpenCap, info.pcrAreaPos.uiRowIndex,
            //                         info.pcrAreaPos.uiColumnIndex, strAreaIndexParam);
            // GetPCRAreaIndexString(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, strAreaIndexParam);
            //轉移到PCR擴增區域
            ActionAddTransToAmplifyAreaTask(info, strAreaIndexParam, true, true);
            //更新信息

            bool bNeedCloseCap = false;
            PCRResource::getInstance().GetNeedCloseCap(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, bNeedCloseCap);
            lastInfo.bNeedCloseCap = bNeedCloseCap;
            if(!lastInfo.bNeedCloseCap)
            {
                //如果当前操作的是最后一个也需要执行关盖
                if(lastInfo.bLastOneInCurTecBatch)
                {
                    lastInfo.bNeedCloseCap = true;
                }
            }
            lastInfo.uiHoleIndex = info.uiHoleIndex + 1;// 双管转移，第二个管的孔位是第一个管的孔位+1
            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask double: "<<lastInfo.bNeedCloseCap<<lastInfo.bLastOneInCurTecBatch<<lastInfo.strSampleID<<lastInfo.uiHoleIndex;
            qVect[0] = info;
            qVect[1] = lastInfo;
            m_pSampleControl->UpdateCurExecSystemBuildInfo(SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA, qVect);

            //如果需要给PCR区域关盖
            ActionAddClosePCRCapTask(lastInfo.bNeedCloseCap, strAreaIndexParam);

            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask pcr double1:"<<info.strSampleID<<strAreaIndexParam<<info.pcrPos.uiRowIndex<<info.pcrPos.uiColumnIndex<<info.uiAmplifyCompIndex<<info.uiHoleIndex;
            qDebug()<<"ActionAddTransToPCRAmplifyAreaTask pcr double2:"<<lastInfo.strSampleID<<strAreaIndexParam<<lastInfo.pcrPos.uiRowIndex<<lastInfo.pcrPos.uiColumnIndex<<lastInfo.uiAmplifyCompIndex<<lastInfo.uiHoleIndex;  
        }
    }
}

void CAffair::ActionAddStartPCRTask()
{
    //启动PCR
    qDebug()<<"-------ActionAddStartPCRTask---------";
#if  Send_By_Virtual == 0
    //提取完成后可以上报test_info信息(单扩增没有提取)
    QString strBatchNo  = _getCurBatchNo(SEST_WAIT_PCR_AMPLIFY);
    QString strPCRInfos = m_pSampleControl->GetAllPCRInfoString(strBatchNo);
    COperationUnit::getInstance().sendStringData(Method_test_info, strPCRInfos, Machine_UpperHost);
    QMap<quint8, QString> qPCRMap = m_pSampleControl->GetCurBatchPCRAreaUsingList(strBatchNo);
    qDebug()<<"ActionAddStartPCRTask AreaList size:"<<qPCRMap.size()<<strPCRInfos;
    
    // 初始化
    QStringList stringList;
    for (size_t i = 0; i < PCR_MODULE_SIZE; i++)
    {
        stringList.append("0");
    }
    
    // 使用区域
    for (quint8 num : qPCRMap.keys()) {
        if (num < PCR_MODULE_SIZE)
        {
            stringList[num] = "1";
        }
    }
    QString strlistPCRIndex = stringList.join(",");

    bool bTransTec = true;
    QVector<PCRResourceTimeInfo> qPCRTimeInfoList;
    QMap<quint8, QString>::iterator itor = qPCRMap.begin();
    for (; itor != qPCRMap.end(); ++itor) {
        quint8 uiPCRIndex = itor.key();
        QString strTecName = itor.value();

        PCRResource::getInstance().AddRecordPCRAreaBatchInfo(uiPCRIndex,strBatchNo);
        PCRResource::getInstance().SetPCRST(uiPCRIndex,PCRST_USING);
        PCRResourceTimeInfo info;
        info.uiAreaIndex = uiPCRIndex;
        info.strTecName = strTecName;
        qPCRTimeInfoList.append(info);

        qDebug()<<"ActionAddStartPCRTask PCR area:"<< uiPCRIndex << "TEC name:"<< strTecName<<strlistPCRIndex;
        if (bTransTec)
        {
            m_pcrModule[PCR_MODULE_MAIN_INDEX].SlotAddSubTask(PTI_TEC_TIMING_TABLE_TRANS_REQ_WITH_PCR_START, strlistPCRIndex, strTecName);
            bTransTec = false;
        }
        m_pcrModule[uiPCRIndex+1].SlotAddSubTask(PTI_TEC_TIMING_PCR_START_REQ, "", strTecName);
    }
    m_pcrModule[PCR_MODULE_MAIN_INDEX].SetFLLedStatus(true); // 灯源开启         
    PCRResource::getInstance().AddRecordPCRAreaDuration(strBatchNo, qPCRTimeInfoList);
    // SendPeroidStateToUpperHost(PEST_AMPLIFY_START,SEST_WAIT_PCR_AMPLIFY);
#elif Send_By_Virtual == 1
    HandlePCREndReply();
#endif
}

void CAffair::ActionAddAbandonPCRTubeTask(quint8 uiRowIndex, quint8 uiColumnIndex, const QString& strAreaParam)
{
    qDebug()<<"-------ActionAddAbandonPCRTubeTask---------";
    QString strParam = QString(",%1,%2").arg(m_pPCRResource->GetPCRSizeType()).arg(strAreaParam);
    // 不管单个或双个pcr管，都以Z1轴为起点
    quint8 columnIndex = (uiColumnIndex % 2 == 1) ? (uiColumnIndex - 1) : uiColumnIndex;
    strParam += QString(",%1,%2").arg(columnIndex).arg(uiRowIndex);
    strParam += QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());//添加pcr抓手检测标记
    strParam += QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());//添加耗材丢弃位参数
    _addPCRTubeExistFalgToParamStr(strParam);
    m_pcrCatchModule.SlotAddSubTask(PCTI_ABANDON_TUBE,"",strParam);
}

void CAffair::ActionAddMotorBoard1InitTask()
{
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, QString("%1").arg(Action_Board1Init), Machine_Motor_1);
}

void CAffair::ActionAddMotorBoard2InitTask()
{
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, QString("%1").arg(Action_Board2Init), Machine_Motor_2);
}

void CAffair::ActionAddMotorBoard3InitTask()
{
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, QString("%1").arg(Action_Board3Init), Machine_Motor_3);
}

void CAffair::ActionAddAbandonPCRTask()
{
    qDebug()<<"-------ActionAddAbandonPCRTask---------";
    QVector<SystemBuildInfo> qVect;
    // bool bGet = m_pSampleControl->GetCurExecSystemBuildInfo(SEST_WAIT_ABANDON, qVect);
    bool bGet = m_pSampleControl->GetWaitAbandonExecSystemBuildInfo(qVect);
    if(bGet && qVect.size()>0)//一次性丟棄一個或2个PCR管
    {
        SystemBuildInfo info = qVect.at(0);
        SystemBuildInfo lastInfo = qVect.at(qVect.size()-1);
        QString strAreaParam = "";

        ActionAddOpenPCRCapTask(info.bNeedOpenCap, info.pcrAreaPos.uiRowIndex,
                                info.pcrAreaPos.uiColumnIndex, strAreaParam,PCTI_ABANDON_OPEN_CAP);
        m_pBackBin->use(qVect.size());
        ActionAddAbandonPCRTubeTask(info.pcrPos.uiRowIndex, info.pcrPos.uiColumnIndex, strAreaParam);
        ActionAddClosePCRCapTask(lastInfo.bNeedCloseCap, strAreaParam,PCTI_ABANDON_CLOSE_CAP);
        quint8 uiHoleIndex = info.pcrPos.uiColumnIndex + (info.pcrPos.uiRowIndex * PCR_SUB_AREA_COLUMN_SIZE);
        PCRResource::getInstance().ResetPCRResourceHoletatus(info.pcrAreaPos.uiRowIndex, info.pcrAreaPos.uiColumnIndex, uiHoleIndex);  
        uiHoleIndex = lastInfo.pcrPos.uiColumnIndex + (lastInfo.pcrPos.uiRowIndex * PCR_SUB_AREA_COLUMN_SIZE);
        PCRResource::getInstance().ResetPCRResourceHoletatus(lastInfo.pcrAreaPos.uiRowIndex, lastInfo.pcrAreaPos.uiColumnIndex, uiHoleIndex);  
    }
    else //无PCR管时
    {
        m_pcrCatchModule.SetIsBusy(false);//没有需要丢弃的pcr管，置为空闲
        qDebug()<<"No pcr tube exists to be abandon";
    }
}

quint8 CAffair::GetPCRAreaIndexString(quint8 uiRowIndex, quint8 uiColumnIndex, QString& strParam)
{
    quint8 uiAreaIndex = uiRowIndex*PCR_COLUMN_SIZE+ uiColumnIndex;
    strParam = QString("%1").arg(uiAreaIndex);
    return uiAreaIndex;
}

void CAffair::slotStartPeriodicResetProcess()
{
    qDebug()<<"slotStartPeriodicResetProcess";
    return;
    // 1、判断复位状态(复位失败后，需要单指令发送)
    // 2、读取数据库时序，取出指令，发送单指令，不管是否执行成功，都发送下一条指令
    quint32 uiPeriod = SystemConfig::getInstance().GetIntValue(SystemConfig::reset,SystemConfig::check_period);
    QList<quint8> listID; 
    listID<<Action_Board1ZInit<<Action_Board2ZInit<<Action_Board3ZInit
          <<Action_Board1RemainInit<<Action_Board2RemainInit<<Action_Board3RemainInit;   
    QString strID = "";
    for(auto& item:listID)
    {
        strID += QString("%1;").arg(item);
        QString str = CTimingInfoDB::getInstance().getComplexTimingContentFromID(strID);
        quint8 u8BoardIndex = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(strID);
        QStringList list = str.split(";");
        for (auto& item:list)
        {
            CCommunicationObject::getInstance().sendMessageToMachine(item.toLatin1(), u8BoardIndex);
            QThread::sleep(uiPeriod);//延时10毫秒
        }        
    }
}

void CAffair::SendSimulateExtractScanResult()
{
    QStringList listResult;
    for (size_t i = 0; i < STRIP_MAX_SIZE; i++)
    {
        listResult.append("");
    }

    QList<int> list;
    CStrip::getInstance().GetMagneticRodSleevePositionAll(list);
    // W0612405120124，货号W061，年份24，月份05，日期:12，流水号01，有效月24个月
    QRandomGenerator *randomGenerator = QRandomGenerator::global();
    int randomNumber = randomGenerator->bounded(100, 999);
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString currentDate = currentDateTime.toString("yyMMdd");
    QString str = QString("%1").arg("W"+QString::number(randomNumber));
    str += currentDate;
    QString strExpiration = "24";

    randomNumber = randomGenerator->bounded(0, 9);

    for (size_t i = 0; i < list.size(); i++)
    {
        if(list[i] == 0 )
        {
            continue;
        }
        QString strTemp = QString("%1").arg(randomNumber+i, 2, 10, QChar('0'));
        strTemp += strExpiration;
        listResult[i] = str+strTemp;
    }
    QString strResult = listResult.join(",");
    COperationUnit::getInstance().sendStringData(Method_extract_qrcode, strResult, Machine_UpperHost); 
    qDebug()<<"SendSimulateExtractScanResult: "<<strResult;   
}

void CAffair::SetMotor3AllOptoStatus(const quint32 u32Status)
{
    m_u32Motor3AllOptoStatus = u32Status;
    qDebug()<<"SetMotor3AllOptoStatus SeqType: "<<_GetSeqType()<<"u32Status: "<<u32Status;
    switch (_GetSeqType())
    {
    case ST_PCR_CLEAN://自检先执行初始化，等待时间较久，不用读取光耦状态再启动
        MaintainSubSystem::getInstance().SelfTestCleanPCR(_GetSeqType());
        break;
    case ST_SELF_TEST:
        MaintainSubSystem::getInstance().SelfTestSetMotor3AllOptoStatus(m_u32Motor3AllOptoStatus);//设置光耦状态
        MaintainSubSystem::getInstance().SelfTestStart(_GetSeqType());//启动自检
        break;
    default:
        break;
    }
}

void CAffair::GetMotor3PCROptoStatus(QList<int>& qList)
{
    //获取PCR区域盖状态 挡住为-0 未挡住-1
    if(m_u32Motor3AllOptoStatus !=0)
    {
        qList.clear();
        bool bPcrOpto5Statusbit16 = (m_u32Motor3AllOptoStatus >> 16) & 1;// PCR遮光盖1检测光耦
        bool bPcrOpto6Statusbit17 = (m_u32Motor3AllOptoStatus >> 17) & 1;// PCR遮光盖2检测光耦
        bool bPcrOpto5Statusbit18 = (m_u32Motor3AllOptoStatus >> 18) & 1;// PCR遮光盖3检测光耦
        bool bPcrOpto6Statusbit19 = (m_u32Motor3AllOptoStatus >> 19) & 1;// PCR遮光盖4检测光耦       
        bool bPcrOpto5Statusbit20 = (m_u32Motor3AllOptoStatus >> 20) & 1;// PCR遮光盖5检测光耦
        bool bPcrOpto6Statusbit21 = (m_u32Motor3AllOptoStatus >> 21) & 1;// PCR遮光盖6检测光耦
        
        // 先检查是否有盖子
        QList<int> qListArea1;
        if(!bPcrOpto5Statusbit20)// PCR遮光盖5有盖子,判断PCR遮光盖1和4检测光耦状态
        {
          if(bPcrOpto5Statusbit16)//第1个需要盖
          {
            qListArea1.push_back(0);
          }else if(bPcrOpto5Statusbit18 && qListArea1.isEmpty())
          {
            qListArea1.push_back(2);
          }
        }
        
        QList<int> qListArea2;
        if(!bPcrOpto6Statusbit21)// PCR遮光盖6有盖子,判断PCR遮光盖2和3检测光耦状态
        {
          if(bPcrOpto6Statusbit17)//第1个需要盖
          {
            qListArea2.push_back(1);
          }else if(bPcrOpto6Statusbit19 && qListArea2.isEmpty())
          {
            qListArea2.push_back(3);
          }
        }   
        qList.append(qListArea1);
        qList.append(qListArea2);

        qDebug()<<"MotorBoard3Clean Opto Status: "<<m_u32Motor3AllOptoStatus<<qList;     
        m_u32Motor3AllOptoStatus = 0;
    }
}

void CAffair::_checkSampleScanTubeAndAction()
{
    // 获取样本管有无
    bool bLeft  = CCommunicationObject::getInstance().GetSampleCodeScanTubeExist(true);
    bool bRight = CCommunicationObject::getInstance().GetSampleCodeScanTubeExist(false);
    if(bLeft || bRight)// 有样本管，打开扫码器并扫码(目前不考虑单管情况)
    {
        //开始扫码(扫码结束后需要放回样本)，需要考虑两个扫码器的等待时间
        // 1、打开扫码器
        if (bLeft)
        {
            CCommunicationObject::getInstance().StartSingleSamplerCodeScanner();
        }
        
        if (bRight)
        {
            CCommunicationObject::getInstance().StartSingleSamplerCodeScanner(false);
        }
        
        qDebug()<<"_checkSampleScanTubeAndAction"<<CCommunicationObject::getInstance().GetSamplerScanCurPos();
        // 2、旋转爪子
        ActionAddSampleScanCodeRotateTask(bLeft,bRight,CCommunicationObject::getInstance().GetSamplerScanCurPos());
    }
    else //没有样本，放回原来位置
    {
        ActionAddSampleScanCodeEndTask(CCommunicationObject::getInstance().GetSamplerScanCurPos());
    } 
}


void CAffair::_calcArraySystemBuildInfo()
{
    // 需要重新计算pcr区域可用资源(特别是上一批全部pcr资源已经使用完成)
    m_pPCRResource->LoadValidPcrAreaHole();  
    QQueue<QVector<SystemBuildInfo>> qQueue;
    m_pSampleControl->RearraySystemBuildInfoByTecType(qQueue);
    m_pPCRResource->AddWaitCapSampleInfo(qQueue);
#if TEST_DISABLE_PCR_HOLE
    // 开始预分配孔位
    QQueue<QVector<SystemBuildInfo>> qCalcSystemBuildInfo;
    m_pPCRResource->CalcSamplePCRHoleInfo(qCalcSystemBuildInfo);
    m_pSampleControl->SetCalcSystemBuildInfo(qCalcSystemBuildInfo);
#endif   
    qDebug()<<"_calcArraySystemBuildInfo:";
}

void CAffair::_sendRunSTErrorNotify()
{
    SeqType type = _GetSeqType();
    switch (type)
    {
    case ST_PERIODIC:
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:%2,%3").arg(m_seqType).arg(m_pSampleControl->GetCurBatchNo()).arg(PEST_SEQ_FAILED), Machine_UpperHost, 1);
        break;
    case ST_RESET:// 仪器复位失败，复位条件不满足
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_seqType), Machine_UpperHost, 1);
        break;    
    case ST_SELF_TEST:
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_seqType), Machine_UpperHost, 1); 
        break;         
    default:
        break;
    }
    _SetRunST(RST_IDLE, __FUNCTION__);
    _SetSeqType(ST_UNDEFINE);
    _ClearData(); 
    qDebug()<<"CAffair::_sendRunSTErrorNotify"<<type;
}

void CAffair::UpdateRunStat(RunStat runStat)
{
    QDFUN_LINE << "m_bPeriodicInit" << m_bPeriodicInit << "runStat" << runStat;
    if (m_bPeriodicInit && RST_IDLE == runStat)
    {
        QDFUN_LINE << "PeriodicProcess";
        m_bPeriodicInit = false;
        PeriodicProcess();
        return;
    }

    _SetRunST(runStat, __FUNCTION__);
    switch (runStat)
    {
    case RST_IDLE:
        _SetSeqType(ST_UNDEFINE);
        _ClearData(); 
        break;
    case RST_WAIT_IDLE:
        break;
    case RST_WAIT_RUN:
        break;
    case RST_RUN:
        break;
    default:
        break;
    }
    qDebug()<<"CAffair::UpdateRunStat"<<runStat;
}

void CAffair::ExtractModuleDebugActionCmdReply(quint16 uiComplexID, quint16 uiResult)
{
    m_extractModule.DebugActionCmdReply(uiComplexID, uiResult);
}

void CAffair::ExtractModuleDebugStart(QString strExtractUIContent)
{
    qDebug()<<"-------ExtractModuleDebugStart---------";
    m_extractModule.SetDebugStatus(true);
    QString strLastStepParams = "";
    QStringList strParamList;
    CExtractParse::getInstance().getExtractMotorContentFromUIContent(strExtractUIContent,strParamList,strLastStepParams);

    m_extractModule.SlotAddSubTask(ETI_EXTRACT_START,"","");
    for(int i=0;i<strParamList.size();i++)
    {
        m_extractModule.SlotAddSubTask(ETI_EXTRACT_RUN,"",strParamList[i]);
    }
    m_extractModule.SlotAddSubTask(ETI_EXTRACT_END,"",strLastStepParams);  
    qDebug() << "ExtractModuleDebugStart"<<strExtractUIContent<<strParamList.size()<<strLastStepParams;
}
