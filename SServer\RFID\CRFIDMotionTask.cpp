#include<QDebug>
#include<QObject>
#include"CRFIDMotionTask.h"
#include"RFID/CRFIDCtrl.h"
#include"./ccommunicationobject.h"

CRFIDMotionTask::CRFIDMotionTask(QObject* parent):QThread(parent)
{
    m_qTaskList.clear();
    m_bIsFeedBack = true;
    //connect(this, &CRFIDMotionTask::sigError,
   //         [](ErrorID errorID, QString strExtraInfo)
  //  {CCommunicationObject::getInstance().handleError(strExtraInfo, Mid_Sub_RFID, errorID);}); //放到CCommunicationObject


    this->start();
}


void CRFIDMotionTask::SlotRFIDReadData(int iType,QString strData)
{
    qDebug()<<"SlotRFIDReadData in";
    QString strLog;
    int  iStatus = CRFIDCtrl::getInstance()._converRFIDDataAndUpdate(strData,RFIDConsumableType(iType));
}

CRFIDMotionTask &CRFIDMotionTask::getInstance()
{
    static CRFIDMotionTask cLoopThread;
    return cLoopThread;
}

void CRFIDMotionTask::SlotFeedBack(int iMethodID,int iStatus,int iType)//   //0 ok ,1  false,  -1:outtime
{
   qDebug()<<__FUNCTION__<<"iMethodID="<<iMethodID<<",iStatus="<<iStatus<<",iType="<<iType;
    m_qMutex.lock();
    m_bIsFeedBack=true;
    if(iStatus ==1)
    {
        if(iMethodID == Method_rfid_read)
        {
            emit sigError("RefreshDataFromRFID,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataReadError);
        }
        else
        {
            if(iType >=RFID_ConsumableType_Reagent1  &&  iType<=RFID_ConsumableType_Reagent4 )
            {
                emit sigError("WriteDataToRFIDReagentBox,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataWriteError);
            }
            else
            {
                 emit sigError("WriteDataToRFIDConsumableBox,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataWriteError);
            }
        }
    }
    if(iStatus ==-1)
    {
        if(iMethodID == Method_rfid_read)
        {
            emit sigError("RefreshDataFromRFID,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataReadTimeout);
        }
        else
        {
            if(iType >=RFID_ConsumableType_Reagent1  &&  iType<=RFID_ConsumableType_Reagent4 )
            {
                emit sigError("WriteDataToRFIDReagentBox,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataWriteTimeout);
            }
            else
            {
                emit sigError("WriteDataToRFIDConsumableBox,"+QString::number(iType),Mid_Sub_RFID,FT_RFIDDataWriteTimeout);
            }

        }
    }

    m_qMutex.unlock();
}

void CRFIDMotionTask::AddTask(CRFIDTask m_task)
{
    m_qMutex.lock();
    m_qTaskList.push_back(m_task);
    m_qMutex.unlock();
}


bool CRFIDMotionTask::GetTask(CRFIDTask &task)
{
    bool iIsGet = false;
    m_qMutex.lock();
    if(m_qTaskList.size() > 0)
    {
        task=m_qTaskList[0];
        iIsGet = true;
    }
    m_qMutex.unlock();
    return iIsGet;
}


void CRFIDMotionTask::SlotResetStatua()
{
    m_qMutex.lock();
    m_bIsFeedBack =true;
    m_qMutex.unlock();
}

void CRFIDMotionTask::run()
{
    while (1)
    {
        if(m_bIsFeedBack)
        {
            CRFIDTask s_mCurrentTask;
            if(GetTask(s_mCurrentTask))
            {
                m_bIsFeedBack = false;
                qDebug()<<"s_mCurrentTask,iType="<<s_mCurrentTask.iType<<",s_mCurrentTask,iMethod="<<s_mCurrentTask.iMethod<<",s_mCurrentTask,ui16Delay="<<s_mCurrentTask.ui16Delay;
                if(s_mCurrentTask.iMethod ==Method_rfid_write)  //写
                {
                    if(s_mCurrentTask.iType >=RFID_ConsumableType_Reagent1  &&  s_mCurrentTask.iType<=RFID_ConsumableType_Reagent4 )
                    {
                       CRFIDCtrl::getInstance().WriteDataToRFIDReagentBox(s_mCurrentTask.rBox);
                    }
                    if(s_mCurrentTask.iType == RFID_ConsumableType_Tip1 ||  s_mCurrentTask.iType == RFID_ConsumableType_Tip2)
                    {
                       CRFIDCtrl::getInstance().WriteTipDataToRFIDConsumableBox(s_mCurrentTask.cBox);
                    }
                    if(s_mCurrentTask.iType == RFID_ConsumableType_TubeCap1 ||  s_mCurrentTask.iType == RFID_ConsumableType_TubeCap2)   //需要整合管帽链表数据
                    {
                        if(s_mCurrentTask.isTubeTask ==1 )   //cBox 为Tube数据，需要取出cap数据整合    注意task.isTubeTask
                        {
                         CRFIDCtrl::getInstance().WriteTubeDataToRFIDConsumableBox(s_mCurrentTask.cBox);
                        }
                        else
                        {
                          CRFIDCtrl::getInstance().WriteCapDataToRFIDConsumableBox(s_mCurrentTask.cBox);
                        }
                    }
                }
                else
                {
                    CRFIDCtrl::getInstance().RefreshDataFromRFID(s_mCurrentTask.iType);                
                }
                // 防止出现其他错误数据，导致延时不确定
                if (s_mCurrentTask.iType >= RFID_ConsumableType_Reagent1  &&  s_mCurrentTask.iType<=RFID_ConsumableType_TubeCap2 )
                {
                    msleep(s_mCurrentTask.ui16Delay);// 有些任务需要延时
                }
                else
                {
                    m_bIsFeedBack = true;// iType为异常类型，需要重置状态(没有下发给下位机，所以不会有任何状态返回)
                }
                m_qTaskList.removeFirst();
            }
        }
         msleep(50);
    }

}


