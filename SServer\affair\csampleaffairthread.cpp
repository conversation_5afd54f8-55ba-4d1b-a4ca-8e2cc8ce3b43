#include "csampleaffairthread.h"
#include <QDebug>
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"

CSampleAffairThread::CSampleAffairThread(quint16 uiUnit, QObject *parent)
    : CAffairObject(uiUnit, parent)
{
    m_bThreadExit = true;
    this->initUnitData();
}

CSampleAffairThread::~CSampleAffairThread()
{
    m_bThreadExit = false;
}

void CSampleAffairThread::run()
{
    while(m_bThreadExit)
    {
        if(this->m_iReadIndex_Profix.load() != this->m_iWriteIndex_Profix.load()
                || this->m_iReadIndex_Condition.load() != this->m_iWriteIndex_Condition.load())
        {
            this->_HandleReceiveList();
        }
        else
        {
            msleep(1);
        }
    }
}

void CSampleAffairThread::_HandleReceiveList()
{
    while (m_iReadIndex_Condition.load() != m_iWriteIndex_Condition.load())
    {
        quint16& uiMessage = m_uiConditionInfoList[m_iReadIndex_Condition.load()];
        // TO DO
        if(uiMessage >= 0 && uiMessage < CONDITION_SIZE)
        {
            // TO DO
            // 如果是第一个动作，判定前置队列m_qProfixInfoList
            if(uiMessage == 0)
            { // 如果序号为0，判定前置队列
                if (m_iReadIndex_Profix.load() != m_iWriteIndex_Profix.load())
                {
                    QByteArray& qMessage = m_qProfixInfoList[m_iReadIndex_Profix.load()];
                    // TO DO
                    // 环形队列
                    m_iReadIndex_Profix.store((m_iReadIndex_Profix.load() + 1) % BUFFER_SIZE);
                }
            }
            else
            {//
                QList<SUnitAffair> sSUnitAffairList = m_sUnitAffairList[uiMessage];
                for(auto sUnitAffair : sSUnitAffairList)
                {
                    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(sUnitAffair.strComplexID);
                    COperationUnit::getInstance().sendStringData(Method_comp_cmd, sUnitAffair.strComplexID, iMachineID);
                }
            }
        }

        // 环形队列
        m_iReadIndex_Condition.store((m_iReadIndex_Condition.load() + 1) % BUFFER_SIZE);
    }
}

void CSampleAffairThread::initUnitData()
{
    QString strProcessContent = CTimingInfoDB::getInstance().getProcessContentFromName("");// 获得单元流程内容
    QStringList strProcessContentList = strProcessContent.split(";");
    QStringList strContentList;
    SUnitAffair sUnitAffair;
    for(auto strContent : strProcessContentList)
    {
        strContentList = strContent.split(",");
        if(strContentList.length() >= 3)
        {
            sUnitAffair.uiUnitName = strContentList[0].toUInt();
            sUnitAffair.uiUnitIndex = strContentList[1].toUInt();
            sUnitAffair.strComplexID = strContentList[2].split("_").at(0);
            if(strContentList.length() >= 4)
            {
                sUnitAffair.strComplexParam = strContentList[3];
            }
            if(sUnitAffair.uiUnitIndex >= 0 && sUnitAffair.uiUnitIndex < CONDITION_SIZE)
            {
                m_sUnitAffairList[sUnitAffair.uiUnitIndex].append(sUnitAffair); // 状态序号对应的复合指令
            }
        }
    }
}
