#include "cwindowobject.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
CWindowObject::CWindowObject(QObject *parent)
    : QObject(parent)
    , m_bThreadExit(false)
{
    pthread_t tid;
    pthread_create(&tid, NULL, _CreateThreadHandleList,this);
    //    QtConcurrent::run(this, &CTcpAnalyzeThread::_createConcurrentThread);// z作为父类，子类创建3个已上会失败
}

CWindowObject::~CWindowObject()
{
    m_bThreadExit = true;
}

void CWindowObject::slotAddReciveMsg(QByteArray qMsgBtyeArray)
{
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qSendMessageInfoList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
    m_conditionVariable.notify_one();// 唤醒
}

void *CWindowObject::_CreateThreadHandleList(void *arg)
{
    CWindowObject* pCWindowObject = (CWindowObject*)arg;
    qDebug() << "Starting _createConcurrentThread in" << pCWindowObject;
    std::unique_lock<std::mutex> uniqueLock(pCWindowObject->m_mutex);
    while(!pCWindowObject->m_bThreadExit)
    {
        pCWindowObject->m_conditionVariable.wait(uniqueLock, [pCWindowObject]
        { return pCWindowObject->m_iReadIndex.load() != pCWindowObject->m_iWriteIndex.load()
                    || pCWindowObject->m_bThreadExit; });
        if (pCWindowObject->m_bThreadExit)
        {
            break;
        }
        pCWindowObject->_HandleReceiveList();
    }
    return NULL;
}

void CWindowObject::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            m_pFramePos = qMessage.data() + gk_iLengthPos;
            m_iReadPayloadLength = GetByte2Int(m_pFramePos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");
            CGlobalConfig::getInstance().printMessageInfo(qMessage,
                                                          "[window->server] ");
            // 转发--用哪种通信方式，获取DestinationID，不修改SourceID
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}
