#ifndef CEXTRACTPARSE_H
#define CEXTRACTPARSE_H
#include <QString>
#include "publicconfig.h"


class CExtractParse
{
public:
    enum EUIContentOneDataEnum
    {
        UI_ONE_A = 2,// 孔位
        UI_ONE_B,
        UI_ONE_C,
        UI_ONE_D,
        UI_ONE_E,
        UI_ONE_F,
        UI_ONE_G,// 加热孔位
        UI_ONE_H,// 加热温度
    };

    explicit CExtractParse();

    static CExtractParse& getInstance();

    QString getExtractMotorContentFromUIContent(QString strUIContent);

    /**
     * @brief getExtractMotorContentFromUIContent 获取提取时序参数
     * @param strUIContent 提取时序UI内容
     * @param strParamList  提取时序参数列表
     * @param strLastStepParams 清洗退磁高度相关参数
     */
    void getExtractMotorContentFromUIContent(QString strUIContent,QStringList &strParamList,QString& strLastStepParams);
private:
    /**
     * @brief _getInitStr 磁棒、磁套、卡盒架复位动作
     * @return
     */
    QString _getInitStr();
    /**
     * @brief _getMagnetTubeStr 扎取磁套动作
     * @return
     */
    QString _getMagnetTubeStr();

    /**
     * @brief _getSeqStepStr 添加当前转换步信息
     * @return
     */
    QString _getSeqStepStr(int iStep);
    /**
     * @brief _getStartHeaterStr 获取启动裂解/洗脱加热指令
     * @param strOneLine
     * @return
     */
    /**
     * @brief _getStartHeaterStr
     * @param fHeaterVal 加热目标温度
     * @param strHeatHole 加热选项，1=裂解孔，2=洗脱孔
     * @return
     */
    QString _getStartHeaterStr(const float& fHeaterVal, const QString& strHeatHole);
    /**
     * @brief _getStopHeaterStr 获取停止裂解/洗脱加热指令
     * @param strHeatHole 加热选项，1=裂解孔，2=洗脱孔
     * @return
     */
    QString _getStopHeaterStr(const QString& strHeatHole);

    /**
     * @brief _getDelayStr 获取延迟动作指令
     * @param iDelaySecond
     * @return
     */
    QString _getDelayStr(int iDelaySecond);


    /**
     * @brief _getExtractEndStr 获取提起收尾指令集信息
     * @return
     */
    QString _getExtractEndStr();

    /**
     * @brief _getLiquidLevel 根据液体容积获取液面高度
     * @param strLiquidVol
     * @param strLiquidLevelH
     * @param strLiquidLevelV
     * @param fLiquidLevelH
     */
    void _getLiquidLevel(QString strLiquidVol, QString &strLiquidLevelH, QString &strLiquidLevelV, float &fLiquidLevelH);

    /**
     * @brief _getMagneticActionStr 获取吸磁指令
     * @param strLiquidLevelV
     * @param strLiquidLevelH
     * @param strHalfE
     * @return
     */
    QString _getMagneticActionStr(const QString &strLiquidLevelV, const QString &strLiquidLevelH, const QString &strHalfE);
    /**
     * @brief _getMixActionStr 获取混匀指令
     * @param fLiquidLevelH
     * @param strLiquidLevelV
     * @param iMixTime
     * @param strMixLevel
     * @return
     */
    QString _getMixActionStr(const float &fLiquidLevelH, const QString &strLiquidLevelV, int iMixTime, QString strMixLevel);

    /**
     * @brief _getEjectMagnetTubeStr 获取退磁套指令
     * @return
     */
    QString _getEjectMagnetTubeStr();

    /**
     * @brief _getEjectMagnetTubeStr 获取清洗磁套指令
     * @return
     */
    QString _getRinseMagnetTubeStr();    
};

#endif // CEXTRACTPARSE_H
