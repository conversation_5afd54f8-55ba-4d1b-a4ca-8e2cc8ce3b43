#include "cconsumablereservation.h"
#include <QDebug>
#include "datacontrol/cprojectdb.h"
#include "consumables/pcrresource.h"
#include "consumables/crecyclebin.h"

CConsumableReservation::CConsumableReservation()
{

}

void CConsumableReservation::test()
{
    QList<QString> qSampleInfoList;
    //       qSampleInfoList.push_back("M100,M102");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    //       qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
    qSampleInfoList.push_back("M100");
    qSampleInfoList.push_back("M100,M102");
//    qSampleInfoList.push_back("M100");
//    qSampleInfoList.push_back("M100,M102");
//    qSampleInfoList.push_back("M100");
//    qSampleInfoList.push_back("M100,M102");
    calcConsumableSupplyPos(qSampleInfoList);
}

QString CConsumableReservation::getReservateResult(const QList<QString>& qSampleInfoList)
{
    QStringList resultList;
    QList<ReservateData> reservateDataList = calcConsumableSupplyPos(qSampleInfoList);
    // params:[耗材类型(RFID类型1-8),是否充足(1:充足，0:不充足),耗材名称,预留位置信息(素引&startpos&endpos+索引...);下一个耗材信息]
    for (auto& reservateData : reservateDataList)
    {
        QStringList posList;
        for(const auto &posInfo : reservateData.qPosList)
        {
            QString strPosList = QString("%1&%2&%3").arg(posInfo.uiAreaIndex).arg(posInfo.uiStartPos).arg(posInfo.uiEndPos);
            posList.append(strPosList);
        }
        QString strData = QString("%1,%2,%3,%4").arg(reservateData.uiType).arg(reservateData.bEnough).arg(reservateData.strName).arg(posList.join("+"));
        resultList.append(strData); 
    }
    
    QString strResult = resultList.join(";");
    qDebug()<<"getReservateResult:"<<strResult;
    return strResult;
}

void CConsumableReservation::_parseSampleInfos(QList<QString> qSampleInfoList,
                                               QMap<QString, int> &qProjTestCounts,
                                               QVector<SimpleSysBuildInfo> &qSimpleSysBuildInfoVect)
{
    //解析，并重新组织获取每个系统构建测试的简单信息，获取不同项目测试的样本个数信息
    int iStripIndex = 1;
    if(qSampleInfoList.size()>0)
    {
        int iCurStripIndex = iStripIndex;
        for (const QString &strProj : qSampleInfoList)
        {
            iStripIndex++;
            iCurStripIndex = iStripIndex;
            QStringList qProjList = strProj.split(",", QString::SkipEmptyParts);
            // Parse project numbers and create ProjectInfo structures
            for (int iProjIdx = 0; iProjIdx < qProjList.size(); iProjIdx++)
            {
                QString strProjID = qProjList[iProjIdx];
                quint8 uiCompNum = CProjectInformation::getInstance().getTubeCountFromProjectLot(strProjID);
                qProjTestCounts[strProjID] += 1;
                for(int i = 0;i<uiCompNum;i++)
                {
                    SimpleSysBuildInfo sysBuildInfo;
                    sysBuildInfo.strProj = strProjID;
                    sysBuildInfo.uiCompIndex = i;
                    sysBuildInfo.uiStripIndex= iCurStripIndex;
                    qSimpleSysBuildInfoVect.push_back(sysBuildInfo);
                     qDebug()<<"CConsumableReservation add new elem strip:"<<iCurStripIndex;
                }
            }
        }
    }
}

quint8 CConsumableReservation::_getReagentReserveInfo(const QMap<QString, int>& qProjTestCounts, QList<ReservateData>& qReservateList)
{
    QMap<quint8, QList<quint8>> qReagentBallReserveMap;
    quint8 uiUseTipCount = 0;

    for (const auto &entry : qProjTestCounts.toStdMap())
    {
        const QString &strProjectName = entry.first;
        int iTestCount = entry.second;
        qDebug() << "project name: " << strProjectName << ", test count: " << iTestCount;
        ReservateData reagent;
        reagent.strName = strProjectName;
        reagent.uiType = CT_REAGENT;
        reagent.bEnough = Reagent::getInstance().IsReagentEnough(strProjectName, iTestCount);
        if(!reagent.bEnough)
        {
            qReservateList.push_back(reagent);
            continue;
        }

        qReagentBallReserveMap.clear();
        //获取所有分装试剂分组信息，按组别进行分装，每个
        QMap<QString, QMap<quint8, QVector<SubPackData>>> subPackMap = Reagent::getInstance().GetReagentBallSubPackDatas();
        foreach (const QString &key, subPackMap.keys())
        {
            QMap<quint8, QVector<SubPackData>> &innerMap = subPackMap[key];
            //获取所有分装试剂分组信息，按组别进行分装，同一类别使用同一个Tip
            uiUseTipCount += innerMap.keys().size();
            foreach(const quint8 & innerKey, innerMap.keys())
            {
                foreach(SubPackData data, innerMap[innerKey])
                {
                    qReagentBallReserveMap[data.uiColumnIndex].push_back(data.uiRowIndex);
                }
            }
        }

        foreach(const quint8 & uiKey, qReagentBallReserveMap.keys())
        {
            RangePos pos;
            pos.uiAreaIndex = uiKey - 1;//试剂列索引从1开始，需要-1改为从0开始的
            if(qReagentBallReserveMap[uiKey].size()>0)
            {
                pos.uiStartPos = qReagentBallReserveMap[uiKey].at(0);
                pos.uiEndPos = qReagentBallReserveMap[uiKey].at(0);
                foreach(quint8 uiRowIndex, qReagentBallReserveMap[uiKey])
                {
                    if(pos.uiStartPos>uiRowIndex)
                    {
                        pos.uiStartPos = uiRowIndex;
                    }
                    if(pos.uiEndPos<uiRowIndex)
                    {
                        pos.uiEndPos = uiRowIndex;
                    }
                    qDebug()<<"Reagent ball ColumnIndex: "<<uiKey <<" row: "<< uiRowIndex;
                }
                reagent.qPosList.push_back(pos);
            }
        }
        qReservateList.push_back(reagent);
    }

    foreach(ReservateData data, qReservateList)
    {
        foreach(RangePos pos, data.qPosList)
        {
            qDebug()<<"Reagent:"<<data.strName<<" is enough"<<data.bEnough<<
                      " pos column:"<<pos.uiAreaIndex << " row start:"<<pos.uiStartPos <<" row end:"<< pos.uiEndPos;
        }
    }
    qDebug()<<"Reagent subpack tip used:"<<uiUseTipCount;
    return uiUseTipCount;
}

QList<ReservateData> CConsumableReservation::calcConsumableSupplyPos(QList<QString> qSampleInfoList,
                                                                     quint8 uiCatchType/* = CT_DOUBLE*/)
{
    //项目编号1,项目编号2
    QList<ReservateData> qList; //试剂、耗材锁定信息列表
    QMap<QString, int> qProjTestCounts; // 用于记录每个项目名称的测试总数
    QVector<SimpleSysBuildInfo> qSimpleSysBuildInfoVect;
    QList<quint8> qConumeList; //系统构建按序操作的数量的集合
    //解析，并重新组织获取每个系统构建测试的简单信息，获取不同项目测试的样本个数信息
    _parseSampleInfos(qSampleInfoList, qProjTestCounts, qSimpleSysBuildInfoVect);

    quint8 uiSingleOpCount = 0;
    quint8 uiDoubleOpCount = 0;
    //获取系统构建单个抓取及双个抓取的各自操作总数
    _getSysBuildReserveInfo(uiSingleOpCount, uiDoubleOpCount, qConumeList, qSimpleSysBuildInfoVect,uiCatchType);
    //获取系统构建过程所需试剂信息，TIP数量信息，并更新耗材锁定
    quint8 uiReagentTipUseSize = _getReagentReserveInfo(qProjTestCounts, qList);

    //获取PCR Tube 和 PCR cap 锁定的耗材位置信息
    for(int i= CT_MAX -1;i>=1;i--)
    {
        ReservateData data;
        data.uiType = i;
        data.bEnough = Consumables::getInstance().IsConsumableEnough(i, uiSingleOpCount+uiDoubleOpCount*2);
        if(data.bEnough)
            data.qPosList = Consumables::getInstance().virtualConsume(i, qConumeList);
        qList.push_back(data);
    }
    // 提取时，提前转移试剂，造成Tip耗量加倍
    qDebug() << "qConumeList 1" << qConumeList;
    qConumeList << qConumeList;
    qDebug() << "qConumeList 2" << qConumeList;
    //分装试剂所需枪头数据插入到头部
    for(int i=0;i<uiReagentTipUseSize;i++)
    {
        qConumeList.insert(0, 1);
    }
    qDebug() << "qConumeList 3" << qConumeList;
    ReservateData tipData;
    tipData.uiType = CT_TIP;
    tipData.bEnough = Consumables::getInstance().IsConsumableEnough(CT_TIP, (uiSingleOpCount+uiDoubleOpCount*2)*2+uiReagentTipUseSize);
    if(tipData.bEnough)
        tipData.qPosList = Consumables::getInstance().virtualConsume(CT_TIP, qConumeList);
    qList.push_back(tipData);

    // 添加耗材回收信息
    quint8 uiTubeCapCount = uiSingleOpCount + uiDoubleOpCount * 2;                        // 获取PCR Tube 和 PCR cap 锁定的耗材数量
    quint8 uiTipCount     = (uiSingleOpCount + uiDoubleOpCount * 2) * 2 + uiReagentTipUseSize;  // 获取Tip200锁定的耗材数量   
    bool bEnough =  CRecycleBin::frontBin().isResourceEnough(uiTipCount)&CRecycleBin::backBin().isResourceEnough(uiTubeCapCount);
    ReservateData recycleData;
    recycleData.uiType = CT_RECYCLE_BIN;
    recycleData.bEnough = bEnough;
    qList.push_back(recycleData);
    
    // 添加PCR可用孔位信息(需要提前加载有效区域和孔位信息)
    PCRResource::getInstance().LoadValidPcrArea();
    PCRResource::getInstance().LoadValidPcrAreaHole();
    ReservateData pcrData;
    pcrData.uiType = CT_PCR_HOLE;
    pcrData.bEnough = PCRResource::getInstance().IsPCRResourceEnough(qSimpleSysBuildInfoVect.size());
    qList.push_back(pcrData);

    return qList;
}


void CConsumableReservation::_getSysBuildReserveInfo(quint8 &uiSingleOpCount,
                                                quint8 &uiDoubleOpCount,
                                                QList<quint8> &qConumeList,
                                                QVector<SimpleSysBuildInfo> qSimpleSysBuildInfoVect,
                                                quint8 uiCatchType)
{
    //计算单个取用及双个取用的数量
    //排序规则：单个抓取，则每次只抓取一个；双个抓取，有临近的卡条直接双个操作，没有临近的卡条独立操作
    if(CT_SINGLE == uiCatchType)//
    {
        uiSingleOpCount = qSimpleSysBuildInfoVect.size();
        for(int i=0;i<qSimpleSysBuildInfoVect.size();i++)
        {
            QVector<SimpleSysBuildInfo> qVect;
            qVect.push_back(qSimpleSysBuildInfoVect.at(i));
            qConumeList.push_back(1);
        }
    }
    else if(CT_DOUBLE == uiCatchType)
    {
        while(qSimpleSysBuildInfoVect.size()>0)
        {
            quint8 uiOpSize = 0;
            SimpleSysBuildInfo info1 = qSimpleSysBuildInfoVect.at(0);
            if(qSimpleSysBuildInfoVect.size()>1)
            {
                for(int i=1;i<qSimpleSysBuildInfoVect.size();i++)
                {
                    if(qSimpleSysBuildInfoVect.at(i).uiStripIndex == info1.uiStripIndex && (i!=qSimpleSysBuildInfoVect.size()-1)) //非最后一个卡条，遇到相同卡条跳过
                        continue;
                    else if(qSimpleSysBuildInfoVect.at(i).uiStripIndex == (info1.uiStripIndex+1))//遇到相邻卡条，匹配成对操作
                    {
                        //pair
                        uiOpSize = 2;
                        qDebug()<<"Virtual double  Pair elem1[stripindex:"<<info1.uiStripIndex <<",ampIndex:" <<info1.uiCompIndex
                               <<"] elem2[stripindex:"<<qSimpleSysBuildInfoVect.at(i).uiStripIndex<<",ampIndex:"<<qSimpleSysBuildInfoVect.at(i).uiCompIndex<<"]";
                        qSimpleSysBuildInfoVect.removeAt(i);
                        qSimpleSysBuildInfoVect.remove(0);
                        uiDoubleOpCount++;
                        break;
                    }
                    else //整个队列都是相同卡条或者最新元素与当前配对的元素不是相邻卡条，都是独立操作
                    {
                        uiOpSize = 1;
                        qDebug()<<"Virtual single pair elem[stripindex:"<<info1.uiStripIndex<<",ampIndex:"<<info1.uiCompIndex<<"]";
                        qSimpleSysBuildInfoVect.removeAt(0);
                        uiSingleOpCount++;
                        break;
                    }
                }
            }
            else
            {
                uiOpSize = 1;
                qDebug()<<"Virtual single  pair elem[stripindex:"<<info1.uiStripIndex<<",ampIndex:"<<info1.uiCompIndex<<"]";
                qSimpleSysBuildInfoVect.removeAt(0);
                uiSingleOpCount++;
            }
            qConumeList.push_back(uiOpSize);
        }
    }
    else
    {
        qDebug()<<"Error catch Type";
    }
    qDebug()<<"SystemBuild SingleOpCount:"<<uiSingleOpCount<<" SystemBuild DoubleOpCount:"<<uiDoubleOpCount;
}
