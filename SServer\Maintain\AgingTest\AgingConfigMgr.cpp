#include<qdebug.h>
#include<QTime>
#include <QFile>
#include <QDir>
#include"AgingConfigMgr.h"
#include "toml/toml.hpp"

namespace AgingConfigMgrTomlSingleton
{
// 定义在头文件的话，引用的是toml库，编译速度较慢
toml::value config;
toml::value tableChild;// 电机配置子表
};

AgingConfigMgr::AgingConfigMgr()
{
    m_mapAgingProject.clear();
    m_strFilePath = "./ServerConfig/AgingConfig.toml";// 电机配置文件路径
    m_bLoadStatus = false;
    _Init();
    GetAgingActionList();
}

QString AgingConfigMgr::GetStringValue(QString type)
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"MotorConfig.toml file load failed!!! "<<m_bLoadStatus;
        return "";
    }

    QString strRetValue = "";
    auto& table = AgingConfigMgrTomlSingleton::tableChild;
    if (!table.contains(type.toStdString()))
    {
        qDebug() <<"AgingConfigMgr::GetStringValue field not exist"<<type;
        return strRetValue;
    }

    auto value = toml::find<std::string>(table, type.toStdString());
    //strRetValue = QString::fromStdString(value);
    strRetValue = QString::fromUtf8(value.c_str()); //中文改这
    //qDebug() <<"MotorConfigMgr::GetStringValue"<<type<<strRetValue;
    return strRetValue;
}

AgingConfigMgr::~AgingConfigMgr()
{

}

AgingConfigMgr &AgingConfigMgr::getInstance()
{
    static AgingConfigMgr obj;
    return obj;
}


QString AgingConfigMgr::GetAgingActionList()
{
    QStringList strList;
    QString strRs = "";
    AgingOrd tempAgingOrd;
    structOrdParam  tempParamOrd;
    QList<AgingOrd> listAgingord;
    QList<structOrdParam> listParamord;
    listAgingord.clear();
    listParamord.clear();
    strList.clear();

    for(auto it= m_mapAgingProject.begin();it!=m_mapAgingProject.end();++it)
    {
        tempAgingOrd.dOrdIdx  = it.value().dOrdIdx;
        tempAgingOrd.strCNName = it.value().strCNName;
        listParamord.clear();
        for(auto subit= it.value().mapAgingParam.begin();subit!=it.value().mapAgingParam.end();++subit)
        {
            tempParamOrd.strParamName = subit.key();
            tempParamOrd.dOrder = subit.value();
            listParamord.append(tempParamOrd);
        }
        tempAgingOrd.listAgingParamOrder =listParamord;
        listAgingord.append(tempAgingOrd);
    }
    std::sort(listAgingord.begin(), listAgingord.end(), [](const AgingOrd &a, const AgingOrd &b) {
        return a.dOrdIdx < b.dOrdIdx; // 先排列aging的 ord
    });
    for(auto &AgingOrd:listAgingord)
    {
        std::sort(AgingOrd.listAgingParamOrder.begin(), AgingOrd.listAgingParamOrder.end(),
                  [](const structOrdParam &a, const structOrdParam &b) {
            return a.dOrder < b.dOrder; // 排列Param
        });
    }

    for(int i=0;i<listAgingord.size();i++)
    {
        if(i!=0)
        {
            strRs = strRs+"$";
        }
        strRs =strRs+listAgingord[i].strCNName;
        // qDebug()<<"i="<<i<<",order="<<listAgingord[i].dOrdIdx<<"name ="<<listAgingord[i].strCNName;
        for(int j=0;j<listAgingord[i].listAgingParamOrder.size();j++)
        {
            //  qDebug()<<"j="<<j<<",order="<<listAgingord[i].listAgingParamOrder[j].dOrder<<"CNNname ="
            //      <<listAgingord[i].listAgingParamOrder[j].strParamName;
            strRs =strRs+"&"+listAgingord[i].listAgingParamOrder[j].strParamName;
        }
    }
    qDebug()<<"!!!!!!strRs="<<strRs;
    return strRs;
}

QStringList  AgingConfigMgr::GetAgingParamNameList(QString strAgingName)
{
    QStringList strListParamRs;
    structOrdParam  tempParamOrd;
    QList<structOrdParam> listParamord;
    listParamord.clear();
    strListParamRs.clear();

    if (m_mapAgingProject.contains(strAgingName))
    {
        for(auto it= m_mapAgingProject.value(strAgingName).mapAgingParam.begin();
            it!=m_mapAgingProject.value(strAgingName).mapAgingParam.end();++it)
        {
            tempParamOrd.strParamName = it.key();
            tempParamOrd.dOrder = it.value();
            listParamord.append(tempParamOrd);
        }
    }

    std::sort(listParamord.begin(), listParamord.end(), [](const structOrdParam &a, const structOrdParam &b) {
        return a.dOrder < b.dOrder;
    });


    for(int i=0;i<listParamord.size();i++)
    {
       strListParamRs.append(listParamord[i].strParamName);
    }

    return strListParamRs;
}

bool AgingConfigMgr::_Init()
{
    QString strCurrentPath = QDir::currentPath();
    QFile file(m_strFilePath);
    qDebug()<<"strCurrentPath="<<strCurrentPath<<",m_strFilePath="<<m_strFilePath;
    if (!file.exists()) {
        m_bLoadStatus = false;
        qWarning() << "AgingConfig.toml file does not exist" << m_strFilePath;
        return false;
    }
    AgingConfigMgrTomlSingleton::config = toml::parse( m_strFilePath.toStdString());
    m_bLoadStatus = true;
    qDebug()<<"AgingConfig::_Init";
    _InitAgingConfigInfoHash();
    return true;
}

QString AgingConfigMgr::GetAgingCmd(QString strAgingName)
{
    QString strCmd="";
    if (m_mapAgingProject.contains(strAgingName))
    {
        strCmd=m_mapAgingProject.value(strAgingName).strCmd;
        return strCmd;
    }
    return strCmd;
}

void AgingConfigMgr::_readDataToAgingParamInfo(AgingActionCfg &agingInfo)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<AgingParamInfoType>();
    QString strValue = "";
    QString  strParamName="";
    double dParamIdx=-1;
    for (int i = 0; i < metaEnum.keyCount(); i++)
    {
        strValue = GetStringValue(metaEnum.valueToKey(i));
        switch (i)
        {
        case AgingParamInfoType::name:
            strParamName = strValue;
            break;
        case AgingParamInfoType:: subOrdIdx:
            dParamIdx = strValue.toDouble();
            break;
        default:
            break;
        }
    }
    if(dParamIdx!=-1)
    {
        agingInfo.mapAgingParam.insert(strParamName,dParamIdx);
    }

}

void AgingConfigMgr::_readDataToAgingSet(AgingActionCfg &commonInfo)
{
    QString strRetValue = "";

    QMetaEnum metaEnum = QMetaEnum::fromType<AgingCommonSetType>();
    QString strValue = "";

    for (int i = 0; i < metaEnum.keyCount(); i++)
    {
        strValue = GetStringValue(metaEnum.valueToKey(i));
        switch (i)
        {
        case AgingCommonSetType:: ordidx:
            commonInfo.dOrdIdx = strValue.toDouble();
            break;
        case AgingCommonSetType:: CNName:
            commonInfo.strCNName = strValue;
            break;
        }
    }
}

bool AgingConfigMgr::_InitAgingConfigInfoHash()
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"AgingConfig.toml file load failed!!! "<<m_bLoadStatus;
        return false;
    }
    auto& config = AgingConfigMgrTomlSingleton::config;
    QString strField = "";

    // 遍历顶层键，查找所有顶层的表
    for( auto& element : config.as_table())
    {
        if(!element.second.is_table())
        {
            continue;
        }
        auto table = toml::find(config, element.first);

        AgingActionCfg AgingInfo;
        // 访问表，它是顶层表的子表
        for( auto& child : table.as_table())
        {
            strField = QString::fromStdString(child.first.c_str());
            //qDebug()<<"MotorConfigMgr::Found child table: "<<strField;
            if(!child.second.is_table())
            {
                qDebug()<<"AgingConfigMgr error,please check";
                return false;
            }
            AgingConfigMgrTomlSingleton::tableChild = child.second;

            if(strField=="commonSet")
            {
                _readDataToAgingSet(AgingInfo);
                continue;
            }
            _readDataToAgingParamInfo(AgingInfo);
        }
        m_mapAgingProject[AgingInfo.strCNName] = AgingInfo;
    }
    //    for(auto it= m_mapAgingProject.begin();it!=m_mapAgingProject.end();++it)
    //    {
    //        AgingActionCfg temp = it.value();
    //       qDebug()<<"Aging key"<<it.key()<<",Aging value  strCmd ="<<temp.strCmd<<",temp.strCNName="<<temp.strCNName<<
    //                 ",temp.dOrdIdx="<<temp.dOrdIdx;
    //        for(auto itt= temp.mapAgingParam.begin();itt!=temp.mapAgingParam.end();++itt)
    //        {
    //            qDebug()<<"mapAgingParam"<<itt.key()<<",subOrd ="<<itt.value();
    //        }
    //    }
    return true;
}
