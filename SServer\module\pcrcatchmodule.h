#ifndef PCRCATCHMODULE_H
#define PCRCATCHMODULE_H
#include <QObject>
#include "devicemodule.h"

enum PCRCatchTaskID
{
    //--------------------------周期时序动作-----------------------------
    PCTI_SWITCH_TUBE = 0,//轉移PCR管(准备离心)
    PCTI_MIX_TUBE,   //混勻機構並混勻(离心)
    PCTI_OPEN_CAP,//打开PCR盖子
    PCTI_TRANS_TO_PCR_AMPLIFY_AREA,//转移PCR管到擴增區
    PCTI_CLOSE_CAP,//关闭PCR盖子
    PCTI_ABANDON_OPEN_CAP,//丢弃时打开PCR盖子
    PCTI_ABANDON_TUBE,//丢弃PCR管
    PCTI_ABANDON_CLOSE_CAP,//丢弃时关闭PCR盖子
    //--------------------------复位清理动作-------------------------------------
    PCTI_CLEAN_CENTIFUGE_AND_SWITCH,//清理离心区域和转移位
    PCTI_CLEAN_PCR_AREA,//清理PCR区域内所有Tube管,并初始化XYZ
    PCTI_GRIPPER_ABANDON_AND_INIT,//爪子移动到丢弃口爪子复位，电机复位
    PCTI_GRIPPER_INIT,
    PCTI_Z_INIT,
    PCTI_EXCEPT_Z_MOTOR_INIT,
    PCTI_REAMIN_INIT,
};

class PCRCatchModule : public DeviceModule {
    Q_OBJECT

public:
    PCRCatchModule(bool bUseThread = false, quint8 quCatchType = DEVICE_CATCH_TYPE ); // 添加默认值
    void SetCatchType(quint8 quCatchType); // 新增设置 m_quCatchType 变量的函数

    /**
     * @brief GetTaskQueueSize 获取任务队列大小
     * @return 任务队列大小
     */    
    quint8 GetTaskQueueSize(); 

    /**
     * @brief GetCurSubTaskID 获取任务id
     * @return 任务id
     */    
    qint8 GetCurSubTaskID();

    /**
     * @brief SetCurSubTaskID 重置默认id
     * @return 
     */    
    void SetCurSubTaskID();

    /**
     * @brief GetIsBusy 获取pcr抓手组件是否可用
     * @return 是否可用
     */    
    bool GetIsBusy();

    /**
     * @brief SetIsBusy 设置pcr抓手组件是否可用
     * @return 是否可用
     */    
    void SetIsBusy(bool bIsBusy);   

    /**
     * @brief ClearTaskQueue 清空任务队列
     * @return 
     */    
    void ClearTaskQueue();      
public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr) override;
    void SlotAddTask(const CmdTask& task) override;
private:
    void _ProcessSubTask() override;
private:
    quint8 m_uiCatchType;      // 添加成员变量
    qint8 m_iCurSubTaskID = -1;// 当前任务id
    bool m_bIsBusy = false;    // pcr组件是否可用
};
#endif // PCRCATCHMODULE_H
