#include "cftpserverthread.h"
#include <QDebug>
#include <QTime>
#include <QDataStream>
#include <QNetworkProxy>
#include "publicconfig.h"
#include "publicfunction.h"
#include "cglobalconfig.h"
#include "control/coperationunit.h"

CFtpServerThread::CFtpServerThread(QObject *parent) : QObject(parent)
{
    m_pConnectTcpSocket = nullptr;
}
CFtpServerThread::CFtpServerThread(int iPort, QHostAddress strHostIP, QString strHostMac, QObject *parent)
    :  QObject(parent)
    , m_iPort(iPort)
    , m_qHostAddress(strHostIP)
    , m_strHostMacAddress(strHostMac)
{
    // 设置不使用代理
    QNetworkProxy::setApplicationProxy(QNetworkProxy::NoProxy);
    m_iConnected = 0;
    m_pThread = new QThread();
    m_pTCPServer = new QTcpServer();
    m_pConnectTcpSocket = nullptr;
    connect(this, &CFtpServerThread::sigInitServer,
            this, &CFtpServerThread::_slotInitServer, Qt::QueuedConnection);

    m_pTCPServer->moveToThread(m_pThread);
    this->moveToThread(m_pThread);

    m_pThread->start();
    emit sigInitServer();
}

CFtpServerThread::~CFtpServerThread()
{
    if(m_pTCPServer != nullptr)
    {
        m_pTCPServer->close();
        m_pTCPServer->deleteLater();
        m_pTCPServer = nullptr;
    }
    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }
}

void CFtpServerThread::slotReInitServer(QHostAddress qHostAddress, QString strHostMac)
{
    m_qHostAddress = qHostAddress;
    m_strHostMacAddress = strHostMac;
    _toListen();
}

void CFtpServerThread::slotSendFile(QString strFilePathName)
{
    if(m_pConnectTcpSocket != nullptr)
    {
        QFile qFile(strFilePathName);
        if (!qFile.open(QIODevice::ReadOnly)) {
            qWarning() << "Cannot open file to send";
            return;
        }
        // 先发送文件名和文件大小
        QString strHeader = qFile.fileName()
                + gk_strSplitFtpFileName
                + QString::number(quint64(qFile.size()))
                + gk_strSplitFtpFileSize;
        QDFUN_LINE << strHeader
                   << strHeader.toLocal8Bit();

        m_pConnectTcpSocket->write(strHeader.toLocal8Bit());
        m_pConnectTcpSocket->write(qFile.readAll());
        qFile.close();
    }
}

void CFtpServerThread::slot_recvmessage()
{
    QTcpSocket* pSocket = (QTcpSocket*)this->sender();
    if(pSocket != NULL)
    {
        m_byReceivedData.append(pSocket->readAll());
        if(m_byReceivedData.contains(gk_strSplitFtpFileName) && m_byReceivedData.contains(gk_strSplitFtpFileSize))
        {// 每次检测到有特殊分隔符，认为是新的文件传输开始，防止旧的异常没有完成
            QDFUN_LINE << m_byReceivedData;
            int iFileNameIndex = m_byReceivedData.indexOf(gk_strSplitFtpFileName);
            int iFileSizeIndex = m_byReceivedData.indexOf(gk_strSplitFtpFileSize);
            QByteArray byFileName = m_byReceivedData.mid(0, iFileNameIndex);
            m_strFileName = QString::fromLocal8Bit(byFileName);
            m_quiFileSize = m_byReceivedData.mid(iFileNameIndex+gk_strSplitFtpFileName.length(),
                                                 iFileSizeIndex-(iFileNameIndex+gk_strSplitFtpFileSize.length())).toUInt();
            m_byReceivedData.remove(0, iFileSizeIndex+gk_strSplitFtpFileSize.length());
            QDFUN_LINE << iFileNameIndex << iFileSizeIndex << byFileName
                       << "file name: " << m_strFileName  << "file size" << m_quiFileSize;
        }
        QDFUN_LINE << m_byReceivedData.size();
        if(m_quiFileSize > 0 && m_byReceivedData.size() >= m_quiFileSize)
        {
            QFileInfo qFileInfo(m_strFileName);
            QString strSaveFile = CGlobalConfig::getInstance().getFTPFilePath() +
                    qFileInfo.fileName();
            CreateDir(qFileInfo.absolutePath());
            QDFUN_LINE << strSaveFile;
            QFile qFile(strSaveFile);
            if (qFile.open(QIODevice::WriteOnly))
            {
                qFile.write(m_byReceivedData);
                qFile.close();
                qDebug() << "File received and written successfully.";
                m_strFileName = "";
                m_quiFileSize = 0;
                m_bMetaDataReceived = true;
                m_byReceivedData.clear();
                emit sigReciveFileFinished(qFileInfo.fileName());
            }
        }
    }
}

void CFtpServerThread::_toListen()
{
    if(!m_qHostAddress.isNull())
    {
        if(m_pTCPServer->listen(m_qHostAddress, m_iPort))
        {
            connect(m_pTCPServer, &QTcpServer::newConnection, this, &CFtpServerThread::slot_newconnect, Qt::QueuedConnection);
        }
        else
        {
            qDebug() << "Failed to start listening:" << m_pTCPServer->errorString();
            emit sigError(FT_Comm_OpenFail, "");
        }
    }
}

void CFtpServerThread::slot_newconnect()
{
    qDebug() << __func__ << __LINE__ << m_iPort;
    if(m_pTCPServer->hasPendingConnections())  //查询是否有新连接
    {
        qDebug() << __func__ << __LINE__ << m_iPort;
        QTcpSocket* pConnectSocket = m_pTCPServer->nextPendingConnection();
        // 先断开旧的连接及其信号
        if (m_pConnectTcpSocket != nullptr)
        {
            qDebug() << __func__ << __LINE__;
            // 断开与旧socket的所有连接
            m_pConnectTcpSocket->disconnect(this);
            m_pConnectTcpSocket->abort();
            m_pConnectTcpSocket->deleteLater();
            m_pConnectTcpSocket = nullptr;
            qDebug() << __func__ << __LINE__;
        }
        qDebug() << __func__ << __LINE__;
        // 设置新的socket
        m_iConnected = 1;
        m_strFileName = "";
        m_quiFileSize = 0;
        m_bMetaDataReceived = true;
        m_pConnectTcpSocket = pConnectSocket;
        qDebug() << "login ftp " << m_pConnectTcpSocket->peerAddress().toString() << m_pConnectTcpSocket->localPort();
        emit sigNewNetworkConnect(m_pConnectTcpSocket->peerAddress().toString(), m_iPort, true);
        m_pConnectTcpSocket->setReadBufferSize(1024*1000 * 100);
        connect(m_pConnectTcpSocket, &QTcpSocket::readyRead, this, &CFtpServerThread::slot_recvmessage);
        connect(m_pConnectTcpSocket, &QTcpSocket::disconnected, this, &CFtpServerThread::slot_disconnect);
        qDebug() << __func__ << QThread::currentThread();
    }
    qDebug() << __func__ << __LINE__;
}

void CFtpServerThread::slot_disconnect()
{
    QTcpSocket* pConnectSocket = qobject_cast<QTcpSocket*>(this->sender());
    if(pConnectSocket)
    {
        emit sigNewNetworkConnect(pConnectSocket->peerAddress().toString(), m_iPort, false);
        m_iConnected = 0;
        if(pConnectSocket == m_pConnectTcpSocket)
        {
            m_pConnectTcpSocket->deleteLater();
            m_pConnectTcpSocket = nullptr;
            qDebug() << __func__ << __LINE__;
        }
        qDebug() << __func__ << __LINE__;
    }
}
void CFtpServerThread::_slotInitServer()
{ //
    _toListen();
}







