#include "cclientwindow.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
#include "datacontrol/ctiminginfodb.h"
#include "error/cerrornotify.h"

CClientWindow::CClientWindow(QObject *parent) : CWindowObject(parent)
{

}

CClientWindow::~CClientWindow()
{
    m_bThreadExit = true;
}

void CClientWindow::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_pFramePos = qMessage.data() + gk_iSeqPos;
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            m_pFramePos = qMessage.data() + gk_iLengthPos;
            m_iReadPayloadLength = GetByte2Int(m_pFramePos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");
            // CGlobalConfig::getInstance().printMessageInfo(qMessage,
            //                                               "[client->server] ");

            // qDebug() << "CClientWindow::_HandleReceiveList" << m_iMethodID;
            const quint16 m_iFaultSimulatedID = 50000;// 故障模拟测试ID，上位机触发故障使用
            if (m_iMethodID != m_iFaultSimulatedID)
            {
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, m_iDestinationID);
            }

            switch (m_iMethodID)
            {
                case m_iFaultSimulatedID:
                _HandleFaultSimulatedID(m_qPayloadString);
                break;
            default:
                break;
            }
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}

void CClientWindow::_HandleFaultSimulatedID(const QString& strPayload)
{
    qDebug() << "CClientWindow::_HandleFaultSimulatedID" << strPayload;
    if (strPayload.isEmpty())
    {
        return;
    }
    
    QStringList strItems = strPayload.split(",");
    if (strItems.size() < 2)
    {
        qDebug() << "CClientWindow::_HandleFaultSimulatedID strItems.size" << strItems.size();
        return;
    }
    
    // 获取module、error等ID
    MidMachineSubmodule uiModuleID = static_cast<MidMachineSubmodule>(strItems[0].toUInt());
    ErrorID uiErrorID = static_cast<ErrorID>(strItems[1].toUInt());;
    QString strFaultMsg = strItems[2];// 如果数据库有对应的故障信息，忽略这部分填充数据
    // 故障模拟，此处可以模拟故障，并将结果返回给上位机
    CErrorNotify::getInstance().addErrorInfoItem(uiModuleID, uiErrorID,strFaultMsg);
}