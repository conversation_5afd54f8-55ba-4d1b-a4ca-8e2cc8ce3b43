#include "caffairobject.h"
#include <QDebug>
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"

CAffairObject::CAffairObject(quint16 uiUnit, QObject *parent)
    : QThread(parent)
    , m_uiUnit(uiUnit)
{
    m_bThreadExit = true;
    this->initUnitData();
}

CAffairObject::~CAffairObject()
{
    m_bThreadExit = false;
}

void CAffairObject::slotAddReciveMsg(QByteArray qMsgBtyeArray)
{
    m_iCurrentWriteIndex_Profix = m_iWriteIndex_Profix.load();
    m_iNextWriteIndex_Profix = (m_iCurrentWriteIndex_Profix + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex_Profix == m_iReadIndex_Profix.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CAffairObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qProfixInfoList[m_iCurrentWriteIndex_Profix] = qMsgBtyeArray;
    m_iWriteIndex_Profix.store(m_iNextWriteIndex_Profix);
}

void CAffairObject::slotAddReciveCondition(quint16 quCondition)
{
    m_iCurrentWriteIndex_Condition= m_iWriteIndex_Condition.load();
    m_iNextWriteIndex_Condition= (m_iCurrentWriteIndex_Condition+ 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex_Condition== m_iReadIndex_Condition.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CAffairObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_uiConditionInfoList[m_iCurrentWriteIndex_Condition] = quCondition;
    m_iWriteIndex_Condition.store(m_iNextWriteIndex_Condition);
}

void CAffairObject::run()
{
    while(m_bThreadExit)
    {
        if(this->m_iReadIndex_Profix.load() != this->m_iWriteIndex_Profix.load()
                || this->m_iReadIndex_Condition.load() != this->m_iWriteIndex_Condition.load())
        {
            this->_HandleReceiveList();
        }
        else
        {
            msleep(1);
        }
    }
}

void CAffairObject::_HandleReceiveList()
{
    while (m_iReadIndex_Condition.load() != m_iWriteIndex_Condition.load())
    {
        quint16& uiMessage = m_uiConditionInfoList[m_iReadIndex_Condition.load()];
        // TO DO
        if(uiMessage >= 0 && uiMessage < CONDITION_SIZE)
        {
            // TO DO
            // 如果是第一个动作，判定前置队列m_qProfixInfoList
            if(uiMessage == 0)
            { // 如果序号为0，判定前置队列
                if (m_iReadIndex_Profix.load() != m_iWriteIndex_Profix.load())
                {
                    QByteArray& qMessage = m_qProfixInfoList[m_iReadIndex_Profix.load()];
                    // TO DO
                    // 环形队列
                    m_iReadIndex_Profix.store((m_iReadIndex_Profix.load() + 1) % BUFFER_SIZE);
                }
            }
            else
            {//
                QList<SUnitAffair> sSUnitAffairList = m_sUnitAffairList[uiMessage];
                for(auto sUnitAffair : sSUnitAffairList)
                {
                    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(sUnitAffair.strComplexID);
                    COperationUnit::getInstance().sendStringData(Method_comp_cmd, sUnitAffair.strComplexID, iMachineID);
                }
            }
        }

        // 环形队列
        m_iReadIndex_Condition.store((m_iReadIndex_Condition.load() + 1) % BUFFER_SIZE);
    }
}

void CAffairObject::initUnitData()
{
    QString strProcessContent = CTimingInfoDB::getInstance().getProcessContentFromName("");// 获得单元流程内容
    QStringList strProcessContentList = strProcessContent.split(";");
    QStringList strContentList;
    SUnitAffair sUnitAffair;
    for(auto strContent : strProcessContentList)
    {
        strContentList = strContent.split(",");
        if(strContentList.length() >= 3)
        {
            sUnitAffair.uiUnitName = strContentList[0].toUInt();
            sUnitAffair.uiUnitIndex = strContentList[1].toUInt();
            sUnitAffair.strComplexID = strContentList[2].split("_").at(0);
            if(strContentList.length() >= 4)
            {
                sUnitAffair.strComplexParam = strContentList[3];
            }
            if(sUnitAffair.uiUnitIndex >= 0 && sUnitAffair.uiUnitIndex < CONDITION_SIZE)
            {
                m_sUnitAffairList[sUnitAffair.uiUnitIndex].append(sUnitAffair); // 状态序号对应的复合指令
            }
        }
    }
}








