#ifndef SYSTEMCONFIG_H
#define SYSTEMCONFIG_H

#include<QObject>
#include<QMap>
#include <QMetaEnum> 

class SystemConfig : public QObject
{
    Q_OBJECT
public:
    static SystemConfig &getInstance();

    enum EnumConfigFieldType
    {
        network,             // 网络配置
        reset,               // 复位配置
        strip,               // 提取条配置
        can,                 // can配置
    };
    Q_ENUM( EnumConfigFieldType) 

    enum EnumNetworkType
    {
        local,               // 是否使用本地网络
        eth0,                // 优先使用有线网络
        disconnected_tip,    // 上位机断开链接后，中位机是否缓存时序执行结果(重连后继续上报)
    };
    Q_ENUM( EnumNetworkType) 

    enum EnumPcrType
    {
        pcr_disable_area,         // pcr禁用区域
        pcr_disable_area_hole,    // pcr禁用孔位
    };
    Q_ENUM( EnumPcrType) 

    enum EnumResetType
    {
        check_period,        // 延时启动定时器时间
        send_period,         // 发送单指令间隔时间
    };
    Q_ENUM( EnumResetType) 

    enum EnumStripType
    {
        volume,               // 容积默认使用700
        pos,                  // 默认使用清洗3位置
        mix_level,            // 混合速度默认使用7
        mix_time,             // 混合时间(S)     
    };
    Q_ENUM( EnumStripType) 

    enum EnumCanConfigType
    {
        bit_rate,               // 波特率默认使用500  
    };
    Q_ENUM( EnumCanConfigType)     
public:
    /**
     * @brief GetStringValue 获取字符串配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    QString GetStringValue(QString field,QString type);

    /**
     * @brief GetStringValue 获取字符串配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    void GetStringValue(QString& value,QString field,QString type);

    /**
     * @brief GetBoolValue 获取布尔配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    bool GetBoolValue(QString field,QString type);

    /**
     * @brief GetBoolValue 获取布尔配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    bool GetBoolValue(EnumConfigFieldType field,EnumNetworkType type);

    /**
     * @brief GetIntValue 获取整形配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    int GetIntValue(EnumConfigFieldType field,EnumResetType type);

    /**
     * @brief GetMetaEnumFiledString 获取枚举对应的字符串
     * @param metaField     枚举信息
     * @return  枚举对应的字符串
     */
    QString GetMetaEnumFiledString(const QMetaEnum metaField,const quint8 type);

    /**
     * @brief Load 加载配置文件
     * @return  加载状态
     */
    bool Load();

signals:

public slots:

private:
  SystemConfig();
  ~SystemConfig();
private:
   QString m_strFilePath; // 配置文件路径
   bool    m_bLoadStatus; // 配置文件加载状态
};


#endif // SYSTEMCONFIG_H
