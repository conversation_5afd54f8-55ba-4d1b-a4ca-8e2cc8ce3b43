#ifndef CPCRRESPERSISTER_H
#define CPCRRESPERSISTER_H
#include <QObject>
#include <QList>
#include "datacontrol/CSystemDB.h"
#include "publicconfig.h"

struct PCRResInfo
{
    quint8 uiPCRST;//PCR状态
    quint8 uiRowIndex;//当前所在行列
    quint8 uiColumnIndex;//当前可用列
    quint8 uiPos;//当前位置
    quint8 uiRemain;//剩余数量
    quint8 uiCapacity;//总容量
    quint8 uiNextSinglePos;//下一个单测试可用位置
    quint8 uiNextDoublePos;//下一个双测试可用位置
    QString strTecName;//所使用的TEC时序名称
    QString strBatchNo;//批次号

    QString printInfo() const {
        QString info;
        info = QString("PCRResInfo[uiPCRST:") + QString::number(uiPCRST) +
        ", uiRowIndex:" + QString::number(uiRowIndex) +
        ", uiColumnIndex:" + QString::number(uiColumnIndex) +
        ", uiPos:" + QString::number(uiPos) +
        ", uiRemain/uiCapacity:" + QString::number(uiRemain) + "/" + QString::number(uiCapacity) +
        ", uiNextSinglePos:" + QString::number(uiNextSinglePos) +
        ", uiNextDoublePos:" + QString::number(uiNextDoublePos) +
        ", strTecName:'" + strTecName + 
        "', strBatchNo:'" + strBatchNo + "']";
        return info;
    }
};

class CPCRResPersister : public QObject
{
    Q_OBJECT
public:
    explicit CPCRResPersister(QObject *parent = nullptr);
    ~CPCRResPersister();

    // 保存单个PCRRes信息
    bool savePCRRes(int index, const PCRResInfo &info);
    // 获取指定索引的PCRRes信息
    PCRResInfo getPCRRes(int index);

    // 批量保存PCRRes信息列表
    bool saveAllPCRRes(const QList<PCRResInfo> &infos);
    // 获取所有PCRRes信息列表
    QList<PCRResInfo> getAllPCRRes();

    //结构体转JSON字符串
    PCRResInfo jsonToPCRInfo(const QString &jsonStr);
    //JSON字符串转结构体
    QString pcrInfoToJson(const PCRResInfo &info);
private:
    CSystemDB *m_dbPtr;
};

#endif // CPCRRESPERSISTER_H
