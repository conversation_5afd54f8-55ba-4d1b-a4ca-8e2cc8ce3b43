#ifndef CERRORNOTIFY_H
#define CERRORNOTIFY_H

#include<QObject>
#include<QThread>
#include<QMap>
#include<atomic>
#include<QMutex>
#include"publicconfig.h"
#include "errorconfig.h"

/**
 * @brief The CErrorNotify class
 * 中位机异常通知上报类，所有中位机异常可通过调用addErrorInfoItem 添加新异常，独立的日志通知线程会发送给中位机及上位机
 */
class CErrorNotify : public QThread
{
    Q_OBJECT
public:
    static CErrorNotify &getInstance();

signals:
public :
    void addErrorInfoItem(MidMachineSubmodule subModule, ErrorID errorID, QString strExtraInfo);
    void addErrorInfoItem(QString strErrorCode,  QString strExtraInfo);

protected:
    virtual void run();
    virtual void _HandleReceiveList();

private:
    explicit CErrorNotify(QObject *parent = nullptr);
    ~CErrorNotify();

protected:
    bool m_bThreadExit;
    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    ErrorInfoItem m_qErrorInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;

};
#endif // CERRORNOTIFY_H
