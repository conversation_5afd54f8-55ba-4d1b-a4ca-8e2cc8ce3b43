#include "caffaircomplexcompose.h"
#include <QDebug>
#include "control/coperationunit.h"

CAffairComplexCompose::CAffairComplexCompose(QObject *parent)
    : CAffairBase(parent)
    , m_bIsRunning(false)
    , m_bStop(false)
{
    this->m_bWait.store(true);
    connect(this, &CAffairComplexCompose::SigStopTimeoutCheck, this,
            &CAffairComplexCompose::SlotStopTimeoutCheck);
}

CAffairComplexCompose::~CAffairComplexCompose()
{

}

void CAffairComplexCompose::startComplexCompose(QString strContent)
{
    QMutexLocker locker(&m_qMutex);
    if(m_bIsRunning)
    {
        qDebug() << " is running, please wait later!";
        return;
    }
    m_sContentSourceList.clear();
    QStringList strComplexList = strContent.split(";");
    m_iCircleTestCnt = 0;
    if(strComplexList.size()>=2) //最后一个是次数
    {
        m_iCircleTestCnt = strComplexList[strComplexList.size()-1].toInt();
    }
    _translateCmd(strComplexList, m_sContentSourceList);

    m_iContentSourceListLength = m_sContentSourceList.length() ;
    if(m_iContentSourceListLength > 0)
    {
        m_bIsRunning = true;
        m_bStop = false;
        this->m_bWait.store(false);
        m_iCurrentIndex = 0;
        m_qSyncConditonMap.clear();
        m_conditionVariable.notify_one();// 唤醒
        QDFUN_LINE << m_iContentSourceListLength ;
    }
    else
    {
        QDFUN_LINE << "error content" << strContent;
    }
}

void CAffairComplexCompose::stopComplexCompose()
{
    QMutexLocker locker(&m_qMutex);
    m_bStop = true;
    emit SigStopTimeoutCheck();
}

bool CAffairComplexCompose::getIsRunning()
{
    QMutexLocker locker(&m_qMutex);
    return m_bIsRunning;
}

void CAffairComplexCompose::setFinished()
{
    QMutexLocker locker(&m_qMutex);
    m_sContentSourceList.clear();
    m_bIsRunning = false;
    m_bStop = false;
    m_iCurrentIndex = 0;
    m_iCircleTestCnt = 0; //重置为0
    m_qSyncConditonMap.clear();
    QDFUN_LINE << "finihed";
}

void CAffairComplexCompose::SlotStopTimeout()
{
    QMutexLocker locker(&m_qMutex);
    if(m_bIsRunning)
    {
        m_bIsRunning = false;
        COperationUnit::getInstance().sendResult(Method_complex_compose_stop, Machine_UpperHost, 0);
        QDFUN_LINE << "motor stop timeout failed,but middleware is ready for next start.";
    }
}

void CAffairComplexCompose::SlotStopTimeoutCheck()
{
    QTimer::singleShot(5000, this, &CAffairComplexCompose::SlotStopTimeout);
}

void CAffairComplexCompose::run()
{
    qDebug() << "Starting _createConcurrentThread in" << this;
    std::unique_lock<std::mutex> uniqueLock(this->m_mutex);
    while(!this->m_bThreadExit)
    {
        this->m_conditionVariable.wait(uniqueLock, [this]
        {
            return !this->m_bWait.load()
                    || this->m_bThreadExit;
        });
        if (this->m_bThreadExit)
        {
            break;
        }
        this->_HandleReceiveList();
    }
}

void CAffairComplexCompose::_HandleReceiveList()
{
    QMutexLocker locker(&m_qMutex);
    for(auto it = m_qSyncConditonMap.constBegin(); it != m_qSyncConditonMap.constEnd(); ++it)
    {
        QDFUN_LINE << "m_qSyncConditonMap " << it.key() <<it.value();
        if(!it.value())
        {// 并行等待条件还有需要等待的指令
            this->m_bWait.store(true);
            return;
        }
    }
    // 全部条件完成，清除
    if(m_qSyncConditonMap.count())
    {
        m_qSyncConditonMap.clear();
    }
    QDFUN_LINE << m_iCurrentIndex << m_iContentSourceListLength;

    if(m_iCurrentIndex >= 0 && m_iCurrentIndex < m_iContentSourceListLength)
    {
        m_sCurrentContent = m_sContentSourceList.at(m_iCurrentIndex);
        QDFUN_LINE << m_sCurrentContent.strComplexID
                   << m_sCurrentContent.strComplexContent;
        COperationUnit::getInstance().sendStringData(
                    Method_comp_cmd, m_sCurrentContent.strComplexContent,
                    m_sCurrentContent.iMachineID);
        while(m_sCurrentContent.quSync)
        {//
            m_qSyncConditonMap.insert(m_sCurrentContent.strComplexID, false);// 插入并行条件
            m_iCurrentIndex++;
            if(m_iCurrentIndex < m_iContentSourceListLength)
            {// 发送下一条并行
                m_sCurrentContent = m_sContentSourceList.at(m_iCurrentIndex);
                COperationUnit::getInstance().sendStringData(
                            Method_comp_cmd, m_sCurrentContent.strComplexContent,
                            m_sCurrentContent.iMachineID);
                m_qSyncConditonMap.insert(m_sCurrentContent.strComplexID, false);// 插入并行条件
            }
            else
            {// 结尾
                break;
            }
        }
        QDFUN_LINE << "m_qSyncConditonMap while" << m_qSyncConditonMap.keys()
                   << m_qSyncConditonMap.values();
        m_iCurrentIndex++;
        //计算一个循环需要执行多少条指令
        int iSingleCmdCnt = m_sContentSourceList.size() / m_iCircleTestCnt;
        if(m_iCurrentIndex % iSingleCmdCnt == 0) //完成一个循环到指令可以发送单个老化成功
        {
            int iRemainCnt  = m_iCircleTestCnt - m_iCurrentIndex / iSingleCmdCnt;
            QDFUN_LINE << "Singel Test run finished,iRemainCnt="<<QString::number(iRemainCnt);
            COperationUnit::getInstance().sendResultList(Method_complex_compose_run, QStringList({QString::number(iRemainCnt)}), Machine_UpperHost);
        }
    }
    else
    {// run finished
        QDFUN_LINE << "run finished";
        m_iCurrentIndex = 0;
        m_iCircleTestCnt =0;
        m_iContentSourceListLength = 0;
        m_sContentSourceList.clear();
        m_bIsRunning = false;

    }
    this->m_bWait.store(true);
}

void CAffairComplexCompose::slotReciveComplexResult(QString strComplexID)
{// 等待业务动作完成决定是否唤醒执行之后动作指令
    QMutexLocker locker(&m_qMutex);
    if(strComplexID.isEmpty())
    {
        QDFUN_LINE << strComplexID;
        return;
    }
    if(m_bStop)
    {
        QDFUN_LINE << m_bStop;
        m_bIsRunning = false;
        COperationUnit::getInstance().sendResult(Method_complex_compose_stop, Machine_UpperHost);
        return;
    }

    if(m_qSyncConditonMap.contains(strComplexID))
    {
        m_qSyncConditonMap[strComplexID] = true;
    }
    QDFUN_LINE << "m_qSyncConditonMap revice" << m_qSyncConditonMap.keys()
               << m_qSyncConditonMap.values();
    this->m_bWait.store(false);
    m_conditionVariable.notify_one();// 唤醒
}



void CAffairComplexCompose::_packageSubCicrleCmd( QList<SContentStruct>  sOriginContentSourceList,int iSubStartIdx,int iSubEndIdx,QList<SContentStruct>&sPackageCmd)
{
    if(iSubStartIdx<0 || iSubEndIdx>sOriginContentSourceList.size() ||  iSubEndIdx -  iSubStartIdx ==1)
    {
        qDebug()<<__FUNCTION__<<"Param Error"<<"iSubStartIdx ="<<iSubStartIdx<<",iSubEndIdx ="<<iSubEndIdx<<",Size ="<<sOriginContentSourceList.size();
        return;
    }
    //取出参数
    QString strSubCircleStartParam,strSubCircleEndParam;
    strSubCircleStartParam= sOriginContentSourceList[iSubStartIdx].strComplexContent;
    strSubCircleEndParam=sOriginContentSourceList[iSubEndIdx].strComplexContent;
    //第一个是序号，去掉
    QStringList strContentListSubStartList = strSubCircleStartParam.split(",");
    QStringList strContentListSubEndList  = strSubCircleEndParam.split(",");

    //有可能是没参数的，长度是1
    if(strContentListSubStartList.size() >2  || strContentListSubEndList.size()>2 )
    {
        qDebug()<<__FUNCTION__<<"Param Error"<<"strContentListSubStart.size ="<<strContentListSubStartList.size() <<",strContentListSubStart.size ="<<strContentListSubEndList.size();
        return;
    }

    QString strParamData=strContentListSubStartList.size()==2? strContentListSubStartList[1]:"";  //没有参数则取空
    int iSubCircleCnt = strContentListSubEndList.size()==2? strContentListSubEndList[1].toInt(): 1;  //没参数就循环1次

    //取出开始循环到参数组合机构
    SSubCircleInfoStruct sSSubCircleInfoStruct;
    QStringList strParamListSplitMoney = strParamData.split("$");
    QStringList strParamListSplitColon;  //colon 冒号
    QStringList strParamListSplitAnd;
    sSSubCircleInfoStruct.iUseCnt =0;
    sSSubCircleInfoStruct.bUseInCurCircle =false;
    QVector<SSubCircleInfoStruct> SubCircleInfoVector;
    for(int i=0;i<strParamListSplitMoney.size();i++)  //     strParamList[x]  形如    m0:0&1
    {
        strParamListSplitColon = strParamListSplitMoney[i].split(":");
        if(strParamListSplitColon.size()==2)
        {
            strParamListSplitAnd = strParamListSplitColon[1].split("&");
            if(strParamListSplitAnd.size()==2)
            {
                sSSubCircleInfoStruct.strParamName = strParamListSplitColon[0];
                sSSubCircleInfoStruct.iInitVal=strParamListSplitAnd[0].toInt();
                sSSubCircleInfoStruct.iStepVal=strParamListSplitAnd[1].toInt();
                SubCircleInfoVector.push_back(sSSubCircleInfoStruct);
            }
        }
    }

    SContentStruct SContentStructTemp;
    int iFitOneName = 0;
    int iReplacVal;
    for(int iCircle=0;iCircle<iSubCircleCnt;iCircle++)
    {
        //已经用过到，在下一次循环更新值
        for(int i =0;i<SubCircleInfoVector.size();i++)
        {
            if(SubCircleInfoVector[i].bUseInCurCircle==true)
            {
                SubCircleInfoVector[i].bUseInCurCircle=false;
                SubCircleInfoVector[i].iUseCnt++;  //累计1
            }
        }

        for(int iIdx =iSubStartIdx+1; iIdx<= iSubEndIdx-1;iIdx++)
        {
            SContentStructTemp = sOriginContentSourceList[iIdx];
            //替换strComplexContent 的 M1 或者M2的值; m1,m2的设置格式为“m1:起始值&迭代变化量$m2:起始值&迭代变化量.....$m_n:起始值&迭代变化量”
            iFitOneName = 0;
            for(int i = 0;i<SubCircleInfoVector.size();i++)
            {
                if(SContentStructTemp.strComplexContent.contains(SubCircleInfoVector[i].strParamName))
                {
                    iReplacVal = SubCircleInfoVector[i].iInitVal  + SubCircleInfoVector[i].iStepVal *SubCircleInfoVector[i].iUseCnt;
                    SubCircleInfoVector[i].bUseInCurCircle = true;  //  使用标志设置

                    SContentStructTemp.strComplexContent =
                            SContentStructTemp.strComplexContent.replace(SubCircleInfoVector[i].strParamName, QString::number(iReplacVal));

                    sPackageCmd.push_back(SContentStructTemp);

                    iFitOneName =1;
                }
            }
            if(iFitOneName ==0)  //没有找到，所以是没有变量的，原始压入
            {
                sPackageCmd.push_back(SContentStructTemp);
            }
        }

    }

    return;
}

void CAffairComplexCompose::_translateCmd(QStringList strInputList, QList<SContentStruct> &sContentSourceListRs)
{
    sContentSourceListRs.clear();
    QList<SContentStruct> sOriginContentList;
    QList<SContentStruct> sPackageContentList;
    for(int i=0;i<strInputList.size()-1;i++)
    {
        QStringList strContentList = strInputList[i].split(",");
        if(strContentList.length() >= 4)
        {
            SContentStruct sSContentStruct;
            sSContentStruct.iMachineID = strContentList[0].toInt();
            sSContentStruct.strComplexID = strContentList[1] ;
            sSContentStruct.strComplexContent = strContentList[1] + "," + strContentList[2];
            if(sSContentStruct.strComplexContent.contains('&') &&sSContentStruct.strComplexID !="-1" )  //循环开始到不提换参数
            {
                sSContentStruct.strComplexContent =
                        sSContentStruct.strComplexContent.replace("&", ",");
            }
            sSContentStruct.quSync = strContentList[3].toInt();
            sOriginContentList.push_back(sSContentStruct);
        }
    }
    int iIsCircle =0;
    int iStartIdx ,iEndIdx;
    for(int i=0;i<sOriginContentList.size();i++)
    {
        iIsCircle =0;
        if(sOriginContentList[i].strComplexID  =="-1")//开始
        {
            for(int j=i;j<sOriginContentList.size();j++)
            {
                if(sOriginContentList[j].strComplexID =="0")//结束
                {
                    iStartIdx = i;
                    iEndIdx = j;
                    iIsCircle = 1;
                    break;
                }
            }
            if(iIsCircle ==0)
            {
                qDebug()<<__FUNCTION__<<"can't Find End";
                return ;//没有找到结束，返回
            }
        }
        if(iIsCircle ==1)
        {
            QList<SContentStruct> sPackageCmd;
            _packageSubCicrleCmd(sOriginContentList,iStartIdx,iEndIdx,sPackageCmd);
            for(int iCmd=0;iCmd<sPackageCmd.size();iCmd++)
            {
                sPackageContentList.push_back(sPackageCmd[iCmd]);
            }
            //最后 i值要更改
            i = iEndIdx;
        }

        if(iIsCircle ==0)
        {
            sPackageContentList.push_back(sOriginContentList[i]);
        }

    }

    for(int iCircleIdx = 0;iCircleIdx<m_iCircleTestCnt;iCircleIdx++)
    {
        for(int iCmd = 0;iCmd<sPackageContentList.size();iCmd++)
        {
            m_sContentSourceList.push_back(sPackageContentList[iCmd]);
        }
    }

}



