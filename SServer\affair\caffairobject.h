#ifndef CAFFAIROBJECT_H
#define CAFFAIROBJECT_H

#include <QObject>
#include <QThread>
#include <atomic>
#include "publicconfig.h"

class CAffairObject : public QThread
{
    Q_OBJECT
public:
    explicit CAffairObject(quint16 uiUnit, QObject *parent = nullptr);
    ~CAffairObject();

    struct SUnitAffair
    {
        quint16 uiUnitName;
        quint16 uiUnitIndex;
        QString strComplexID;
        QString strComplexParam;
    };

public slots:
    void slotAddReciveMsg(QByteArray qMsgBtyeArray);
    void slotAddReciveCondition(quint16 quCondition);

protected:
    virtual void run();
    virtual void _HandleReceiveList();
    virtual void initUnitData();

protected:
    bool m_bThreadExit;
    quint16 m_uiUnit;//
    //
    QList<SUnitAffair> m_sUnitAffairList[CONDITION_SIZE];

    //
    QByteArray m_qProfixInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex_Profix{0};
    std::atomic<int> m_iReadIndex_Profix{0};
    int m_iNextWriteIndex_Profix;
    int m_iCurrentWriteIndex_Profix;
    //
    quint16 m_uiConditionInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex_Condition{0};
    std::atomic<int> m_iReadIndex_Condition{0};
    int m_iNextWriteIndex_Condition;
    int m_iCurrentWriteIndex_Condition;

};

#endif // CAFFAIROBJECT_H
