#include "consumables.h"
#include <QDate>
#include <QDebug>
#include "cglobalconfig.h"
#include "error/cerrornotify.h"
#include"./RFID/CRFIDMotionTask.h"
#include"./RFID/CRFIDCtrl.h"
#include "control/coperationunit.h"
#include "HalSubSystem/HalSubSystem.h"

QString kConsNames[CT_MAX] = {
    "TIP",
    "CAP",
    "TUBE"
};

Consumables::Consumables()
{
    m_qMutex.lock();
    for(int i=0;i<CT_MAX;i++)
    {
        m_uiCatchType[i] = DEVICE_CATCH_TYPE ;
        m_curUsedBox[i] = nullptr;
        m_curUsedListIndex[i] = 0;
    }
    m_uiCapacityInfo[CT_TIP][0] = TIP_ROW_SIZE;
    m_uiCapacityInfo[CT_TIP][1] = TIP_COLUMN_SIZE;
    m_uiCapacityInfo[CT_CAP][0] = CAP_ROW_SIZE;
    m_uiCapacityInfo[CT_CAP][1] = CAP_COLUMN_SIZE;
    m_uiCapacityInfo[CT_TUBE][0] = TUBE_ROW_SIZE;
    m_uiCapacityInfo[CT_TUBE][1] = TUBE_COLUMN_SIZE;
    m_qMutex.unlock();
//    _Init();
}

Consumables &Consumables::getInstance()
{
    static Consumables consumables;
    return consumables;
}

void Consumables::_Init()
{
    QString strBatchArray[CT_MAX] = {"WTIP240327","WTUBE240327","WCAP240327"};
    ConsumableBox consumableBox = {};
    consumableBox.uiState = CBST_VALID;//盒的状态
    consumableBox.qExpDate = QDate::currentDate().addDays(365);//有效期
    consumableBox.uiCRC = 0;//校验

    for(int j =0;j<CT_MAX;j++)
    {
        consumableBox.strBatchNo = strBatchArray[j];//批号
        consumableBox.iRemain = m_uiCapacityInfo[j][0]*m_uiCapacityInfo[j][1];//剩余次数
        consumableBox.uiCapacity = m_uiCapacityInfo[j][0]*m_uiCapacityInfo[j][1];//总份数
        consumableBox.uiType = j;//卡盒类型
        consumableBox.uiNextSingleAvrPos = 1;//下一个抓取单个耗材的位置
        consumableBox.uiNextDoubleAvrPos = consumableBox.uiCapacity;//下一个抓取两个耗材的位置
        for(int i=0;i<2;i++)
        {
            consumableBox.strSeqNo = QString("%1").arg(i+1);//序列号
            AddConsumable(consumableBox.uiType,i,consumableBox);
        }
    }
    qDebug()<<"Consumable Init";
}

void Consumables::_UpdateStatus(ConsumableBoxState status, quint8 uiType,RFIDConsumableType cType)
{    
    // ElecMagneticLock::EnumLockType type = ElecMagneticLock::Tip;
    // if(CT_TIP != uiType )//TIP头是用一个电磁锁，PCR管和管帽用一个电磁锁
    // {
    //     type = ElecMagneticLock::PCR;
    // }
    // // 需要亮灯和解锁
    // switch (status)
    // {
    // case CBST_VALID://可用
    //     // if (m_bSystemBuildBusyStatus)
    //     {
    //         HalSubSystem::getInstance().SetRFIDElecMagneticLock(ElecMagneticLock::Lock,type,cType);
    //     }
    //     break;
    // case CBST_EMPTY://空
    // case CBST_INVALID://不可用
    //     HalSubSystem::getInstance().SetRFIDElecMagneticLock(ElecMagneticLock::Unlock,type,cType);
    //     break;            
    // default:
    //     break;
    // }
    // qDebug() << "0:Lock 1/2:Unlock" << status << "0:Tip 1:Tupe 2:Cap" << uiType << "index" << cType;
}

void Consumables::SetCatchType(quint8 uiType, quint8 uiCatchType)
{
    QMutexLocker locker(&m_qMutex);
    m_uiCatchType[uiType] = uiCatchType;
}

bool Consumables::GetNextAvrConsumable(quint8 uiType, quint8 &uiRowIndex, quint8 &uiColumnIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    QList<quint8> qList = m_qConsumableList[uiType];
    qDebug()<<"Consumables::GetNextAvrConsumable qList"<<qList.size()<<uiType;
    if(qList.size()>0)
    {
        QMap<quint8, ConsumableBox>* pValueMap = &m_qConsumableMapList[uiType];
        qDebug()<<"Consumables::GetNextAvrConsumable pValueMap"<<pValueMap->size();
        if(pValueMap)
        {
            quint8 uiIndex = 0;
GET_NEXT:
            if (uiIndex >= qList.size())
            {
                qWarning()<<"Consumables::GetNextAvrConsumable uiIndex error"<<qList.size()<<uiIndex;
                return bResult;
            }
            
            QMap<quint8, ConsumableBox>::iterator itor = pValueMap->find(qList.at(uiIndex));
            if(itor != pValueMap->end())
            {
                ConsumableBox* pBox = &itor.value();
                if(pBox)
                {
                    qDebug()<<"Consumables::GetNextAvrConsumable: "<<pBox->iRemain<<m_uiCatchType[uiType];
                    quint8 uiPos = 0;
                    if(pBox->iRemain>0)
                    {
                        if(m_uiCatchType[uiType] == CT_SINGLE)
                        {
                            uiPos = pBox->uiNextSingleAvrPos;
                        }
                        else if(m_uiCatchType[uiType] == CT_DOUBLE)
                        {
                            if(pBox->iRemain>1)
                            {
                                uiPos = pBox->uiNextDoubleAvrPos;
                            }
                            else
                            {
                                if(qList.size()>1)
                                {
                                    uiIndex++;
                                    goto GET_NEXT;
                                }
                                else
                                {
                                    return bResult;
                                }
                            }
                        }
                    }
                    else //余量不足
                    {
                        if(qList.size()>1)
                        {
                            uiIndex++;
                            goto GET_NEXT;
                        }
                        else
                        {
                            return bResult;
                        }
                    }
                    m_curUsedBox[uiType] = pBox;
                    m_curUsedListIndex[uiType] = uiIndex;
                    int iRemainPos = pBox->uiCapacity - uiPos;
                    if(iRemainPos>=0)
                    {
                        uiRowIndex = (iRemainPos)/m_uiCapacityInfo[uiType][1];
                        uiColumnIndex = (iRemainPos)%m_uiCapacityInfo[uiType][1];
                        qDebug()<<"m_uiCapacityInfo: "<<iRemainPos<<m_uiCapacityInfo[uiType][1]<<uiRowIndex<<uiColumnIndex;
                    }
                    else
                    {
                        qDebug()<<"Get Consumables error: "<<uiType;
                    }
                    qDebug()<<"--GetNextAvrConsumable---UIType:"<<uiType<<"index"<<qList.at(uiIndex)<<"catchType"<<m_uiCatchType[uiType]<<"pos"<<uiPos
                           <<"row:"<<uiRowIndex<<"columnIndex: "<<uiColumnIndex<<"uiIndex: "<<uiIndex<<"iRemainPos"<<iRemainPos;
                    bResult = true;
                }
            }
        }
    }
    return bResult;
}

bool Consumables::GetNextCrossConsumable(quint8 uiType
    , quint8 &uiRowIndex1, quint8 &uiColumnIndex1, quint8 &uiAreaIndex1
    , quint8 &uiRowIndex2, quint8 &uiColumnIndex2, quint8 &uiAreaIndex2)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    QList<quint8> qList = m_qConsumableList[uiType];
    qDebug()<<"Consumables::GetNextAvrConsumable qList"<<qList.size()<<uiType;
    if(qList.size() > 1) // 跨区要有两个耗材盒
    {
        QMap<quint8, ConsumableBox>* pValueMap = &m_qConsumableMapList[uiType];
        if(pValueMap)
        {
            uiAreaIndex1 = qList.at(0);
            uiAreaIndex2 = qList.at(1);
            qDebug() << "Consumables::GetNextCrossConsumable pValueMap" << pValueMap->size() << uiAreaIndex1 << uiAreaIndex2;
            if (pValueMap->find(uiAreaIndex1) != pValueMap->end() && pValueMap->find(uiAreaIndex2) != pValueMap->end())
            {
                ConsumableBox* pBox1 = &pValueMap->find(uiAreaIndex1).value();
                ConsumableBox* pBox2 = &pValueMap->find(uiAreaIndex2).value();
                if (pBox1 && pBox2)
                {
                    if(1 == pBox1->iRemain && 1 == pBox2->iRemain)
                    {
                        quint8 uiPos1 = pBox1->uiNextSingleAvrPos;
                        quint8 uiPos2 = pBox2->uiNextSingleAvrPos;
                        m_curUsedBox[uiType] = pBox1;
                        m_curUsedListIndex[uiType] = 0;
    
                        int iRemainPos1 = pBox1->uiCapacity - uiPos1;
                        if(iRemainPos1 >= 0)
                        {
                            uiRowIndex1 = iRemainPos1 / m_uiCapacityInfo[uiType][1];
                            uiColumnIndex1 = iRemainPos1 % m_uiCapacityInfo[uiType][1];
                            qDebug() << "m_uiCapacityInfo 1: " << iRemainPos1 << m_uiCapacityInfo[uiType][1] << uiRowIndex1 << uiColumnIndex1;
                        }
                        else
                        {
                            qDebug()<<"Get Consumables 1 error: " << uiType << iRemainPos1;
                        }
    
                        int iRemainPos2 = pBox2->uiCapacity - uiPos2;
                        if(iRemainPos2 >= 0)
                        {
                            uiRowIndex2 = iRemainPos2 / m_uiCapacityInfo[uiType][1];
                            uiColumnIndex2 = iRemainPos2 % m_uiCapacityInfo[uiType][1];
                            qDebug() << "m_uiCapacityInfo 2: " << iRemainPos2 << m_uiCapacityInfo[uiType][1] << uiRowIndex2 << uiColumnIndex2;
                        }
                        else
                        {
                            qDebug()<<"Get Consumables 2 error: " << uiType << iRemainPos2;
                        }
    
                        bResult = true;
                    }
                }
            
            }
        }
    }
    return bResult;
}

void Consumables::AddConsumable(quint8 uiType, quint8 uiIndex, ConsumableBox box,int iNeedWriteRFID)
{
    QMutexLocker qLocker(&m_qMutex);
    qDebug()<<"Consumables::AddConsumable uiType:"<<uiType<<"uiIndex:"<<uiIndex<<"remain:"<<box.iRemain<<iNeedWriteRFID;
    m_qConsumableMapList[uiType][uiIndex] = box;
    
    // CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Loaded, QString("%1 index %2 is loaded.").arg(kConsNames[uiType]).arg(uiIndex));
    if(iNeedWriteRFID==1)
    {
        CRFIDTask task;
        RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(box.uiType, uiIndex);
        task.iType = RFIDConsumableType(cType);
        task.iMethod = Method_rfid_write;
        box.uiType= task.iType;
        task.cBox = box;
        task.isTubeTask = uiType==CT_TUBE ? 1:0;
        CRFIDMotionTask::getInstance().AddTask(task);
    }

    if(box.iRemain>0)
    {
        m_qConsumableList[uiType].append(uiIndex);
        _SortConsumableBoxList(uiType);//耗材排序
        // //FIXME 需要判断耗材是否充足，控制灯和锁的状态(开/关)
        // _UpdateStatus(ConsumableBoxState::CBST_VALID,uiType,CRFIDCtrl::getInstance().ConverIdxToConsumableType(box.uiType, uiIndex));          
    }    
}

void Consumables::AddVirtualConsumable(quint8 uiType, quint8 uiIndex, quint8 uiRemain)
{
    qDebug()<<"AddVirtual Consumable uiType:"<<uiType<<"uiIndex:"<<uiIndex<<"remain:"<<uiRemain;
    QString strBatchArray[CT_MAX] = {"WTIP240327","WTUBE240327","WCAP240327"};
    ConsumableBox consumableBox = {};
    consumableBox.uiState = CBST_VALID;//盒的状态
    consumableBox.qExpDate = QDate::currentDate().addDays(365);//有效期
    consumableBox.uiCRC = 0;//校验
    consumableBox.strBatchNo = strBatchArray[uiType];//批号
    consumableBox.iRemain = uiRemain;//剩余次数
    consumableBox.uiCapacity = m_uiCapacityInfo[uiType][0]*m_uiCapacityInfo[uiType][1];//总份数
    consumableBox.uiType = uiType;//卡盒类型
    consumableBox.uiNextSingleAvrPos = 1;//下一个抓取单个耗材的位置
    consumableBox.uiNextDoubleAvrPos = consumableBox.uiCapacity;//下一个抓取两个耗材的位置
    consumableBox.strSeqNo = QString("000%1").arg(uiIndex+1);//序列号

    AddConsumable(consumableBox.uiType, uiIndex, consumableBox);
}

void Consumables::RemoveConsumableBox(quint8 uiType, quint8 uiIndex,bool bNeedLock)
{
    auto funcRemoveConsumableBox=[&](){
        qDebug()<<"RemoveConsumableBox: "<<uiType<<uiIndex<<bNeedLock;
        QList<quint8>* pList = &m_qConsumableList[uiType];
        for(int i=0; pList&&i<pList->size(); i++)
        {
            if(pList->at(i) == uiIndex)
            {
                pList->removeAt(i);
                // CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Cons, FT_Material_Unloaded,
                //                                              QString("%1 index %2 is unloaded.").arg(kConsNames[uiType]).arg(uiIndex));
                break;
            }
        }
        //删除Map 记录
        QMap<quint8, ConsumableBox>* pValueMap = &m_qConsumableMapList[uiType];
        if(pValueMap)
        {
            QMap<quint8, ConsumableBox>::iterator itor = pValueMap->find(uiIndex);
            if(itor != pValueMap->end())
            {
                pValueMap->erase(itor);
            }
        }
        // 新增指针清理
        if(m_curUsedBox[uiType] && 
           m_qConsumableMapList[uiType].contains(uiIndex) &&
           &m_qConsumableMapList[uiType][uiIndex] == m_curUsedBox[uiType]) 
         {
             m_curUsedBox[uiType] = nullptr;
         }        
    };
    //删除关联
    if (bNeedLock)
    {
        QMutexLocker qLocker(&m_qMutex);
        funcRemoveConsumableBox();
    }
    funcRemoveConsumableBox();
    _SortConsumableBoxList(uiType);//耗材排序
}

bool Consumables::IsConsumableEnough(quint8 uiType, quint16 uiConsumeSize)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bEnough = false;
    QMap<quint8, ConsumableBox>* pValueMap = &m_qConsumableMapList[uiType];
    if(pValueMap)
    {
        QMap<quint8, ConsumableBox>::iterator itor = pValueMap->begin();
        while(itor != pValueMap->end())
        {
            if(itor.value().iRemain>=uiConsumeSize)
            {
                bEnough = true;
                break;
            }
            else
            {
                uiConsumeSize = uiConsumeSize - itor.value().iRemain;
            }
            itor++;
        }
    }
    qDebug()<<"Consumables::IsConsumableEnough:"<<uiType<<"uiConsumeSize"<<uiConsumeSize<<"bEnough"<<bEnough;
    return bEnough;
}

void Consumables::Consume(quint8 uiType)
{  
    qDebug()<<"Consumables::Consume:"<<uiType;
    QMutexLocker qLocker(&m_qMutex);
    // 增加类型安全检查
    if(uiType >= CT_MAX) {
        qDebug() << "Invalid consumable type:" << uiType;
        return;
    }    
    int iIndex =  GetConsumableBoxIndex(uiType,false);
    qDebug()<<"Consume iIndex:"<<iIndex;

    // 新增有效性检查 -------------------------------------------------
    if(!m_qConsumableMapList[uiType].contains(iIndex)) {
        qDebug() << "invalid consume:" << uiType << iIndex;
        return;
    }
    ConsumableBox& currentBox = m_qConsumableMapList[uiType][iIndex];
    if(currentBox.iRemain <= 0) {
        qDebug() << "consume empty:" << uiType << iIndex;
        return;
    }
    if(m_uiCatchType[uiType] == CT_DOUBLE && currentBox.iRemain < 2) {
        qDebug() << "consume insufficient :" << uiType << iIndex << currentBox.iRemain;
        return;
    }    

    // ConsumableBox* pBox = m_curUsedBox[uiType];
    m_curUsedBox[uiType] = &currentBox; // 强制同步指针
    // if(pBox)
    {
        int iConsumSize = 0;
        qDebug()<<__FUNCTION__<<QThread::currentThreadId();
        if(m_uiCatchType[uiType] == CT_SINGLE )
        {
            currentBox.iRemain--;
            currentBox.uiNextSingleAvrPos++;
            iConsumSize = 1;
        }
        else
        {
            currentBox.iRemain -= 2;
            currentBox.uiNextDoubleAvrPos -= 2;
            iConsumSize = 2;
        }

        //step2 update RFID data
        CRFIDTask task;
        RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(uiType, iIndex);
        task.iType = RFIDConsumableType(cType);
        task.iMethod = Method_rfid_write;
        task.cBox = currentBox;
        task.cBox.uiType = RFIDConsumableType(cType);
        task.isTubeTask = uiType==CT_TUBE ? 1:0;
        qDebug()<<"task.iType="<< task.iType<<", task.iMethod="<< task.iMethod<<",  task.isTubeTask ="<<  task.isTubeTask ;
        CRFIDMotionTask::getInstance().AddTask(task);
    
        qDebug()<<"Consumables:"<<uiType<<"Seq"<<currentBox.strSeqNo<<"remain:"<<currentBox.iRemain
                <<"consumeSize:"<<iConsumSize <<"single"<<currentBox.uiNextSingleAvrPos<<"double:"<<currentBox.uiNextDoubleAvrPos
                <<"qExpDate"<<currentBox.qExpDate <<"BatchNo"<<currentBox.strBatchNo<<"Capacity:"<<currentBox.uiCapacity;

    }
    if(currentBox.iRemain <= 0)
    {
        RemoveConsumableBox(uiType, iIndex,false);

        // //FIXME 需要判断耗材是否充足，控制灯和锁的状态(开/关)
        // qDebug() << "Consumables::Consume remove"<<uiType<<iIndex;
        // _UpdateStatus(ConsumableBoxState::CBST_INVALID,uiType,CRFIDCtrl::getInstance().ConverIdxToConsumableType(uiType, iIndex));
        
        // 屏蔽老化测试代码
        // if(CGlobalConfig::getInstance().getAgingMode())
        // {
        //     ConsumableBox box = *pBox;
        //     box.iRemain = box.uiCapacity;
        //     box.uiNextSingleAvrPos = 1;
        //     box.uiNextDoubleAvrPos = box.iRemain;
        //     AddConsumable(uiType, iIndex, box);//使用时注意，这个函数与本函数qLocker冲突，需要注意
        //     qDebug()<<"Consume Type:"<<box.uiType<<"index"<<box.strSeqNo
        //            <<"Aging Mode reset consume remain:"<<box.iRemain;
        // }
    }
}

quint8 Consumables::GetConsumableRowIndex(quint8 uiType)
{
    QMutexLocker qLocker(&m_qMutex);
    quint8 uiPos = 0;
    ConsumableBox* pBox = m_curUsedBox[uiType];
    if(m_uiCatchType[uiType] == CT_SINGLE )
    {
        uiPos = pBox->uiNextSingleAvrPos;
    }
    else if(pBox->iRemain>1)
    {
        uiPos = pBox->uiNextDoubleAvrPos;
    }
    quint8 uiRowIndex = (pBox->uiCapacity - uiPos)/m_uiCapacityInfo[uiType][1];
    return uiRowIndex;
}

quint8 Consumables::GetConsumableColumnIndex(quint8 uiType)
{
    QMutexLocker qLocker(&m_qMutex);
    quint8 uiPos = 0;
    ConsumableBox* pBox = m_curUsedBox[uiType];
    if(pBox == nullptr)
    {
        qDebug()<<"GetConsumableColumnIndex:"<<uiType<<"pBox is nullptr";
        return 0;
    }
    if(m_uiCatchType[uiType] == CT_SINGLE )
    {
        uiPos = pBox->uiNextSingleAvrPos;
    }
    else if(pBox->iRemain>1)
    {
        uiPos = pBox->uiNextDoubleAvrPos;
    }
    quint8 uiColumnIndex = (pBox->uiCapacity - uiPos)%m_uiCapacityInfo[uiType][1];
    return uiColumnIndex;
}

quint8 Consumables::GetConsumableBoxIndex(quint8 uiType,bool bNeedLock)
{
    auto funcGetConsumableBoxIndex=[&]()->quint8{
        quint8 uiIndex = 0;
        QList<quint8>* pList = &m_qConsumableList[uiType];
        if(pList && !pList->isEmpty())
        {
            if(pList->size()>m_curUsedListIndex[uiType])
            {
                uiIndex = pList->at(m_curUsedListIndex[uiType]);
            }
            qDebug() << "Consumables::GetConsumableBoxIndex size"<<pList->size()<<m_curUsedListIndex[uiType]<<uiIndex;
        }
        return uiIndex;
    };
    qDebug()<<"GetConsumableBoxIndex: "<<uiType<<bNeedLock;
    //获取某耗材当前在用的盒子索引
    if (bNeedLock)
    {
        QMutexLocker qLocker(&m_qMutex);
        return funcGetConsumableBoxIndex();
    }
    return funcGetConsumableBoxIndex();
}

bool Consumables::IsConsumableCurBoxIndexNoOne(quint8 uiType)
{
    bool bResult = false;
    if(GetConsumableBoxIndex(uiType) ==0)
        bResult = true;
    return bResult;
}

QList<RangePos> Consumables::virtualConsume(quint8 uiType, QList<quint8> qConsumeList)
{
    //模拟体系构建耗材消耗全过程，并计算各种耗材
    QList<RangePos> qList;

    QMap<quint8, ConsumableBox> qValueMap = m_qConsumableMapList[uiType];
    QMap<quint8, ConsumableBox> qValueMapOrg = qValueMap;
    QList<quint8> qIndexList = m_qConsumableList[uiType];
    int iIndex = 0;
    ConsumableBox* pBox = &qValueMap[qIndexList[iIndex]];
    
    qDebug()<<"virtualConsume qValueMap: "<<qValueMap.keys();
    for(int i = 0; i<qConsumeList.size();i++)
    {
CONSUME:
        qDebug()<<"virtualConsume: "<<i<<iIndex<<qConsumeList.at(i);
        pBox = &qValueMap[qIndexList[iIndex]];
        int iConsumeSize = 0;
        if(pBox->iRemain>=qConsumeList.at(i))
        {
            if(qConsumeList.at(i)==1)
            {
                pBox->iRemain--;
                pBox->uiNextSingleAvrPos++;
                iConsumeSize = 1;
            }
            else if(qConsumeList.at(i)==2)
            {
                pBox->iRemain -= 2;
                pBox->uiNextDoubleAvrPos -= 2;
                iConsumeSize = 2;
            }
            qDebug()<<"Virtual Consumables:"<<uiType<<"Seq"<<pBox->strSeqNo<<"remain:"<<pBox->iRemain
                   <<"consumeSize:"<<iConsumeSize <<"single"<<pBox->uiNextSingleAvrPos<<"double:"<<pBox->uiNextDoubleAvrPos;
        }
        else if(pBox->iRemain<qConsumeList.at(i))
        {
            iIndex++;
            if(iIndex>1)
            {
                qDebug()<<"1Virtual consume error: "<<iIndex;
                break;
            }
            goto CONSUME;
        }
        if(pBox->iRemain == 0)
        {
            iIndex++;
            if(iIndex>1)
            {
                qDebug()<<"2Virtual consume error: "<<iIndex;
                break;
            }
        }
        else
        {
            iIndex = 0;
        }
    }

    RangePos pos;
    for(int i= 0;i<qValueMap.size();i++)
    {
        if (qIndexList.size() <= i)
        {
            continue;
        }
        
        if(qValueMapOrg[qIndexList[i]].uiNextSingleAvrPos != qValueMap[qIndexList[i]].uiNextSingleAvrPos)
        {
            pos.uiAreaIndex = qIndexList[i];
            pos.uiStartPos = qValueMap[qIndexList[i]].uiCapacity - qValueMap[qIndexList[i]].uiNextSingleAvrPos + 1;
            pos.uiEndPos = qValueMap[qIndexList[i]].uiCapacity -  qValueMapOrg[qIndexList[i]].uiNextSingleAvrPos;
            qList.push_back(pos);
            qDebug()<<kConsNames[uiType]<<" area: "<<qIndexList[i]<<" SinglePos start:"<<pos.uiStartPos<<" End:"<<pos.uiEndPos;
            qDebug()<<kConsNames[uiType]<<" Index: "<<qIndexList[i]<<"org nextSingleAvr pos:"<<
                      qValueMapOrg[qIndexList[i]].uiNextSingleAvrPos <<"new nextSingleAvr pos:"<< qValueMap[qIndexList[i]].uiNextSingleAvrPos;
        }
        if(qValueMapOrg[qIndexList[i]].uiNextDoubleAvrPos != qValueMap[qIndexList[i]].uiNextDoubleAvrPos)
        {
            pos.uiAreaIndex = qIndexList[i];
            pos.uiStartPos = qValueMap[qIndexList[i]].uiCapacity - qValueMapOrg[qIndexList[i]].uiNextDoubleAvrPos;
            pos.uiEndPos = qValueMap[qIndexList[i]].uiCapacity - qValueMap[qIndexList[i]].uiNextDoubleAvrPos - 1;
            qList.push_back(pos);
            qDebug()<<kConsNames[uiType]<<" area: "<<qIndexList[i]<<" DoublePos start:"<<pos.uiStartPos<<" End:"<<pos.uiEndPos;
            qDebug()<<kConsNames[uiType]<<" Index: "<<qIndexList[i]<<"org nextDoublePosAvr pos:"<<
                      qValueMapOrg[qIndexList[i]].uiNextDoubleAvrPos <<"new nextDoublePosAvr pos:"<< qValueMap[qIndexList[i]].uiNextDoubleAvrPos;
        }
    }
    return qList;
}

ConsumableBoxState Consumables::CheckConsumableBoxEnough(quint8 uiType, quint16 uiIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    ConsumableBoxState state = CBST_EMPTY;
    ConsumableBox* pBox = &m_qConsumableMapList[uiType][uiIndex];
    if(pBox && pBox->iRemain)
    {
        state = CBST_VALID;
    }  
    qDebug()<<"CheckConsumableBoxEnough::IsConsumableBoxEnough"<<" uiType:"<<uiType<<" index:"<<uiIndex<<" remain:"<<pBox->iRemain<<"state: "<<state;
    return state;
}

void Consumables::CheckConsumableBoxStatus()
{
    ConsumableBoxState state = CheckConsumableBoxEnough(CT_TIP,0);
    _UpdateStatus(state,CT_TIP,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_TIP, 0));          

    state = CheckConsumableBoxEnough(CT_TIP,1);
    _UpdateStatus(state,CT_TIP,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_TIP, 1));

    state = CheckConsumableBoxEnough(CT_TUBE,0);
    _UpdateStatus(state,CT_TUBE,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_TUBE, 0));

    state = CheckConsumableBoxEnough(CT_TUBE,1);
    _UpdateStatus(state,CT_TUBE,CRFIDCtrl::getInstance().ConverIdxToConsumableType(CT_TUBE, 1));
}

// 优先使用余量少的耗材
void Consumables::_SortConsumableBoxList(quint8 uiType)
{
    if(m_qConsumableMapList[uiType].isEmpty())
    {
        qDebug()<<"Consumables::_SortConsumableBoxList error: "<<uiType;
        return;
    }    

    QList<ConsumableBox> qListBox;
    auto& list = m_qConsumableList[uiType];
    qDebug()<<"Consumables::_SortConsumableBoxList: "<<uiType<<" list:"<<list;

    for (auto index : list)
    {
        auto box = m_qConsumableMapList[uiType][index];
        box.uiColumnIndex = index;
        qListBox.push_back(box);
    }
    
    std::sort(qListBox.begin(), qListBox.end(), [](const ConsumableBox &a, const ConsumableBox &b) {
        return a.iRemain < b.iRemain;
    });

    list.clear();
    for (auto box : qListBox)
    {
        list.push_back(box.uiColumnIndex);
    }
    qDebug()<<"Consumables::_SortConsumableBoxList sort list: "<<list;

}
