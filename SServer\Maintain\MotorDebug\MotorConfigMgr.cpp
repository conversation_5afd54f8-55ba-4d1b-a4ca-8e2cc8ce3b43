#include<qdebug.h>
#include<QTime>
#include <QFile>
#include <QDir>
#include"MotorConfigMgr.h"
#include "toml/toml.hpp"

namespace MotorConfigMgrTomlSingleton 
{
// 定义在头文件的话，引用的是toml库，编译速度较慢
toml::value config;
toml::value tableChild;// 电机配置子表
};

MotorConfigMgr::MotorConfigMgr()
{
    m_strFilePath = "./ServerConfig/MotorConfig.toml";// 电机配置文件路径
    m_bLoadStatus = false;
    _Init();
}

MotorConfigMgr::~MotorConfigMgr()
{

}

MotorConfigMgr &MotorConfigMgr::getInstance()
{
    static MotorConfigMgr obj;
    return obj;
}

QString MotorConfigMgr::GetStringValue(QString field, QString type)
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"MotorConfig.toml file load failed!!! "<<m_bLoadStatus;
        return "";
    }
    
    QString strRetValue = "";
    auto& config = MotorConfigMgrTomlSingleton::config;
    if (!config.contains(field.toStdString()))
    {
        qDebug() <<"MotorConfigMgr::GetStringValue field not exist"<<field;
        return strRetValue;
    }
    
    auto table = toml::find(config, field.toStdString());
    if (table.is_table() && table.contains(type.toStdString()))
    {
        auto value = toml::find<std::string>(table, type.toStdString());
        strRetValue = QString::fromStdString(value);
    }
 //   qDebug() <<"MotorConfigMgr::GetStringValue"<<field<<type<<strRetValue;
    return strRetValue;
}

QString MotorConfigMgr::GetStringValue(QString type)
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"MotorConfig.toml file load failed!!! "<<m_bLoadStatus;
        return "";
    }
    
    QString strRetValue = "";
    auto& table = MotorConfigMgrTomlSingleton::tableChild;
    if (!table.contains(type.toStdString()))
    {
        qDebug() <<"MotorConfigMgr::GetStringValue field not exist"<<type;
        return strRetValue;
    }
    
    auto value = toml::find<std::string>(table, type.toStdString());
    //strRetValue = QString::fromStdString(value);
    strRetValue = QString::fromUtf8(value.c_str()); //中文改这
    //qDebug() <<"MotorConfigMgr::GetStringValue"<<type<<strRetValue;
    return strRetValue;
}

bool MotorConfigMgr::GetBoolValue(UnitModuleFiled field, MotorInfoType type)
{
    QMetaEnum metaFieldType = QMetaEnum::fromType<UnitModuleFiled>();
    QString strConfigField = metaFieldType.valueToKey(static_cast<int>(field));

    QMetaEnum metaType = QMetaEnum::fromType<MotorInfoType>();
    QString strType = metaType.valueToKey(static_cast<int>(field));
    QString strValue = GetStringValue(strConfigField,strType);
    return strValue == "1";
}

int MotorConfigMgr::GetIntValue(UnitModuleFiled field, MotorInfoType type)
{
    int value = 0;
    QMetaEnum metaConfigFieldType = QMetaEnum::fromType<UnitModuleFiled>();
    QString strConfigField = metaConfigFieldType.valueToKey(static_cast<int>(field));

    QMetaEnum metaType = QMetaEnum::fromType<MotorInfoType>();
    QString strType = metaType.valueToKey(static_cast<int>(field));

    QString strValue = GetStringValue(strConfigField,strType);
    if (strValue.isEmpty())
    {
        return value;
    }
    value = strValue.toInt();

    qDebug()<<"MotorConfigMgr::GetIntValue: "<<strConfigField<<strType<<strValue;
    return value;
}


int MotorConfigMgr::GetMotorConfigInfo(QString strModuleName,QString strActionName,QString strMotorName,MotorConfigInfo &RsStructInfo,int &iRsBoardID)
{
    QString strSearchName = strModuleName+"-"+strActionName;
    if (m_mapDebugProject.contains(strSearchName))
    {
        if (m_mapDebugProject[strSearchName].m_hashDeviceCfg.contains(strMotorName))
        {
            RsStructInfo= m_mapDebugProject[strSearchName].m_hashDeviceCfg[strMotorName];
            iRsBoardID    = m_mapDebugProject[strSearchName].m_CommonCfg.iBoardIdx;
            return 0;
        }
    }
    qDebug()<<"MotorConfigMgr::GetMotorConfigInfo: "<<strSearchName<<strMotorName<<" not exist";
    return 1;
}

int MotorConfigMgr::SetMotorConfigInfoFlag(QString strModuleName,QString strActionName,QString strMotorName,QString strFlag)
{
      QString strSearchName = strModuleName+"-"+strActionName;
    if (m_mapDebugProject.contains(strSearchName))
    {
        if ( m_mapDebugProject[strSearchName].m_hashDeviceCfg.contains(strMotorName))
        {
            m_mapDebugProject[strSearchName].m_hashDeviceCfg[strMotorName].strFlag = strFlag;
            return 0;
        }
    }
    return 1;
}

int MotorConfigMgr::GetActionPrepareBoardIDAndMethodID(QString strModuleName,QString strActionName,int &RsBoardID,QStringList &strListMethodID,QString &strAddParam)
{
    QString strSearchName = strModuleName+"-"+strActionName;
    if (m_mapDebugProject.contains(strSearchName))
    {
        ModuleConfig structElement=m_mapDebugProject.value(strSearchName);
        RsBoardID     = structElement.m_CommonCfg.iBoardIdx;
        strListMethodID.append(structElement.m_CommonCfg.strInitCmd);
        strListMethodID.append(structElement.m_CommonCfg.strVerifyCmd);
        strAddParam = structElement.m_CommonCfg.strParam;
        return 0;
    }
    return 1;
}

int MotorConfigMgr::SetAllSubActionMotorFlag(QString strModuleName,QString strActionName,QString strFlag)
{
    QString strSearchName = strModuleName+"-"+strActionName;
    if (m_mapDebugProject.contains(strSearchName))
    {
        QHash<QString, MotorConfigInfo>::iterator  it;
        for (it = m_mapDebugProject[strSearchName].m_hashDeviceCfg.begin(); it != m_mapDebugProject[strSearchName].m_hashDeviceCfg.end(); ++it)
        {
            it.value().strFlag=strFlag;
        }
        return 0;
    }
    return 1;
}


int MotorConfigMgr:: GetActionDeviceListNumAndName(QString strModuleName,QString strActionName,QStringList &strlistName)
{
    QString strSearchName = strModuleName+"-"+strActionName;
    if (m_mapDebugProject.contains(strSearchName))
    {
        ModuleConfig structElement=m_mapDebugProject.value(strSearchName);
        QHash<QString,MotorConfigInfo>::const_iterator it;
        for(it =structElement.m_hashDeviceCfg.begin();it!=structElement.m_hashDeviceCfg.end();++it)
        {
            strlistName.append(it.key());
        }
        return structElement.m_hashDeviceCfg.size();
    }
    return 0;
}


bool MotorConfigMgr::_Init()
{
    QString strCurrentPath = QDir::currentPath();
    QFile file(m_strFilePath);
    qDebug()<<"strCurrentPath="<<strCurrentPath<<",m_strFilePath="<<m_strFilePath;
    if (!file.exists()) {
        m_bLoadStatus = false;
        qWarning() << "MotorConfig.toml file does not exist" << m_strFilePath;
        return false;
    }
    MotorConfigMgrTomlSingleton::config = toml::parse( m_strFilePath.toStdString());
    m_bLoadStatus = true;
    qDebug()<<"SystemConfig::_Init";
    _InitMotorConfigInfoHash();
    return true;
}

void MotorConfigMgr::_readDataToMotorConfigInfo(MotorConfigInfo &motorInfo)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<MotorInfoType>();
    QString strValue = "";

    for (int i = 0; i < metaEnum.keyCount(); i++)
    {
        strValue = GetStringValue(metaEnum.valueToKey(i));
        switch (i)
        {
        case MotorInfoType::DeviceType:
            motorInfo.strDeviceType = strValue;
            break;
        case MotorInfoType:: SubOrdIdx:
            motorInfo.strSubOrdIdx = strValue;
            break;
        case MotorInfoType::name:
            motorInfo.strName = strValue;
            break;
        case MotorInfoType::direct:
        {
            if (!strValue.isEmpty())
            {
                motorInfo.bDirect = (strValue == "1");
            }
        }
            break;
        case MotorInfoType::limit:
            motorInfo.strLimit = strValue;
            break;
        case MotorInfoType::unload:
            motorInfo.strUnloadCmd = strValue;
            break;
        case MotorInfoType::addparam:
            motorInfo.strParam = strValue;
            break;
        case MotorInfoType::IsNeedSave:
            motorInfo.iIsNeedSave = strValue.toInt();
            break;
        case MotorInfoType::MotorIdx:
            motorInfo.iMotorIdx = strValue.toInt();
            break;
        case MotorInfoType::PosID:
            motorInfo.iPosID = strValue.toInt();
            break;
        default:
            break;
        }
    }
    motorInfo.strFlag ="NO";
}

void MotorConfigMgr::_readDataToCommonSet(CommonConfig &commonInfo)
{
    QString strRetValue = "";

    QMetaEnum metaEnum = QMetaEnum::fromType<CommonSetType>();
    QString strValue = "";

    for (int i = 0; i < metaEnum.keyCount(); i++)
    {
        strValue = GetStringValue(metaEnum.valueToKey(i));
        switch (i)
        {
        case CommonSetType::initcmd:
            commonInfo.strInitCmd = strValue;
            break;
        case CommonSetType::verifycmd:
            commonInfo.strVerifyCmd= strValue;
            break;
        case CommonSetType:: ordidx:
            commonInfo.dOrdIdx = strValue.toDouble();
            break;
        case CommonSetType:: BoardIdx:
            commonInfo.iBoardIdx = strValue.toInt();
            break;
        case CommonSetType:: param:
            commonInfo.strParam = strValue;
            break;
        case CommonSetType:: CNName:
            commonInfo.strCNName = strValue;
            break;
        }
    }
}

bool MotorConfigMgr::_InitMotorConfigInfoHash()
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"MotorConfig.toml file load failed!!! "<<m_bLoadStatus;
        return false;
    }

    auto& config = MotorConfigMgrTomlSingleton::config;
    QString strField = "";

    // 遍历顶层键，查找所有顶层的表
    for( auto& element : config.as_table())
    {
        if(!element.second.is_table())
        {
            continue;
        }
      //  qDebug()<<"MotorConfigMgr::Found table: "<<element.first.c_str();
        auto table = toml::find(config, element.first);

        ModuleConfig structModuleConfig;

        // 访问 motorX 表，它是顶层表的子表
        for( auto& child : table.as_table())
        {
            strField = QString::fromStdString(child.first.c_str());
            //qDebug()<<"MotorConfigMgr::Found child table: "<<strField;
            if(!child.second.is_table())
            {
                qDebug()<<"MotorConfigMgr error,please check";
                return false;
            }
            MotorConfigMgrTomlSingleton::tableChild = child.second;
            MotorConfigInfo motorInfo;

            if(strField=="commonSet")
            {
                _readDataToCommonSet(structModuleConfig.m_CommonCfg);
                continue;
            }
            _readDataToMotorConfigInfo(motorInfo);
            structModuleConfig.m_hashDeviceCfg[motorInfo.strName]=motorInfo;
        }
        m_mapDebugProject[structModuleConfig.m_CommonCfg.strCNName] = structModuleConfig;
    }
    qDebug()<<"MotorConfigMgr::_InitMotorConfigInfoHash"<<m_mapDebugProject.keys();
    return true;
}


QString MotorConfigMgr::GetDebugProjectActionList(QString strModuleName)
{
    //加了“-”再查找，因为有些名字可能会包含，
    strModuleName =strModuleName+"-";
    QStringList strList;
    QString strRs = "";
    structOrdSubAction tempOrd;
    QList<structOrdSubAction> listord;
    listord.clear();
    strList.clear();

    for(auto it= m_mapDebugProject.begin();it!=m_mapDebugProject.end();++it)
    {
        if (it.key().contains(strModuleName))
        {
            tempOrd.dOrder =  it.value().m_CommonCfg.dOrdIdx;
            qDebug()<<__FUNCTION__<<"strName"<<strModuleName<<",dOrder ="<<tempOrd.dOrder;
            //将“-”截断，去掉模块名称，发送动作名称给上位机
            QStringList strList = it.key().split("-");
            if(strList.size()==2)
            {
                tempOrd.strSubActionName=strList[1];
                listord.append(tempOrd);
            }
        }
    }
    std::sort(listord.begin(), listord.end(), [](const structOrdSubAction &a, const structOrdSubAction &b) {
        return a.dOrder < b.dOrder; // 从小到大排序
    });

    for(int i=0;i<listord.size();i++)
    {
        qDebug()<<"i="<<i<<",order="<<listord[i].dOrder<<"name ="<<listord[i].strSubActionName;
        strList.append(listord[i].strSubActionName);
    }
    strRs =strList.join("&");
    return strRs;
}
