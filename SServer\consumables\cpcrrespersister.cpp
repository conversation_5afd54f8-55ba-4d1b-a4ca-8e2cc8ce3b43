#include "cpcrrespersister.h"
#include <QJsonDocument>
#include <QJsonObject>

CPCRResPersister::CPCRResPersister(QObject *parent)
    : QObject(parent)
{
    m_dbPtr = &CSystemDB::getInstance();
}

CPCRResPersister::~CPCRResPersister()
{
}

bool CPCRResPersister::savePCRRes(int index, const PCRResInfo &info)
{
#if 1
    QString strJsonString = pcrInfoToJson(info);
    if (strJsonString.isEmpty()) return false;
    return m_dbPtr->addKeyValue(QString("PCRInfo_%1").arg(index), strJsonString);
#else
    return true;
#endif

}

PCRResInfo CPCRResPersister::getPCRRes(int index)
{
    QString strJsonString = m_dbPtr->getValueFromKey(QString("PCRInfo_%1").arg(index)).toString();
    if (strJsonString.isEmpty())
    {
        // 初始化默认PCRResInfo或处理错误情况
        return PCRResInfo();
    }
    return jsonToPCRInfo(strJsonString);
}

bool CPCRResPersister::saveAllPCRRes(const QList<PCRResInfo> &infos)
{
    bool bOK = true;
    for(int i = 0; i < infos.size() && bOK; ++i) {
        bOK = savePCRRes(i, infos[i]);
    }
    return bOK;
}

QList<PCRResInfo> CPCRResPersister::getAllPCRRes()
{
    QList<PCRResInfo> allInfos;
    const int iMaxIdx = PCR_ROW_SIZE*PCR_COLUMN_SIZE; // 假设的方法获取最大索引
    for(int i = 0; i < iMaxIdx; ++i) {
        PCRResInfo info = getPCRRes(i);
        allInfos << info;
    }
    return allInfos;
}


QString CPCRResPersister::pcrInfoToJson(const PCRResInfo &info)
{
    QJsonObject jsonObj;
    jsonObj["uiPCRST"] = info.uiPCRST;
    jsonObj["uiRowIndex"] = info.uiRowIndex;
    jsonObj["uiColumnIndex"] = info.uiColumnIndex;
    jsonObj["uiRemain"] = info.uiRemain;
    jsonObj["uiCapacity"] = info.uiCapacity;
    jsonObj["uiNextSinglePos"] = info.uiNextSinglePos;
    jsonObj["uiNextDoublePos"] = info.uiNextDoublePos;
    jsonObj["strTecName"] = info.strTecName;

    return QJsonDocument(jsonObj).toJson(QJsonDocument::Compact);
}

PCRResInfo CPCRResPersister::jsonToPCRInfo(const QString &strJsonStr)
{
    PCRResInfo info;
    QJsonDocument doc = QJsonDocument::fromJson(strJsonStr.toUtf8());
    if (!doc.isObject())
    {
        return info;
    }
    QJsonObject jsonObj = doc.object();
    info.uiPCRST = jsonObj["uiPCRST"].toInt();
    info.uiRowIndex = jsonObj["uiRowIndex"].toInt();
    info.uiColumnIndex = jsonObj["uiColumnIndex"].toInt();
    info.uiRemain = jsonObj["uiRemain"].toInt();
    info.uiCapacity = jsonObj["uiCapacity"].toInt();
    info.uiNextSinglePos = jsonObj["uiNextSinglePos"].toInt();
    info.uiNextDoublePos = jsonObj["uiNextDoublePos"].toInt();
    info.strTecName = jsonObj["strTecName"].toString();

    return info;
}
