#ifndef TESTPREPROCESSING__H
#define TESTPREPROCESSING__H

#include <QObject>
#include <QTimer>
#include <atomic>

class TestPreprocessing : public QObject
{
    Q_OBJECT
public:
    explicit TestPreprocessing(QObject* parent = nullptr);
    ~TestPreprocessing();

    // 启动预处理流程
    void start(QString strParams);

signals:
    void sigPreprocessingSuccess();
    void sigPreprocessingFailed(const QString& errMsg);
    void sigStepChanged(const QString& stepName);

private slots:
    void _onResetFinished(bool success, const QString& msg);
    void _onSampleExistFinished(bool success, const QString& msg);
    void _onSampleScanFinished(bool success, const QString& msg);

private:
    void _startReset();
    void _startSampleExist();
    void _startSampleScan();
    void _abort(const QString& errMsg);

    enum Step {
        STEP_IDLE,
        STEP_RESET,
        STEP_SAMPLE_EXIST,
        STEP_SAMPLE_SCAN,
        STEP_DONE,
        STEP_ERROR
    };
    std::atomic<Step> m_eCurStep;
    QTimer* m_pTimeoutTimer;
    QString m_strParams;
};

#endif // TESTPREPROCESSING__H
