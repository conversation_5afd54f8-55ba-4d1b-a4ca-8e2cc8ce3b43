#include "publicfunction.h"
#include <QDir>
#include <QDebug>
#include <QEventLoop>
#include <QTimer>
#include <QDataStream>
#include <QCoreApplication>
#include <QCryptographicHash>
#include <tuple> // for std::tuple

static unsigned short ccitt_table[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7,
    0x8108, 0x9129, 0xA14A, 0xB16B, 0xC18C, 0xD1AD, 0xE1CE, 0xF1EF,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52B5, 0x4294, 0x72F7, 0x62D6,
    0x9339, 0x8318, 0xB37B, 0xA35A, 0xD3BD, 0xC39C, 0xF3FF, 0xE3DE,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64E6, 0x74C7, 0x44A4, 0x5485,
    0xA56A, 0xB54B, 0x8528, 0x9509, 0xE5EE, 0xF5CF, 0xC5AC, 0xD58D,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76D7, 0x66F6, 0x5695, 0x46B4,
    0xB75B, 0xA77A, 0x9719, 0x8738, 0xF7DF, 0xE7FE, 0xD79D, 0xC7BC,
    0x48C4, 0x58E5, 0x6886, 0x78A7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xC9CC, 0xD9ED, 0xE98E, 0xF9AF, 0x8948, 0x9969, 0xA90A, 0xB92B,
    0x5AF5, 0x4AD4, 0x7AB7, 0x6A96, 0x1A71, 0x0A50, 0x3A33, 0x2A12,
    0xDBFD, 0xCBDC, 0xFBBF, 0xEB9E, 0x9B79, 0x8B58, 0xBB3B, 0xAB1A,
    0x6CA6, 0x7C87, 0x4CE4, 0x5CC5, 0x2C22, 0x3C03, 0x0C60, 0x1C41,
    0xEDAE, 0xFD8F, 0xCDEC, 0xDDCD, 0xAD2A, 0xBD0B, 0x8D68, 0x9D49,
    0x7E97, 0x6EB6, 0x5ED5, 0x4EF4, 0x3E13, 0x2E32, 0x1E51, 0x0E70,
    0xFF9F, 0xEFBE, 0xDFDD, 0xCFFC, 0xBF1B, 0xAF3A, 0x9F59, 0x8F78,
    0x9188, 0x81A9, 0xB1CA, 0xA1EB, 0xD10C, 0xC12D, 0xF14E, 0xE16F,
    0x1080, 0x00A1, 0x30C2, 0x20E3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83B9, 0x9398, 0xA3FB, 0xB3DA, 0xC33D, 0xD31C, 0xE37F, 0xF35E,
    0x02B1, 0x1290, 0x22F3, 0x32D2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xB5EA, 0xA5CB, 0x95A8, 0x8589, 0xF56E, 0xE54F, 0xD52C, 0xC50D,
    0x34E2, 0x24C3, 0x14A0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xA7DB, 0xB7FA, 0x8799, 0x97B8, 0xE75F, 0xF77E, 0xC71D, 0xD73C,
    0x26D3, 0x36F2, 0x0691, 0x16B0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xD94C, 0xC96D, 0xF90E, 0xE92F, 0x99C8, 0x89E9, 0xB98A, 0xA9AB,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18C0, 0x08E1, 0x3882, 0x28A3,
    0xCB7D, 0xDB5C, 0xEB3F, 0xFB1E, 0x8BF9, 0x9BD8, 0xABBB, 0xBB9A,
    0x4A75, 0x5A54, 0x6A37, 0x7A16, 0x0AF1, 0x1AD0, 0x2AB3, 0x3A92,
    0xFD2E, 0xED0F, 0xDD6C, 0xCD4D, 0xBDAA, 0xAD8B, 0x9DE8, 0x8DC9,
    0x7C26, 0x6C07, 0x5C64, 0x4C45, 0x3CA2, 0x2C83, 0x1CE0, 0x0CC1,
    0xEF1F, 0xFF3E, 0xCF5D, 0xDF7C, 0xAF9B, 0xBFBA, 0x8FD9, 0x9FF8,
    0x6E17, 0x7E36, 0x4E55, 0x5E74, 0x2E93, 0x3EB2, 0x0ED1, 0x1EF0
};

/**
     * @brief getCRC16  获取CRC16
     * @param data  计算的数据
     * @param len  数据的长度
     * @param oldCRC16  上一个CRC16的值，用于循环计算大文件的CRC16。第一个数据的CRC16则传入0x0。
     * @return
     */
unsigned short GetCRC16(const char *pData, unsigned long ulLen, unsigned long ulOldCRC16)
{
    unsigned short crc16 = ulOldCRC16;

    while (ulLen-- > 0)
        crc16 = ccitt_table[(crc16 >> 8 ^ *pData++) & 0xff] ^ (crc16 << 8);
    return crc16;
}
quint16 GetSmallByte(quint16 qDate)
{
    char *pByte = (char*)&qDate;

    quint16 quSmallByte = *((quint8*)pByte);
    pByte++;
    quint16 quBigByte = *((quint8*)pByte);
    int iNumber = (quSmallByte << 8) | quBigByte;
    return iNumber;
}
quint32 GetSmallByte32(quint32 qDate)
{
    char *pByte = (char*)&qDate;
    quint32 quSmallByte = *((quint8*)pByte);
    pByte++;
    quint16 quBigByte = *((quint8*)pByte);
    quSmallByte= (quSmallByte << 8) | quBigByte;
    pByte++;
    quBigByte = *((quint8*)pByte);
    quSmallByte= (quSmallByte << 8) | quBigByte;
    pByte++;
    quBigByte = *((quint8*)pByte);
    quSmallByte= (quSmallByte << 8) | quBigByte;
    return quSmallByte;
}

void CreateDir(QString strDir)
{
    qDebug() << "mkdir " << strDir;
    QDir dir(strDir);
    if(!dir.exists())
    {
        QString strCmd = QString("mkdir %1").arg(strDir);
        [[maybe_unused]] bool bResult = dir.mkpath(strDir);
#ifdef __arm__
        if(!bResult)
        {
            int iResult = system(strCmd.toLocal8Bit().data());
            system("sync");
        }
#endif
    }
}

void Delay_MSec(uint iMSecTime)
{
    QEventLoop loop;
    QTimer::singleShot(iMSecTime, &loop, SLOT(quit()));
    loop.exec();
}


bool ReadFile(QString strFileName,QString& strFileContext,QIODevice::OpenModeFlag flag)
{
    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<"ERR:open "<<strFileName<<file.errorString();
        return false;
    }
    QByteArray byteJson = file.readAll();
    file.close();

    strFileContext = QString::fromLocal8Bit(byteJson);

    return true;
}

bool WriteFile(QString strFileName, QString strFileContext,QIODevice::OpenModeFlag flag)
{
    //qDebug()<<"Write:"<<strFileName<<strFileContext;

    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<"ERR:write "<<strFileName<<file.errorString();
        return false;
    }

    file.write(strFileContext.toLocal8Bit());
    file.close();

    return true;
}

bool WriteFile(QString strFileName, QByteArray byteFileContext,QIODevice::OpenModeFlag flag)
{
    //qDebug()<<"Write:"<<strFileName<<strFileContext;

    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<"ERR:write "<<strFileName<<file.errorString();
        return false;
    }

    file.write(byteFileContext);
    file.close();

    return true;
}
quint16 GetByte2Int(char *pByte) {
    //TODO 未检查数组越界
    quint16 quBigByte = static_cast<quint16>(static_cast<unsigned char>(pByte[0]));
    quint16 quSmallByte = static_cast<quint16>(static_cast<unsigned char>(pByte[1]));
    quint16 iNumber  = (quSmallByte << 8) | quBigByte;
    return iNumber;
}
quint32 GetByte4Int(char *pByte) {
    //TODO 未检查数组越界
    quint32 quByte0 = static_cast<quint32>(static_cast<unsigned char>(pByte[0]));
    quint32 quByte1 = static_cast<quint32>(static_cast<unsigned char>(pByte[1]));
    quint32 quByte2 = static_cast<quint32>(static_cast<unsigned char>(pByte[2]));
    quint32 quByte3 = static_cast<quint32>(static_cast<unsigned char>(pByte[3]));
    quint32 iNumber = (quByte3 << 24) | (quByte2 << 16) | (quByte1 << 8) | quByte0;
    return iNumber;
}
QSqlDatabase ConnectDataBase(const QString &kstrDBName, const QString &kstrConnectName)
{
    QSqlDatabase qSqlDataBase = QSqlDatabase::addDatabase("QSQLITE", kstrConnectName);
    qSqlDataBase.setDatabaseName(kstrDBName);
    qSqlDataBase.setPassword(kstrConnectName);
    return qSqlDataBase;
}

QString JoinStringFromList(QList<qreal> dData, QString strSplit)
{
    QStringList strList;
    for (qreal val : dData) {
        strList << QString::number(val);
    }
    QString strResult = strList.join(strSplit);
    return strResult;
}


QList<qreal> SplitDoubleFromQString(QString strData, QString strSplit)
{
    QList<qreal> dDataList;

    QStringList strMeltingList = strData.split(strSplit);
    for(int i = 0; i < strMeltingList.count(); ++i)
    {
        dDataList.push_back(strMeltingList[i].toDouble());
    }
    return dDataList;
}


std::vector<double> getXVectorFromQPontF(const QList<QPointF> &qSrc)
{
    std::vector<double> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).x());
    }
    return dSrcDataVector;
}

std::vector<double> getYVectorFromQPontF(const QList<QPointF> & qSrc)
{
    std::vector<double> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).y());
    }
    return dSrcDataVector;
}

void getXYVectorFromQPontF(const QList<QPointF> &qSrc, std::vector<double> &dXVector, std::vector<double> &dYVector)
{
    dXVector.clear();
    dYVector.clear();
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dXVector.push_back(qSrc.at(i).x());
        dYVector.push_back(qSrc.at(i).y());
    }
}
QList<qreal> getXListFromQPontF(const QList<QPointF> &qSrc)
{
    QList<qreal> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).x());
    }
    return dSrcDataVector;
}

QList<qreal> getYListFromQPontF(const QList<QPointF> &qSrc)
{
    QList<qreal> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).y());
    }
    return dSrcDataVector;
}

void getXYListFromQPontF(const QList<QPointF> &qSrc, QList<qreal> &qXList, QList<qreal> &qYList)
{
    qXList.clear();
    qYList.clear();
    for(int i = 0; i < qSrc.length(); ++i)
    {
        qXList.push_back(qSrc.at(i).x());
        qYList.push_back(qSrc.at(i).y());
    }
}

QByteArray GetSendData(const SCanBusDataStruct &sSCanBusDataStruct)
{
    QByteArray qBlockByteArray;
    QDataStream qOutDataStream
            (&qBlockByteArray,QIODevice::ReadWrite);
    qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
    qOutDataStream << quint16(0x4D40)
                   << quint16(0x2A31)
                   << sSCanBusDataStruct.quVersion
                   << sSCanBusDataStruct.quMachineID
                   << sSCanBusDataStruct.quCmdID
                   << sSCanBusDataStruct.quDestinationID
                   << sSCanBusDataStruct.quSourceID
                   << sSCanBusDataStruct.quFormat;
    qOutDataStream  << sSCanBusDataStruct.quFrameSeq;
    qOutDataStream << sSCanBusDataStruct.quMethonID;
    qOutDataStream << sSCanBusDataStruct.quResult;
    qOutDataStream << sSCanBusDataStruct.quSync;
    qOutDataStream << sSCanBusDataStruct.quReserve;

    //    qDebug() << "send data machine id: " << sSCanBusDataStruct.quMachineID
    //             << " quDestinationID: " << sSCanBusDataStruct.quDestinationID
    //             <<   " quMethonID: " << sSCanBusDataStruct.quMethonID
    //               << " sync: " << sSCanBusDataStruct.quSync
    //               << "seq: " << sSCanBusDataStruct.quFrameSeq
    //               << " data : " << sSCanBusDataStruct.qbPayload;

    qOutDataStream << quint16(sSCanBusDataStruct.qbPayload.count());
    qBlockByteArray += sSCanBusDataStruct.qbPayload;
    qOutDataStream.device()->seek(gk_iFrameDataPos+sSCanBusDataStruct.qbPayload.count());
    quint16 iCrc16 = GetCRC16(qBlockByteArray.data(), qBlockByteArray.count(), 0);
    qOutDataStream << quint16(GetSmallByte(iCrc16));
    return qBlockByteArray;
}


QString GetHostMac(const QString &interfaceName)
{

    foreach (const QNetworkInterface &interface, QNetworkInterface::allInterfaces()) {
        if (interface.name() == interfaceName) {
            foreach (const QNetworkAddressEntry &entry, interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    return interface.hardwareAddress();
                }
            }
        }
    }
    return "";
}

QString GetHostIP(const QString &interfaceName)
{
    foreach (const QNetworkInterface &interface, QNetworkInterface::allInterfaces()) {
        if (interface.name() == interfaceName) {
            foreach (const QNetworkAddressEntry &entry, interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    return entry.ip().toString();
                }
            }
        }
    }
    return "";
}
QHostAddress getInterfaceAddress(const QString &interfaceName)
{
    foreach (const QNetworkInterface &interface, QNetworkInterface::allInterfaces()) {
        if (interface.name() == interfaceName) {
            foreach (const QNetworkAddressEntry &entry, interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    return entry.ip();
                }
            }
        }
    }
    return QHostAddress();
}

QList<quint32> convertUint32ToData(quint32 iData, const QString &strPos)
{
    QStringList strPosList = strPos.split(',');
    // 把QString里面的数据根据','分割成一个列表

    QList<quint32> iResultList;
    // 最终返回的列表

    for (const QString& strPos : strPosList)
    {
        if (strPos.contains('-'))
        {
            QStringList iRangeList = strPos.split('-');
            // 如果包含'-'，说明是一个范围，按照范围来提取数据
            if(iRangeList.size()>1)
            {
                int iStart = iRangeList[0].toInt();
                int iEnd = iRangeList[1].toInt();

                quint32 iNum = 0;
                for(int i = iStart; i <= iEnd; ++i)
                {
                    int iBit = (iData >> i) & 1; // 使用位运算符来读取每一位
                    iNum |= (iBit << (i - iStart));    //将每一位拼接起来
                }
                iResultList.append(iNum);
            }
        }
        else
        {
            // 如果不包含'-'，说明只是一个数字，只取对应的位

            int iIndex = strPos.toInt();
            // 转换 QString 中的数字为 int

            quint32 iBit = (iData >> iIndex) & 1;
            // 提取 data 在 index 位置上的位

            iResultList.append(iBit);
        }
    }

    return iResultList;
}

QStringList convertByteArrayToAsciiList(QByteArray qbReadBuffArray)
{
    QStringList qstrResultList;
    for(int i=0; i<qbReadBuffArray.size(); i++){
        int value = (int)(unsigned char)qbReadBuffArray[i];
        if((i==0 && value == 91) || (i==qbReadBuffArray.size()-1 && value == 93)) {
            continue;  // 跳过 ASCII 值为91([")和93(])
        }
        qstrResultList << QString::number(value);
    }
    return qstrResultList;
}

QString processStringAsProcessDebug(const QString &strInput)
{// 分割字符串
    QStringList strPartsList = strInput.split('|');
    QStringList strProcessedParts;

    for (const QString &strPart : strPartsList) {
        // 找到第一个下划线的位置
        int firstUnderscoreIndex = strPart.indexOf('_');
        if (firstUnderscoreIndex != -1) {
            // 移除第一个下划线及其后面的所有字符
            strProcessedParts.append(strPart.left(firstUnderscoreIndex));
        } else {
            // 如果没有下划线，保留原样
            strProcessedParts.append(strPart);
        }
    }

    // 重组字符串
    return strProcessedParts.join('|');
}

void setupTimer(int iTimeout, QObject *pReceiver, const char *pMethod)
{
    QTimer::singleShot(iTimeout, pReceiver, pMethod);
}

QString findCommandNameById(QString id)
{
#if Q_OS_QML

    QXlsx::Document doc(QCoreApplication::applicationDirPath() + "/cmd/ProtocolSpecification_M1.xlsx"); //指定文件路径
    foreach (QString sheetName, doc.sheetNames()) {
        doc.selectSheet(sheetName);
        int idColumn = 0;
        int nameColumn = 0;
        for (int column = 1; column <= doc.dimension().columnCount(); ++column)
        {
            QString header = doc.read(1, column).toString();
            if (header == "id") idColumn = column;
            if (header == "指令名称") nameColumn = column;
        }
        if (idColumn == 0 || nameColumn == 0) continue;
        for (int row = 2; row <= doc.dimension().rowCount(); ++row)
        {
            QXlsx::Cell *cell=doc.cellAt(row,idColumn);
            QString strReadData = "";
            if(cell) {
                QString formula=doc.read(row,idColumn).toString();
                QString formulaResult=cell->value().toString();

                if(formula.startsWith('=')) { //如果开始字符是"="，则判断其为公式
                    strReadData = formulaResult;
                } else { // 否则,为原始值
                    strReadData = formula;
                }
            }
            if (strReadData == id)
                return doc.read(row, nameColumn).toString();
        }
    }
#endif
    return QString(); //如果在所有表中都未找到，那么将返回一个空字符串
}

QStringList getFilesFromDirectory(const QString &strDirectoryPath)
{
    QDir qDir(strDirectoryPath);
    // 设置过滤器，只列出文件，排除目录
    qDir.setFilter(QDir::Files | QDir::NoDotAndDotDot);
    // 获取目录中所有文件的列表
    QStringList strFileList = qDir.entryList();
    return strFileList;
}

std::tuple<qint64, QList<QByteArray>, QByteArray> splitUpgradeBinaryFile(const QString &filePath, qint64 packageSize)
{
    QFile file(filePath);
    QList<QByteArray> packages;
    QByteArray fileMd5;

    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open file for reading:" << file.errorString();
        return std::make_tuple(0, packages, fileMd5);
    }

    qint64 fileSize = file.size();
    QCryptographicHash md5Hash(QCryptographicHash::Md5);

    while (!file.atEnd()) {
        QByteArray chunk = file.read(packageSize);
        packages.append(chunk);
        md5Hash.addData(chunk);
    }

    fileMd5 = md5Hash.result();
    file.close();
    return std::make_tuple(fileSize, packages, fileMd5);
}
