#include<qdebug.h>
#include<QTime>
#include "publicconfig.h"
#include "MotorDebug.h"
#include "MotorConfigMgr.h"
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include"ccommunicationobject.h"
MotorDebug::MotorDebug()
{
    m_pMotorConfigMgr = &MotorConfigMgr::getInstance();
}

MotorDebug::~MotorDebug()
{

}

void MotorDebug::DebugOperation(const QString strParams)
{
    // 一个完整的调试命令为字符串 (一个MotorDebugOperation + 多个MotorDebugParam-由;(分号)作为间隔)
    //调试项目;get;MotorDebugParam1+MotorDebugParam2+MotorDebugParam3+MotorDebugParam4+MotorDebugParam5+MotorDebugParam6

    QStringList listParams = strParams.split(",");
    if (listParams.size() !=5)
    {
        qDebug()<<__FUNCTION__<<"error,parma = "<<strParams;
        return;
    }

    m_strCurDebugEnumNumModuleName = listParams[0];
    m_strCurDebugEnumStrSubActionName =listParams[1];
    m_strCurDebugMotorName =listParams[2];

    QString strActionMethod = listParams[3];
    QString strParam = listParams[4];

    MotorConfigMgr::MotorActionMethod enumNumberActionMethod;
    enumNumberActionMethod = static_cast<MotorConfigMgr::MotorActionMethod>(strActionMethod.toInt());

    qDebug()<<__FUNCTION__<<"strModuleName="<<m_strCurDebugEnumNumModuleName<<",strSubActionName="<<m_strCurDebugEnumStrSubActionName
           <<",strMotorName="<<m_strCurDebugMotorName<<",strActionMethod="<<strActionMethod<<",strParam="<<strParam;
    MotorConfigInfo structMotorConfigInfo;
    int iBoardIdx;
    int iStatus = m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                        m_strCurDebugEnumStrSubActionName,
                                                        m_strCurDebugMotorName,
                                                        structMotorConfigInfo,iBoardIdx);
    if(iStatus!=0){return;}
    int iMotorIdx = structMotorConfigInfo.iMotorIdx;
    qDebug()<<__FUNCTION__<<"iBoardIdx="<<iBoardIdx<<",iMotorIdx="<<iMotorIdx;
    QString strpayload;
    switch (enumNumberActionMethod)
    {
    case MotorConfigMgr::MotorActionMethod::Action_IncreaseStep:
    {
        strpayload = QString::number(iMotorIdx)+",1,"+strParam;
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_CommonCmd_MOVE, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_DecreaseStep:
    {
        strpayload = QString::number(iMotorIdx)+",0,"+strParam;
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_CommonCmd_MOVE, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_Reset:
    {
        strpayload = QString::number(iMotorIdx);
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_CommonCmd_MotorReset, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_GoTestPos:
    {
        strpayload = QString::number(iMotorIdx)+","+strParam;
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_CommonCmd_MRTP, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_ClawCatch:
    {
        strpayload = QString::number(iMotorIdx)+","+QString::number(100);
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_claw_RunClawToPos, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_ClawRelease:
    {
        strpayload = QString::number(iMotorIdx)+","+QString::number(0);
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_claw_RunClawToPos, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_TakeOffTip:
    {
        strpayload = QString::number(iMotorIdx);
        COperationUnit::getInstance().sendStringData(Method_pump_init, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_SendMethodID:
    {
        qDebug()<<"structMotorConfigInfo.strPamra="<<structMotorConfigInfo.strParam;
        COperationUnit::getInstance().sendStringData(structMotorConfigInfo.strParam.toInt(), "", iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_OpenScanLight:
    {
        //暂时定义样本条的两个是1,2  ，提取的是3
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Left )
        {
            CCommunicationObject::getInstance().StartSingleSamplerCodeScanner(true);
        }
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Right )
        {
            CCommunicationObject::getInstance().StartSingleSamplerCodeScanner(false);
        }
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_ExtractScan )
        {
            CCommunicationObject::getInstance().sendStartScanCmdToZebraScanner();
        }
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_CloseScanLight:
    {
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Left  ||
                iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Right )
        {
            CCommunicationObject::getInstance().StopSamplerCodeScanner();
        }
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_ExtractScan )
        {
            CCommunicationObject::getInstance().closeZebraScanner();
        }
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_Rotate:
    {
        strpayload = QString::number(iMotorIdx);
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        COperationUnit::getInstance().sendStringData(Method_claw_InitRotate, strpayload, iBoardIdx);
        break;
    }
    case MotorConfigMgr::MotorActionMethod::Action_ScanRs:
    {
        strpayload = QString::number(iMotorIdx);
        qDebug()<<__FUNCTION__<<"payload="<<strpayload;
        QStringList strResult;
        if(iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Left )
        {
            CCommunicationObject::getInstance().GetSampleCodeScanLeftResult(strResult);
            CCommunicationObject::getInstance().ResetSampleScanPos();
        }
        else if (iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_SampleScan_Right )
        {
            CCommunicationObject::getInstance().GetSampleCodeScanRightResult(strResult);
            CCommunicationObject::getInstance().ResetSampleScanPos();
        }
        else if (iMotorIdx ==MotorConfigMgr::ScanMotorIdx::Motor_ExtractScan )
        {
            QString strTemp;
            CCommunicationObject::getInstance().GetExtractScanLastRs(strTemp);
            strResult.append(strTemp);
        }
        else
        {
            qDebug()<<"param error";
        }

        qDebug()<<"strResult="<<strResult;
        if(strResult.size()!=0)
        {
            QString strMsg=m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_DoMotion)+","+
                    m_strCurDebugEnumStrSubActionName+","+m_strCurDebugMotorName+","+
                    QString::number(MotorConfigMgr::MotorActionMethod::Action_ScanRs)+","+
                    strResult[0];
            qDebug()<<__FUNCTION__<<"@@Scan Rs,upload Msg="<<strMsg;
            COperationUnit::getInstance().sendStringData(Method_pos_debug, strMsg, Machine_UpperHost);
        }
        break;
    }
    }
}

void MotorDebug::StartDebug(const QString strProject)
{
    // 需要执行开始调试的自检或者初始化时序
    qDebug() << "StartDebug:" << strProject;

    // QString strInitSeq = m_pMotorConfigMgr->GetDebugProjectInitSeq(strProject);// 获取配置文件时序名称(是序号需要在业务动作编写)
    // 获取初始化时序(只考虑当前组件复位)
    // int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(strInitSeq);
    // COperationUnit::getInstance().sendStringData(Method_comp_cmd, strInitSeq, iMachineID);  //发送指令给下位机模块
    //m_iCurrentMachineID = iMachineID;
    m_strCurrentDebugProject = strProject;
}

void MotorDebug::SetMotorPos(const QString strParams)
{
    qDebug()<<"SetMotorPos"<<strParams;
    // 调试项目;set;MotorDebugParam1+$+MotorDebugParam2+$+MotorDebugParam3+$+MotorDebugParam4+$+MotorDebugParam5+$+MotorDebugParam6
    // 例： MotorDebugParam1 由参数(电机名称,执行命令,参数)
    QStringList listParams = strParams.split("+$+");
    for (auto& param:listParams)
    {
        QStringList listInfos = strParams.split(",");
        MotorDebugParam config;// 解析协议
        config.strName  = listInfos[0];
        config.strCmd   = listInfos[1];
        config.strValue = listInfos[2];
        // 电机的motorid 可以由上位机下发，或者在配置文件增加字段
        // 可以使用m_iCurrentMachineID发送
        COperationUnit::getInstance().sendStringData(1056, config.strValue, m_iCurrentMachineID);
    }
}

void MotorDebug::GetMotorPos(const QString strParams)
{
    qDebug()<<"GetMotorPos"<<strParams;

    // 可以在数据库(需要新建一个数据库)获取，也可以在下位机获取

    // 调试项目;get;MotorDebugParam1+$+MotorDebugParam2+$+MotorDebugParam3+$+MotorDebugParam4+$+MotorDebugParam5+$+MotorDebugParam6
    // 例： MotorDebugParam1 由参数(电机名称,执行命令,参数)
    QStringList listParams = strParams.split("+$+");
    for (auto& param:listParams)
    {
        QStringList listInfos = strParams.split(",");
        MotorDebugParam config;// 解析协议
        config.strName  = listInfos[0];
        config.strCmd   = listInfos[1];
        config.strValue = listInfos[2];
    }
    
}

QString MotorDebug::_tranModuleEnumNunToString(int iModuleNum)
{
    QMetaEnum metaType = QMetaEnum::fromType<MotorConfigMgr::UnitModuleFiled>();
    QString  enumstrModuleName = metaType.valueToKey(static_cast<int>(iModuleNum));
    return enumstrModuleName;
}

void MotorDebug::AskDebugPosActionList(QString strInputParam)  //这个是枚举的数字
{
    MotorConfigMgr::UnitModuleFiled enumNumberModuleName;
    enumNumberModuleName = static_cast<MotorConfigMgr::UnitModuleFiled>(strInputParam.toInt());

    QString  enumstrModuleName =  _tranModuleEnumNunToString(enumNumberModuleName);

    qDebug()<<__FUNCTION__<<"Module Name ="<<enumstrModuleName;
    switch (enumNumberModuleName)
    {
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_Sample:
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_Extract:
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_ADP:
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_PCR:
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_SampleScan:
    case MotorConfigMgr::UnitModuleFiled::ModuleFiled_StripScan:
    {
        QString strRs=m_pMotorConfigMgr->GetDebugProjectActionList(enumstrModuleName);
        QString payload = QString::number(enumNumberModuleName)+","
                +QString::number(Method_DebugPos_AskActionList)+","+strRs;
        COperationUnit::getInstance().sendStringResult(Method_pos_debug, payload, Machine_UpperHost);
        qDebug()<<__FUNCTION__<<"strRs="<<strRs;
        break;
    }
    default:
    {
        qDebug()<<__FUNCTION__<<"enum error , enumNumberModuleName="<<enumNumberModuleName;
        break;
    }
    }
    return ;

}

void MotorDebug::AskDebugPosDeviceList(QString strInputParam)
{
    qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 2)
    {
        return;
    }

    m_strCurDebugEnumNumModuleName = listParams[0];
    m_strCurDebugEnumStrSubActionName =listParams[1];

    QStringList strListDeviceName;
    int iSatus1  = m_pMotorConfigMgr->GetActionDeviceListNumAndName(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                                    m_strCurDebugEnumStrSubActionName,strListDeviceName);
    int  iSatus2=m_pMotorConfigMgr->SetAllSubActionMotorFlag(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                             m_strCurDebugEnumStrSubActionName,"NO");  //先设置为pos没回复，须清，多次点击
    if(iSatus1 ==0 || iSatus2!=0)
    {
        qDebug()<<__FUNCTION__<<"GetActionMotorListNumAndName or SetAllSubActionMotorFlag error,strSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return ;
    }
    qDebug()<<__FUNCTION__<<"strSubActionName="<<m_strCurDebugEnumStrSubActionName<<",Device List="<<strListDeviceName;

    QMetaEnum metaEnum =QMetaEnum::fromType<MotorConfigMgr::EnumDeviceType>();
    //遍历MotorList找出信息，压入，
    for(int i=0;i<strListDeviceName.size();i++)
    {
        MotorConfigInfo structMotorConfigInfo;
        int iBoardID ;
        int iStatus=m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                          m_strCurDebugEnumStrSubActionName,strListDeviceName[i],
                                                          structMotorConfigInfo,iBoardID);
        if(iStatus!=0){return;}
        qDebug()<<"@@@structMotorConfigInfo.strDeviceType="<<structMotorConfigInfo.strDeviceType;
        bool ok;
        int EumuDeviceTypeVal = metaEnum.keyToValue(structMotorConfigInfo.strDeviceType.toStdString().c_str(), &ok);
        if (!ok) { qDebug()<<__FUNCTION__<<"conver error ";  return ;}
        //查找
        if(EumuDeviceTypeVal !=MotorConfigMgr::DeviceType_Motor)//非电机类型，不用查询，直接发给上位机 ，再设置标记为ok
        {
            int iOK=m_pMotorConfigMgr->SetMotorConfigInfoFlag(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                              m_strCurDebugEnumStrSubActionName,strListDeviceName[i],"OK");
            if(iOK!=0)
            {
                qDebug()<<__FUNCTION__<<"SetMotorConfigInfoFlag ok Err";
                return;
            }
            //Device1Index$Device1Type$Device1Name$Device1OriginalPos$Device1TestPos
            QString strMsg=m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_AskDeviceList)+","+
                    m_strCurDebugEnumStrSubActionName+","+QString::number(strListDeviceName.size())+","+
                    structMotorConfigInfo.strSubOrdIdx+"$" +
                    QString::number(EumuDeviceTypeVal)+"$" +
                    structMotorConfigInfo.strName+"$"+
                    QString::number(structMotorConfigInfo.iMotorIdx)+"$"+" ";
            qDebug()<<__FUNCTION__<<"@@StrMsg  unMotor="<<strMsg;
            COperationUnit::getInstance().sendStringData(Method_pos_debug, strMsg, Machine_UpperHost);
        }
        else
        {
            if(structMotorConfigInfo.strFlag=="NO")      //能从 iBoardIdx+ iMotorIdx+iPos定位到唯一的
            {
                QString strpayload=QString::number(structMotorConfigInfo.iMotorIdx)+","+QString::number(structMotorConfigInfo.iPosID);
                COperationUnit::getInstance().sendStringData(Method_CommonCmd_GAMP, strpayload,iBoardID);
                qDebug()<<__FUNCTION__<<"@@Send StrMsg="<<strpayload;
            }
        }
    }

}

void MotorDebug::SaveMotorParam(QString strInputParam)
{
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 4)
    {
        qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
        return;
    }
    QString strModuleName = listParams[0];
    QString strSubActionName =listParams[1];
    QString strMotorName =listParams[2];
    QString strParam = listParams[3];

    qDebug()<<__FUNCTION__<<"strModuleName="<<strModuleName<<",strSubActionName="<<strSubActionName
           <<",strMotorName="<<strMotorName<<",strParam="<<strParam;

    //还需要找出该动作对应下位机PosID
    MotorConfigInfo structMotorConfigInfo;
    int iBoardIdx;
    int iStatus =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(strModuleName.toInt()),
                                                       strSubActionName,strMotorName,
                                                       structMotorConfigInfo,iBoardIdx);
    if(iStatus!=0){return;}
    int iPosID=structMotorConfigInfo.iPosID;
    int iMotorIdx = structMotorConfigInfo.iMotorIdx;

    QString strpayload = QString::number(iMotorIdx)+";"+QString::number(iPosID)+","+" "+","+strParam;
    qDebug()<<__FUNCTION__<<"payload="<<strpayload;
    COperationUnit::getInstance().sendStringData(Method_CommonCmd_SCMP, strpayload, iBoardIdx);
}

int MotorDebug::findNextCmdBoardMethodAndParam(QString strInputCmdParam,QString strParam,int iCommonBoardIdx ,
                                               int &iNextBoardIdx,int &iNextMethodID,QString &strPaylodParam,int iRlyMethod)
{
    // BoardIDX &MethodIDX | BoardIDY &MethodIDY
    //param 10|11$12|13&14 - > ( 传入的是已经切割好的输入init或者verify)  即   10|11   或者    12|13&14
    bool bFind =iRlyMethod==-1?true:false;
    QString strData;
    QStringList strListMerge;
    QStringList strListCmd = strInputCmdParam.split("|");
    QStringList strListParam= strParam.split("|");
    if( strParam!="" &&  strListParam.size()!=strListCmd.size())
    {
        qDebug()<<"param error!";
        return -1;
    }
    int iCurBoardID,iCurMethodID;
    int iCurCmdIdx =-1;
    QString strGetPayParam;
    qDebug()<<"strListCmd="<<strListCmd;
    for(int i =0;i<strListCmd.size();i++)
    {
        QStringList strSubList=strListCmd[i].split("&");
        qDebug()<<"strSubList="<<strSubList;
        if (strSubList.size()==1)  //输入的只有MethodId
        {
            iCurBoardID = iCommonBoardIdx;
            iCurMethodID = strSubList[0].toInt();
        }
        else if(strSubList.size()==2)  //包含boardId 及 MethodId
        {
            iCurBoardID     = strSubList[0].toInt();
            iCurMethodID = strSubList[1].toInt();
        }
        else
        {
            qDebug()<<"Param error,param="<<strListCmd[i];
            return -1;
        }
        if(iRlyMethod == iCurMethodID)   {iCurCmdIdx =i; bFind =true;}
        if(strParam=="") {strGetPayParam ="";}
        if(strParam!="") {strGetPayParam =strListParam[i].replace("&",",");}

        strData =QString::number(iCurBoardID)+"&"+QString::number(iCurMethodID)+"&"+strGetPayParam;
        strListMerge.append(strData);
    }
    if(bFind==true && iCurCmdIdx<strListCmd.size()-1)
    {
        qDebug()<<"strListCmd="<<strListCmd<<",strListMerge="<<strListMerge<<",iCurCmdIdx="<<iCurCmdIdx;
        QStringList strListRs= strListMerge[iCurCmdIdx+1].split("&");
        iNextBoardIdx = strListRs[0].toInt();
        iNextMethodID = strListRs[1].toInt();
        strPaylodParam =  strListRs[2];
        return 0;
    }
    else if (bFind==true && iCurCmdIdx==strListCmd.size()-1)
    {
        return 1;
    }
    else
    {
        qDebug()<<"Param error,iRlyMethood idxl="<<iCurCmdIdx<<",bFind="<<bFind;
        return -1;
    }

}

//0当前已经发出 1全部发出   -1失败
int MotorDebug::DoPrepareAction(QString strInputParam,QString strUIPayload,int iRlyMethood)
{
    qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 2)
    {
        return -1;
    }
    m_strCurDebugEnumNumModuleName = listParams[0];
    m_strCurDebugEnumStrSubActionName =listParams[1];
    int iCommonBoardID,iRsBoardID,iRsInitCmd;
    QString strAddParam,strInitParam="",strRsInitCmd,strParam;
    QStringList strListMergeCmd;
    int iStatus = m_pMotorConfigMgr->GetActionPrepareBoardIDAndMethodID(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                                        m_strCurDebugEnumStrSubActionName,iCommonBoardID,strListMergeCmd,strAddParam);
    if(iStatus !=0 )
    {
        qDebug()<<__FUNCTION__<<"DoPrepareAction error,m_strCurDebugEnumStrSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return -1;
    }
    qDebug()<<"strListMergeCmd="<<strListMergeCmd<<",strAddParam="<<strAddParam;
    strRsInitCmd =strListMergeCmd[0];
    if(strAddParam!="" && strAddParam.split("$").size()!=2) {qDebug()<<"Param error";return -1;}
    if(strAddParam.split("$").size()==2) {strInitParam =strAddParam.split("$")[0];}

    QStringList strListParam= strInitParam.split("|");
    if(strListParam.size()!=1 && strUIPayload!="")
    {
        qDebug()<<"now error";  //目前暂时不支持外传入参数的时候不支持有两个cmdId
        return -1;
    }

    iStatus =findNextCmdBoardMethodAndParam(strRsInitCmd,strInitParam,iCommonBoardID,iRsBoardID,iRsInitCmd,strParam,iRlyMethood);
    if(iStatus==-1){return -1;}

    if(iStatus ==0)
    {
        QString strpayload = QString::number(iRsInitCmd);
        if(strUIPayload==""){strpayload=strpayload+","+strParam;}
        if(strUIPayload!="") {strpayload=strpayload+","+strUIPayload;}

        qDebug()<<__FUNCTION__<<"subActionName="<<m_strCurDebugEnumStrSubActionName<<",iRsInitCmd="<<iRsInitCmd<<",payload="<<strpayload;
        COperationUnit::getInstance().sendStringResult(Method_comp_cmd, strpayload, iRsBoardID);
        return 0;
    }
    return 1;
}

//0当前已经发出 1全部发出   -1失败
int MotorDebug::DoVerifyAction(QString strInputParam,QString strUIPayload,int iRlyMethood)
{
    qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 2)
    {
        return -1;
    }
    m_strCurDebugEnumNumModuleName = listParams[0];
    m_strCurDebugEnumStrSubActionName =listParams[1];
    int iCommonBoardID,iRsBoardID,iRsVerifyCmd;
    QString strAddParam,strVerifyParam="",strRsVerifyCmd,strParam;
    QStringList strListMergeCmd;
    int iStatus = m_pMotorConfigMgr->GetActionPrepareBoardIDAndMethodID(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                                        m_strCurDebugEnumStrSubActionName,iCommonBoardID,strListMergeCmd,strAddParam);
    if(iStatus !=0 )
    {
        qDebug()<<__FUNCTION__<<"DoPrepareAction error,m_strCurDebugEnumStrSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return -1;
    }
    strRsVerifyCmd =strListMergeCmd[1];
    if(strAddParam!="" && strAddParam.split("$").size()!=2) {qDebug()<<"Param error";return -1;}
    if(strAddParam.split("$").size()==2) {strVerifyParam =strAddParam.split("$")[1];}

    QStringList strListParam= strVerifyParam.split("|");
    if(strListParam.size()!=1 && strUIPayload!="")
    {
        qDebug()<<"now error";  //目前暂时不支持外传入参数的时候不支持有两个cmdId
        return -1;
    }
    iStatus =findNextCmdBoardMethodAndParam(strRsVerifyCmd,strVerifyParam,iCommonBoardID,iRsBoardID,iRsVerifyCmd,strParam,iRlyMethood);
    if(iStatus==-1){return -1;}

    if(iStatus==0)
    {
        QString strpayload = QString::number(iRsVerifyCmd);
        if(strUIPayload==""){strpayload=strpayload+","+strParam;}
        if(strUIPayload!="") {strpayload=strpayload+","+strUIPayload;}

        qDebug()<<__FUNCTION__<<"subActionName="<<m_strCurDebugEnumStrSubActionName<<",iRsVerifyCmd="<<iRsVerifyCmd<<",payload = "<<strpayload;
        COperationUnit::getInstance().sendStringResult(Method_comp_cmd, strpayload, iRsBoardID);
        return 0;
    }
    return 1;
}

void MotorDebug::SaveSuckZParam(QString strZPos,QString strZMaxPos)
{
    int iRsBoardID;
    //取出两个位置的pos
    MotorConfigInfo structMotorConfigInfoZ,structMotorConfigInfoMaxZ;
    int iStatus1 =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                        m_strCurDebugEnumStrSubActionName,"SuckZ",  //配置档name要填“SuckZ”
                                                        structMotorConfigInfoZ,iRsBoardID);
    int iStatus2 =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                        m_strCurDebugEnumStrSubActionName,"SuckMaxZ",  //配置档name要填“SuckMaxZ”
                                                        structMotorConfigInfoMaxZ,iRsBoardID);
    if(iStatus1 !=0  || iStatus2 !=0 )
    {
        qDebug()<<__FUNCTION__<<"Method_LiquidTest_Save   error,m_strCurDebugEnumStrSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return ;
    }
    int iMotorIdxZ = structMotorConfigInfoZ.iMotorIdx;
    int iZPos= structMotorConfigInfoZ.iPosID;
    int iMotorIdxMaxZ = structMotorConfigInfoMaxZ.iMotorIdx;
    int iMaxZPos= structMotorConfigInfoMaxZ.iPosID;

    QString strpayloadZ = QString::number(iMotorIdxZ)+";"+QString::number(iZPos)+","+" "+","+strZPos;
    QString strpayloadMaxZ = QString::number(iMotorIdxMaxZ)+";"+QString::number(iMaxZPos)+","+" "+","+strZMaxPos;
    qDebug()<<__FUNCTION__<<"Z payload="<<strpayloadZ<<",MaxZ payload="<<strpayloadMaxZ;
    COperationUnit::getInstance().sendStringData(Method_CommonCmd_SCMP, strpayloadZ, iRsBoardID);
    COperationUnit::getInstance().sendStringData(Method_CommonCmd_SCMP, strpayloadMaxZ, iRsBoardID);
    return;
}

void MotorDebug:: SaveSplitZParam(QString strSplitZPos)
{
    int iRsBoardID;
    //取出两个位置的pos
    MotorConfigInfo structMotorConfigInfoZ;
    int iStatus1 =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                        m_strCurDebugEnumStrSubActionName,"SplitZ",  //SplitZ
                                                        structMotorConfigInfoZ,iRsBoardID);

    if(iStatus1 !=0)
    {
        qDebug()<<__FUNCTION__<<"Method_LiquidTest_Save   error,m_strCurDebugEnumStrSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return ;
    }
    int iMotorIdxZ = structMotorConfigInfoZ.iMotorIdx;
    int iZPos= structMotorConfigInfoZ.iPosID;

    QString strpayloadZ = QString::number(iMotorIdxZ)+";"+QString::number(iZPos)+","+" "+","+strSplitZPos;
    qDebug()<<__FUNCTION__<<"Z payload="<<strpayloadZ;
    COperationUnit::getInstance().sendStringData(Method_CommonCmd_SCMP, strpayloadZ, iRsBoardID);
    return;
}

void MotorDebug::DoLiquidTest(QString strInputParam)
{
    //     ModuleName：ModuleFiled_LiquidTest
    //     SubActionName: （此处传字符串，非枚举）
    //       eg: ModuleFiled_LiquidTest-LeftADP_Freeze0,  etc
    //     ActionName:  0 :Go     1：开始液面探测(只能单独发，不能与Go和并)      2：保存    3 复位丢Tip
    //     Param：保存的时候用上；SuckZ|SuckZMax  （界面处的两个值）
    //     Rs:液面探测返回
    qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 4)
    {
        return;
    }
    m_strCurDebugEnumNumModuleName = listParams[0];
    m_strCurDebugEnumStrSubActionName =listParams[1];
    int iActionNum = listParams[2].toInt();
    QString strParam = listParams[3]; //传来的两个极限位置“|”分割志  2个的是suck 1个是split
    int iRsBoardID;  //液面init是go 命令 ，验证按钮就是复位丢tip命令
    MotorConfigInfo structMotorConfigInfo;
    QStringList strListCmd;
    QString strAddParam;
    int iStatus1 =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                        m_strCurDebugEnumStrSubActionName,"ADP",  //配置档name要填“ADP”
                                                        structMotorConfigInfo,iRsBoardID);
    if(iStatus1 !=0 )
    {
        qDebug()<<__FUNCTION__<<"GetMotorConfigInfo  error,m_strCurDebugEnumStrSubActionName="<<m_strCurDebugEnumStrSubActionName;
        return ;
    }
    switch (iActionNum)
    {
    case Method_LiquidTest_Go:
    {
        //配置档ID填在Init栏目
        QString strPreParam=m_strCurDebugEnumNumModuleName+","+m_strCurDebugEnumStrSubActionName;
        DoPrepareAction(strPreParam,"",-1);
        break;
    }
    case Method_LiquidTest_StartDetect:
    {
        QString strpayload = QString::number(structMotorConfigInfo.iMotorIdx);
        COperationUnit::getInstance().sendStringResult(Method_CommonCmd_LiquidDetect, strpayload, iRsBoardID);
        break;
    }
    case Method_LiquidTest_Save:
    {
        QStringList listSaveParams = strParam.split("|");
        if (listSaveParams.size() == 1)
        {
            SaveSplitZParam(listSaveParams[0]);
        }
        else if  (listSaveParams.size() == 2 )
        {
            SaveSuckZParam(listSaveParams[0],listSaveParams[1]);
        }
        else
        {
            qDebug()<<"save param err , is"<<strParam;
            return;
        }

        break;
    }
    case Method_Liquidest_Reset:
    {
        //配置档ID填在verify栏目
        QString strPreParam=m_strCurDebugEnumNumModuleName+","+m_strCurDebugEnumStrSubActionName;
        DoVerifyAction(strPreParam,"",-1);
        break;
    }
    }

}

void MotorDebug::DoLiquidCalSuck(QString strInputParam)
{
    qDebug()<<__FUNCTION__<<"strInputParam ="<<strInputParam;
    QStringList listParams = strInputParam.split(",");
    if (listParams.size() != 2)
    {
        return;
    }
    m_strCurDebugEnumNumModuleName = listParams[0];

    QStringList strList = listParams[1].split("|");
    if (strList.size() != 5)
    {
        qDebug()<<"Param error ,param ="<<listParams[1];
        return;
    }
    int iADPidx = strList[0].toInt();   //移液器序号  1右边   2:左边
    int iTipVol = strList[1].toInt();    //Tip体积
    int iActionIdx = strList[2].toInt();  //0 go 1 Reset  //2上样  //3 下样
    int iPosIdx = strList[3].toInt();    //去到的耗材位置
    int iSuckVol = strList[4].toInt();  // 步数

    //根据ADP和Tip体积决定调用那个项目号
    QString strTitle="";
    QString strPayload ="";
    if(iADPidx ==1)  //右边
    {
        if(iTipVol==200)    {strTitle= "RightADP200"; strPayload =QString::number(iPosIdx)+",2000,"+QString::number(iSuckVol);}
        if(iTipVol ==1000)  {strTitle= "RightADP1000";strPayload =QString::number(iPosIdx)+",10000,"+QString::number(iSuckVol);}
    }
    if(iADPidx ==2)  //左边
    {
        if(iTipVol==200)    {strTitle= "LeftADP200"; strPayload =QString::number(iPosIdx)+",2000,"+QString::number(iSuckVol);}
        if(iTipVol ==1000)  {strTitle= "LeftADP1000";strPayload =QString::number(iPosIdx)+",10000,"+QString::number(iSuckVol);}
    }
    if(iActionIdx ==2 || iActionIdx==3)  //2 与3 是上下样，在toml的title修改
    {
        strTitle= "SampleAction";
        strPayload="";
    }

    m_strCurDebugEnumStrSubActionName = strTitle;
    qDebug()<<"strTitle="<<strTitle<<",strPayload="<<strPayload;
    //吸液参数由UI传入，另外传入参数的时候不支持有两个cmdId
    if(iActionIdx ==0 || iActionIdx==2) //0 go 1 Reset  2上样  3 下样
    {
        QString strPreParam=m_strCurDebugEnumNumModuleName+","+m_strCurDebugEnumStrSubActionName;
        DoPrepareAction(strPreParam,strPayload,-1); //有个吸液的参数
    }
    if(iActionIdx ==1 || iActionIdx==3)
    {
        QString strPreParam=m_strCurDebugEnumNumModuleName+","+m_strCurDebugEnumStrSubActionName;
        DoVerifyAction(strPreParam,"",-1);
    }

}

void MotorDebug::HandleCmdReply(quint16 uiComplexID, QString strpayload,quint16 uiResult)
{
    qDebug()<<__FUNCTION__<<"enter HandleCmdReply,uiComplexID="<<uiComplexID<<",strpayload="<<strpayload<<",uiResult="<<uiResult;
    //需要处理执行失败情况
    switch (uiComplexID)
    {
    case Method_comp_cmd:
    {
        QString strPreParam=m_strCurDebugEnumNumModuleName+","+m_strCurDebugEnumStrSubActionName;
        int iStatus = DoPrepareAction(strPreParam,"",strpayload.toInt()); ////找到strpayload再发出
        if(iStatus==1)
        {
            QString strMsg =m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_Prepare)+","+m_strCurDebugEnumStrSubActionName+","+QString::number(uiResult);
            qDebug()<<"strMsg="<<strMsg;
            COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost);
        }
        else{qDebug()<<"Reply not find in prepare Cmd";}
        iStatus= DoVerifyAction(strPreParam,"",strpayload.toInt()); //找到strpayload再发出
        if(iStatus==1)
        {
            QString strMsg =m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_Verify)+","+m_strCurDebugEnumStrSubActionName;
            COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost,uiResult);
        }
        else{qDebug()<<"Reply not find in verify Cmd";}

        break;
    }
    case Method_CommonCmd_MRTP:
    {
        QStringList strList = strpayload.split(",");  //第一个为电机的id，后续并行的时候，可以用这个区分是那个电机，第二个是值
        if(strList.size()!=2)
        {
            qDebug()<<__FUNCTION__<<",error ,strpayload="<<strpayload;
            return;
        }
        QString strMsg =m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_DoMotion)+","+m_strCurDebugEnumStrSubActionName+","+
                m_strCurDebugMotorName+","+QString::number(MotorConfigMgr::MotorActionMethod::Action_GoTestPos)+","+strList[1];
        qDebug()<<"strMsg="<<strMsg;
        COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost);
        break;
    }
    case Method_CommonCmd_MOVE:
    {
        QStringList strList = strpayload.split(",");
        if(strList.size()!=2)
        {
            qDebug()<<__FUNCTION__<<",error ,strpayload="<<strpayload;
            return;
        }
        QString strMsg =m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_DoMotion)+","+m_strCurDebugEnumStrSubActionName+","+
                m_strCurDebugMotorName+","+QString::number(MotorConfigMgr::MotorActionMethod::Action_IncreaseStep)+","+strList[1];   //增加减小都用 Action_DecreaseStep
        qDebug()<<"strMsg="<<strMsg;
        COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost);
        break;
    }
    case Method_CommonCmd_MotorReset:
    {
        QStringList strList = strpayload.split(",");
        if(strList.size()!=2)
        {
            qDebug()<<__FUNCTION__<<",error ,strpayload="<<strpayload;
            return;
        }
        QString strMsg =m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_DoMotion)+","+m_strCurDebugEnumStrSubActionName+","+
                m_strCurDebugMotorName+","+QString::number(MotorConfigMgr::MotorActionMethod::Action_Reset)+","+strList[1];   //增加减小都用 Action_DecreaseStep
        qDebug()<<"strMsg="<<strMsg;
        COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost);
        break;
    }
    case Method_CommonCmd_GAMP:
    {
        qDebug()<<__FUNCTION__<<"Method_CommonCmd_GAMP="<<strpayload;
        QStringList strList = strpayload.split(",");  //下位机定义的第四个为位置
        if(strList.size()!=4)
        {
            qDebug()<<__FUNCTION__<<",error ,Method_CommonCmd_GAMP strpayload="<<strpayload;
            return;
        }
        //[motorID,posID,posName,posVal]
        int iMotorID= strList[0].toInt();
        int iPosID= strList[1].toInt();
        int iPosVal= strList[3].toInt();

        QStringList strListDeviceName;
        int iSatus  = m_pMotorConfigMgr->GetActionDeviceListNumAndName(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                                       m_strCurDebugEnumStrSubActionName,
                                                                       strListDeviceName);
        if(iSatus ==0)
        {
            qDebug()<<__FUNCTION__<<"GetActionMotorListNumAndName error,strSubActionName="<<m_strCurDebugEnumStrSubActionName;
            return ;
        }
        for(int i=0;i<strListDeviceName.size();i++)
        {
            MotorConfigInfo structMotorConfigInfo ;
            int iUnuseBoardidx;
            int iOK =m_pMotorConfigMgr->GetMotorConfigInfo(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                           m_strCurDebugEnumStrSubActionName,
                                                           strListDeviceName[i],
                                                           structMotorConfigInfo,iUnuseBoardidx);
            if(iOK!=0){return;}
            iOK=m_pMotorConfigMgr->SetMotorConfigInfoFlag(_tranModuleEnumNunToString(m_strCurDebugEnumNumModuleName.toInt()),
                                                          m_strCurDebugEnumStrSubActionName,
                                                          strListDeviceName[i],"OK");
            if(iOK!=0){
                qDebug()<<__FUNCTION__<<"set ok error";
                return;
            }
            if(structMotorConfigInfo.iPosID == iPosID &&
                    structMotorConfigInfo.iMotorIdx ==  iMotorID  ) //下位机返回了motorID,posID，根据这个找出strname在Qhash    电机的才进入这个
            {
                //Device1Index$Device1Type$Device1Name$Device1OriginalPos$Device1TestPos
                QString strMsg=m_strCurDebugEnumNumModuleName+","+QString::number(Method_DebugPos_AskDeviceList)+","+
                        m_strCurDebugEnumStrSubActionName+","+QString::number(strListDeviceName.size())+","+
                        structMotorConfigInfo.strSubOrdIdx+"$"+
                        QString::number(MotorConfigMgr::DeviceType_Motor)+"$" +
                        structMotorConfigInfo.strName+"$"+
                        QString::number(structMotorConfigInfo.iIsNeedSave)+"$"+
                        QString::number(iPosVal);
                qDebug()<<__FUNCTION__<<"@@StrMsg Motor="<<strMsg;
                COperationUnit::getInstance().sendStringData(Method_pos_debug, strMsg, Machine_UpperHost);
                break;
            }
        }
        break;
    }
    case Method_CommonCmd_LiquidDetect:
    {
        QStringList strListRs = strpayload.split(",");   //第二个是位置
        if(strListRs.size()!=2)
        {
            qDebug()<<"LIquid Rs error ,is "<<strpayload;
            return;
        }
        QString strMsg =QString::number(MotorConfigMgr::UnitModuleFiled::ModuleFiled_LiquidTest)+","
                +QString::number(Method_DebugPos_LiquidTest)+","
                +QString::number(Method_LiquidTest_StartDetect)+","+strListRs[1];
        qDebug()<<"strMsg="<<strMsg;
        COperationUnit::getInstance().sendStringResult(Method_pos_debug, strMsg, Machine_UpperHost);
        break;
    }
    default:
        break;
    }
}

void MotorDebug::_SaveParam()
{
    // 保存到数据库或者配置文件
}

void MotorDebug::_Prev()
{
}

void MotorDebug::_Next()
{
}
