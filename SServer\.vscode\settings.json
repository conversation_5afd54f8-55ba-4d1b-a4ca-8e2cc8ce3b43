{
    // ==================== MarsCode插件配置 ====================
    "marscode.codeCompletionPro": {
        "enableCodeCompletionPro": true    // 启用AI代码补全增强功能
    },
    "marscode.enableInlineCommand": true,   // 启用内联命令功能

    // ==================== 文件类型关联配置 ====================
    // 告诉VSCode如何识别无扩展名的C++头文件，提供正确的语法高亮和智能提示
    "files.associations": {
        "array": "cpp",                     // 将 array 文件识别为C++
        "deque": "cpp",                     // 将 deque 文件识别为C++
        "list": "cpp",                      // 将 list 文件识别为C++
        "string": "cpp",                    // 将 string 文件识别为C++
        "unordered_map": "cpp",             // 将 unordered_map 文件识别为C++
        "vector": "cpp",                    // 将 vector 文件识别为C++
        "string_view": "cpp",               // 将 string_view 文件识别为C++
        "initializer_list": "cpp",          // 将 initializer_list 文件识别为C++
        "atomic": "cpp",                    // 将 atomic 文件识别为C++
        "*.tcc": "cpp",                     // 将 .tcc 文件识别为C++
        "bitset": "cpp",                    // 将 bitset 文件识别为C++
        "cctype": "cpp",                    // 将 cctype 文件识别为C++
        "chrono": "cpp",                    // 将 chrono 文件识别为C++
        "clocale": "cpp",                   // 将 clocale 文件识别为C++
        "cmath": "cpp",                     // 将 cmath 文件识别为C++
        "codecvt": "cpp",                   // 将 codecvt 文件识别为C++
        "condition_variable": "cpp",        // 将 condition_variable 文件识别为C++
        "csignal": "cpp",                   // 将 csignal 文件识别为C++
        "cstdarg": "cpp",                   // 将 cstdarg 文件识别为C++
        "cstddef": "cpp",                   // 将 cstddef 文件识别为C++
        "cstdint": "cpp",                   // 将 cstdint 文件识别为C++
        "cstdio": "cpp",                    // 将 cstdio 文件识别为C++
        "cstdlib": "cpp",                   // 将 cstdlib 文件识别为C++
        "cstring": "cpp",                   // 将 cstring 文件识别为C++
        "ctime": "cpp",                     // 将 ctime 文件识别为C++
        "cwchar": "cpp",                    // 将 cwchar 文件识别为C++
        "cwctype": "cpp",                   // 将 cwctype 文件识别为C++
        "exception": "cpp",                 // 将 exception 文件识别为C++
        "algorithm": "cpp",                 // 将 algorithm 文件识别为C++
        "filesystem": "cpp",                // 将 filesystem 文件识别为C++
        "functional": "cpp",                // 将 functional 文件识别为C++
        "iterator": "cpp",                  // 将 iterator 文件识别为C++
        "map": "cpp",                       // 将 map 文件识别为C++
        "memory": "cpp",                    // 将 memory 文件识别为C++
        "memory_resource": "cpp",           // 将 memory_resource 文件识别为C++
        "numeric": "cpp",                   // 将 numeric 文件识别为C++
        "optional": "cpp",                  // 将 optional 文件识别为C++
        "random": "cpp",                    // 将 random 文件识别为C++
        "ratio": "cpp",                     // 将 ratio 文件识别为C++
        "source_location": "cpp",           // 将 source_location 文件识别为C++
        "system_error": "cpp",              // 将 system_error 文件识别为C++
        "tuple": "cpp",                     // 将 tuple 文件识别为C++
        "type_traits": "cpp",               // 将 type_traits 文件识别为C++
        "utility": "cpp",                   // 将 utility 文件识别为C++
        "fstream": "cpp",                   // 将 fstream 文件识别为C++
        "future": "cpp",                    // 将 future 文件识别为C++
        "iomanip": "cpp",                   // 将 iomanip 文件识别为C++
        "iosfwd": "cpp",                    // 将 iosfwd 文件识别为C++
        "iostream": "cpp",                  // 将 iostream 文件识别为C++
        "istream": "cpp",                   // 将 istream 文件识别为C++
        "limits": "cpp",                    // 将 limits 文件识别为C++
        "mutex": "cpp",                     // 将 mutex 文件识别为C++
        "new": "cpp",                       // 将 new 文件识别为C++
        "ostream": "cpp",                   // 将 ostream 文件识别为C++
        "sstream": "cpp",                   // 将 sstream 文件识别为C++
        "stdexcept": "cpp",                 // 将 stdexcept 文件识别为C++
        "streambuf": "cpp",                 // 将 streambuf 文件识别为C++
        "thread": "cpp",                    // 将 thread 文件识别为C++
        "cinttypes": "cpp",                 // 将 cinttypes 文件识别为C++
        "typeinfo": "cpp",                  // 将 typeinfo 文件识别为C++
        "variant": "cpp",                   // 将 variant 文件识别为C++
        "qnetworkconfigurationmanager": "cpp", // Qt网络配置管理器文件识别为C++
        "qqmlapplicationengine": "cpp",     // Qt QML应用引擎文件识别为C++
        "*.qml": "qml",                     // QML文件识别
        "*.pro": "qmake",                   // Qt项目文件识别
        "*.ui": "xml"                       // Qt界面文件识别为XML
    },

    // ==================== C++插件配置 ====================
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", // 使用CMake工具提供配置
    "C_Cpp.default.intelliSenseMode": "windows-gcc-x64",           // 智能感知模式：Windows GCC 64位
    "C_Cpp.default.compilerPath": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/gcc.exe", // 编译器路径
    "C_Cpp.default.cppStandard": "c++17",                          // C++17标准
    "C_Cpp.default.cStandard": "c17",                              // C17标准
    "C_Cpp.intelliSenseEngine": "default",                         // 默认智能感知引擎
    "C_Cpp.errorSquiggles": "enabled",                             // 启用错误波浪线提示
    "C_Cpp.autocomplete": "default",                               // 默认自动补全
    "C_Cpp.formatting": "vcFormat",                                // 使用VC格式化器

    // ==================== 调试器配置 ====================
    "debug.console.fontSize": 14,                      // 调试控制台字体大小
    "debug.console.fontFamily": "Consolas",            // 调试控制台字体
    "debug.allowBreakpointsEverywhere": true,          // 允许在任何地方设置断点
    "debug.showInlineBreakpointCandidates": true,      // 显示内联断点候选
    "debug.showSubSessionsInToolBar": true,            // 在工具栏显示子会话
    "debug.console.acceptSuggestionOnEnter": "on",     // 回车接受建议

    // ==================== 终端环境变量配置 ====================
    "terminal.integrated.env.windows": {
        // 添加Qt和MinGW到系统PATH
        "PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}",
        "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins", // Qt平台插件路径
        "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins",              // Qt插件路径
        "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml",               // QML导入路径
        // Qt日志规则：启用默认debug，禁用Qt内部debug，启用警告/错误/致命错误
        "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true",
        "QT_FORCE_STDERR_LOGGING": "1",                                           // 强制stderr输出
        "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}",   // 日志消息格式
        "PYTHONIOENCODING": "utf-8",                                              // Python编码
        "CHCP": "65001"                                                           // 代码页UTF-8
    },

    // ==================== 终端界面配置 ====================
    "terminal.integrated.fontSize": 14,                            // 终端字体大小
    "terminal.integrated.fontFamily": "JetBrains Mono, Consolas, 'Courier New', monospace", // 字体列表
    "terminal.integrated.fontWeight": "normal",                    // 字体粗细
    "terminal.integrated.lineHeight": 1.2,                        // 行高
    "terminal.integrated.defaultProfile.windows": "PowerShell",    // 默认Windows终端：PowerShell
    "terminal.integrated.encoding": "utf8",                       // 终端编码
    // PowerShell启动参数：无配置文件，绕过执行策略，设置UTF-8编码
    "terminal.integrated.shellArgs.windows": ["-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", "chcp 65001 >$null; powershell.exe"],
    "terminal.integrated.smoothScrolling": true,                  // 平滑滚动
    "terminal.integrated.cursorBlinking": true,                   // 光标闪烁
    "terminal.integrated.cursorStyle": "line",                    // 光标样式：线条
    "terminal.integrated.rightClickBehavior": "copyPaste",        // 右键行为：复制粘贴
    "terminal.integrated.copyOnSelection": true,                  // 选中时自动复制

    // ==================== Qt插件配置 ====================
    "qt.qtdir": "D:/QT/Qt5.12.8/5.12.8/mingw73_64",              // Qt安装目录
    "qt.qmake": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin/qmake.exe", // qmake路径
    "qt.creator": "D:/QT/Tools/QtCreator/bin/qtcreator.exe",      // Qt Creator路径

    // ==================== 格式化配置（已禁用） ====================
    "qml.format.formatOnSave": false,          // QML保存时不格式化
    "qml.format.insertFinalNewline": false,    // QML文件末尾不插入换行
    "qml.format.alignObjectProperties": false, // QML对象属性不对齐
    "editor.formatOnSave": false,              // 保存时不格式化
    "editor.formatOnType": false,              // 输入时不格式化
    "editor.formatOnPaste": false,             // 粘贴时不格式化

    // ==================== 编辑器行为配置 ====================
    "editor.tabSize": 4,                       // Tab大小：4个空格
    "editor.insertSpaces": true,               // 插入空格而非Tab字符
    "editor.detectIndentation": false,         // 不自动检测缩进（强制使用tabSize）
    "editor.rulers": [80, 120],                // 显示标尺线：80列和120列
    "editor.wordWrap": "bounded",              // 限制换行
    "editor.wordWrapColumn": 120,              // 在120列处换行

    // ==================== 文件处理配置（已禁用） ====================
    "files.eol": "\n",                         // 行结束符：LF（Unix风格）
    "files.encoding": "utf8",                  // 文件编码：UTF-8
    "files.trimTrailingWhitespace": false,     // 不移除行尾空格
    "files.insertFinalNewline": false,         // 不插入文件末尾换行

    // ==================== 搜索排除配置 ====================
    "search.exclude": {
        "**/build": true,           // 搜索时排除build目录
        "**/debug": true,           // 排除debug目录
        "**/release": true,         // 排除release目录
        "**/.git": true,            // 排除git目录
        "**/node_modules": true,    // 排除node_modules
        "**/bower_components": true, // 排除bower_components
        "**/*.tmp": true,           // 排除临时文件
        "**/*.o": true,             // 排除对象文件
        "**/*.exe": true,           // 排除可执行文件
        "**/*.dll": true,           // 排除动态库
        "**/*.pdb": true,           // 排除调试符号文件
        "**/Makefile*": true,       // 排除Makefile
        "**/*.pro.user*": true      // 排除Qt用户配置文件
    },

    // ==================== 文件资源管理器排除配置 ====================
    "files.exclude": {
        "**/build": false,          // 文件管理器中显示build目录
        "**/debug": false,          // 显示debug目录
        "**/release": false,        // 显示release目录
        "**/.git": true,            // 排除git目录
        "**/node_modules": true,    // 排除node_modules
        "**/bower_components": true, // 排除bower_components
        "**/*.tmp": true,           // 排除临时文件
        "**/*.o": true,             // 排除对象文件
        "**/*.pdb": true,           // 排除调试符号文件
        "**/*.pro.user*": true      // 排除Qt用户配置文件
    },

    // ==================== 问题面板配置 ====================
    "problems.decorations.enabled": true,      // 启用问题装饰
    "problems.showCurrentInStatus": true,      // 在状态栏显示当前问题

    // ==================== 界面主题配置 ====================
    "workbench.colorTheme": "Shades of Purple (Super Dark)",              // 主题：GitHub深色
    "workbench.iconTheme": "material-icon-theme",       // 图标主题：Material
    "terminal.integrated.minimumContrastRatio": 4.5,    // 终端最小对比度
    "terminal.integrated.tabs.enabled": true,           // 启用终端选项卡
    "terminal.integrated.tabs.location": "right",       // 选项卡位置：右侧
    "terminal.integrated.gpuAcceleration": "on",        // 启用GPU加速

    // ==================== 输出颜色化配置 ====================
    // 匹配spdlog格式：[debug], [info], [warn], [error], [critical]
    "output-colorizer.includePatterns": [
        ".*\\[D\\].*",                              // 为debug日志着色
        ".*\\[I\\].*",                               // 为info日志着色  
        ".*\\[W\\].*",                               // 为warn日志着色
        ".*\\[F\\].*",                            // 为warning日志着色
        ".*\\[E\\].*",                              // 为error日志着色
        ".*\\[C\\].*"                            // 为critical日志着色
    ],

    // ==================== 注释增强配置 ====================
    "better-comments.tags": [
        {
            "tag": "!",                 // !标记的注释
            "color": "#FF2D00",         // 红色
            "strikethrough": false,     // 不删除线
            "underline": false,
            "backgroundColor": "transparent",
            "bold": false,
            "italic": false
        },
        {
            "tag": "?",                 // ?标记的注释
            "color": "#3498DB",         // 蓝色
            "strikethrough": false,
            "underline": false,
            "backgroundColor": "transparent",
            "bold": false,
            "italic": false
        },
        {
            "tag": "//",                // //标记的注释
            "color": "#474747",         // 灰色
            "strikethrough": true,      // 删除线效果
            "underline": false,
            "backgroundColor": "transparent",
            "bold": false,
            "italic": false
        },
        {
            "tag": "todo",              // TODO注释
            "color": "#FF8C00",         // 橙色
            "strikethrough": false,
            "underline": false,
            "backgroundColor": "transparent",
            "bold": false,
            "italic": false
        },
        {
            "tag": "*",                 // *标记的注释
            "color": "#98C379",         // 绿色
            "strikethrough": false,
            "underline": false,
            "backgroundColor": "transparent",
            "bold": false,
            "italic": false
        }
    ],
    "cmake.options.statusBarVisibility": "visible",
    "cmake.buildDirectory": "${workspaceFolder}\\builds\\${buildKit}\\${buildType}",
    "cmake.useCMakePresets": "never"
}
