﻿#include "errorcodedb.h"
#include <QDebug>
#include <QSqlError>
#include <QString>
#include "publicconfig.h"
#include "cglobalconfig.h"
#ifdef Q_OS_QML
#include "qxlsx/xlsxdocument.h"
#endif

ErrorCodeDB::ErrorCodeDB(QObject *parent)
{
    Q_UNUSED(parent);
    m_strErrorCodeFieldsTabelName = "errorCodeInfo";
    m_strErrorCodeFieldsFieldsList << errorCodeFields.error_code << errorCodeFields.board_name
                                   << errorCodeFields.board_id << errorCodeFields.business_name << errorCodeFields.business_id
                                   << errorCodeFields.fault_code << errorCodeFields.fault_level_description
                                   << errorCodeFields.fault_level << errorCodeFields.user_fault_description
                                   << errorCodeFields.factory_fault_description << errorCodeFields.fault_handle_plan;
}

ErrorCodeDB &ErrorCodeDB::getInstance()
{
    static ErrorCodeDB ErrorCodeDB;
    return ErrorCodeDB;
}

void ErrorCodeDB::initDataBase()
{
    // 创建数据库
    QList<QStringList> strFieldNameLists;
    strFieldNameLists << m_strErrorCodeFieldsFieldsList;
    QStringList strTableNameList;
    strTableNameList << m_strErrorCodeFieldsTabelName;

    QVector<QPair<QString, QString>> fieldsAndTypes;
    QStringList strFieldNameList ;
    for(int i = 0; i != strFieldNameLists.length(); ++i)
    {
        fieldsAndTypes.clear();
        strFieldNameList = strFieldNameLists.at(i);
        for(auto strField : strFieldNameList)
        {
            fieldsAndTypes.push_back({strField, "VARCHAR"});
        }

        if (this->createDBTable(strTableNameList.at(i), fieldsAndTypes))
        {
            QDFUN_LINE << strTableNameList.at(i) << "Table created successfully";
        }
        else
        {
            QDFUN_LINE << strTableNameList.at(i) << "Failed to create table";
        }
    }
    //readFromXlsxAndSave();
}

bool ErrorCodeDB::addErrorCodeInfo(QStringList strDataList)
{
    bool bResult = this->addOneDBRecord(m_strErrorCodeFieldsTabelName,
                                        m_strErrorCodeFieldsFieldsList, strDataList, errorCodeFields.error_code);
    return bResult;
}

QString ErrorCodeDB::getFaultLevelFromErrorCode(QString strErrorCode)
{
    QString strQueryResult ;
    if(strErrorCode == "")
    {
        return strQueryResult;
    }
    QMap<QString, QString> strConditionMap =
    {{errorCodeFields.error_code, strErrorCode}};
    strQueryResult = this->getOneRecordOneField(m_strErrorCodeFieldsTabelName,
                                                  errorCodeFields.fault_level, strConditionMap).toString();
    return strQueryResult;
}

QStringList ErrorCodeDB::getErrorInfoFromErrorCode(QString strErrorCode)
{
    QStringList strQueryResult ;
    if(strErrorCode == "")
    {
        return strQueryResult;
    }
    QMap<QString, QString> strConditionMap =
    {{errorCodeFields.error_code, strErrorCode}};
    strQueryResult = this->getOneRecordMoreFields(m_strErrorCodeFieldsTabelName,
                                                  m_strErrorCodeFieldsFieldsList, strConditionMap);
    return strQueryResult;
}

#ifdef Q_OS_QML
void ErrorCodeDB::readFromXlsxAndSave(QString strFilePath)
{
    if(strFilePath=="")
    {
        strFilePath = CGlobalConfig::getInstance().getFaultCodeXlsxPath();
    }
    QXlsx::Document doc(strFilePath);
    //遍历每个表格
    QRegExp regularHEX("\\(0x[0-9A-Fa-f]{2}\\)");
    QRegExp regularNumber("[0-9A-Fa-f]*");
    foreach (QString sheetName, doc.sheetNames()) {
        if (regularHEX.indexIn(sheetName) != -1) { // 如果sheet名称符合条件
            doc.selectSheet(sheetName);
            for (int row = 3; row <= doc.dimension().rowCount(); ++row)
            {
                QXlsx::Cell *cell=doc.cellAt(row,1);
                QString strReadData = "";
                if(cell) {
                    QString formula=doc.read(row,1).toString();
                    QString formulaResult=cell->value().toString();

                    if(formula.startsWith('=')) { //如果开始字符是"="，则判断其为公式
                        strReadData = formulaResult;
                    } else { // 否则,为原始值
                        strReadData = formula;
                    }
                }
                if (regularNumber.exactMatch(strReadData)&&strReadData!="")
                {
                    QStringList strErrorCodeInfoList;
                    int iMinColumnCount = qMin(11, doc.dimension().columnCount());
                    for(int col = 1; col <= iMinColumnCount; ++col){
                        QXlsx::Cell *cell=doc.cellAt(row,col);
                        QString strReadData = "";
                        if(cell) {
                            QString formula=doc.read(row,col).toString();
                            QString formulaResult=cell->value().toString();

                            if(formula.startsWith('=')) { //如果开始字符是"="，则判断其为公式
                                strReadData = formulaResult;
                            } else { // 否则,为原始值
                                strReadData = formula;
                            }
                        }
                        strErrorCodeInfoList << strReadData;
                    }
                    addErrorCodeInfo(strErrorCodeInfoList);
                }
            }
        }
    }
}

#endif
