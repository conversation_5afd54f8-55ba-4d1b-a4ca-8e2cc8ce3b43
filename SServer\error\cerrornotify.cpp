#include "cerrornotify.h"
#include <QDebug>
#include "control/coperationunit.h"

CErrorNotify &CErrorNotify::getInstance()
{
    static CErrorNotify instance;
    return instance;
}



CErrorNotify::CErrorNotify(QObject *parent)
    : QThread(parent)
  , m_bThreadExit(false)
{

}

CErrorNotify::~CErrorNotify()
{
    m_bThreadExit = true;
}

void CErrorNotify::addErrorInfoItem(MidMachineSubmodule subModule, ErrorID errorID, QString strExtraInfo)
{
    QString strErrorCode = getErrorCode(subModule, errorID);
    addErrorInfoItem(strErrorCode, strExtraInfo);
    qDebug()<<"addErrorInfoItem: "<<subModule<<errorID<<strErrorCode<<strExtraInfo;
}


void CErrorNotify::addErrorInfoItem(QString strErrorCode, QString strExtraInfo)
{
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qErrorInfoList[m_iCurrentWriteIndex].strErrorCode = strErrorCode;
    m_qErrorInfoList[m_iCurrentWriteIndex].strExtraInfo = strExtraInfo;
    m_iWriteIndex.store(m_iNextWriteIndex);
    m_conditionVariable.notify_one();// 唤醒
}

void CErrorNotify::run()
{
    qDebug() << "Starting _createConcurrentThread in" << this;
    std::unique_lock<std::mutex> uniqueLock(this->m_mutex);
    while(!this->m_bThreadExit)
    {
        this->m_conditionVariable.wait(uniqueLock, [this]
        {
            return this->m_iReadIndex.load() != this->m_iWriteIndex.load()
                    || this->m_bThreadExit;
        });
        if (this->m_bThreadExit)
        {
            break;
        }
        this->_HandleReceiveList();
    }
}

void CErrorNotify::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        ErrorInfoItem item = m_qErrorInfoList[m_iReadIndex.load()];
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        QString strErrorInfo = QString("%1,%2").arg(item.strErrorCode).arg(item.strExtraInfo);
        static QString strLastErrorCode;
        // qDebug()<<"CErrorNotify::_HandleReceiveList";
        //需要拦截重复的错误，避免重复上报
        if (strLastErrorCode.compare(strErrorInfo) == 0)
        {
            continue;
        }
        strLastErrorCode = strErrorInfo;   

        //中位机的错误需要同时上发上位机及中位机进行处理
        COperationUnit::getInstance().sendStringData(Method_error_info, strErrorInfo, Machine_UpperHost);
        COperationUnit::getInstance().sendStringData(Method_error_info, strErrorInfo, Machine_Middle_Host);
    }
}

