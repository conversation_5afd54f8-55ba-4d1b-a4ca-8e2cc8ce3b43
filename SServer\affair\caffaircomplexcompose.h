#ifndef CAFFAIRCOMPLEXCOMPOSE_H
#define CAFFAIRCOMPLEXCOMPOSE_H

#include <QObject>
#include <QMutex>
#include <QTimer>

#include "caffairbase.h"

struct SContentStruct
{
    int iMachineID;
    QString strComplexID;
    QString strComplexContent;// id,param
    quint8 quSync;// 0：不并行，1：并行
};

struct SSubCircleInfoStruct
{
    QString strParamName;  //名称
    int iInitVal;    // 初始值
    int iStepVal;  //步进值
    int iUseCnt; //已经使用次数
    bool bUseInCurCircle; //已经在本次循环使用
};


class CAffairComplexCompose : public CAffairBase
{
    Q_OBJECT
public:
    explicit CAffairComplexCompose(QObject *parent = nullptr);
    ~CAffairComplexCompose();

public:
    void startComplexCompose(QString strContent);
    void stopComplexCompose();
    bool getIsRunning();
    void setFinished();

private:
    /**
     * @brief _packageSubCicrleCmd 指定某个循环的开始与结束
     * @param sOriginContentSourceList
     * @param iSubStartIdx 循环开始的序号   后面是参数
     * @param iSubEndIdx 循环结束到序号    后面加循环次数, iSubStartIdx~iSubEndIdx中间的就是打包到信息
     * @param sPackageCmd
     */
    void _packageSubCicrleCmd( QList<SContentStruct>  sOriginContentSourceList,int iSubStartIdx,int iSubEndIdx,QList<SContentStruct>&sPackageCmd);
    void _translateCmd(QStringList strInputList, QList<SContentStruct> &sContentSourceListRs);

public slots:
    void SlotStopTimeout();
    void SlotStopTimeoutCheck();
signals:
    void SigStopTimeoutCheck();
protected:
    void run() override;
    void _HandleReceiveList() override;

public slots:
    void slotReciveComplexResult(QString strComplexID);

private:
    bool m_bIsRunning;
    bool m_bStop;
    std::atomic<bool> m_bWait{false};// true:等待执行结果帧后执行下一条,false:不用等待，执行
    QMutex m_qMutex;

    QList<SContentStruct> m_sContentSourceList;
    int m_iContentSourceListLength;
    QMap<QString, bool> m_qSyncConditonMap;// 并行执行条件
    int m_iCurrentIndex;// 当前指令队列中执行的序号
    int m_iCircleTestCnt; //循环测试次数
    SContentStruct m_sCurrentContent;
};

#endif // CAFFAIRCOMPLEXCOMPOSE_H
