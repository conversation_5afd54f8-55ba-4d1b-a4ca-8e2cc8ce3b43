#include<qdebug.h>
#include<QTime>
#include"MaintainSubSystem.h"
#include"MotorDebug/MotorConfigMgr.h"
#include"publicconfig.h"

MaintainSubSystem::MaintainSubSystem()
{
    m_pMotorDebug = new MotorDebug();
	m_AgingTest         =new AgingTest();
    m_pSelfTest   = new CSelfTest();
    m_pResetTest  = new CResetTest();
    _Init();
}

MaintainSubSystem::~MaintainSubSystem()
{

}

MaintainSubSystem &MaintainSubSystem::getInstance()
{
    static MaintainSubSystem obj;
    return obj;
}

void MaintainSubSystem::_Init()
{
    qDebug() << "MaintainSubSystem _Init";
    iRun =0;
    // TODO后期需要更换为从数据库读取
    // 获取配置文件信息

}

void MaintainSubSystem::MotorPosDebug(const QString strParams)
{
    qDebug()<<__FUNCTION__<<"strParams="<<strParams;
    if(strParams=="")
    {
        qDebug()<<__FUNCTION__<<"error, param is null";
         return ;
    }
    QStringList listParams = strParams.split(",");
    if (listParams.size() <=1)
    {
        qDebug()<<__FUNCTION__<<"error, param is "<<strParams;
        return;
    }
    quint8 u8ModuleNamePosDebug = listParams[0].toInt();
    MotorConfigMgr:: UnitModuleFiled enumNumberModuleName = static_cast<  MotorConfigMgr:: UnitModuleFiled>(u8ModuleNamePosDebug);
    SubMethodPosDebug m_SubMehod = static_cast<SubMethodPosDebug>(listParams[1].toInt());
    int iFirstDotIdx = strParams.indexOf(",");  //删掉第一个的'”,“与第二个’，‘之间的字符，组合为subpayload输出，已经保证最小有一个”,“
    int iSecondDotIdx = (iFirstDotIdx!=-1)? strParams.indexOf(",",iFirstDotIdx+1):-1;
    QString strSubPayload=iSecondDotIdx==-1? listParams[0]: strParams.left(iFirstDotIdx)+strParams.mid(iSecondDotIdx);  ////仅有一个与多个

    switch (m_SubMehod)
    {
    case Method_DebugPos_AskActionList:
    {
        if(listParams.size()!=2 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_AskActionList param error or strcut is null ,param="<<strParams;
            return;
        }
        m_pMotorDebug->AskDebugPosActionList(strSubPayload); ////发送信息给上位机绘图使用
        break;
    }
    case Method_DebugPos_AskDeviceList:
    {
        if(listParams.size()!=3 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_AskDeviceList param error or strcut is null,param="<<strParams;
            return;
        }
        m_pMotorDebug->AskDebugPosDeviceList(strSubPayload); ////发送信息给上位机绘图使用
        break;
    }
    case Method_DebugPos_SaveParam:
    {
        if(listParams.size()!=5 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_SaveParam param error or strcut is null,param="<<strParams;
            return;
        }
        m_pMotorDebug->SaveMotorParam(strSubPayload);
        break;
    }
    case Method_DebugPos_Prepare:
    {
        if(listParams.size()!=3 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_Prepare param error or strcut is null,param="<<strParams;
            return;
        }
        m_pMotorDebug->DoPrepareAction(strSubPayload);
        break;
    }
    case Method_DebugPos_Verify:
    {
        if(listParams.size()!=3 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_Verify param error or strcut is null,param="<<strParams;
            return;
        }
        m_pMotorDebug->DoVerifyAction(strSubPayload);
        break;
    }
    case Method_DebugPos_LiquidCalSuck:
    {
        if(listParams.size()!=3 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"Method_DebugPos_LiquidCalSuck param error or strcut is null,param="<<strParams;
            return;
        }
        qDebug()<<"strParams="<<strParams<<",strSubPayload="<<strSubPayload;
        m_pMotorDebug->DoLiquidCalSuck(strSubPayload);
        break;
    }
    case Method_DebugPos_LiquidTest:
    {
        if(listParams.size()!=5 || m_pMotorDebug==nullptr)
        {
            qDebug()<<__FUNCTION__<<"ModuleFiled_LiquidTest param error or strcut is null,param="<<strParams;
            return;
        }
        m_pMotorDebug->DoLiquidTest(strSubPayload);
        break;
    }
    case Method_DebugPos_DoMotion:
    {
        switch (enumNumberModuleName)
        {
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_ADP:  //
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_Sample:  //
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_PCR:
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_Extract:
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_SampleScan:
        case MotorConfigMgr::UnitModuleFiled::ModuleFiled_StripScan:
        {
            qDebug()<<__FUNCTION__<<"sample DoMotion param = "<<strSubPayload;
            if (m_pMotorDebug)
            {
                m_pMotorDebug->DebugOperation(strSubPayload);
            }
        }
            break;
        }
        break;
    }
    default:
    {
        qDebug()<<__FUNCTION__<<"m_SubMehod="<<m_SubMehod;
    }
        break;
    }

    //qDebug() << "StartDebug:" << strParams;
}


 void MaintainSubSystem::AgingTestDebug(const QString strParams)
 {
     m_AgingTest->AgingTestOperation(strParams);
 }

void MaintainSubSystem::SetPosDebugRun(int _iRun)
{
    iRun = _iRun;
}

void MaintainSubSystem::HandleCmdReply(quint16 uiComplexID, QString strpayload,quint16 uiResult)
{
    if(iRun==1)
    {
        m_pMotorDebug->HandleCmdReply(uiComplexID,strpayload,uiResult);
    }
}

void MaintainSubSystem::HandleAgingTestCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult)
{
    
}

#pragma region 仪器自检 {
void MaintainSubSystem::SelfTestStart(quint16 uiSeqType)
{
    if (m_pSelfTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::SelfTestStart m_pSelfTest is nullptr";
        return;
    }
    m_pSelfTest->StartSelfTest(uiSeqType);
}

void MaintainSubSystem::SelfTestCleanPCR(quint16 uiSeqType)
{
    if (m_pSelfTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::SelfTestCleanPCR m_pSelfTest is nullptr";
        return;
    }
    m_pSelfTest->StartCleanPCR(uiSeqType);
}

void MaintainSubSystem::SelfTestSetMotor3AllOptoStatus(quint16 u32Motor3AllOptoStatus)
{
    if (m_pSelfTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::SelfTestSetMotor3AllOptoStatus m_pSelfTest is nullptr";
        return;
    }
    m_pSelfTest->SetMotor3AllOptoStatus(u32Motor3AllOptoStatus);
}   

void MaintainSubSystem::SelfTestSendGainOptoStatusCommand()
{
    if (m_pSelfTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::SelfTestSendGainOptoStatusCommand m_pSelfTest is nullptr";
        return;
    }
    m_pSelfTest->SendGainOptoStatusCommand();
}

void MaintainSubSystem::SelfTestHandleTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    if (m_pSelfTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::SelfTestHandleTimeseqReply m_pSelfTest is nullptr";
        return;
    }
    m_pSelfTest->HandleTimeseqReply(uiComplexID, uiResult);
}
#pragma endregion 仪器自检 }

#pragma region 仪器复位 {
void MaintainSubSystem::ResetTestStart(quint16 uiSeqType)
{
    if (m_pResetTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::ResetTestStart m_pResetTest is nullptr";
        return;
    }
    m_pResetTest->StartResetTest(uiSeqType);
}

void MaintainSubSystem::ResetTestHandleTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    if (m_pResetTest == nullptr)
    {
        qDebug() << "MaintainSubSystem::ResetTestHandleTimeseqReply m_pResetTest is nullptr";
        return;
    }
    m_pResetTest->HandleTimeseqReply(uiComplexID, uiResult);
}
#pragma endregion 仪器复位 }
