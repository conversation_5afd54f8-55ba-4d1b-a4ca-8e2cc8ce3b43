#ifndef DEVICEMODULE_H
#define DEVICEMODULE_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QQueue>
#include <QDebug>
#include <SampleControl/samplecontrol.h>
#include "publicconfig.h"

struct CmdTask {
    quint8 uiSubTaskID;      // 用于识别类型的 quint8 参数
    QString strCommandStr;    // 命令字符串
    QString strParamStr;      // 参数字符串
    bool bSync = false;
    // 其他任务相关的属性可以在这里添加
};

class DeviceModule : public QObject {
    Q_OBJECT

public:
    DeviceModule(QString strName, bool bUseThread = false);
    virtual ~DeviceModule();

    void SetState(bool bIsBusy);
    //清空待做時序動作列表
    void InitData();

signals:
    void SignalStatusChanged(const QString& strStatus);
    void SignalAddNewTaskToQueue(const CmdTask& task);
    void SignaModuleStateChanged(quint8 quState);
    void SignalSetState(bool bIsBusy);
    void SignalAddNewSubTask(const CmdTask& task);
    void SignalInitData();

public slots:
    virtual void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr);
    virtual void SlotAddTask(const CmdTask& task);

protected:
    virtual void SlotInitialize();
    virtual void SlotProcessTask(const CmdTask& task);
    virtual void  _ProcessSubTask() = 0;
    virtual void SlotInitData();
    void _AddSubTask(QString strParam, quint16 uiSubTaskID);

protected slots:
    void _SlotExecRemainTask();
    void _SlotModuleState(quint8 quState);
    void _SlotSetState(bool bIsBusy);
    void _SlotAddNewSubTask(const CmdTask& task);
    void _SlotAddNewTaskToQueue(const CmdTask& task);

protected:
    bool m_bUseThread;
    bool m_bIsBusy = false;
    QThread* m_pThread = nullptr;

    QQueue<CmdTask> m_qWaitProcessSubTask;
    QQueue<CmdTask> m_qTaskQueue;

    QQueue<quint8> m_qSampleQueue;//rowIndex*RowSize+columnIndex
    QString m_strName;
};

#endif // DEVICEMODULE_H
