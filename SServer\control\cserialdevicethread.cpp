#include "cserialdevicethread.h"
#include <QDebug>
#include <QDataStream>
#include "publicconfig.h"
#include "publicfunction.h"
#include "cglobalconfig.h"
CSerialDeviceThread::CSerialDeviceThread(QString strSerialName,  QString m_strBandRate,QObject *parent)
    : QObject(parent)
    , m_strSerialName((strSerialName)),
      m_strBandRate((m_strBandRate))
{
    memset(m_bWaitAck, 0, BUFFER_SIZE);
    m_iSeqNumber = -1;

    m_bOpenSerialPort = false;
    m_pReadFramesTimer = new QTimer();
    m_pReadFramesTimer->setSingleShot(false);
    m_pResendTimer = new QTimer();
    m_pResendTimer->setSingleShot(false);

    m_pThread = new QThread();
    m_pSerialPort = new QSerialPort();

    _InitPort(m_strSerialName,m_strBandRate);// 必须先初始化，后moveto

    this->moveToThread(m_pThread);
    m_pSerialPort->moveToThread(m_pThread);
    m_pReadFramesTimer->moveToThread(m_pThread);
    m_pResendTimer->moveToThread(m_pThread);

    connect(m_pReadFramesTimer,SIGNAL(timeout()),this,SLOT(_slotReadFramesTimer()));
    connect(m_pResendTimer,SIGNAL(timeout()),this,SLOT(_slotReSendTimer()));

    connect(this, &CSerialDeviceThread::sigReadTimer,
            this, &CSerialDeviceThread::_slotStartTimer, Qt::QueuedConnection);
    connect(this,&CSerialDeviceThread::sigResetCom,
            this,&CSerialDeviceThread::_slotResetCom,Qt::AutoConnection);


    m_pThread->start();
    emit sigReadTimer();
}

CSerialDeviceThread::~CSerialDeviceThread()
{
    if(m_pSerialPort != nullptr)
    {
        m_pSerialPort->close();
        m_pSerialPort->deleteLater();
        m_pSerialPort = nullptr;
    }
    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }
}

void CSerialDeviceThread::slotWaitACK(quint16 iFrameNumber)
{
    m_bWaitAck[iFrameNumber] = true;
}

void CSerialDeviceThread::slotSendAckBack(QByteArray qSendMsgAarry)
{
    _sendFrameData(qSendMsgAarry, true);
}

void CSerialDeviceThread::slotSendMessage(QByteArray qMsgBtyeArray)
{
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        emit sigError(FT_Comm_CacheFull, "");
        return;
    }
    m_qSendMessageList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
}

void CSerialDeviceThread::slotSendMessageNoList(QByteArray qSendMsgAarry)
{// 直接写入厂家串口，非标准协议数据
    _sendFrameData(qSendMsgAarry);
}
void CSerialDeviceThread::_slotReadFramesTimer()
{
    if(!m_bOpenSerialPort)
    {
        return;
    }
    // 读
    m_qReadFrameByteArray = m_pSerialPort->readAll();
    if(m_qReadFrameByteArray != "")
    {
        emit sigReciveMessage(m_qReadFrameByteArray);
        qDebug()   << "++++++++++++++  serial read "  << m_strSerialName << "-"
                   << m_qReadFrameByteArray ;
    }
    // 写
    if(m_iReadIndex.load() != m_iWriteIndex.load())
    {
        m_sCurrentSendMessage = m_qSendMessageList[m_iReadIndex.load()];
        _reSetFrameNumber(m_sCurrentSendMessage);
        _sendFrameData(m_sCurrentSendMessage);
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        // 重发
        MessageInfo messageInfo;
        messageInfo.qSendMessageDataByteArray = m_sCurrentSendMessage;
        messageInfo.timestamp = QDateTime::currentMSecsSinceEpoch();
        m_pFramePos = m_sCurrentSendMessage.data() + gk_iSeqPos;
        messageInfo.iSeqNumber =  GetByte2Int(m_pFramePos);
        //
        m_iRingCurrentWriteIndex = m_iRingWriteIndex.load();
        m_iRingNextWriteIndex = (m_iRingCurrentWriteIndex + 1) % BUFFER_SIZE;
        //
        if (m_iRingNextWriteIndex == m_iRingReadIndex.load()) {
            // 原则上不可能有65535个重发存在，故而不做考虑
            qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-resnd tcp^^^^^^^^^^^^^";
            emit sigError(FT_Comm_CacheFull, "");
        }
        if(m_iRingCurrentWriteIndex != messageInfo.iSeqNumber )
        {
            qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-iSeqNumber^^^^^^^^^^^^^"
                       << m_iRingCurrentWriteIndex << messageInfo.iSeqNumber;
        }
        m_bWaitAck[messageInfo.iSeqNumber] = false;
        m_sRingMessageInfoList[m_iRingCurrentWriteIndex] = messageInfo;
        m_iRingWriteIndex.store(m_iRingNextWriteIndex);
    }
    if(m_iReSendReadIndex.load() != m_iReSendWriteIndex.load())
    {// 重发队列
        qInfo() << "---------serial resend -----------";
        QByteArray& qMessage = m_qReSendMessageList[m_iReSendReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            qDebug()  << "resend Message_" << "serial" << qMessage.toHex(':').toUpper();
            _sendFrameData(qMessage);
            emit sigError(FT_Comm_Resend, QString("resend message method id %1").arg(GetByte2Int(qMessage.data() + gk_iMethodIDPos)));
        }
        // 环形队列
        m_iReSendReadIndex.store((m_iReSendReadIndex.load() + 1) % BUFFER_SIZE);
    }
}

void CSerialDeviceThread::ReSetCom(QString strComName,QString strBandRate)
{
    emit sigResetCom(strComName,strBandRate);
}

void CSerialDeviceThread::_slotResetCom(QString strComName,QString strBandRate)
{
    if(strComName != "")
    {
        _InitPort(strComName,strBandRate);
    }
    else
    {
        if(m_pSerialPort != nullptr)
        {
            m_bOpenSerialPort = false;
            m_pSerialPort->close();
        }
    }
}

void CSerialDeviceThread::_InitPort(QString strComName,QString strBandRate)
{
    QString strLog;
    if(m_pSerialPort->isOpen())
        m_pSerialPort->close();
    m_pSerialPort->setPortName(strComName);
    if(m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_pSerialPort->setBaudRate(strBandRate.toInt());
        m_pSerialPort->setDataBits(QSerialPort::Data8);
        m_pSerialPort->setParity(QSerialPort::NoParity);
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
        m_bOpenSerialPort = true;
        m_pSerialPort->setReadBufferSize(1024);
        strLog = QString("open sucess:") + strComName + " " + strBandRate;
        sigNewConnect(true);
    }
    else
    {
        m_bOpenSerialPort = false;
        strLog = QString("open failed:") + strComName + m_pSerialPort->errorString();
        sigNewConnect(false);
        emit sigError(FT_Comm_OpenFail, "");
    }
    qDebug()<<strLog;
}

void CSerialDeviceThread::_sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend)
{
    m_pSerialPort->write(qSendMsgAarry);
    QString strType = bACKSend ? "ack" : "data";
    CGlobalConfig::getInstance().printMessageInfo(qSendMsgAarry,
                                                  "[server->serial "  + strType + "]");
}

void CSerialDeviceThread::_reSetFrameNumber(QByteArray &qByteArray)
{// 帧号
    m_iSeqNumber++;
    if(m_iSeqNumber > 0xFFFF)
    {
        m_iSeqNumber = 0;
    }
    // 将 quint16 值转换为字节序列，你可以选择使用大端或小端
    if(qByteArray.count() >= gk_iFrameLengthNotData)// 帧长
    {
        QByteArray qBlockByteArray;
        QDataStream qOutDataStream
                (&qBlockByteArray,QIODevice::ReadWrite);
        qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
        qOutDataStream << quint16(m_iSeqNumber);
        qByteArray = qByteArray.replace(gk_iSeqPos, 2,  qBlockByteArray);
        quint16 iCrc16 = GetCRC16(qByteArray.data(), qByteArray.count()-2, 0);
        QByteArray qBlockByteArrayCrc;
        QDataStream qOutDataStreamCrc
                (&qBlockByteArrayCrc,QIODevice::ReadWrite);
        qOutDataStreamCrc.setByteOrder(QDataStream::BigEndian);  // 设置xiao端格式
        qOutDataStreamCrc << quint16(iCrc16);
        qByteArray = qByteArray.replace(qByteArray.count()-2, 2,  qBlockByteArrayCrc);
    }
}

void CSerialDeviceThread::_slotStartTimer()
{
    m_pReadFramesTimer->start(10);//
    m_pResendTimer->start(100);
}

void CSerialDeviceThread::_slotReSendTimer()
{
    m_iRingCurrentReadIndex = m_iRingReadIndex.load();
    if (m_iRingCurrentReadIndex != m_iRingWriteIndex.load())
    {
        const MessageInfo& messageInfo = m_sRingMessageInfoList[m_iRingCurrentReadIndex];
        if(!m_bWaitAck[messageInfo.iSeqNumber])
        {// 重发
            const qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
            if ((currentTime - messageInfo.timestamp) >= gk_iWaitMsecs)
            { // If the message is old, resend it
                m_iReSendCurrentWriteIndex = m_iReSendWriteIndex.load();
                m_iReSendNextWriteIndex = (m_iReSendCurrentWriteIndex + 1) % BUFFER_SIZE;

                if (m_iReSendNextWriteIndex == m_iReSendReadIndex.load())
                { // 原则上不可能有65535个重发存在，故而不做考虑
                    qWarning() << "m_qReSendMessageInfoList^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
                    emit sigError(FT_Comm_CacheFull, "");
                    return;
                }
                m_qReSendMessageList[m_iReSendCurrentWriteIndex] = messageInfo.qSendMessageDataByteArray;
                m_iReSendWriteIndex.store(m_iReSendNextWriteIndex);
                qDebug() <<  "CSerialDeviceThread_slotReSendTimer"  << __LINE__ << m_iRingCurrentReadIndex << m_iRingWriteIndex.load()
                          << m_iReSendReadIndex.load()  <<  m_iReSendCurrentWriteIndex << m_iReSendNextWriteIndex
                          << messageInfo.iSeqNumber << messageInfo.timestamp;
                m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
            }
        }
        else
        {
            m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
        }
    }
}
