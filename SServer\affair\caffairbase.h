/*****************************************************
  * Copyright: 万孚生物
  * Author: mflin
  * Date: 2024-1-24
  * Description:  业务流程管理
  * -----------------------------------------------------------------
  * History:
  *1.创建业务流程管理
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CAFFAIRBASE_H
#define CAFFAIRBASE_H
#include<QObject>
#include<QThread>
#include<QMap>
#include<atomic>
#include<QMutex>
#include"publicconfig.h"


class CAffairBase : public QThread
{
    Q_OBJECT


public:
    explicit CAffairBase(QObject *parent = nullptr);
    ~CAffairBase();

signals:

public slots:
    void slotAddReciveMsg(QByteArray qMsgBtyeArray);

protected:
    virtual void run();
    virtual void _HandleReceiveList();

protected:
    bool m_bThreadExit;
    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    QByteArray m_qSendMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;



};

#endif // CAFFAIRBASE_H
