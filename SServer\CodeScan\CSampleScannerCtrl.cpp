#include<QDebug>
#include <QCoreApplication>
#include"CSampleScannerCtrl.h"
#include<QThread>
#include <QRegularExpression> 
#include <QTextCodec>

namespace SampleScannerCtrl
{
   //停止
   static uchar stopScanCmd[] = {0x02,0x43,0x2D,0x0D,0x0A};
   //开始
//    static uchar continueScan[] = {0x02,0x43,0x2B,0x0D,0x0A};
   static uchar continueScan[] = {0x02,0x50,0x54,0x30,0x33,0x32,0x30,0x33,0x34,0x30,0x31,0x0D,0x0A};
   //版本
   static uchar versionScanCmd[] = {0x02,0x56,0x0D,0x0A};
   //关闭重复码
   static uchar closeRepeatScanCmd[] = {0x02,0x50,0x54,0x30,0x30,0x32,0x30,0x33,0x34,0x41,0x30,0x0D,0x0A};

   static uchar defaultScanCmd[] = {0x02,0x50,0x43,0x32,0x30,0x0D,0x0A};

}

using namespace SampleScannerCtrl;

CSampleScannerCtrl::CSampleScannerCtrl(QString strSerialName,
                                       QString strBandRate,
                                       QString strScannerName)
{
    m_uiCountSamplePos = 0;
    m_strScannerName = strScannerName;
    m_bInitOk = false;
    m_pThreadForInit = new QThread();
    pExtractScannerThread = new CExtractScannerThread(strSerialName ,strBandRate,nullptr,strScannerName);
    connect(pExtractScannerThread,SIGNAL(sigReciveSeirlaData(QByteArray)),this,SLOT(slotReceSerialData(QByteArray)));
    connect(this,SIGNAL(sigWriteData(QByteArray)),pExtractScannerThread,SLOT(sendFrameData(QByteArray)));
    this->moveToThread(m_pThreadForInit);
    if( pExtractScannerThread->GetIsOpenSerialPort())
    {
        connect(m_pThreadForInit, &QThread::started, this,&CSampleScannerCtrl::InitScanCodeSetting);
        m_pThreadForInit->start();
    }
}


void CSampleScannerCtrl::InitScanCodeSetting()
{
    QByteArray versionScanCmdQBA = QByteArray::fromRawData((const char*)versionScanCmd, sizeof(versionScanCmd));
    QByteArray closeRepeatScanCmdQBA = QByteArray::fromRawData((const char*)closeRepeatScanCmd, sizeof(closeRepeatScanCmd));
    QByteArray defaultScanCmdQBA = QByteArray::fromRawData((const char*)defaultScanCmd, sizeof(defaultScanCmd));

    _doSendCmd(versionScanCmdQBA);//获取版本号
    _doSendCmd(closeRepeatScanCmdQBA);//关闭重复输出
    
    // OpenDoSingleScanCmd();
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::InitScanCodeSetting"<<versionScanCmdQBA.toHex()<<closeRepeatScanCmdQBA.toHex();
}

CSampleScannerCtrl::~CSampleScannerCtrl()
{

}

void CSampleScannerCtrl::slotReceSerialData(QByteArray data)
{
    const QString strCommandRspMsg = "PS0";//应答帧
    const QString strCommandRspVersionMsg = "CR100";//版本应答帧

    QString strData = QString::fromLatin1(data);
    if (strData.contains(strCommandRspVersionMsg))//判断是否能获取到版本号
    {
        CloseSingleScanCmd();//关闭扫描
        m_bInitOk = true;
        qDebug()<<m_strScannerName<<"Recv Command Rsp Version Msg:"<<strCommandRspVersionMsg;
        return;
    }
    
    QRegularExpression codeRegex("(\\x02.*?\\r\\n)(?=\\x02|$)");
    QRegularExpressionMatchIterator i = codeRegex.globalMatch(data);
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::slotReceSerialData"<<data;

    QString barcode ="";
    while (i.hasNext()) {
        QRegularExpressionMatch match = i.next();
         barcode = match.captured(0);

        // 去掉特殊字符 \x02 和 \r\n
        barcode = barcode.replace("\x02", "");
        barcode = barcode.replace("\r\n", "");
        if (barcode.startsWith(strCommandRspMsg) || barcode.length()<=strCommandRspMsg.size())
        {
            qDebug()<<m_strScannerName << "Matched Command Rsp Msg:" << barcode;
            return;
        }
        m_hashSampleCodeResult[GetSampleCurPos()] = barcode;
        qDebug() <<m_strScannerName<< "Matched Code barcode:" <<GetSampleCurPos()<<m_hashSampleCodeResult[GetSampleCurPos()]<<barcode;
    }    
}

void CSampleScannerCtrl::_doSendCmd(QByteArray qSendData)
{
    emit sigWriteData(qSendData);
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::_doSendCmd"<<qSendData.toHex();
}

int CSampleScannerCtrl::CloseSingleScanCmd()
{
    QByteArray stopScanCmdQBA = QByteArray::fromRawData((const char*)stopScanCmd, sizeof(stopScanCmd));
    _doSendCmd(stopScanCmdQBA);
    return 1;
}

int CSampleScannerCtrl::OpenDoSingleScanCmd()
{
    QByteArray continueScanQBA = QByteArray::fromRawData((const char*)continueScan, sizeof(continueScan));
    _doSendCmd(continueScanQBA);
    return 0;
}

qint8 CSampleScannerCtrl::GetSampleNextPos()
{
    QMutexLocker qLocker(&m_qMutex);
    m_uiCountSamplePos++;
    return m_uiCountSamplePos;
}

qint8 CSampleScannerCtrl::GetSampleCurPos()
{
    QMutexLocker qLocker(&m_qMutex);
    return m_uiCountSamplePos;
}

bool CSampleScannerCtrl::IsScanFinish()
{
    if(m_uiCountSamplePos >= (m_uiMaxsamplePos-1))
    {
        return true;
    }
    return false;
}

void CSampleScannerCtrl::ResetSamplePos()
{
    QMutexLocker qLocker(&m_qMutex);
    CloseSingleScanCmd();
    m_uiCountSamplePos = 0;
    m_hashSampleCodeResult.clear();
    qDebug()<<m_strScannerName<<"ResetSamplePos"<<m_uiCountSamplePos<<m_hashSampleCodeResult.size();
}

void CSampleScannerCtrl::GetSampleCodeResult(QStringList &result)
{
    QMutexLocker qLocker(&m_qMutex);
    result.clear();

    for(int i=0;i<m_uiMaxsamplePos;i++)
    {
        if(m_hashSampleCodeResult.contains(i))//有获取到数据
        {
            result.append(m_hashSampleCodeResult[i]);
        }
        else
        {
            if (m_hashSampleCodeExist.contains(i)&&
                m_hashSampleCodeExist.value(i))// 判断是否有样本管，有样本管但是扫码失败，填WF+Error
            {
                result.append("WF+Error");//WF+Error
            }
            else
            {
                result.append("");//否则填空
            }            
            
        }
    }
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::GetSampleCodeResult"<<result;
}

QStringList CSampleScannerCtrl::GetSampleCodeAllResult(QStringList &resultLeft, QStringList &resultRight)
{
    QStringList result;
    // 确保两个列表长度相同
    int length = qMin(resultLeft.size(), resultRight.size());

    // 合并两个列表
    for (int i = 0; i < length; ++i) {
        result.append(resultLeft[i]);
        result.append(resultRight[i]);
    }

    // 如果 resultLeft 比 resultRight 长，则将多余的元素添加到结果列表中
    for (int i = length; i < resultLeft.size(); ++i) {
        result.append(resultLeft[i]);
    }

    // 如果 resultRight 比 resultLeft 长，则将多余的元素添加到结果列表中
    for (int i = length; i < resultRight.size(); ++i) {
        result.append(resultRight[i]);
    }

    // 返回合并后的结果列表
    return result;
}

QString CSampleScannerCtrl::GetSampleCodeScanSingleResult(qint8 uiIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    QString strResult;//返回结果
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::GetSampleCodeScanSingleResult"<<uiIndex<<m_hashSampleCodeResult.keys();
    if(m_hashSampleCodeResult.contains(uiIndex))
    {
        strResult = m_hashSampleCodeResult[uiIndex];
    }
    return strResult;
}

bool CSampleScannerCtrl::GetInitStatus()
{
    QMutexLocker qLocker(&m_qMutex);
    qDebug()<<m_strScannerName<<"CSampleScannerCtrl::GetInitStatus"<<m_bInitOk;
    return m_bInitOk;
}

void CSampleScannerCtrl::SetMaxSampleRowPos(const quint8 uiRow)
{
    if(uiRow <= 0 || uiRow > MAX_SACN_SAMPLE_ROW)
    {
        qDebug()<<"SetMaxSampleRowPos: "<<uiRow<<" is error!";
        return;
    }
    QMutexLocker qLocker(&m_qMutex);
    m_uiMaxsamplePos = uiRow;
    qDebug()<<m_strScannerName<<"SetMaxSampleRowPos: "<<m_uiMaxsamplePos;
}

void CSampleScannerCtrl::SetSampleCodeScanTubeExist(bool bExist)
{
    m_hashSampleCodeExist[GetSampleCurPos()] = bExist;
    qDebug()<<m_strScannerName<<"SetSampleCodeScanSingleResult: "<<GetSampleCurPos()<<bExist;
}

void CSampleScannerCtrl::SetSampleCodeScanAllTubeExist(const QString strParam)
{
    QStringList strList = strParam.split(",");
    if (strList.size() != MAX_SACN_SAMPLE_ROW)
    {
        qDebug()<<"SetSampleCodeScanAllTubeExist size error"<<strList;
        return;
    }

    bool bExist = false;
    quint8 u8Count = 0;
    for (auto param : strList)
    {
        bExist = false;
        if (param != "0")// 非0有试管
        {
            bExist = true;
        }
        m_hashSampleCodeExist[u8Count] = bExist;
        u8Count++;
    }
    qDebug()<<m_strScannerName<<"SetSampleCodeScanAllTubeExist:"<<strParam;
}

bool CSampleScannerCtrl::GetSampleCodeScanTubeExist()
{
    bool bExist = false;
    quint8 u8CurPos = GetSampleCurPos();
    if (m_hashSampleCodeExist.contains(u8CurPos))
    {
        bExist = m_hashSampleCodeExist[u8CurPos];
    }
    qDebug()<<m_strScannerName<<"GetSampleCodeScanTubeExist: "<<u8CurPos<<bExist;
    return bExist;
}