#ifndef HALSUBSYSTEM_H
#define HALSUBSYSTEM_H

#include<QObject>
#include<QVector>
#include "Hal/ElecMagneticLock.h"
#include "Hal/Led.h"

class HalSubSystemPrivate;
class HalSubSystem : public QObject
{
    Q_OBJECT
public:
    static HalSubSystem &getInstance();

public:
    /**
     * @brief SetElecMagneticLock 设置锁状态
     * @param state           锁状态
     * @param uiLockType      锁类型
     * @param uiIndex        类型对应的索引
     * @return  
     */
    void SetRFIDElecMagneticLock(ElecMagneticLock::EnumLockState state,quint16 uiLockType,quint16 uiIndex,EnumMachineID machineID = Machine_Function_manager_Ctrl);

    /**
     * @brief SetRunStatus 设置运行状态
     * @param state     状态
     * @return  
     */
    void SetRunStatus(quint16 state); 

    /**
     * @brief SetAllElecMagneticUnlock 全部电磁锁解锁
     * @param 
     * @return  
     */
    void SetAllElecMagneticUnlock();     

    /**
     * @brief SetLedTrigger 触发灯状态
     * @param state         状态
     * @param moudle        模块
     * @param machineID     板卡id
     * @return 
     */  
    void SetLedTrigger(Led::EnumTriggerState state, Led::EnumMoudle moudle,EnumMachineID machineID);   
    
    /**
     * @brief GetLedMoudleStatus 获取灯状态
     * @param moudle        模块
     * @return 
     */  
    Led::EnumTriggerState GetLedMoudleStatus(Led::EnumMoudle moudle);     
signals:

public slots:

private:
  HalSubSystem();
  ~HalSubSystem();
private:
  HalSubSystemPrivate * const d_ptr;
  Q_DECLARE_PRIVATE(HalSubSystem);
  Q_DISABLE_COPY(HalSubSystem);
};


#endif // HALSUBSYSTEM_H
