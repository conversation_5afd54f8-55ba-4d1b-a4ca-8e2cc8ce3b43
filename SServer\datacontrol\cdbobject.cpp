﻿#include "cdbobject.h"

#include <QDebug>
#include <QSqlRecord>
#include <QThread>

CDBObject::CDBObject(QObject *parent) : QObject(parent)
{
}

bool CDBObject::createDBTable(const QString &kstrTableName,
                              const QVector<QPair<QString, QString>> &fieldsAndTypes,
                              const QString &strOtherCondition)
{
    if (kstrTableName.isEmpty() || fieldsAndTypes.isEmpty())
    {
        DEBUG_LOG << "Table name is empty or no fields provided";
        return false;
    }

    QSqlDatabase qWriteSqlDB = acquireWriteConnection(); // 不存在多线程并发，所以不需要加锁
    QSqlQuery qWriteSqlQuery(qWriteSqlDB);

    bool bSuccess = false;
    qWriteSqlDB.transaction();

    try
    {
        QString strCreateTable = "CREATE TABLE IF NOT EXISTS " + kstrTableName +
                                 " ("
                                 "id INTEGER PRIMARY KEY AUTOINCREMENT, "; // 默认自增id为数据库表的key

        for (const auto &field : fieldsAndTypes)
        {
            strCreateTable += field.first + " " + field.second + ", ";
        }

        strCreateTable.chop(2); // 移除最后的逗号和空格
        if (!strOtherCondition.isEmpty())
        {
            strCreateTable += " " + strOtherCondition;
        }
        strCreateTable += ")";

        bSuccess = qWriteSqlQuery.exec(strCreateTable);

        if (bSuccess)
        {
            qWriteSqlDB.commit();
        }
        else
        {
            qWriteSqlDB.rollback();
            qWarning() << "Create table failed:"
                       << kstrTableName << qWriteSqlQuery.lastError().text();
        }
    }
    catch (const std::exception &e)
    {
        qWriteSqlDB.rollback();
        qWarning() << "Exception during create table:" << e.what();
        bSuccess = false;
    }
    return bSuccess;
}

bool CDBObject::addFieldToTable(const QString &kstrTableName, 
                           const QStringList &strNewFieldList,
                           const bool &bIsDeleteOldTable)
{
    if (kstrTableName.isEmpty() || strNewFieldList.isEmpty()) {
        qWarning() << "Invalid parameters: table name or field list is empty";
        return false;
    }

    return getWriteConnectionPool()->executeWrite([&]() {
        QSqlDatabase db = acquireWriteConnection();
        if (!db.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }

        QSqlQuery query(db);
        bool bSuccess = false;

        try {
            // 开启事务
            if (!db.transaction()) {
                qWarning() << "Failed to start transaction:" << db.lastError().text();
                return false;
            }

            // 检查表是否存在
            bool bTableExists = false;
            if (query.exec(QString("SELECT 1 FROM sqlite_master WHERE type='table' AND name='%1'")
                .arg(kstrTableName))) {
                bTableExists = query.next();
            }

            if (!bTableExists) {
                // 表不存在，直接创建新表
                QString strCreateTableSql = QString("CREATE TABLE %1 (").arg(kstrTableName);
                // 添加自增主键
                strCreateTableSql += "id INTEGER PRIMARY KEY AUTOINCREMENT, ";
                
                // 添加所有字段
                for (const QString &field : strNewFieldList) {
                    strCreateTableSql += QString("%1 VARCHAR, ").arg(field);
                }
                strCreateTableSql.chop(2); // 移除最后的逗号和空格
                strCreateTableSql += ")";

                if (!query.exec(strCreateTableSql)) {
                    qWarning() << "Failed to create new table:" << query.lastError().text();
                    return false;
                }

                bSuccess = true;
                db.commit();
                return true;
            }

            // 表存在，继续原有逻辑
            // 1. 获取旧表的所有字段信息
            QVector<QPair<QString, QString>> oldFields;
            if (query.exec(QString("PRAGMA table_info(%1)").arg(kstrTableName))) {
                while (query.next()) {
                    QString fieldName = query.value(1).toString();
                    QString fieldType = query.value(2).toString();
                    oldFields.append({fieldName, fieldType});
                }
            } else {
                qWarning() << "Failed to get table info:" << query.lastError().text();
                return false;
            }

            // 检查是否需要迁移
            bool bNeedMigration = false;
            QSet<QString> strOldFieldSet;
            for (const auto &field : oldFields) {
                strOldFieldSet.insert(field.first);
            }

            // 检查新字段是否都已存在
            for (const QString &newField : strNewFieldList) {
                if (!strOldFieldSet.contains(newField)) {
                    bNeedMigration = true;
                    break;
                }
            }

            // 如果不需要迁移，直接返回成功
            if (!bNeedMigration) {
                qDebug() << "Table" << kstrTableName << "already has all required fields, no migration needed";
                db.commit();
                return true;
            }

            // 需要迁移，继续执行原有逻辑
            QString strOldTableName = kstrTableName;
            QString strNewTableName = kstrTableName + "_new";
            QString strNewOldTableName = kstrTableName + "_old_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss");

            // 2. 创建新表
            QString strCreateTableSql = QString("CREATE TABLE %1 (").arg(strNewTableName);
            // 添加自增主键
            strCreateTableSql += "id INTEGER PRIMARY KEY AUTOINCREMENT, ";
            
            // 添加所有新字段
            for (const QString &field : strNewFieldList) {
                // 查找字段在旧表中的类型
                QString strFieldType = "VARCHAR"; // 默认类型
                for (const auto &oldField : oldFields) {
                    if (oldField.first == field) {
                        strFieldType = oldField.second;
                        break;
                    }
                }
                strCreateTableSql += QString("%1 %2, ").arg(field).arg(strFieldType);
            }
            strCreateTableSql.chop(2); // 移除最后的逗号和空格
            strCreateTableSql += ")";

            if (!query.exec(strCreateTableSql)) {
                qWarning() << "Failed to create new table:" << query.lastError().text();
                return false;
            }

            // 3. 复制数据
            QStringList selectFields;
            // 首先添加 id 字段
            selectFields << "id";
            
            // 然后添加其他字段
            for (const QString &field : strNewFieldList) {
                // 检查字段是否存在于旧表中
                bool fieldExists = false;
                for (const auto &oldField : oldFields) {
                    if (oldField.first == field) {
                        fieldExists = true;
                        selectFields << field;
                        break;
                    }
                }
                if (!fieldExists) {
                    selectFields << "NULL as " + field;
                }
            }

            // 不使用QString的arg，直接拼接字符串
            QString insertSql = "INSERT INTO " + strNewTableName + " SELECT " + selectFields.join(", ") + " FROM " + strOldTableName;

            if (!query.exec(insertSql)) {
                qWarning() << "Failed to copy data:" << query.lastError().text();
                return false;
            }

            // 4. 重命名表
            // 不使用QString的arg，直接拼接字符串
            QString renameOldTableSql = "ALTER TABLE " + strOldTableName + " RENAME TO " + strNewOldTableName;
            if (!query.exec(renameOldTableSql)) {
                qWarning() << "Failed to rename old table:" << query.lastError().text();
                return false;
            }

            // 不使用QString的arg，直接拼接字符串
            QString renameNewTableSql = "ALTER TABLE " + strNewTableName + " RENAME TO " + strOldTableName;
            if (!query.exec(renameNewTableSql)) {
                qWarning() << "Failed to rename new table:" << query.lastError().text();
                return false;
            }

            // 5. 删除临时表
            if (bIsDeleteOldTable) {
                if (!query.exec(QString("DROP TABLE IF EXISTS %1").arg(strNewOldTableName))) {
                    qWarning() << "Failed to drop temp table:" << query.lastError().text();
                    return false;
                }
            }

            bSuccess = true;
            db.commit();
        }
        catch (const std::exception &e) {
            db.rollback();
            qWarning() << "Exception during table migration:" << e.what();
            bSuccess = false;
        }

        return bSuccess;
    });
}

bool CDBObject::_addOneDBRecord(const QString &kstrTableName,
                               const QVector<QPair<QString, QString>> &kstrAddOnePair,
                               const QString &strSameKeyValue)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        if (kstrAddOnePair.isEmpty()) {
            return false;
        }

        // 获取写连接并开启事务
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }

        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;

        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction()) {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();                
                return false;
            }

            // 如果需要检查重复
            if (!strSameKeyValue.isEmpty())
            {
                QString strValue;
                for (const auto &pair : kstrAddOnePair)
                {
                    if (pair.first == strSameKeyValue)
                    {
                        strValue = pair.second;
                        break;
                    }
                }

                if (!strValue.isEmpty())
                {
                    // 在同一事务中进行查询和更新，避免并发问题
                    QString strCheckSql = QString("SELECT 1 FROM %1 WHERE %2 = :bind_value LIMIT 1")
                                        .arg(kstrTableName, strSameKeyValue);
                    qWriteSqlQuery.prepare(strCheckSql);
                    qWriteSqlQuery.bindValue(":bind_value", strValue);

                    if (qWriteSqlQuery.exec())
                    {
                        bool bRecordExists = qWriteSqlQuery.next();
                        qWriteSqlQuery.finish(); // 清理查询

                        if (bRecordExists)
                        {
                            // 存在记录，执行更新
                            QStringList strSetPartsList;
                            for (const auto &pair : kstrAddOnePair)
                            {
                                if (pair.first != strSameKeyValue)
                                {
                                    strSetPartsList.append(pair.first + "=:" + pair.first);
                                }
                            }

                            QString strSetClause = strSetPartsList.join(", ");
                            QString strUpdateSql = QString("UPDATE %1 SET %2 WHERE %3 = :bind_value")
                                                 .arg(kstrTableName, strSetClause, strSameKeyValue);

                            qWriteSqlQuery.prepare(strUpdateSql);
                            for (const auto &pair : kstrAddOnePair)
                            {
                                if (pair.first != strSameKeyValue)
                                {
                                    qWriteSqlQuery.bindValue(":" + pair.first, pair.second);
                                }
                            }
                            qWriteSqlQuery.bindValue(":bind_value", strValue);

                            bSuccess = qWriteSqlQuery.exec();
                            if (!bSuccess) {
                                qWarning() << "Update failed:" << qWriteSqlQuery.lastError().text();
                            }
                        }
                    }
                }
            }

            // 如果不需要更新或更新失败，执行插入
            if (!bSuccess)
            {
                QStringList strFieldsList, strValuesList;
                for (const auto &pair : kstrAddOnePair)
                {
                    strFieldsList.append(pair.first);
                    strValuesList.append(":" + pair.first);
                }

                QString strInsertSql = QString("INSERT INTO %1 (%2) VALUES (%3)")
                                      .arg(kstrTableName, strFieldsList.join(", "), strValuesList.join(", "));

                qWriteSqlQuery.prepare(strInsertSql);
                for (const auto &pair : kstrAddOnePair)
                {
                    qWriteSqlQuery.bindValue(":" + pair.first, pair.second);
                }

                bSuccess = qWriteSqlQuery.exec();
                if (!bSuccess) {
                    qWarning() << "Insert failed:" << qWriteSqlQuery.lastError().text();
                }
            }

            // 提交或回滚事务
            if (bSuccess)
            {
                if (!qWriteSqlDB.commit()) {
                    qWarning() << "Commit failed:" << qWriteSqlDB.lastError().text();
                    qWriteSqlDB.rollback();
                    bSuccess = false;
                }
            }
            else
            {
                qWriteSqlDB.rollback();
            }
        }
        catch (const std::exception &e)
        {
            qWarning() << "Exception during database operation:" << e.what();
            qWriteSqlDB.rollback();
            bSuccess = false;
        }

        // 确保释放连接
        
        return bSuccess; });
}

bool CDBObject::addOneDBRecord(const QString &kstrTableName,
                               const QStringList &strFieldNameList,
                               const QStringList &strFieldValueList,
                               const QString &strSameKeyValue)
{
    bool bResult = false;
    if (strFieldNameList.length() != strFieldValueList.length())
    {
        DEBUG_LOG << "Error Field length" << strFieldValueList;
        return bResult;
    }
    QVector<QPair<QString, QString>> strInsertMap;
    for (int i = 0; i < strFieldValueList.length(); ++i)
    {
        strInsertMap.push_back({strFieldNameList[i], strFieldValueList[i].toLocal8Bit()});
    }
    bResult = this->_addOneDBRecord(kstrTableName, strInsertMap, strSameKeyValue);

    return bResult;
}

bool CDBObject::_addOneDBRecordOfMoreSame(const QString &kstrTableName,
                               const QVector<QPair<QString, QString>> &kstrAddOnePair,
                               const QStringList &strSameKeyValueList)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }
        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;
        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction()) {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();                
                return false;
            }

            // 构建检查记录是否存在的查询
            QString strWhereClause;
            if (!strSameKeyValueList.isEmpty())
            {
                QStringList strConditionParts;
                for (const auto &key : strSameKeyValueList)
                {
                    for (const auto &pair : kstrAddOnePair)
                    {
                        if (pair.first == key)
                        {
                            strConditionParts.append(pair.first + "=:" + key + "_where");
                            break;
                        }
                    }
                }
                strWhereClause = strConditionParts.join(" AND ");
            }

            if (!strWhereClause.isEmpty())
            {

                QString strCheckSql = QString("SELECT 1 FROM %1 WHERE %2 LIMIT 1").arg(kstrTableName, strWhereClause);
                qWriteSqlQuery.prepare(strCheckSql);
                for (const auto &pair : kstrAddOnePair)
                {
                    if (strSameKeyValueList.contains(pair.first))
                    {
                        qWriteSqlQuery.bindValue(":" + pair.first + "_where", pair.second);
                    }
                }
                if (qWriteSqlQuery.exec() && qWriteSqlQuery.next())
                {
                    // 存在记录，执行更新操作
                    QStringList strSetPartsList;
                    for (const auto &pair : kstrAddOnePair)
                    {
                        if (!strSameKeyValueList.contains(pair.first))
                        {
                            strSetPartsList.append(pair.first + "=:" + pair.first);
                        }
                    }
                    QString strUpdateSql = QString("UPDATE %1 SET %2 WHERE %3").arg(kstrTableName, strSetPartsList.join(", "), strWhereClause);
                    qWriteSqlQuery.prepare(strUpdateSql);
                    for (const auto &pair : kstrAddOnePair)
                    {
                        if (!strSameKeyValueList.contains(pair.first))
                        {
                            qWriteSqlQuery.bindValue(":" + pair.first, pair.second);
                        }
                    }
                    for (const auto &key : strSameKeyValueList)
                    {
                        for (const auto &pair : kstrAddOnePair)
                        {
                            if (pair.first == key)
                            {
                                qWriteSqlQuery.bindValue(":" + key + "_where", pair.second);
                                break;
                            }
                        }
                    }
                    bSuccess = qWriteSqlQuery.exec();
                    if (bSuccess)
                    {
                        qWriteSqlDB.commit();
                    }
                    else
                    {
                        qWriteSqlDB.rollback();
                        qWarning() << "Insert failed:" << qWriteSqlQuery.lastError().text();
                    }
                    return bSuccess;
                }
            }
            // 记录不存在，执行插入操作
            QStringList strFieldsList, strValuesList;
            for (const auto &pair : kstrAddOnePair)
            {
                strFieldsList.append(pair.first);
                strValuesList.append(":" + pair.first);
            }

            QString strInsertSql = QString("INSERT INTO %1 (%2) VALUES (%3)").arg(kstrTableName, strFieldsList.join(", "), strValuesList.join(", "));
            qWriteSqlQuery.prepare(strInsertSql);
            for (const auto &pair : kstrAddOnePair)
            {
                qWriteSqlQuery.bindValue(":" + pair.first, pair.second);
            }
            bSuccess = qWriteSqlQuery.exec();
            if (bSuccess)
            {
                qWriteSqlDB.commit();
            }
            else
            {
                DEBUG_LOG << "Insert error:" << qWriteSqlQuery.lastError().text()
                          << strInsertSql;
                return false;
            }
        }
        catch (const std::exception &e)
        {
        qWriteSqlDB.rollback();
        qWarning() << "Exception during insert:" << e.what();
        bSuccess = false;
        }
        
        return bSuccess; });
}

bool CDBObject::addOneDBRecordOfMoreSame(const QString &kstrTableName,
                               const QStringList &strFieldNameList,
                               const QStringList &strFieldValueList,
                               const QStringList &strSameKeyValue)
{
    bool bResult = false;
    if (strFieldNameList.length() != strFieldValueList.length())
    {
        DEBUG_LOG << "Error Field length" << strFieldValueList;
        return bResult;
    }
    QVector<QPair<QString, QString>> strInsertMap;
    for (int i = 0; i < strFieldValueList.length(); ++i)
    {
        strInsertMap.push_back({strFieldNameList[i], strFieldValueList[i].toLocal8Bit()});
    }
    bResult = this->_addOneDBRecordOfMoreSame(kstrTableName, strInsertMap, strSameKeyValue);

    return bResult;
}

bool CDBObject::addMoreDBRecord(const QString &kstrTableName, const QStringList &strFieldNameList,
                                const QList<QStringList> &strFieldValueList, const QStringList &strSameKeyValue)
{
    QVector<QPair<QString, QString>> strInsertMap;
    for (int i = 0; i < strFieldValueList.length(); ++i)
    {
        if (strFieldNameList.length() != strFieldValueList[i].length())
        {
            DEBUG_LOG << "Error Field length" << strFieldValueList;
            return false;
        }
        for (int j = 0; j < strFieldValueList[i].length(); ++j)
        {
            strInsertMap.push_back({strFieldNameList[j], strFieldValueList[i][j].toLocal8Bit()});
        }
        if (!this->_addOneDBRecordOfMoreSame(kstrTableName, strInsertMap, strSameKeyValue))
        {
            return false;
        }
        strInsertMap.clear();
    }
    return true;
}

bool CDBObject::appendOneDBRecord(const QString &kstrTableName,
                                  const QString &strFieldName,
                                  const QString &strFieldValue,
                                  const QString &kstrConditioFieldName,
                                  const QString &kstrConditioFieldValue)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }
        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;

        // 检查是否存在符合条件的记录
        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction()) {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();                
                return false;
            }
            QString strCheckSql = QString("SELECT %1 FROM %2 WHERE %3 = :condition_value LIMIT 1")
                                    .arg(strFieldName, kstrTableName, kstrConditioFieldName);
            qWriteSqlQuery.prepare(strCheckSql);
            qWriteSqlQuery.bindValue(":condition_value", kstrConditioFieldValue);

            // 如果存在记录，进行更新操作
            if (qWriteSqlQuery.exec() && qWriteSqlQuery.next())
            {
                // 获取现有的字段值
                QString currentValue = qWriteSqlQuery.value(0).toString();

                // 将新值追加到现有值之后
                QString updatedValue = currentValue + strFieldValue;

                // 构建更新语句
                QString strUpdateSql = QString("UPDATE %1 SET %2 = :new_value WHERE %3 = :condition_value")
                                        .arg(kstrTableName, strFieldName, kstrConditioFieldName);
                qWriteSqlQuery.prepare(strUpdateSql);
                qWriteSqlQuery.bindValue(":new_value", updatedValue);
                qWriteSqlQuery.bindValue(":condition_value", kstrConditioFieldValue);

                bSuccess = qWriteSqlQuery.exec();
                if (bSuccess)
                {
                    qWriteSqlDB.commit();
                }
                else
                {
                    qWriteSqlDB.rollback();
                    qWarning() << "Update failed:" << qWriteSqlQuery.lastError().text();
                }
                return bSuccess;
            }
        }
        catch (const std::exception &e)
        {
        qWriteSqlDB.rollback();
        qWarning() << "Exception during insert:" << e.what();
        bSuccess = false;
        }
        
        return bSuccess; });
}

uint32_t CDBObject::getDBRecordCount(const QString &kstrTableName,
                                     const QMap<QString, QString> &kstrConditionMap)
{
    uint32_t iFindResult = 0;
    if (kstrTableName.isEmpty())
    {
        return iFindResult;
    }
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT COUNT(id) FROM %1").arg(kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return iFindResult;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            if (qReadSqlQuery.next())
            {
                iFindResult = qReadSqlQuery.value(0).toUInt();
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return iFindResult;
}

uint32_t CDBObject::getDBRecordCountFromData(const QString &kstrTableName,
                                     const QString &strDateFieldName,
                                     const QDate &qBeginDate,
                                     const QDate &qEndDate)
{
    uint32_t iFindResult = 0;

    // 检查输入参数是否有效
    if (kstrTableName.isEmpty() || strDateFieldName.isEmpty() || !qBeginDate.isValid() || !qEndDate.isValid())
    {
        DEBUG_LOG << "input invalid: table name, date field name or date range is invalid."
                  << kstrTableName << strDateFieldName << qBeginDate << qEndDate;
        return iFindResult;
    }

    // 构建日期范围的 WHERE 条件
    QString strConditions = strDateFieldName + " BETWEEN '" + qBeginDate.toString("yyyy-MM-dd") 
                            + "' AND '" + qEndDate.toString("yyyy-MM-dd") + "'";

    // 构建完整的 SQL 查询语句
    QString strSelect = "SELECT COUNT(id) FROM " + kstrTableName + " WHERE " + strConditions;

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return iFindResult;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        // 执行查询
        if (qReadSqlQuery.exec(strSelect))
        {
            if (qReadSqlQuery.next())
            {
                iFindResult = qReadSqlQuery.value(0).toUInt();
            }
        }
        else
        {
            DEBUG_LOG << "query failed: " << qReadSqlQuery.lastError().text();
        }
    }
    return iFindResult;
}

uint32_t CDBObject::getDBRecordCountFromLastDays(const QString &kstrTableName,
                                     const QString &strDateFieldName, const QString &strLastDays)
{
    QDate qEndDate = QDate::currentDate();
    QDate qBeginDate = qEndDate.addDays(-strLastDays.toInt() + 1); // +1是为了包含今天
    uint32_t iCount = this->getDBRecordCountFromData(kstrTableName, strDateFieldName, qBeginDate, qEndDate);
    return iCount;
}

uint32_t CDBObject::getDBRecordCountByKeyword(const QString &kstrTableName,
                                              const QStringList &kstrFindDataList, const QString &strKeyValue)
{
    uint32_t iFindResult = 0;
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || strKeyValue.isEmpty())
    {
        DEBUG_LOG << "Invalid input: Table name, column name, or keyword is empty.";
        return iFindResult;
    }

    if (kstrFindDataList.isEmpty() || strKeyValue.isEmpty())
    {
        iFindResult = this->getDBRecordCount(kstrTableName);
        return iFindResult;
    }

    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (const QString &field : kstrFindDataList)
    {
        strConditionList.append(QString("%1 LIKE '%%2%'").arg(field).arg(strKeyValue));
    }
    QString strConditions = strConditionList.join(" OR ");

    // 构建SQL查询语句
    QString strSelect = QString("SELECT COUNT(id) FROM %1").arg(kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return iFindResult;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        // 执行SQL查询
        if (qReadSqlQuery.exec(strSelect))
        {
            if (qReadSqlQuery.next())
            {
                iFindResult = qReadSqlQuery.value(0).toUInt();
            }
        }
        else
        {
            DEBUG_LOG << "查询失败: " << qReadSqlQuery.lastError().text();
        }
    }
    return iFindResult;
}

QVariant CDBObject::getOneRecordOneField(const QString &kstrTableName, const QString &kstrFindData,
                                         const QString &kstrConditioFieldName, const QString &kstrConditioFieldValue)
{
    QMap<QString, QString> strConditionMap =
        {{kstrConditioFieldName, kstrConditioFieldValue}};
    QVariant qFindResult = this->getOneRecordOneField(kstrTableName,
                                                      kstrFindData, strConditionMap)
                               .toString();
    return qFindResult;
}

QVariant CDBObject::getOneRecordOneField(const QString &kstrTableName,
                                         const QString &kstrFindData,
                                         const QMap<QString, QString> &kstrConditionMap)
{// QMap<QString, QString> strConditionMap = {{FLIED_Extract.extractID, strExtractID}};
    QVariant qFindResult;
    if (kstrTableName.isEmpty())
    {
        return qFindResult;
    }
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(kstrFindData, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResult;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            if (qReadSqlQuery.next())
            {
                qFindResult = qReadSqlQuery.value(0);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text()
                      << strSelect;
        }
    }
    return qFindResult;
}

QStringList CDBObject::getMultipleRecordsFieldWithCondition(const QString &kstrTableName,
                                                            const QString &kstrFindData,
                                                            const QString &kstrConditionField, const QString &kstrConditionValue)
{
    QStringList resultList;
    if (kstrTableName.isEmpty())
    {
        return resultList;
    }

    // 构建 WHERE 条件字符串，使用 LIKE 操作符
    QString strCondition = QString("%1 LIKE '%%2%'").arg(kstrConditionField, kstrConditionValue);

    // 构建完整的 SQL 查询语句
    QString strSelect = "SELECT " + kstrFindData + " FROM " + kstrTableName + " WHERE " + strCondition;

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return resultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            // 遍历结果集，获取所有符合条件的记录
            while (qReadSqlQuery.next())
            {
                resultList << qReadSqlQuery.value(0).toString();
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }

    return resultList;
}

QStringList CDBObject::getOneRecordMoreFields(const QString &kstrTableName,
                                              const QStringList &kstrFindDataList,
                                              const QMap<QString, QString> &kstrConditionMap)
{
    QStringList qFindResultList;
    if (kstrTableName.isEmpty() || kstrFindDataList.length() < 1)
    {
        return qFindResultList;
    }
    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(strSelectFields, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            if (qReadSqlQuery.next())
            {
                for (const auto &kstrData : kstrFindDataList)
                {
                    qFindResultList.push_back(QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray())); // 对于有中文用于通信的，不能直接toString
                }
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultList;
}

QStringList CDBObject::getOneRecordMoreFields(const QString &kstrTableName,
                                              const QStringList &kstrFindDataList,
                                              const QString &kstrConditioFieldName,
                                              const QString &kstrConditioFieldValue)
{

    QMap<QString, QString> strConditionMap =
        {{kstrConditioFieldName, kstrConditioFieldValue}};
    QStringList qFindResult = this->getOneRecordMoreFields(kstrTableName,
                                                           kstrFindDataList, strConditionMap);
    return qFindResult;
}

QList<QStringList> CDBObject::getMultipleRecordsMoreFieldsWithCondition(const QString &kstrTableName,
                                                                        const QStringList &kstrFindDataList,
                                                                        const QString &kstrConditionField,
                                                                        const QString &kstrConditionValue)
{
    QList<QStringList> qFindResultList;
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty())
    {
        return qFindResultList;
    }

    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");

    // 构建 WHERE 条件字符串，使用 LIKE 操作符
    QString strCondition = QString("%1 LIKE '%%2%'").arg(kstrConditionField, kstrConditionValue);

    // 构建完整的 SQL 查询语句
    QString strSelect = "SELECT " + strSelectFields + " FROM " + kstrTableName + " WHERE " + strCondition;

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            // 遍历结果集，获取所有符合条件的记录
            while (qReadSqlQuery.next())
            {
                QStringList singleRecord;
                for (const auto &kstrData : kstrFindDataList)
                {
                    singleRecord.push_back(QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray())); // 处理中文字符
                }
                qFindResultList.push_back(singleRecord); // 将每条记录添加到结果列表中
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }

    return qFindResultList;
}

QStringList CDBObject::getMoreRecordsOneField(const QString &kstrTableName, const QString &kstrFindData,
                                              const QString &kstrConditioFieldName, const QString &kstrConditioFieldValue,
                                              const QString &strOrderBy)
{
    QMap<QString, QString> strConditionMap =
        {{kstrConditioFieldName, kstrConditioFieldValue}};
    QStringList qFindResultList = this->getMoreRecordsOneField(kstrTableName,
                                                               kstrFindData, strConditionMap, strOrderBy);
    return qFindResultList;
}

QStringList CDBObject::getMoreRecordsOneField(const QString &kstrTableName,
                                              const QString &kstrFindData,
                                              const QMap<QString, QString> &kstrConditionMap,
                                              const QString &strOrderBy)
{
    QStringList qFindResultList;
    if (kstrTableName.isEmpty() || kstrFindData.isEmpty())
    {
        return qFindResultList;
    }
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(kstrFindData, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy; // ORDER BY CAST(cmd_id AS INTEGER)
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            while (qReadSqlQuery.next())
            {
                qFindResultList.append(QString::fromLocal8Bit(qReadSqlQuery.value(0).toByteArray()));
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultList;
}

QStringList CDBObject::getMoreRecordsOneFieldBatch(const QString &kstrTableName, 
                                                const QString &kstrFindData, const QString &kstrConditionField, 
                                                const QStringList &kstrConditionValues, const QString &strOrderBy)
{
    QStringList qFindResultList;
    if (kstrTableName.isEmpty() || kstrFindData.isEmpty() || kstrConditionValues.isEmpty())
    {
        DEBUG_LOG << "Invalid input parameters";
        return qFindResultList;
    }

    // 构建 IN 查询的占位符
    QString strPplaceholders = QString("(%1)").arg(QVector<QString>(kstrConditionValues.size(), "?").toList().join(","));

    // 构建完整的 SQL 查询语句
    QString strSelect = "SELECT " + kstrFindData + ", " + kstrConditionField + " FROM " + kstrTableName + " WHERE " + kstrConditionField + " IN " + strPplaceholders;
                           
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy;
    }

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }

    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        qReadSqlQuery.prepare(strSelect);
        
        // 绑定参数
        for(const QString &value : kstrConditionValues)
        {
            qReadSqlQuery.addBindValue(value);
        }

        if (qReadSqlQuery.exec())
        {
            // 使用 Map 存储结果，保持原始顺序
            QMap<QString, QString> resultMap;
            while (qReadSqlQuery.next())
            {
                QString conditionValue = qReadSqlQuery.value(kstrConditionField).toString();
                QString findValue = qReadSqlQuery.value(kstrFindData).toString();
                resultMap[conditionValue] = findValue;
            }

            // 按原始条件值列表顺序构建结果
            for(const QString &conditionValue : kstrConditionValues)
            {
                qFindResultList.append(resultMap.value(conditionValue, ""));
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultList;
}

QList<QStringList> CDBObject::getMoreRecordsMoreFieldsBatch(const QString &kstrTableName, const QStringList &kstrFindDataList, 
    const QString &kstrConditionField, const QStringList &kstrConditionValues, const QString &strOrderBy)
{
    QList<QStringList> qFindResultList;
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || kstrConditionValues.isEmpty())
    {
        DEBUG_LOG << "Invalid input parameters";
        return qFindResultList;
    }

    // 构建查询字段字符串，包含条件字段
    QString strSelectFields = kstrFindDataList.join(", ");
    
    // 构建 IN 查询的占位符
    QString strPlaceholders = QString("(%1)").arg(QVector<QString>(kstrConditionValues.size(), "?").toList().join(","));

    // 构建完整的 SQL 查询语句
    QString strSelect = "SELECT " + strSelectFields + ", " + kstrConditionField + " FROM " + kstrTableName + " WHERE " + kstrConditionField + " IN " + strPlaceholders;
                           
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy;
    }

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }

    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        qReadSqlQuery.prepare(strSelect);
        
        // 绑定参数
        for(const QString &value : kstrConditionValues)
        {
            qReadSqlQuery.addBindValue(value);
        }

        if (qReadSqlQuery.exec())
        {
            // 使用 Map 存储结果，保持原始顺序
            QMap<QString, QStringList> resultMap;
            while (qReadSqlQuery.next())
            {
                QString conditionValue = qReadSqlQuery.value(kstrConditionField).toString();
                QStringList strOneRowList;
                
                // 获取所有查询字段的值
                for (const auto &kstrData : kstrFindDataList)
                {
                    strOneRowList.push_back(QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray()));
                }
                resultMap[conditionValue] = strOneRowList;
            }

            // 按原始条件值列表顺序构建结果
            for(const QString &conditionValue : kstrConditionValues)
            {
                if (resultMap.contains(conditionValue))
                {
                    qFindResultList.append(resultMap[conditionValue]);
                }
                else
                {
                    // 如果没有找到对应的记录，添加空值列表
                    QStringList emptyRow;
                    for (int i = 0; i < kstrFindDataList.size(); ++i)
                    {
                        emptyRow.append("");
                    }
                    qFindResultList.append(emptyRow);
                }
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultList;
}

QStringList CDBObject::getDistinctFieldValues(const QString &kstrTableName, const QString &strFieldName)
{
    QStringList valuesList;

    // 构造查询字符串，使用 SELECT DISTINCT 获取不重复的字段值
    QString strSql = QString("SELECT DISTINCT %1 FROM %2").arg(strFieldName, kstrTableName);

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return valuesList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        if (qReadSqlQuery.exec(strSql))
        {
            while (qReadSqlQuery.next())
            {
                // 添加不重复的值到列表中
                valuesList.append(qReadSqlQuery.value(0).toString());
            }
        }
        else
        {
            DEBUG_LOG << "Query failed: " << qReadSqlQuery.lastError().text();
        }
    }

    return valuesList;
}

QList<QStringList> CDBObject::getMoreRecordsMoreFields(const QString &kstrTableName,
                                                       const QStringList &kstrFindDataList,
                                                       const QMap<QString, QString> &kstrConditionMap,
                                                       const QString &strOrderBy)
{
    QList<QStringList> qFindResultList;
    if (kstrTableName.isEmpty())
    {
        return qFindResultList;
    }
    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(strSelectFields, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy; // ORDER BY CAST(cmd_id AS INTEGER)
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            while (qReadSqlQuery.next())
            {
                QStringList strOneRowList;
                for (const auto &kstrData : kstrFindDataList)
                {
                    strOneRowList.push_back(QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray()));
                }
                qFindResultList.append(strOneRowList);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultList;
}

QString CDBObject::getMoreRecordsMoreFieldsString(const QString &kstrTableName,
                                                  const QStringList &kstrFindDataList,
                                                  const QMap<QString, QString> &kstrConditionMap,
                                                  const QString &strOrderBy)
{
    QString qFindResult;
    if (kstrTableName.isEmpty())
    {
        return qFindResult;
    }
    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(strSelectFields, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy; // ORDER BY CAST(cmd_id AS INTEGER)
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResult;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            while (qReadSqlQuery.next())
            {
                QStringList strOneRowList;
                for (const auto &kstrData : kstrFindDataList)
                {
                    strOneRowList.push_back(QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray()));
                }
                qFindResult += strOneRowList.join(",") + ";";
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResult;
}

QList<QMap<QString, QString>> CDBObject::getMoreRecordsMoreFieldsMap(const QString &kstrTableName,
                                                                     const QStringList &kstrFindDataList,
                                                                     const QMap<QString, QString> &kstrConditionMap,
                                                                     const QString &strOrderBy)
{
    QList<QMap<QString, QString>> qFindResultMap;
    if (kstrTableName.isEmpty())
    {
        return qFindResultMap;
    }
    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");
    // 构建WHERE条件字符串
    QStringList strConditionList;
    for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
    {
        strConditionList.append(it.key() + "='" + it.value() + "'");
    }
    QString strConditions = strConditionList.join(" AND ");
    // 构建完整的SQL查询语句
    QString strSelect = QString("SELECT %1 FROM %2").arg(strSelectFields, kstrTableName);
    if (!strConditions.isEmpty())
    {
        strSelect += " WHERE " + strConditions;
    }
    if (!strOrderBy.isEmpty())
    {
        strSelect += " ORDER BY " + strOrderBy;
    }

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return qFindResultMap;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strSelect))
        {
            while (qReadSqlQuery.next())
            {
                QMap<QString, QString> strOneRowMap;
                for (const auto &kstrData : kstrFindDataList)
                {
                    strOneRowMap[kstrData] = QString::fromLocal8Bit(qReadSqlQuery.value(kstrData).toByteArray());
                }
                qFindResultMap.append(strOneRowMap);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return qFindResultMap;
}

bool CDBObject::updateValueFromConditionFieldName(const QString &kstrTableName,
                                                  const QString &kstrUpdateFieldName, const QString &kstrUpdateieldValue,
                                                  const QString &kstrConditioFieldName, const QString &kstrConditioFieldValue)
{
    bool bResult = false;
    QMap<QString, QString> strUpdataMap;
    QMap<QString, QString> strConditionMap;
    strUpdataMap.insert(kstrUpdateFieldName, kstrUpdateieldValue);
    strConditionMap.insert(kstrConditioFieldName, kstrConditioFieldValue);
    bResult = this->updateDBRecord(kstrTableName, strUpdataMap, strConditionMap);
    return bResult;
}

QList<QStringList> CDBObject::getSomeDBRecordOnePageOrderBy(const QString &kstrTableName,
                                                            const QStringList &kstrFindDataList,
                                                            const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                            const QString &orderByField) // Add this parameter
{
    QList<QStringList> strResultList;
    if (iPageLength < 1)
    {
        DEBUG_LOG << "Query failed(param): " << iBeginIndex << iPageLength;
        return strResultList;
    }

    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");
    // Three more placeholders are added in the string.
    QString strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " ORDER BY " + orderByField
                       + " DESC LIMIT " + QString::number(iPageLength) + " OFFSET " + QString::number(iBeginIndex);

    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strQuery))
        {
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                strResultList.append(strRowValuesList);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

QList<QStringList> CDBObject::getSomeDBRecordOnePageOrderByDate(const QString &kstrTableName,
                                                                const QStringList &kstrFindDataList,
                                                                const QString &strDateFieldName,
                                                                const QDate &qBeginDate, const QDate &qEndDate,
                                                                const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                                const QString &orderByField)
{
    QList<QStringList> strResultList;

    // 输入参数校验
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || strDateFieldName.isEmpty() ||
        !qBeginDate.isValid() || !qEndDate.isValid())
    {
        DEBUG_LOG << "input invalid: table name, field list, date field, date range or page length is invalid."
                  << kstrTableName << kstrFindDataList << strDateFieldName << qBeginDate << qEndDate << iPageLength;
        return strResultList;
    }

    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");

    // 构建日期范围的 WHERE 条件
    QString strDateCondition = strDateFieldName + " BETWEEN '" + qBeginDate.toString("yyyy-MM-dd") + "' AND '" + qEndDate.toString("yyyy-MM-dd") + "'";

    // 构建完整的查询 SQL 语句
    QString strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " WHERE " + strDateCondition 
        + " ORDER BY " + orderByField + " ASC LIMIT " + QString::number(iPageLength) + " OFFSET " + QString::number(iBeginIndex);

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        // 执行查询语句
        if (qReadSqlQuery.exec(strQuery))
        {
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;

                // 遍历查询结果的每一列
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                strResultList.append(strRowValuesList);
            }
        }
        else
        {
            DEBUG_LOG << "query failed: " << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

QList<QStringList> CDBObject::getSomeDBRecordOnePageOrderByDate(const QString &kstrTableName,
                                                                const QStringList &kstrFindDataList,
                                                                const QString &strDateFieldName, const QString &strLastDays,
                                                                const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                                const QString &orderByField)
{
    int iDays = strLastDays.toInt();
    QDate qBeginDate = QDate::currentDate().addDays(-iDays);
    return this->getSomeDBRecordOnePageOrderByDate(kstrTableName, kstrFindDataList,
                                                   strDateFieldName, qBeginDate, QDate::currentDate(),
                                                   iBeginIndex, iPageLength, orderByField);
}

QList<QStringList> CDBObject::searchMultipleRecordsByKey(const QString &kstrTableName,
                                                         const QStringList &kstrFindDataList, const QString &strKeyValue, const bool isDescending)
{
    QList<QStringList> resultList;

    // 检查表名和字段列表是否有效
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || strKeyValue.isEmpty())
    {
        DEBUG_LOG << "Invalid table name, field list, or search key.";
        return resultList;
    }

    // 构建 SQL 查询字符串
    QString strQueryStr = "SELECT * FROM " + kstrTableName + " WHERE ";

    // 构建字段的 LIKE 条件
    QStringList strConditions;
    for (const QString &field : kstrFindDataList)
    {
        strConditions << QString("%1 LIKE '%%2%'").arg(field).arg(strKeyValue);
    }

    // 将所有条件组合成查询语句
    strQueryStr += strConditions.join(" OR ");

    // 设置全局排序方式，根据 isDescending 控制是升序还是降序
    QString order = isDescending ? "DESC" : "ASC";
    strQueryStr += " ORDER BY id " + order; // 全局按 id 排序
    // 执行 SQL 查询
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return resultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        if (qReadSqlQuery.exec(strQueryStr))
        {
            // 遍历查询结果
            while (qReadSqlQuery.next())
            {
                QStringList record;
                // 遍历字段列表，获取每个字段的值
                for (const QString &field : kstrFindDataList)
                {
                    record.append(qReadSqlQuery.value(field).toString());
                }
                resultList.append(record);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return resultList;
}
QList<QStringList> CDBObject::searchMultipleRecordsByDate(const QString &kstrTableName,
                                                          const QStringList &kstrFindDataList,
                                                          const QString &strDateFieldName,
                                                          const QDate &qBeginDate, const QDate &qEndDate,
                                                          const QString &orderByField)
{
    QList<QStringList> resultList;

    // 检查输入是否有效
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || strDateFieldName.isEmpty() ||
        !qBeginDate.isValid() || !qEndDate.isValid())
    {
        DEBUG_LOG << "无效输入: 表名、字段列表或日期范围有误。";
        return resultList;
    }

    // 构建查询字段字符串
    QString strSelectFields = kstrFindDataList.join(", ");

    // 构建日期范围条件
    QString strDateCondition = strDateFieldName + " BETWEEN '" + qBeginDate.toString("yyyy-MM-dd") + "' AND '" + qEndDate.toString("yyyy-MM-dd") + "'";

    // 构建 SQL 查询语句
    QString strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " WHERE " + strDateCondition + " ORDER BY " + orderByField + " ASC";

    // 获取数据库连接
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return resultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        // 执行查询
        if (qReadSqlQuery.exec(strQuery))
        {
            while (qReadSqlQuery.next())
            {
                QStringList record;

                // 遍历查询结果中的每个字段
                for (const QString &field : kstrFindDataList)
                {
                    record.append(qReadSqlQuery.value(field).toString());
                }
                resultList.append(record);
            }
        }
        else
        {
            DEBUG_LOG << "查询失败: " << qReadSqlQuery.lastError().text();
        }
    }

    return resultList;
}

QList<QStringList> CDBObject::searchMultipleRecordsByDate(const QString &kstrTableName,
                                                          const QStringList &kstrFindDataList,
                                                          const QString &strDateFieldName, const QString &strLastDays)
{
    int iDays = strLastDays.toInt();
    QDate qBeginDate = QDate::currentDate().addDays(-iDays);
    QList<QStringList> resultList = this->searchMultipleRecordsByDate(kstrTableName, kstrFindDataList,
                                                                      strDateFieldName, qBeginDate, QDate::currentDate());
    return resultList;
}

QList<QStringList> CDBObject::searchMultipleRecordsByKeyOnePage(const QString &kstrTableName,
                                                                const QStringList &kstrFindDataList,
                                                                const QString &strKeyValue,
                                                                const uint32_t &iBeginIndex,
                                                                const uint32_t &iPageLength, bool isDescending)
{
    QList<QStringList> resultList;

    // 检查输入参数的有效性
    if (kstrTableName.isEmpty() || kstrFindDataList.isEmpty() || strKeyValue.isEmpty() || iPageLength < 1)
    {
        DEBUG_LOG << "Invalid input parameters.";
        return resultList;
    }

    // 构建查询字段列表
    QString strSelectFields = kstrFindDataList.join(", ");

    // 构建 SQL 查询字符串
    QString queryStr = QString("SELECT %1 FROM %2 WHERE ").arg(strSelectFields).arg(kstrTableName);

    // 构建字段的 LIKE 条件
    QStringList conditions;
    for (const QString &field : kstrFindDataList)
    {
        conditions << QString("%1 LIKE '%%2%'").arg(field).arg(strKeyValue);
    }

    // 拼接 WHERE 条件
    queryStr += conditions.join(" OR ");

    // 设置全局排序方式，根据 isDescending 控制是升序还是降序
    QString order = isDescending ? "DESC" : "ASC";
    queryStr += " ORDER BY id " + order; // 全局按 id 排序

    // 添加分页部分
    queryStr += " LIMIT " + QString::number(iPageLength) + " OFFSET " + QString::number(iBeginIndex);

    // 执行 SQL 查询
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return resultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);

        if (qReadSqlQuery.exec(queryStr))
        {
            // 遍历查询结果
            while (qReadSqlQuery.next())
            {
                QStringList record;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    record.append(qReadSqlQuery.value(i).toString());
                }
                resultList.append(record);
            }
        }
        else
        {
            DEBUG_LOG << "Query failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return resultList;
}

bool CDBObject::updateDBRecord(const QString &kstrTableName,
                               const QMap<QString, QString> &kstrUpdateMap,
                               const QMap<QString, QString> &kstrConditionMap)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();        
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }
        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;
        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction())
            {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();
                return false;
            }

            // 构建UPDATE语句的SET部分
            QStringList strSetPartsList;
            for (const QString &key : kstrUpdateMap.keys())
            {
                strSetPartsList.append(key + "=:" + key);
            }
            QString strSetClause = strSetPartsList.join(", ");
            // 构建WHERE条件
            QStringList strConditionPartsList;
            for (const QString &key : kstrConditionMap.keys())
            {
                strConditionPartsList.append(key + "=:" + key + "_cond");
            }
            QString strWhereClause = strConditionPartsList.join(" AND ");
            // 组合成完整的UPDATE语句
            QString strSqlUpdate = QString("UPDATE %1 SET %2 WHERE %3").arg(kstrTableName, strSetClause, strWhereClause);

            qWriteSqlQuery.clear();
            qWriteSqlQuery.prepare(strSqlUpdate);
            // 绑定更新值参数
            for (auto it = kstrUpdateMap.constBegin(); it != kstrUpdateMap.constEnd(); ++it)
            {
                qWriteSqlQuery.bindValue(":" + it.key(), it.value());
            }
            // 绑定条件值参数
            for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
            {
                qWriteSqlQuery.bindValue(":" + it.key() + "_cond", it.value());
            }
            // 执行查询
            bSuccess = qWriteSqlQuery.exec();
            if (bSuccess)
            {
                qWriteSqlDB.commit();
            }
            else
            {
                qWriteSqlDB.rollback();
                qWarning() << "Update failed:" << qWriteSqlQuery.lastError().text();
            }
        }
        catch (const std::exception &e)
        {
        qWriteSqlDB.rollback();
        qWarning() << "Exception during update:" << e.what();
        bSuccess = false;
        }
        
        return bSuccess; });
}

bool CDBObject::deleteDBRecord(const QString &kstrTableName,
                               const QMap<QString, QString> &kstrConditionMap)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }   
        
        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;
        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction()) {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();
                return false;
            }

            // 构建DELETE FROM语句的WHERE部分
            QStringList strConditionParts;
            for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
            {
                strConditionParts.append(it.key() + "=:" + it.key());
            }
            QString strWhereClause = strConditionParts.join(" AND ");
            // 组合成完整的DELETE FROM语句
            QString strSqlDelete = QString("DELETE FROM %1").arg(kstrTableName);
            if (!strWhereClause.isEmpty())
            {
                strSqlDelete += " WHERE " + strWhereClause;
            }

            qWriteSqlQuery.clear();
            qWriteSqlQuery.prepare(strSqlDelete);
            // 绑定条件值参数
            for (auto it = kstrConditionMap.constBegin(); it != kstrConditionMap.constEnd(); ++it)
            {
                qWriteSqlQuery.bindValue(":" + it.key(), it.value());
            }
            // 执行查询
            bSuccess = qWriteSqlQuery.exec();
            if (bSuccess)
            {
                qWriteSqlDB.commit();
            }
            else
            {
                qWriteSqlDB.rollback();
                qWarning() << "Delete failed:" << qWriteSqlQuery.lastError().text();
            }
        }
        catch (const std::exception &e)
        {
        qWriteSqlDB.rollback();
        qWarning() << "Exception during delete:" << e.what();
        bSuccess = false;
        }
        
        return bSuccess; });
}

bool CDBObject::deleteDBRecord(const QString &kstrTableName, const QString &strFiledName, const QString &strFiledValue)
{
    bool bResult = false;
    if (strFiledValue.isEmpty())
    {
        DEBUG_LOG << "strFiledValue is Empty";
        return bResult;
    }
    QMap<QString, QString> strConditionMap =
        {{strFiledName, strFiledValue}};

    bResult = this->deleteDBRecord(kstrTableName, strConditionMap);
    return bResult;
}

bool CDBObject::deleteDBTable(const QString &kstrTableName)
{
    return getWriteConnectionPool()->executeWrite([&]()
                                                  {
        QSqlDatabase qWriteSqlDB = acquireWriteConnection();
        if (!qWriteSqlDB.isValid()) {
            qWarning() << "Failed to acquire write connection";
            return false;
        }
        QSqlQuery qWriteSqlQuery(qWriteSqlDB);
        bool bSuccess = false;
        try
        {
            // 开启事务
            if (!qWriteSqlDB.transaction()) {
                qWarning() << "Failed to start transaction:" << qWriteSqlDB.lastError().text();
                return false;
            }
       
            // 对于使用 id INTEGER PRIMARY KEY AUTOINCREMENT创建的自增主键数据库
            // 此种删除表方法后，如果重新创建了表，自增id不会从1开始，会沿用删除前的id自增顺序
            // 如果需要自增id从1开始，可以自行执行下述语句
            // DELETE FROM sqlite_sequence WHERE name = 'your_table_name';
            QString strSqlDeleteTable = QString("DROP TABLE IF EXISTS %1").arg(kstrTableName);

            bSuccess = qWriteSqlQuery.exec(strSqlDeleteTable);
            if (bSuccess)
            {
                qWriteSqlDB.commit();
            }
            else
            {
                qWriteSqlDB.rollback();
                qWarning() << "Delete table failed:" << qWriteSqlQuery.lastError().text();
            }
        }
        catch (const std::exception &e)
        {
            qWriteSqlDB.rollback();
            qWarning() << "Exception during delete table:" << e.what();
            bSuccess = false;
        }
        
        return bSuccess; });
}


QList<QStringList> CDBObject::getSomeDBRecordByIdRange(const QString &kstrTableName,
                                                       const QStringList &kstrFindDataList,
                                                       const uint32_t &lastId,
                                                       const uint32_t &iPageLength,
                                                       bool isForward,
                                                       int skipCount,
                                                       bool bIsDescending)
{
    QList<QStringList> strResultList;
    if (iPageLength < 1)
    {
        DEBUG_LOG << "find failed: invalid parameters";
        return strResultList;
    }

    QString strSelectFields = kstrFindDataList.join(", ");
    QString strQuery;
    QString strOrder;
    QString strWhere;

    if (skipCount > 0) {
        // 跳页，正序/倒序都用offset
        strOrder = bIsDescending ? "DESC" : "ASC";
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " ORDER BY id " + strOrder 
                    + " LIMIT " + QString::number(iPageLength) + " OFFSET " + QString::number(skipCount);
    } else {
        // id偏移，正序/倒序+向上/向下
        if (!bIsDescending) {
            // 正序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("WHERE id > %1").arg(lastId);
                strOrder = "ASC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("WHERE id < %1").arg(lastId);
                strOrder = "DESC";
            }
        } else {
            // 倒序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("WHERE id < %1").arg(lastId);
                strOrder = "DESC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("WHERE id > %1").arg(lastId);
                strOrder = "ASC";
            }
        }             
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strWhere + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength);    
    }

    DEBUG_LOG << "getSomeDBRecordByIdRange" << strQuery;
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strQuery))
        {
            QList<QStringList> tmpList;
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                tmpList.append(strRowValuesList);
            }
            // 需要reverse的情况：正序上一页、倒序上一页
            if ((!bIsDescending && !isForward) || (bIsDescending && !isForward)) {
                std::reverse(tmpList.begin(), tmpList.end());
            }
            strResultList = tmpList;
        }
        else
        {
            DEBUG_LOG << "find failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

QList<QStringList> CDBObject::searchSomeRecordsByKeyOnePageIdRange(const QString &kstrTableName,
                                                                   const QStringList &kstrFindDataList,
                                                                   const QString &strKeyValue,
                                                                   const uint32_t &lastId,
                                                                   const uint32_t &iPageLength,
                                                                   bool isForward,
                                                                   int skipCount,
                                                                   bool bIsDescending)
{
    QList<QStringList> strResultList;
    if (iPageLength < 1 || kstrFindDataList.isEmpty())
    {
        DEBUG_LOG << "find failed: invalid parameters";
        return strResultList;
    }

    QString strSelectFields = kstrFindDataList.join(", ");
    QString strOrder;
    QString strWhere;
    QString strQuery;

    // 构建LIKE条件
    QStringList strLikeConditions;
    if (!strKeyValue.isEmpty()) {
        for (const QString &field : kstrFindDataList) {
            strLikeConditions << QString("%1 LIKE '%%2%'").arg(field).arg(strKeyValue);
        }
    }

    if (skipCount > 0) {
        // 跳页
        strOrder = bIsDescending ? "DESC" : "ASC";
        QString strWhereClause = strLikeConditions.isEmpty() ? 
            "" : QString("WHERE (%1)").arg(strLikeConditions.join(" OR "));            
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strWhereClause + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength)
                    + " OFFSET " + QString::number(skipCount);
    } else {
        // id偏移
        QStringList strWhereConditions;
        if (!bIsDescending) {
            if (isForward) {
                if (lastId > 0)
                    strWhereConditions << QString("id > %1").arg(lastId);
                strOrder = "ASC";
            } else {
                if (lastId > 0)
                    strWhereConditions << QString("id < %1").arg(lastId);
                strOrder = "DESC";
            }
        } else {
            if (isForward) {
                if (lastId > 0)
                    strWhereConditions << QString("id < %1").arg(lastId);
                strOrder = "DESC";
            } else {
                if (lastId > 0)
                    strWhereConditions << QString("id > %1").arg(lastId);
                strOrder = "ASC";
            }
        }
        if (!strLikeConditions.isEmpty()) {
            strWhereConditions << QString("(%1)").arg(strLikeConditions.join(" OR "));
        }
        QString strWhereClause = strWhereConditions.isEmpty() ? 
            "" : QString("WHERE %1").arg(strWhereConditions.join(" AND "));
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strWhereClause + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength);
   
    }

    DEBUG_LOG << "searchSomeRecordsByKeyOnePageIdRange" << strQuery;
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }

    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strQuery))
        {
            QList<QStringList> tmpList;
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                tmpList.append(strRowValuesList);
            }
            // 需要reverse的情况：正序上一页、倒序上一页
            if ((!bIsDescending && !isForward) || (bIsDescending && !isForward)) {
                std::reverse(tmpList.begin(), tmpList.end());
            }
            strResultList = tmpList;
        }
        else
        {
            DEBUG_LOG << "find failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

QList<QStringList> CDBObject::getSomeDBRecordOrderByDateIdRange(const QString &kstrTableName,
                                                const QStringList &kstrFindDataList,
                                                const QString &strDateFieldName,
                                                const QDate &qBeginDate,
                                                const QDate &qEndDate,
                                                const uint32_t &lastId,
                                                const uint32_t &iPageLength,
                                                bool isForward,
                                                int skipCount,
                                                bool bIsDescending)
{
    QList<QStringList> strResultList;
    if (iPageLength < 1)
    {
        DEBUG_LOG << "find failed: invalid parameters";
        return strResultList;
    }

    QString strSelectFields = kstrFindDataList.join(", ");
    QString strQuery;
    QString strOrder;
    QString strWhere;
    
    // 构建日期范围条件
    QString strDateCondition = strDateFieldName + " BETWEEN '" + qBeginDate.toString("yyyy-MM-dd") + "' AND '" + qEndDate.toString("yyyy-MM-dd") + "'";

    if (skipCount > 0) {
        // 跳页，正序/倒序都用offset
        strOrder = bIsDescending ? "DESC" : "ASC";
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strDateCondition + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength)
                    + " OFFSET " + QString::number(skipCount);
    } else {
        // id偏移，正序/倒序+向上/向下
        if (!bIsDescending) {
            // 正序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id > %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "ASC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id < %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "DESC";
            }
        } else {
            // 倒序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id < %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "DESC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id > %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "ASC";
            }
        }
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strWhere + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength);
    }

    DEBUG_LOG << "getSomeDBRecordOrderByDateIdRange" << strQuery;
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strQuery))
        {
            QList<QStringList> tmpList;
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                tmpList.append(strRowValuesList);
            }
            // 需要reverse的情况：正序上一页、倒序上一页
            if ((!bIsDescending && !isForward) || (bIsDescending && !isForward)) {
                std::reverse(tmpList.begin(), tmpList.end());
            }
            strResultList = tmpList;
        }
        else
        {
            DEBUG_LOG << "find failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

QList<QStringList> CDBObject::getSomeDBRecordOrderByDateIdRange(const QString &kstrTableName,
                                                const QStringList &kstrFindDataList,
                                                const QString &strDateFieldName,
                                                const QString &strLastDays,
                                                const uint32_t &lastId,
                                                const uint32_t &iPageLength,
                                                bool isForward,
                                                int skipCount,
                                                bool bIsDescending)
{
    QList<QStringList> strResultList;
    if (iPageLength < 1)
    {
        DEBUG_LOG << "find failed: invalid parameters";
        return strResultList;
    }

    // 计算日期范围
    QDate qEndDate = QDate::currentDate();
    QDate qBeginDate = qEndDate.addDays(-strLastDays.toInt() + 1); // +1是为了包含今天

    QString strSelectFields = kstrFindDataList.join(", ");
    QString strQuery;
    QString strOrder;
    QString strWhere;
    
    // 构建日期范围条件，使用BETWEEN，与第一个函数保持一致的风格
    // 不使用QString的arg，直接拼接字符串
    QString strDateCondition = "WHERE " + strDateFieldName + " BETWEEN '" 
                                + qBeginDate.toString("yyyy-MM-dd") + "' AND '" + qEndDate.toString("yyyy-MM-dd") + "'";

    if (skipCount > 0) {
        // 跳页，正序/倒序都用offset
        strOrder = bIsDescending ? "DESC" : "ASC";
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strDateCondition + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength)
                    + " OFFSET " + QString::number(skipCount);
    } else {
        // id偏移，正序/倒序+向上/向下
        if (!bIsDescending) {
            // 正序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id > %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "ASC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id < %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "DESC";
            }
        } else {
            // 倒序
            if (isForward) {
                // 下一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id < %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "DESC";
            } else {
                // 上一页
                if (lastId > 0)
                    strWhere = QString("%1 AND id > %2").arg(strDateCondition).arg(lastId);
                else
                    strWhere = strDateCondition;
                strOrder = "ASC";
            }
        }
        // 不使用QString的arg，直接拼接字符串
        strQuery = "SELECT " + strSelectFields + " FROM " + kstrTableName + " " 
                    + strWhere + " ORDER BY id " + strOrder + " LIMIT " + QString::number(iPageLength);
    }

    DEBUG_LOG << "getSomeDBRecordOrderByDateIdRange" << strQuery;
    ConnectionGuard connectionGuard(this);
    if (!connectionGuard.isValid())
    {
        qWarning() << "connectionGuard is not valid:" << connectionGuard.getLastError();
        return strResultList;
    }
    QSqlDatabase &qThreadReadDb = connectionGuard.get();
    if (qThreadReadDb.isOpen())
    {
        QSqlQuery qReadSqlQuery(qThreadReadDb);
        if (qReadSqlQuery.exec(strQuery))
        {
            QList<QStringList> tmpList;
            while (qReadSqlQuery.next())
            {
                QStringList strRowValuesList;
                for (int i = 0; i < qReadSqlQuery.record().count(); ++i)
                {
                    strRowValuesList << qReadSqlQuery.value(i).toString();
                }
                tmpList.append(strRowValuesList);
            }
            // 需要reverse的情况：正序上一页、倒序上一页
            if ((!bIsDescending && !isForward) || (bIsDescending && !isForward)) {
                std::reverse(tmpList.begin(), tmpList.end());
            }
            strResultList = tmpList;
        }
        else
        {
            DEBUG_LOG << "find failed:" << qReadSqlQuery.lastError().text();
        }
    }
    return strResultList;
}

