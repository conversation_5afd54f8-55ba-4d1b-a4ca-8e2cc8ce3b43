#ifndef AGINGTEST_H
#define AGINGTEST_H

#include<QObject>
#include "AgingConfigMgr.h"
#include"publicconfig.h"

class AgingTest : public QObject
{
    Q_OBJECT
public:
    
    enum AgingTestCmd // 电机命令
    {
        incrssease,
    };
    Q_ENUM( AgingTestCmd)
    
    
public:
    
    void AgingTestOperation(const QString strParams);
    
    void HandleCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult);
    
    void AskAgingTestActionList();
      
private:
    
    AgingConfigMgr* m_pAgingConfigMgr;


public:
    AgingTest();
    ~AgingTest();
private:
    Q_DISABLE_COPY(AgingTest);

};


#endif // AGINGTEST_H
