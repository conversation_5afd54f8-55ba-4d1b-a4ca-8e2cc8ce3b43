﻿#ifndef CSYSTEMDB_H
#define CSYSTEMDB_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QMutex>
#include <QVariant>
#include <QMap>
#if Q_OS_QML
#include <QQmlEngine>
#endif
#include <QJsonObject>
#include "cdbobject.h"

// 前向声明
class CSystemDBImpl;

// 缓存层类
class CSystemDB : public QObject
{
    Q_OBJECT
public:
    explicit CSystemDB(QObject *parent = nullptr);
#if Q_OS_QML
    // 用于QML中的单例模式
    static QObject* qmlSingletonInstance(QQmlEngine* engine, QJSEngine* scriptEngine)
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static CSystemDB& getInstance();

public:
    Q_INVOKABLE void initDataBase();
    Q_INVOKABLE void updateDBFile(const QString &strNewFilePath);

    // config
    Q_INVOKABLE QVariant getValueFromKey(QVariant qKey);
    Q_INVOKABLE int getIntValueFromKey(QVariant qKey);
    Q_INVOKABLE float getFloatValueFromKey(QVariant qKey);
    Q_INVOKABLE bool getBoolValueFromKey(QVariant qKey);
    Q_INVOKABLE QString getStringValueFromKey(QVariant qKey);
    Q_INVOKABLE bool addKeyValue(QVariant qKey, QVariant qValue);

private:
    void loadAllConfigs();

private:
    QMap<QString, QVariant> m_mapConfigs;  // 内存中的配置缓存
    QMutex m_mutex;  // 用于线程安全
    CSystemDBImpl& m_dbImpl;  // 实际的数据库操作对象
};

// 实际数据库操作类
class CSystemDBImpl : public CDBObject
{
    Q_OBJECT
public:
    explicit CSystemDBImpl(QObject *parent = nullptr);
    static CSystemDBImpl& getInstance();

protected:
    QString getDatabasePath() const override {
        return CGlobalConfig::getInstance().GetSystemDBDir();
    }
    QString getConnectionName() const override {
        return gk_strSystemDBConnect;
    }
    WriteConnectionPool *getWriteConnectionPool() override {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }

public:
    void initDataBase();
    void updateDBFile(const QString &strNewFilePath);
    void setDefaultValue();
    QVariant getValueFromKey(QVariant qKey);
    int getIntValueFromKey(QVariant qKey);
    float getFloatValueFromKey(QVariant qKey);
    bool getBoolValueFromKey(QVariant qKey);
    QString getStringValueFromKey(QVariant qKey);
    bool addKeyValue(QVariant qKey, QVariant qValue);
    QMap<QString, QVariant> getAllConfigs();

private:
    bool _getKayIsExist(QVariant qKey);

private:
    struct FieldName_ConfigInfo {
        static inline const QString id = "id";
        static inline const QString key = "key";
        static inline const QString value = "value";
    };

    int m_iConfigTableColumnCount;
    QString m_strTableConfigName;
    FieldName_ConfigInfo FLIED_Config;
    QStringList m_strFieldNameConfigList;
};

#endif // CSYSTEMDB_H
