#include "ctcpanalyzethread.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"

CTcpAnalyzeThread::CTcpAnalyzeThread(QObject *parent) : QObject(parent)
{
    m_bThreadExit = false;
    m_iFrameNumber = 0;
    // 参数初始化
    m_qbReadBuffArray = "";
    m_qbReadBuffArray.reserve(1024 * 1024 * 4);
    m_bReCalculation = true;
    m_pCleanupTimer = new QTimer();
    connect(m_pCleanupTimer, &QTimer::timeout, this, &CTcpAnalyzeThread::slotCleanupReceivedPackets);
    m_pCleanupTimer->start(1000); // 每秒清理一次

    m_qReciveDataByteArrayList.reserve(40960);
    m_iPayloadMaxLength = 512;
    pthread_t tid;
    pthread_create(&tid, NULL, _createThreadHandleList,this);
}

CTcpAnalyzeThread::~CTcpAnalyzeThread()
{
    m_bThreadExit = true;
}

void CTcpAnalyzeThread::slotReciveOriginalMessageData(QByteArray qReadMsgArray)
{
    this->_addReadData(qReadMsgArray);
}


void CTcpAnalyzeThread::_addReadData(const QByteArray & qMsgBtyeArray)
{
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qWarning() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        emit sigError(FT_Comm_CacheFull, "");
        return;
    }
    m_qSendMessageInfoList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
    m_conditionVariable.notify_one();// 唤醒
    //    qDebug() << "++++++++++++++++ tcp read" << qMsgBtyeArray.count()
    //             << qMsgBtyeArray;
}

bool CTcpAnalyzeThread::_isValidFrameHeader(const QByteArray &buffer)
{
    static const QByteArray s_kExpectedHeaderByteArray = QByteArray::fromHex("404D312A");
    return buffer.startsWith(s_kExpectedHeaderByteArray);
}

uint64_t CTcpAnalyzeThread::_combineIDs(uint16_t cmd_type, uint16_t frame_id, uint16_t command_id)
{
    return (static_cast<uint64_t>(cmd_type) << 32) |(static_cast<uint64_t>(frame_id) << 16) | command_id;
}

void CTcpAnalyzeThread::_processPacket(
        const quint16 &iCmdType,
        const quint16 & iSeqNumber,
        const quint16 & iMethodID)
{
    uint64_t combined_id = _combineIDs(iCmdType, iSeqNumber, iMethodID);
    QDateTime current_time = QDateTime::currentDateTime();
    {
        QMutexLocker locker(&m_qSameFrameMutex);
        if (!m_qReceivedPackets.contains(combined_id))
        {
            // 数据包是新的，转发到业务层
            emit sigReciveMessage(m_qCurrentReciveDataByteArray);
            // 存储数据包的时间戳
            m_qReceivedPackets.insert(combined_id, current_time);
        }
        else
        {
            // 重复数据包，忽略
            qInfo() << "Warning CTcp_processSamePacket" << hex << "iSeqNumber" << iSeqNumber << "iMethodID" << iMethodID;
        }
    }
}

void CTcpAnalyzeThread::slotCleanupReceivedPackets()
{
    QDateTime current_time = QDateTime::currentDateTime();
    QMutexLocker locker(&m_qSameFrameMutex);
    auto it = m_qReceivedPackets.begin();
    while (it != m_qReceivedPackets.end())
    {
        if (it.value().secsTo(current_time) > 5)
        { // 清理5秒以上未被访问的数据包
            it = m_qReceivedPackets.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

void *CTcpAnalyzeThread::_createThreadHandleList(void *arg)
{
    CTcpAnalyzeThread* pCTcpAnalyzeThread = (CTcpAnalyzeThread*)arg;
    std::unique_lock<std::mutex> uniqueLock(pCTcpAnalyzeThread->m_mutex);
    while(!pCTcpAnalyzeThread->m_bThreadExit)
    {
        pCTcpAnalyzeThread->m_conditionVariable.wait(uniqueLock, [pCTcpAnalyzeThread]
        { return pCTcpAnalyzeThread->m_iReadIndex.load() != pCTcpAnalyzeThread->m_iWriteIndex.load()
                    || pCTcpAnalyzeThread->m_bThreadExit;
        });
        if (pCTcpAnalyzeThread->m_bThreadExit)
        {
            break;
        }
        pCTcpAnalyzeThread->_handleReceiveList();
    }
    return NULL;
}

void CTcpAnalyzeThread::_handleReceiveList()
{  // 接收
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        m_qbReadBuffArray += m_qSendMessageInfoList[m_iReadIndex.load()];
        m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
        while(m_iRedaBuffArrayLength >= gk_iFrameLengthNotData)// 一帧至少长度为21
        {
            if (_isValidFrameHeader(m_qbReadBuffArray))
            {//
                if(m_bReCalculation)
                {
                    pLength = m_qbReadBuffArray.data() + gk_iSeqPos;
                    m_iReadSeqNumber = GetByte2Int(pLength);
                    pLength = m_qbReadBuffArray.data() + gk_iLengthPos;
                    m_iReadPayloadLength = GetByte2Int(pLength);
                    m_bReCalculation = false;
                }
                // 判定帧长度与payload长度正确性
                if(m_iRedaBuffArrayLength >= m_iReadPayloadLength + gk_iFrameLengthNotData)//m_iFrameLengthNotData为除去payload的剩余帧长度
                {
                    m_bReCalculation = true;
                    m_qFindHeaderByteArray = m_qbReadBuffArray.mid(4, m_iReadPayloadLength+gk_iFrameLengthNotData-4);
                    int iTitleIndex = m_qFindHeaderByteArray.indexOf(gk_strHeadBytes);// 在一个标准帧中查找是否存在header
                    if(iTitleIndex >= 0)
                    {// payload包含下一帧数据// payload包含@M1*
                        qInfo() << "**********bad frame  length: " << m_iReadPayloadLength + gk_iFrameLengthNotData
                                << "but next header pos is: " << iTitleIndex+4
                                << m_qbReadBuffArray;
                        m_qbReadBuffArray = m_qbReadBuffArray.remove(0, 4 + iTitleIndex);// 删除bad帧
                        m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                        continue;
                    }
                    // 判断CRC
                    m_qCRCByteArray = m_qbReadBuffArray.mid(m_iReadPayloadLength+gk_iFrameDataPos, 2);
                    m_iReadDataCRC = m_qCRCByteArray.toHex().toInt(&m_bOk, 16);
                    m_iGetCRC = GetCRC16(m_qbReadBuffArray.data(), m_iReadPayloadLength + gk_iFrameDataPos, 0);
                    if(m_iGetCRC != m_iReadDataCRC)
                    {
                        qDebug() << "read data is " << m_qbReadBuffArray.toHex(':').toUpper();
                        qDebug() << "this read crc "<<m_qCRCByteArray.toHex(':').toUpper() << m_iReadDataCRC << "but get crc is " << m_iGetCRC;
                        qInfo() << "bad frame crc ***************************************";
                       emit sigError(FT_Comm_PacketCRCError, QString("calc crc:%1,read crc:%2").arg(m_iGetCRC).arg(m_iReadDataCRC));
                        // 去掉@M1*开始查找
                        m_qFindHeaderByteArray = m_qbReadBuffArray.mid(4, m_iReadPayloadLength+gk_iFrameLengthNotData-4);
                        // 存在下一帧数据，判断CRC中是否存在@,payload上面已经判断，不再判定
                        int iTitleIndex = m_qFindHeaderByteArray.indexOf(gk_strHeadBytes);
                        if(iTitleIndex >= 0)
                        {
                            m_qbReadBuffArray = m_qbReadBuffArray.remove(0, 4 + iTitleIndex);// 删除bad帧
                            m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                            continue;
                        }
                        QString strHeadBytes = gk_strHeadBytes;
                        int iMidPos = 4;
                        bool bFind = false;
                        while(iTitleIndex < 0 && iMidPos > 0)
                        {
                            iMidPos--;
                            strHeadBytes = gk_strHeadBytes.mid(0, iMidPos);
                            iTitleIndex = m_qFindHeaderByteArray.indexOf(strHeadBytes);
                            if(iTitleIndex >= 0)
                            {
                                m_qbReadBuffArray = m_qbReadBuffArray.remove(0, 4 + iTitleIndex);// 删除bad帧
                                m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                                bFind = true;
                                break;
                            }
                        }
                        if(!bFind)
                        {  // 不存在@M1*或者@，丢帧，crc错误，直接丢弃
                            m_qbReadBuffArray = m_qbReadBuffArray.remove(0, m_iReadPayloadLength+gk_iFrameLengthNotData);// 删除一帧
                            m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                        }
                        continue;
                        qDebug() << "----------------------read bad crc finish----------------------\r\n";
                    }
                    //////////////////////////////////////////////////////////////////////

                    QString strRec = "";
                    if(m_qbReadBuffArray[6] == static_cast<char>(0x02))
                    {// ACK应答包
                        emit sigWaitACK(m_iReadSeqNumber);
                        #ifndef ShortOutPutLog
                            strRec = "ack";
                        #endif
                    }
                    else
                    {
                        m_qCurrentReciveDataByteArray = m_qbReadBuffArray.mid(0, m_iReadPayloadLength+gk_iFrameLengthNotData);
                        if(m_qbReadBuffArray[6] != static_cast<char>(0x05))
                        {
                            // send ack,立刻返回ACK
                            SCanBusDataStruct sSCanBusDataStruct;
                            sSCanBusDataStruct.quMachineID =  m_qCurrentReciveDataByteArray[gk_iMachineIDPos];
                            sSCanBusDataStruct.quCmdID = 0x02;
                            sSCanBusDataStruct.quDestinationID = m_qCurrentReciveDataByteArray[gk_iSourceIDPos];
                            sSCanBusDataStruct.quSourceID = m_qCurrentReciveDataByteArray[gk_iDestinationIDPos];
                            sSCanBusDataStruct.quFrameSeq = m_iReadSeqNumber;
                            m_iMethodACK = GetByte2Int(m_qCurrentReciveDataByteArray.data() + gk_iMethodIDPos);
                            sSCanBusDataStruct.quMethonID = m_iMethodACK;

                            QByteArray qSendAckBackArra = GetSendData(sSCanBusDataStruct);
                            emit sigSendACKBack(qSendAckBackArra);
                        }
                        _processPacket(m_qCurrentReciveDataByteArray[gk_iCmdIDPos],
                                    m_iReadSeqNumber, m_iMethodACK);// 判定是否有重复帧包
                        //emit sigReciveMessage(m_qCurrentReciveDataByteArray);
                        #ifndef ShortOutPutLog
                            strRec = "data";
                        #endif
                    }
                    //////////////////////////////////////////////////////////////////////
                    #ifndef ShortOutPutLog
                    // qDebug() << "tcp finish" << strRec << m_iReadSeqNumber << m_qbReadBuffArray.toHex(':').toUpper();
                    #endif
                    m_qbReadBuffArray = m_qbReadBuffArray.remove(0, m_iReadPayloadLength+gk_iFrameLengthNotData);// 删除一帧
                    m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                }
                else
                {//等待足够的帧数据解析完整帧
                    qDebug() << "**********not one fream  payload length: " << m_iReadPayloadLength
                             << "readbuff size: " << m_iRedaBuffArrayLength
                             << m_qbReadBuffArray;
                    // 查找是不是丢帧，错误帧中包含头，不满足完整帧，但是中间包含头，丢弃前面部分
                    // 去掉@M1*开始查找
                    QByteArray qFindHead = m_qbReadBuffArray.mid(4, m_iRedaBuffArrayLength-4);
                    // 存在下一帧数据，判断CRC中是否存在@,payload上面已经判断，不再判定
                    int iTitleIndex = qFindHead.indexOf(gk_strHeadBytes);
                    if(iTitleIndex >= 0)
                    {
                        m_qbReadBuffArray = m_qbReadBuffArray.remove(0, 4 + iTitleIndex);// 删除bad帧
                        m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                        m_bReCalculation = true;
                        qDebug() << "**********bad fream header, next fream is: " << m_qbReadBuffArray;
                        continue;// 帧中间有header，丢弃再次尝试
                    }
                    break;// 等待后继帧数据
                }
            }
            else
            {
                #ifndef ShortOutPutLog
                qDebug() << "**********bad fream  not @M1* " << m_qbReadBuffArray << m_qbReadBuffArray.count();
                #endif
                m_bReCalculation = true;
                int iTitleIndex = m_qbReadBuffArray.indexOf("@");
                if(iTitleIndex <= 0)
                {// 稳定后，整帧丢弃
                    m_qbReadBuffArray = "";
                    m_iRedaBuffArrayLength = 0;
                }
                else
                {
                    m_qbReadBuffArray = m_qbReadBuffArray.remove(0, iTitleIndex);// 首字节不是@M1*，摒弃
                    m_iRedaBuffArrayLength = m_qbReadBuffArray.length();
                }
            }
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}
