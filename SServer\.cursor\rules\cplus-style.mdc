---
description: 
globs: 
alwaysApply: true
---
---
name: cpp-wf-style.mdc                          # 建议保存到 .cursor/rules/
description: >
  万孚生物 C++17 + Qt5/6 代码规范精简版（按 Q/W-J003.0001-2022）
  —— 用于 Cursor AI 自动携带的提示集  
globs:
  - "**/*.cpp"
  - "**/*.cc"
  - "**/*.cxx"
  - "**/*.h"
  - "**/*.hpp"
alwaysApply: false                              # 仅在编辑 C/C++ 文件时注入
---

# 0. 通用基线
- C++17 标准编译；Qt 5.12 LTS
- 全项目统一 `-Wall -Wextra -Werror -fPIC`；跨平台保持编译选项一致。
- **行宽 ≤ 80**，缩进 **4 空格**，严禁制表符。  
- UTF-8 源文件；避免非 ASCII 字符，必须使用时写作 UTF-8 字面量或 `\uXXXX`。

# 1. 头文件
- 必须用 **路径全称**+双下划线 `#define` 防护：`FOO_SRC_BAR_BAZ__H`.
- 与 `.cpp` 一一对应；仅单元测试或 `main()` 可例外无头文件。
- `#include` 顺序  
  1) 相关头（同名 .h） 2) C 系统 3) C++ 系统 4) 其他库 5) 本项目  
  每组内再按字母序；禁止使用 `.`、`..` 目录跳级。
- 避免前置声明；除非能显著减编译时且不隐藏依赖。

# 2. 作用域与命名空间
- `.cpp` 内部私有符号 ➜ 匿名命名空间或 `static`。
- 仅使用 **具名命名空间** (基于模块路径)；禁止 `using namespace` 指示。
- 不得在 `std` 命名空间增添任何实体；禁止 **内联命名空间**。

# 3. 类设计
- **结构体 vs 类**：仅纯数据 POD 使用 `struct`；其余均 `class`。
- 默认禁拷贝/移动；确需拷贝则同时提供五（或三）法则实现。
- 构造函数 **不得** 调用虚函数或隐式失败；复杂初始化用 `Init()` / 工厂。
- 继承：首选组合。若使用继承 ➜ `public` 继承；多继承仅 1 个实现父类，其余 `Interface` 纯接口。
- 数据成员全部 `private`，必要接口 `public`，内部扩展极少用 `protected`.

# 4. 命名约定（匈牙利-混合）
| 实体                 | 规则 / 前缀示例 | 说明 |
|----------------------|-----------------|------|
| **文件**             | `my_module.cpp` | 小写+下划线 |
| **类/枚举/typedef**  | `CMyClass`, `SData`, `EStatus` | PascalCase，类前缀 **C**，结构体 **S**，枚举 **E** |
| **函数/公共方法**    | `DoProcessData()` | PascalCase，动词+名词 |
| **私有方法**         | `_LoadConfig()`   | 下划线前缀 |
| **变量**             | 类型缩写 + 名词   | `iCount`, `strName`, `pWidget` |
| **成员变量**         | `m_` + 类型缩写  | `m_iIndex`, `m_pDevice` |
| **静态成员**         | `sm_`            | `sm_mapCache` |
| **全局变量**         | `g_` / `gs_`     | 避免使用；必要时 `g_strAppDir` |
| **常量**             | `k` + Pascal     | `kMaxRetry` |
| **宏 / 常量枚举值**  | `UPPER_SNAKE`    | `MAX_BUFFER_SIZE` |

> 详细类型缩写：`i` int │ `d` double │ `p` pointer │ `vec` vector … 参见企业标准附表。

# 5. 函数规范
- **参数顺序**：先输入值/`const&`，后输出裸指针。
- 所有引用参数必须 `const T&`；允许 `const T*` 但须说明原因。
- 禁缺省参数于虚函数；普通函数首选重载，缺省仅在可读性显著提升时使用。
- 每个函数 ≤ 40 行为宜；长函数应拆分内部逻辑。
- 返回值后置语法仅在 Lambda 或模板推导难以阅读时使用。

# 6. Qt 特化
- 派生 `QObject` 必加 `Q_OBJECT` 与 `Q_DISABLE_COPY`.
- 连接统一 **新连接语法**；跨线程需 `Qt::QueuedConnection`.
- UI 文本 `tr()`；字符串常量 `QStringLiteral`.
- 禁止在 UI 线程执行阻塞 I/O；使用 worker `QObject` + `moveToThread()`.

# 7. 内存与性能
- 静态/全局对象 **仅 POD**；禁止非 POD 静态（除 `constexpr`）。
- 优先 `std::unique_ptr` / `QScopedPointer`；观察者用 `QPointer`.
- 迭代器、自定义模板类型使用前置 `++i`.
- 循环与算法：能用 `<algorithm>` 就别手写；大数据结构用 `reserve()`.

# 8. 错误处理 & 日志
- 库层返回 `expected<T,E>` / `std::optional`; APP 层可抛 `std::runtime_error`.
- 禁止在析构函数抛异常；Qt 槽内必须捕获所有异常并记录 `qCritical`.
- 日志：统一 `QLoggingCategory` + `spdlog` sink；分类路径 `wf.module.sub`.

# 9. 代码格式细节
- **括号**：`if (...) {` 同行开；`else` 另起一行。
- 所有语句块必须写 `{}`（单行亦然）。
- `switch` 必含 `default`; 不可省 `break` 除非 `[[fallthrough]]`.
- 指针 `*`/引用 `&` 紧贴变量名：`int* pData; const Obj& ref`.
- `return` 表达式不加多余圆括号。

# 10. 注释 & 文档
- 顶部文件头含版权、作者、功能、修订历史；遵循 Doxygen。
- 类、复杂函数必须块注释；变量必要时右侧简注；实现细节写行内注释。
- TODO/FIXME 规格：`// TODO(username YYYY-MM-DD): 描述`.

# 11. 禁止事项（❌）
- 全局可变对象、静态非 POD、裸 `new/delete`、`using namespace`.
- C 风格强转、隐式类型转换（未显式 `explicit`）。
- 宏滥用：除 include-guard，其余尽量用 `constexpr`, `inline`.
- 重载 `&&`, `||`, `,` 或用户字面量；在头文件使用匿名命名空间。
- 静态变量依赖初始化顺序；退出时访问已析构对象。


