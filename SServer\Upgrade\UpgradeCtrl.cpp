#include"UpgradeCtrl.h"
#include <QDebug>
#include "control/coperationunit.h"
#include "cglobalconfig.h"
#include "publicfunction.h"
#include <QCryptographicHash>
#include<QDataStream>
#include<QCoreApplication>
#include<QFileInfo>
#include<QJsonDocument>
#include<QJsonObject>

CUpgradeCtrl::CUpgradeCtrl(QObject *parent) : QObject(parent)
{
    StopAllUpgradeTimer();
    m_bAutoupgrade =false;
    m_bStopUpgrade =false;
    m_queUpgradeBoard.clear();
    m_ListBoardVersion.clear();
    connect(this, &CUpgradeCtrl::sigKillUpgradeTimer, this, &CUpgradeCtrl::slotKillUpgradeTimer);
}
void CUpgradeCtrl::ResetUpgradeStatus()//清空自动的列表及版本信息
{
    m_bAutoupgrade =false;
    m_bStopUpgrade =false;
    m_queUpgradeBoard.clear();
    m_ListBoardVersion.clear();
}

void CUpgradeCtrl::slotKillUpgradeTimer(int _ID)
{
    qDebug()<<"Timer size = "<<m_timer.size();
    if(m_timer.contains(_ID))
    {
        QTimer* timer = m_timer.value(_ID);
        timer->stop();
        timer->deleteLater();
        m_timer.remove(_ID);
    }
}


void CUpgradeCtrl::StopAllUpgradeTimer()
{
    QMapIterator<int,QTimer*> it(m_timer);
    while(it.hasNext())
    {
        it.next();
        QTimer* timer =it.value();
        timer->stop();
        timer->deleteLater();
    }
    m_timer.clear();
}

CUpgradeCtrl &CUpgradeCtrl::getInstance()
{
    static CUpgradeCtrl cCUpgradeCtrl;
    return cCUpgradeCtrl;
}

void CUpgradeCtrl::SetStopUpgradeFlag(bool bFlag)
{
    m_bStopUpgrade = bFlag;
}

void CUpgradeCtrl::UpgradeReq(QString qPayloadString,int iIsExtractPath)
{
    SetStopUpgradeFlag(false);
    quint16 uiDest = qPayloadString.toUInt();
    switch (uiDest)
    {
    case Machine_Middle_Host:
    {// 中位机升级
        break;
    }
    case Machine_Function_manager_Ctrl:
    {
        QString strBoardFunctionFilePath = CGlobalConfig::getInstance().getUDiskBoardFunctionUpdatePath(iIsExtractPath);
        auto[iFileSize, qBoardFunctionUpgradePackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strBoardFunctionFilePath, gk_iPackageSize_FunctionManager);
                m_qBoardFunctionUpgradePackagesList = qBoardFunctionUpgradePackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_FunctionManager)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, Machine_Function_manager_Ctrl);
        m_UpdateMsgStruct_BoardFunction.m_PackageReq.clear();
        m_UpdateMsgStruct_BoardFunction.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    case Machine_Motor_1:
    case Machine_Motor_2:
    case Machine_Motor_3:
    case Machine_Motor_4:
    {
        QString strBoardMotorFilePath = CGlobalConfig::getInstance().getUDiskBoardMotorUpdatePath(iIsExtractPath);
        auto[iFileSize, qBoardMotorUpgradePackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strBoardMotorFilePath, gk_iPackageSize_Motor);
                m_qBoardMotorUpgradePackagesList = qBoardMotorUpgradePackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_Motor)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, uiDest);
        m_UpdateMsgStruct_BoardMotor.m_PackageReq.clear();
        m_UpdateMsgStruct_BoardMotor.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }

    case Machine_PCR_MainCtrl:
    {
        QString strPCRMainFilePath = CGlobalConfig::getInstance().getUDiskPCRFileUpdatePath(iIsExtractPath);
        auto[iFileSize, qPCRMainUpgradePackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strPCRMainFilePath, gk_iPackageSize_PCRMain);
                m_qPCRMainUpgradePackagesList = qPCRMainUpgradePackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_PCRMain)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, Machine_PCR_MainCtrl);
        m_UpdateMsgStruct_PCRMain.m_PackageReq.clear();
        m_UpdateMsgStruct_PCRMain.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    case  Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    {
        QString strTECFilePath = CGlobalConfig::getInstance().getUDiskTECUpgradePath(iIsExtractPath);
        auto[iFileSize, qTECPackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strTECFilePath, gk_iPackageSize_TEC);
                m_qTECPackagesUpgradeList = qTECPackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_TEC)};
        qDebug()<<"iFileSize="<<iFileSize<<",packseize="<<gk_iPackageSize_TEC;
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, uiDest);
        m_UpdateMsgStruct_TEC.m_PackageReq.clear();
        m_UpdateMsgStruct_TEC.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    case Machine_RFID_1:
    case Machine_RFID_2:
    case Machine_RFID_3:
    {
        QString strRFIDFilePath = CGlobalConfig::getInstance().getUDiskRFIDUpgradePath(iIsExtractPath);
        auto[iFileSize, qstrRFIDFilePathPackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strRFIDFilePath, gk_iPackageSize_RFID);
                m_qRFIDUpgradePackagesList = qstrRFIDFilePathPackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_RFID)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, uiDest);
        m_UpdateMsgStruct_RFID.m_PackageReq.clear();
        m_UpdateMsgStruct_RFID.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    case Machine_Fluorence:
    {
        QString strFLUFilePath = CGlobalConfig::getInstance().getUDiskFLUpgradePath(iIsExtractPath);
        auto[iFileSize, qstrFLFilePathPackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strFLUFilePath, gk_iPackageSize_Fluorence);
                m_qFluorenceUpgradePackagesList = qstrFLFilePathPackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_Fluorence)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, Machine_Fluorence);
        m_UpdateMsgStruct_Fluorence.m_PackageReq.clear();
        m_UpdateMsgStruct_Fluorence.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    case Machine_Power_Ctrl:
    {
        QString strPowerFilePath = CGlobalConfig::getInstance().getUDiskBoardPowerUpdatePath(iIsExtractPath);
        auto[iFileSize, qstrPowerFilePathPackagesList, qMD5Vlue] = splitUpgradeBinaryFile(strPowerFilePath, gk_iPackageSize_PowerCtrl);
                m_qBoardPowerUpgradePackagesList = qstrPowerFilePathPackagesList;
                QStringList strSendList = QStringList{QString::number(uiDest), QString::number(iFileSize),
                    QString::number(gk_iPackageSize_PowerCtrl)};
        COperationUnit::getInstance().sendDataList(Method_upgrade_req, strSendList, Machine_Power_Ctrl);
        m_UpdateMsgStruct_BoardPower.m_PackageReq.clear();
        m_UpdateMsgStruct_BoardPower.m_lastReqDataTime=QDateTime::currentDateTime();
        connect(this,SIGNAL(sigUpgradeTimeOut(int)),this,SLOT(slotUpgradeTimeOut(int)));
        emit sigUpgradeTimeOut(int(uiDest));
        break;
    }
    default:
        break;
    }
}


void CUpgradeCtrl::UpgradData(quint8 iSourceID,QString qPayloadString)
{
    if(m_bStopUpgrade ==true)
    {
        qDebug()<<"停止升级，不再回复数据包";
        emit sigKillUpgradeTimer(iSourceID);
        ResetUpgradeStatus();
        return;
    }
    quint16 uiPackID = qPayloadString.toUInt();
    switch (iSourceID)
    {
    case Machine_Middle_Host:
    {// 中位机升级
        break;
    }
    case Machine_Function_manager_Ctrl:
    {
        if(uiPackID >= 0 && uiPackID < m_qBoardFunctionUpgradePackagesList.length())
        {
            int iToalPack= m_qBoardFunctionUpgradePackagesList.length();
            QString strPayload = QString::number(Machine_Function_manager_Ctrl) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qBoardFunctionUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, Machine_Function_manager_Ctrl);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();
            m_UpdateMsgStruct_BoardFunction.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_BoardFunction.m_lastReqDataTime =QDateTime::currentDateTime();
        }
        qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<",uiPackID="<<uiPackID;
        break;
    }
    case Machine_Motor_1:
    case Machine_Motor_2:
    case Machine_Motor_3:
    case Machine_Motor_4:
    {
        if(uiPackID >= 0 && uiPackID < m_qBoardMotorUpgradePackagesList.length())
        {
            int iToalPack= m_qBoardMotorUpgradePackagesList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qBoardMotorUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();
            m_UpdateMsgStruct_BoardMotor.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_BoardMotor.m_lastReqDataTime =QDateTime::currentDateTime();
        }
        else
        {
            qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<"error ,Out idx";
        }
        qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<",uiPackID="<<uiPackID;
        break;
    }
    case Machine_PCR_MainCtrl:
    {
        if(uiPackID >= 0 && uiPackID < m_qPCRMainUpgradePackagesList.length())
        {
            int iToalPack= m_qPCRMainUpgradePackagesList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qPCRMainUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();//<<"Msg ="<<qSendData;
            m_UpdateMsgStruct_PCRMain.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_PCRMain.m_lastReqDataTime =QDateTime::currentDateTime();

        }

        break;
    }
    case Machine_PCR_Ctrl:
    case Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    {
        if(uiPackID >= 0 && uiPackID < m_qTECPackagesUpgradeList.length())
        {
            int iToalPack= m_qTECPackagesUpgradeList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qTECPackagesUpgradeList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();//<<"Msg ="<<qSendData;
            m_UpdateMsgStruct_TEC.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_TEC.m_lastReqDataTime =QDateTime::currentDateTime();
        }

        break;
    }
    case Machine_RFID_1:
    case Machine_RFID_2:
    case Machine_RFID_3:
    {

        if(uiPackID >= 0 && uiPackID < m_qRFIDUpgradePackagesList.length())
        {
            int iToalPack= m_qRFIDUpgradePackagesList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qRFIDUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();//<"Msg ="<<qSendData;
            m_UpdateMsgStruct_RFID.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_RFID.m_lastReqDataTime =QDateTime::currentDateTime();
        }
        else
        {
            qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<"error ,Out idx";
        }
        break;
    }
    case Machine_Fluorence:
    {
        if(uiPackID >= 0 && uiPackID < m_qFluorenceUpgradePackagesList.length())
        {
            int iToalPack= m_qFluorenceUpgradePackagesList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qFluorenceUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();//<<"Msg ="<<qSendData;
            m_UpdateMsgStruct_Fluorence.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_Fluorence.m_lastReqDataTime =QDateTime::currentDateTime();
        }
        else
        {
            qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<"error ,Out idx";
        }
        break;
    }
    case Machine_Power_Ctrl:
    {
        if(uiPackID >= 0 && uiPackID < m_qBoardPowerUpgradePackagesList.length())
        {
            int iToalPack= m_qBoardPowerUpgradePackagesList.length();
            QString strPayload = QString::number(iSourceID) + ","+ QString::number(uiPackID)+","+QString::number(iToalPack) ;
            QByteArray qSendData = m_qBoardPowerUpgradePackagesList.at(uiPackID);
            _packageMsg(uiPackID,qSendData);
            _addMD5(qSendData);
            COperationUnit::getInstance().sendQByteData(Method_upgrade_data, qSendData, iSourceID);
            COperationUnit::getInstance().sendStringData(Method_upgrade_progressBar, strPayload, Machine_UpperHost);
            qDebug()<<__FUNCTION__<<"@@@@dataSize="<<qSendData.size();//<<"Msg ="<<qSendData;
            m_UpdateMsgStruct_BoardPower.m_PackageReq.insert(uiPackID);
            m_UpdateMsgStruct_BoardPower.m_lastReqDataTime =QDateTime::currentDateTime();
        }
        else
        {
            qDebug()<<__FUNCTION__<<"m_iSourceID="<<iSourceID<<"error ,Out idx";
        }
        break;
    }
    default:
        break;
    }
}

void CUpgradeCtrl::_packageMsg(quint16 qID,QByteArray &RsMsg)
{
    QByteArray arr;
    QDataStream stream(&arr, QIODevice::WriteOnly);
    stream<< qID;
    RsMsg.insert(0,arr);
}

void CUpgradeCtrl::_addMD5(QByteArray &RsMsg)
{
    QCryptographicHash md5Hash(QCryptographicHash::Md5);
    md5Hash.addData(RsMsg);
    RsMsg= RsMsg + md5Hash.result();
}

void CUpgradeCtrl::_checkUpgradeStatus(EnumMachineID _ID,QDateTime lastReqDataTime,QList<QByteArray> PackagesUpgradeList,QSet<int> UpgradePackageReq)
{
    if(lastReqDataTime.secsTo(QDateTime::currentDateTime())<gk_iUpdateTimeOut)
    {

    }
    else
    {
        for(int i=0;i<PackagesUpgradeList.size();i++)
        {
            if(!UpgradePackageReq.contains(i))
            {
                //存在没有处理 超时
                qDebug()<<"error,Timeout ,ID="<<_ID;
                slotUpgradeEndRs(EnumMachineID(_ID),1);  //1FAIL
                break;
            }
        }
        qDebug()<<"停止升级定时器，id="<<_ID;
        emit sigKillUpgradeTimer(_ID);
    }
}

void CUpgradeCtrl::slotUpgradeTimeOut(int _ID)
{
    qDebug()<<"enter slotUpgradeTimeOut";
    //    QTimer* timer = new QTimer(this);
    //    connect(timer, &QTimer::timeout,this,[=](){
    //        slotCheckUpgradeStatus(EnumMachineID(_ID));
    //    });
    //    timer->start(gk_iUpdateTimeOut*1000);
    if(m_timer.contains(_ID))
    {
        QTimer *oldTimer = m_timer.value(_ID);
        oldTimer->stop();
        oldTimer->deleteLater();
        m_timer.remove(_ID);
    }
    QTimer* timer = new QTimer(this);
    connect(timer, &QTimer::timeout,this,[=](){
        slotCheckUpgradeStatus(EnumMachineID(_ID));
    });
    timer->start(gk_iUpdateTimeOut*1000);
    m_timer.insert(_ID,timer);
}


void CUpgradeCtrl::slotCheckUpgradeStatus(EnumMachineID _ID)
{
    qDebug()<<"enter slotCheckUpgradeStatus,_ID="<<_ID;
    if(_ID == Machine_Function_manager_Ctrl)
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_BoardFunction.m_lastReqDataTime,
                            m_qBoardFunctionUpgradePackagesList,
                            m_UpdateMsgStruct_BoardFunction.m_PackageReq);
    }
    if(_ID == Machine_Motor_1 || _ID == Machine_Motor_2 ||_ID == Machine_Motor_3 ||_ID == Machine_Motor_4)
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_BoardMotor.m_lastReqDataTime,
                            m_qBoardMotorUpgradePackagesList,
                            m_UpdateMsgStruct_BoardMotor.m_PackageReq);
    }
    if(_ID == Machine_PCR_MainCtrl )
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_PCRMain.m_lastReqDataTime,
                            m_qPCRMainUpgradePackagesList,
                            m_UpdateMsgStruct_PCRMain.m_PackageReq);
    }
    if(_ID == Machine_PCR_Ctrl_1 || _ID == Machine_PCR_Ctrl_2 ||
            _ID == Machine_PCR_Ctrl_3 ||_ID == Machine_PCR_Ctrl_4)
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_TEC.m_lastReqDataTime,
                            m_qTECPackagesUpgradeList,
                            m_UpdateMsgStruct_TEC.m_PackageReq);
    }
    if(_ID == Machine_Fluorence )
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_Fluorence.m_lastReqDataTime,
                            m_qFluorenceUpgradePackagesList,
                            m_UpdateMsgStruct_Fluorence.m_PackageReq);
    }
    if(_ID == Machine_RFID_1 || _ID == Machine_RFID_2 || _ID == Machine_RFID_3 )
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_RFID.m_lastReqDataTime,
                            m_qRFIDUpgradePackagesList,
                            m_UpdateMsgStruct_RFID.m_PackageReq);
    }
    if(_ID ==  Machine_Power_Ctrl)
    {
        _checkUpgradeStatus(_ID,m_UpdateMsgStruct_BoardPower.m_lastReqDataTime,
                            m_qBoardPowerUpgradePackagesList,
                            m_UpdateMsgStruct_BoardPower.m_PackageReq);
    }
}

int CUpgradeCtrl::ReadJsonFile(EnumMachineID _ID,QString &strVersion)
{
    strVersion="";
    QString strJsonPath = CGlobalConfig::getInstance().getUpdateBoardVersionCheckPath();
    QFile JsonFile(strJsonPath);
    if(!JsonFile.open(QIODevice::ReadOnly))
    {
        qDebug()<<"Json fail";
        return 1;
    }
    QByteArray jsonData =JsonFile.readAll();
    JsonFile.close();
    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData,&parseError);
    if(parseError.error!=QJsonParseError::NoError)
    {
        qDebug()<<"json Parse Err";
        return 1;
    }
    if(jsonDoc.isNull() || !jsonDoc.isObject())
    {
        qDebug()<<"Invalid JSON struct";
        return 1;
    }
    QJsonObject rootObj = jsonDoc.object();
    QString strName = "";
    switch (_ID)
    {
    case Machine_Function_manager_Ctrl:
    {
        strName ="Board_Function";
        break;
    }
    case Machine_Motor_1:
    case Machine_Motor_2:
    case Machine_Motor_3:
    case Machine_Motor_4:
    {
        strName ="Board_Motor";
        break;
    }
    case Machine_PCR_MainCtrl:
    {
        strName ="Board_PCRMain";
        break;
    }
    case  Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    {
        strName ="Board_TEC";
        break;
    }
    case Machine_RFID_1:
    case Machine_RFID_2:
    case Machine_RFID_3:
    {
        strName ="Board_RFID";
        break;
    }
    case Machine_Fluorence:
    {
        strName ="Board_Fluorence";
        break;
    }
    case Machine_Power_Ctrl:
    {
        strName ="Board_Power";
        break;
    }
    default:
    {
        return 1;
    }
    }
    strVersion = rootObj[strName].toString();
    return  0;
}

int CUpgradeCtrl::ReadUpgradeBinaryFileAndVerSion(EnumMachineID _ID,QString strfilePath,int iPackSize,QString &strVersion,int &iPackNum)
{
    if(iPackSize<0){return 1;}

    QFile file(strfilePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qWarning() << "Could not open file for reading:" << file.errorString();
        return 1;
    }
    qint64 fileSize = file.size();
    iPackNum=(fileSize+iPackSize-1)/iPackSize;  //向上取整
    //读取版本号 用于升级后的比对;
    int iStatus = ReadJsonFile(_ID,strVersion);
    qDebug()<<"ID="<<_ID<<",strVersion="<<strVersion<<",iStatus="<<iStatus;
    return iStatus;
}

int CUpgradeCtrl::CalUpgradeFile(EnumMachineID _ID,int iIsExtractPath,QString &strVersion,int &iPackNum)
{
    int iStatus =1;
    iPackNum =0;
    strVersion="";
    switch (_ID)
    {
    case Machine_Function_manager_Ctrl:
    {
        QString strBoardFunctionFilePath = CGlobalConfig::getInstance().getUDiskBoardFunctionUpdatePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strBoardFunctionFilePath,gk_iPackageSize_FunctionManager,strVersion,iPackNum);
        break;
    }
    case Machine_Motor_1:
    case Machine_Motor_2:
    case Machine_Motor_3:
    case Machine_Motor_4:
    {
        QString strBoardMotorFilePath = CGlobalConfig::getInstance().getUDiskBoardMotorUpdatePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strBoardMotorFilePath,gk_iPackageSize_Motor,strVersion,iPackNum);
        break;
    }
    case Machine_PCR_MainCtrl:
    {
        QString strPCRMainFilePath = CGlobalConfig::getInstance().getUDiskPCRFileUpdatePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strPCRMainFilePath,gk_iPackageSize_PCRMain,strVersion,iPackNum);
        break;
    }
    case  Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    {
        QString strTECFilePath = CGlobalConfig::getInstance().getUDiskTECUpgradePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strTECFilePath,gk_iPackageSize_TEC,strVersion,iPackNum);
        qDebug()<<"Machine_TEC Status ="<<iStatus;
        break;
    }
    case Machine_RFID_1:
    case Machine_RFID_2:
    case Machine_RFID_3:
    {
        QString strRFIDFilePath = CGlobalConfig::getInstance().getUDiskRFIDUpgradePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strRFIDFilePath,gk_iPackageSize_RFID,strVersion,iPackNum);
        break;
    }
    case Machine_Fluorence:
    {
        QString strFLUFilePath = CGlobalConfig::getInstance().getUDiskFLUpgradePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strFLUFilePath,gk_iPackageSize_Fluorence,strVersion,iPackNum);
        break;
    }
    case Machine_Power_Ctrl:
    {
        QString strPowerFilePath = CGlobalConfig::getInstance().getUDiskBoardPowerUpdatePath(iIsExtractPath);
        iStatus =  ReadUpgradeBinaryFileAndVerSion(_ID,strPowerFilePath,gk_iPackageSize_PowerCtrl,strVersion,iPackNum);
        break;
    }
    default:
    {
        break;
    }
    }
    return iStatus;
}

void CUpgradeCtrl::MakeUpgradeBoardInfo(int iIsExtractPath)
{
    //读入有多少个符合的升级文件
    //遍历U盘可能存在的Bin
    int iStatus;
    int iPackTemp,iSumPackNum=0;
    QString strVersion;
    UpdateBoardCheck tempStruct;
    tempStruct.iCheck =-1;
    tempStruct.iRepeatTime=0;
    iStatus = CalUpgradeFile(Machine_Function_manager_Ctrl,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct .ID = Machine_Function_manager_Ctrl;
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp;
        m_queUpgradeBoard.enqueue(Machine_Function_manager_Ctrl);
        m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_Motor_1,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp*4; //4个电机板子
        m_queUpgradeBoard.enqueue(Machine_Motor_1); tempStruct.ID =Machine_Motor_1; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_Motor_2); tempStruct.ID =Machine_Motor_2;m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_Motor_3); tempStruct.ID =Machine_Motor_3;m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_Motor_4); tempStruct.ID =Machine_Motor_4;m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_PCR_MainCtrl,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct .ID = Machine_PCR_MainCtrl;
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp;
        m_queUpgradeBoard.enqueue(Machine_PCR_MainCtrl);
        m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_PCR_Ctrl_1,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp*4;  //4个TEC
        m_queUpgradeBoard.enqueue(Machine_PCR_Ctrl_1);tempStruct.ID =Machine_PCR_Ctrl_1; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_PCR_Ctrl_2);tempStruct.ID =Machine_PCR_Ctrl_2; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_PCR_Ctrl_3);tempStruct.ID =Machine_PCR_Ctrl_3; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_PCR_Ctrl_4);tempStruct.ID =Machine_PCR_Ctrl_4; m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_RFID_1,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp*3;  //3个RFID
        m_queUpgradeBoard.enqueue(Machine_RFID_1);tempStruct.ID =Machine_RFID_1; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_RFID_2);tempStruct.ID =Machine_RFID_2; m_ListBoardVersion.append(tempStruct);
        m_queUpgradeBoard.enqueue(Machine_RFID_3);tempStruct.ID =Machine_RFID_3; m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_Fluorence,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct .ID = Machine_Fluorence;
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp;
        m_queUpgradeBoard.enqueue(Machine_Fluorence);
        m_ListBoardVersion.append(tempStruct);
    }
    iStatus = CalUpgradeFile(Machine_Power_Ctrl,iIsExtractPath,strVersion,iPackTemp);
    if(iStatus==0)
    {
        tempStruct .ID = Machine_Power_Ctrl;
        tempStruct.strVersion = strVersion;
        iSumPackNum=iSumPackNum+iPackTemp;
        m_queUpgradeBoard.enqueue(Machine_Power_Ctrl);
        m_ListBoardVersion.append(tempStruct);
    }
    for(auto it = m_ListBoardVersion.begin();it!=m_ListBoardVersion.end();++it)
    {
        qDebug()<<"@@@Id="<<it->ID<<",version = "<<it->strVersion;
    }
}

void CUpgradeCtrl::slotExtractAutoupgradeZipFile()
{
    QString strZipPath =  CGlobalConfig::getInstance().getUDiskAutoUpgradeZipPath();
    qDebug()<<"strZipPath="<<strZipPath;
    QFileInfo fileInfo(strZipPath);
    if(!fileInfo.exists())
    {
        qDebug()<<"不存在升级目录压缩包";
        QString strMsg =QString::number(-1)+","+QString::number(1);
        COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
        return;
    }

    QString strOutputDir = QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir;
    qDebug()<<"strOutputDir="<<strOutputDir;
    //先删掉里面所有的文件，防止升级中断的时候里面残留旧文件
    QDir dir(strOutputDir);
    if(dir.exists())
    {
        if(!dir.removeRecursively())
        {
            qDebug()<<"删除目录失败";
            return;
        }
    }
    if(!QDir().mkpath(strOutputDir))
    {
        qDebug()<<"创建目录失败";
        return;
    }
    m_extractProcess =new QProcess(this);
    connect(m_extractProcess,QOverload<int,QProcess::ExitStatus>::of(&QProcess::finished),
            this,&CUpgradeCtrl::slotExtractFinished);
    m_extractProcess->setProgram("unzip");
    m_extractProcess->setArguments({"-o",strZipPath,"-d",strOutputDir});
    m_extractProcess->start();
    if(!m_extractProcess->waitForStarted())
    {
        qDebug()<<"进程启动失败:"<<m_extractProcess->errorString();
    }
}

void CUpgradeCtrl::slotExtractFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    qDebug()<<"Receive slotExtractFinished @@@@@@@@@@@@@@@@@";
    bool success =(exitCode ==QProcess::NormalExit && exitStatus==0);
    QString errorMsg;

    if(!success)
    {
        errorMsg =m_extractProcess->errorString();
        if(errorMsg.isEmpty())
        {
            errorMsg =QString("解压失败，错误码:%1").arg(m_extractProcess->exitCode());
        }
    }
    if(success)
    {
        qDebug()<<"解压成功,开始自动升级";
        AutoUpgrade();
    }
    m_extractProcess->deleteLater();
    m_extractProcess=nullptr;
}

void CUpgradeCtrl::AutoUpgrade()
{
    m_bAutoupgrade = true;
    int iIsExtractPath =1;
    MakeUpgradeBoardInfo(iIsExtractPath);
    //告诉上位机总共有多少个是需要升级的
    QQueue<EnumMachineID>::iterator it;
    EnumMachineID tempMachineID;
    for(it =m_queUpgradeBoard.begin();it!=m_queUpgradeBoard.end();++it)
    {
        tempMachineID = *it;
        qDebug()<<"Send Method_upgrade_req Msg to Machine_UpperHost,ID="<<tempMachineID;
        COperationUnit::getInstance().sendStringResult(Method_upgrade_req, QString::number(tempMachineID), Machine_UpperHost, 0);
    }
    qDebug()<<"Upgrade LIst size = "<<m_queUpgradeBoard.size();
    //先升级列表的第一个
    if(m_queUpgradeBoard.size()!=0)
    {
        EnumMachineID curUpgradeMachineId=m_queUpgradeBoard.dequeue();
        CUpgradeCtrl::getInstance().UpgradeReq(QString::number(curUpgradeMachineId),iIsExtractPath);
    }
    else  //没有下位机升级文件，升级上中位机
    {
        qDebug()<<"没有下位机升级文件，升级上中位机";
        DoShFile();
    }
}


void CUpgradeCtrl::slotUpgradeEndRs(EnumMachineID ID,int iRs)
{
    QString strMsg =QString::number(ID)+","+QString::number(iRs);
    COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
    qDebug()<<"send UpgradeEnd="<<strMsg;
    if(iRs==0) //升级成功，关掉定时器
    {
        slotKillUpgradeTimer(ID);
    }

    if(iRs!=0 && m_bAutoupgrade==true)   //出现升级失败的情况，后续不再升级
    {
        qDebug()<<"升级失败，板卡ID="<<ID;
        for(int i=0;i<m_ListBoardVersion.size();i++)
        {
            if( m_ListBoardVersion[i].ID == ID)
            {
                if(m_ListBoardVersion[i].iRepeatTime ==0)////重新升级当前
                {
                    m_ListBoardVersion[i].iRepeatTime++;
                    qDebug()<<"升级失败，板卡ID="<<ID<<",重新升级";  //上面已经关掉定时器
                    CUpgradeCtrl::getInstance().UpgradeReq(QString::number(ID),1);
                    return;
                }
                else
                {
                    ResetUpgradeStatus();
                    QString strMsg =QString::number(-1)+","+QString::number(1);
                    COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
                    return;
                }
            }
        }

        ResetUpgradeStatus();
        QString strMsg =QString::number(-1)+","+QString::number(1);
        COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
        return;
    }

    //升级完成校准版本号是否正确
    if(m_bAutoupgrade==true)
    {
        for(auto it = m_ListBoardVersion.begin();it!=m_ListBoardVersion.end();++it)
        {
            qDebug()<<"ID="<<it->ID<<",iCheck="<<it->iCheck<<"current id = "<<ID;
            if(it->ID==ID && it->iCheck ==-1)
            {
                COperationUnit::getInstance().sendStringData(Method_board_info, "",  it->ID);
                it->iCheck =0;
                break;
            }
        }
    }

}

void CUpgradeCtrl::DoShFile()
{
    QString strShPath=  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+"Upgrade.sh";
    strShPath ="sh "+strShPath;
    qDebug()<<"strShPath="<<strShPath;
    system(strShPath.toStdString().c_str());
//    // system("sh Upgrade.sh");

  //   slotExtractAutoupgradeZipFile(); //循环

}

void CUpgradeCtrl::slotBoardVersionInfo(EnumMachineID ID,QString strVersion)
{
    //这个信号有可能从升级页面触发而非一键升级页面
    if(m_bAutoupgrade==false)
    {
        return;
    }
    int iStatus =0;
    for(auto it = m_ListBoardVersion.begin();it!=m_ListBoardVersion.end();++it)
    {
        if(it->iCheck==0 && it->ID==ID)
        {
            if(it->strVersion==strVersion  )
            {
                it->iCheck =1;
                iStatus =1;
                break;
            }
            else
            {
                qDebug()<<"版本号不对,ID ="<<ID<<",strVersion="<<strVersion<<",it->version="<<it->strVersion;
                QString strMsg =QString::number(-1)+","+QString::number(1);  //-1 代表一键升级的校验
                COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
                ResetUpgradeStatus();
                return; //直接return 不要继续校验了
            }
        }
    }
    //由于是一步步升级的,没有并行，所有不用加锁
    if(iStatus==1)
    {
        if(m_queUpgradeBoard.size()!=0)
        {
            EnumMachineID curUpgradeMachineId=m_queUpgradeBoard.dequeue();
            CUpgradeCtrl::getInstance().UpgradeReq(QString::number(curUpgradeMachineId),1);
        }
        else
        {
            //全部校验完成
            QString strMsg =QString::number(-1)+","+QString::number(0);  //-1 代表一键升级的校验
            //lxj 老化
            COperationUnit::getInstance().sendStringData(Method_upgrade_end, strMsg, Machine_UpperHost);
            ResetUpgradeStatus();
            qDebug()<<"All Check Finish";
            //执行sh文件，升级上中位机
            DoShFile();
        }
    }

}

