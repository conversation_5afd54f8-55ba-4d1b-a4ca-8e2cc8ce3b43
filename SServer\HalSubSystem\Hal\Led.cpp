#include<qdebug.h>
#include<QTime>
#include"Led.h"
#include "control/coperationunit.h"
#include "publicconfig.h"

Led::Led(QObject *parent) : QObject(parent)
{

}

void Led::Trigger(EnumTriggerState state, EnumMoudle moudle,EnumMachineID machineID)
{
    QString strInputParam = QString("%1,%2").arg(moudle).arg(state);
    COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_CloseValve,strInputParam,machineID);
    m_ledMap[moudle] = state;
    qDebug()<<"Led::Trigger"<<state<<moudle<<strInputParam;    
}

Led::EnumTriggerState Led::GetMoudleStatus(EnumMoudle moudle)
{
    EnumTriggerState state = OFF;
    if (m_ledMap.contains(moudle))
    {
        state = m_ledMap[moudle];
    }
    qDebug()<<"Led::GetMoudleStatus"<<state<<moudle;    
    return state;
}
