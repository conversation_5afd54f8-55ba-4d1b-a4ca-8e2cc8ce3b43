/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-12-18
  * Description: can协议解析线程，
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/

#ifndef CCANANALYZETHREAD_H
#define CCANANALYZETHREAD_H
#include <QObject>
#include <QThread>
#include <QTime>
#include <QCanBusFrame>
#include <QVector>
#include <QMutex>
#include <QMap>
#include <QTimer>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CCanAnalyzeThread : public QObject
{
    Q_OBJECT
public:
    explicit CCanAnalyzeThread(QObject *parent = nullptr);
    ~CCanAnalyzeThread();

signals:
    void sigWaitACK(quint16 iFrameNumber);
    void sigSendACKBack(QByteArray qSendMsgByteArray);// 发送数据
    void sigReciveMessage(QByteArray qReciveMsgByteArray);// 完整帧

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    void slotReciveOriginalMessageData(QVector<QCanBusFrame> qCanFramsVector);// 接受原始数据
    void slotCleanupReceivedPackets();
private:
    static void* _createThreadHandleList(void* arg);
    void _handleReceiveList();
    void _addReadCanFrame(const QVector<QCanBusFrame> &qCanFramsVector);
    uint64_t _combineIDs(uint16_t can_id, uint16_t cmd_type,
                         uint16_t frame_id, uint16_t command_id);
    void _processPacket(const quint16 &iCanID, const quint16 &iCmdType,
                        const quint16 &iSeqNumber, const quint16 &iMethodID);

private:
    QVector<QByteArray> m_qbReadBuffArray;
    QVector<int> m_iRedaBuffArrayLength;
    QVector<int> m_iReadPayloadLength;
    QVector<quint16> m_iReadSeqNumber;
    // QVector<bool> m_bReCalculation;

    char *pLength;
    QList<QByteArray> m_qReciveDataByteArrayList;// 解析后完整帧
    QByteArray m_qCurrentSendPortDataByte;
    QByteArray m_qCurrentReciveDataByteArray;// 获取插入List临时变量
    QByteArray m_qGetReciveDataByteArray;// 获取的临时变量

    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    QCanBusFrame m_qSendMessageInfoList[BUFFER_SIZE];//原始读取数据  m_qCanReadMsgList
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;

    QByteArray m_qFindHeaderByteArray;// 去除头部，为了查找下一帧头
    int m_iMinPayloadLength;
    QByteArray m_qCRCByteArray;
    quint16 m_iReadDataCRC;
    quint16 m_iGetCRC;
    bool m_bOk;
    quint32 m_iMethodACK;

    quint16 m_iFrameNumber;
    bool m_bSendDataCount;
    bool m_bReadMsgCount;
    int m_iPayloadMaxLength;
    //
    bool m_bThreadExit;
    int m_iiPackID;
    quint32 m_iMaxCanID;// 功能板：1；电机板1-3：2，3，4；温控板：5；RFID：6；
    quint32 m_iCanID;
    int m_iTitleIndex;
    // 相同帧包过滤
    QMutex m_qSameFrameMutex;
    QMap<uint64_t, QDateTime> m_qReceivedPackets;
    QTimer *m_pCleanupTimer ;

};
#endif // CCANANALYZETHREAD_H
