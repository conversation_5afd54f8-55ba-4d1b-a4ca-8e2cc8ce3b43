/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description:  父类，处理各个设备的数据业务
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CWINDOWOBJECT_H
#define CWINDOWOBJECT_H

#include <QObject>
#include <QTimer>
#include <atomic>
#include <QThread>
#include "publicconfig.h"
#include "cglobalconfig.h"
class CWindowObject : public QObject
{
    Q_OBJECT
public:
    explicit CWindowObject(QObject *parent = nullptr);
    ~CWindowObject();
signals:

    void sigRFIDRsMsg(QByteArray qBtyeArray);//  RFID 信息
    void sigUpgradeEndMsg(EnumMachineID ID,int iRs);
public slots:
    void slotAddReciveMsg(QByteArray qMsgBtyeArray);
protected:
    static void* _CreateThreadHandleList(void* arg);// 处理中位机业务通讯
    virtual void _HandleReceiveList();

protected:
    bool m_bThreadExit;
    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    QByteArray m_qSendMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;

    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iSourceID;
    quint8 m_iResult;
    char *m_pFramePos;
    QByteArray m_qPayloadByteArray;
    QString m_qPayloadString;
    QString m_strPayloadString;
    quint16 m_iReadPayloadLength;
    quint32 m_iReciveMachineID;
};
#endif // CWINDOWOBJECT_H
