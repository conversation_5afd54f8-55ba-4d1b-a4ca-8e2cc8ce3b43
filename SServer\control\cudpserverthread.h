#ifndef CUDPSERVERTHREAD_H
#define CUDPSERVERTHREAD_H

#include <QObject>
#include <QUdpSocket>
#include <QTimer>
#include <QThread>
class CUdpServerThread : public QObject
{
    Q_OBJECT
public:
    explicit CUdpServerThread(int iPort, QHostAddress strHostIP, QString strHostMac, QObject *parent = nullptr);
    ~CUdpServerThread();

signals:

    void sigInitServer();
public slots:

    void slotReInitServer(QHostAddress qHostAddress, QString strHostMac );
    void slotServerConnect(bool bConnect);
private slots:
    void _slotInitServer();
    void _slotBroadcast();
    void _slotHeartBeatTimer();
private:
    void _initUdpInfo();
    void _updateSendMessag();
private:
    QThread *m_pThread;
    QUdpSocket *m_pUdpSocket;
    QTimer *m_pBroadcastTimer;
    QTimer *m_pHeartBeatTimer;
    bool m_bThreadExit;
    QHostAddress m_qHostAddress;
    int m_iPort;
    QString m_strHostMacAddress;
    QString m_strConnected;

    QString m_strSendMessage;
};

#endif // CUDPSERVERTHREAD_H
