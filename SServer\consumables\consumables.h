/*****************************************************
  * Copyright: 万孚生物
  * Author: mflin
  * Date: 2024-2-1
  * Description:  耗材管理
  * -----------------------------------------------------------------
  * History:
  *Modified:2024-3-27 add  test datas and fix bug
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CONSUMABLES_H
#define CONSUMABLES_H
#include <QtGlobal>
#include <QObject>
#include <QMap>
#include <QMutex>

#include "publicconfig.h"

#define TIP_ROW_SIZE 16
#define TIP_COLUMN_SIZE 6
#define TUBE_ROW_SIZE 8
#define TUBE_COLUMN_SIZE 6
#define CAP_ROW_SIZE 8
#define CAP_COLUMN_SIZE 6

#define CONSUMABLE_BOX_SIZE 2



enum ConsumableBoxState
{
    CBST_VALID, //可用
    CBST_EMPTY,//空
    CBST_INVALID,//不可用
};

struct RangePos
{
    quint8 uiAreaIndex;
    quint8 uiStartPos;
    quint8 uiEndPos;
};

struct ConsumableReservation {
    bool bIsSufficient;         // 1. 目前是否足够
    int iReserveCount;          // 2. 损耗的数量
    QString strPosRange; // 3. 起始位置及终止位置的字符串组合形式,格式："A1-B5"
};
class Consumables
{
public:
    static Consumables& getInstance();

    void SetCatchType(quint8 uiType, quint8 uiCatchType);

    /**
     * @brief GetNextAvrConsumable 获取下一个可用的特定耗材信息
     * @param type
     * @param uiRowIndex
     * @param uiColumnIndex
     * @return
     */
    bool GetNextAvrConsumable(quint8 uiType, quint8& uiRowIndex, quint8& uiColumnIndex);

    /**
     * @brief GetNextCrossConsumable 获取下一个跨区的特定耗材信息
     * @param type
     * @param uiRowIndex
     * @param uiColumnIndex
     * @param uiAreaIndex
     * @return
     */
    bool GetNextCrossConsumable(quint8 uiType
        , quint8 &uiRowIndex1, quint8 &uiColumnIndex1, quint8 &uiAreaIndex1
        , quint8 &uiRowIndex2, quint8 &uiColumnIndex2, quint8 &uiAreaIndex2);

    /**
     * @brief AddConsumable 添加特定位的耗材盒信息
     * @param type
     * @param uiIndex
     * @param box
     */
    void AddConsumable(quint8 uiType, quint8 uiIndex, ConsumableBox box,int iNeedWriteRFID=1);

    void AddVirtualConsumable(quint8 uiType, quint8 uiIndex, quint8 uiRemain);

    /**
     * @brief RemoveConsumableBox 删除特定位耗材卡盒
     * @param uiIndex
     */
    void RemoveConsumableBox(quint8 uiType, quint8 uiIndex,bool bNeedLock = true);

    //确认某耗材是否有足够的量可供消耗
    bool IsConsumableEnough(quint8 uiType, quint16 uiConsumeSize);

    void Consume(quint8 uiType);//消耗耗材更新数量及RFID数据

    void SetConsumableCurSize(quint8 uiType, quint16 uiSize, quint16 uiBoxIndex = 2);//设置特定耗材特定盒装数量

    quint8 GetConsumableRowIndex(quint8 uiType);//获取特定耗材当前可用的行索引

    quint8 GetConsumableColumnIndex(quint8 uiType);//获取特定耗材当前可用的列索引

    quint8 GetConsumableBoxIndex(quint8 uiType,bool bNeedLock = true);//获取特定耗材当前在用的盒装索引

    bool IsConsumableCurBoxIndexNoOne(quint8 uiType);//确认特定耗材在用盒装是否为第一个备用

    QList<RangePos> virtualConsume(quint8 uiType, QList<quint8> qConsumeList);//虚拟消耗

    /**
     * @brief CheckConsumableBoxEnough 检查耗材卡盒状态
     * @param uiType    类型
     * @param uiIndex   索引
     * @return 状态状态
     */
    ConsumableBoxState CheckConsumableBoxEnough(quint8 uiType, quint16 uiIndex);

    /**
     * @brief CheckConsumableBoxStatus 检查耗材卡盒状态
     * @param 
     */
    void CheckConsumableBoxStatus();
 
private:
    Consumables();

    void _Init();

    /**
     * @brief _UpdateStatus 更新锁和灯状态
     * @param state     盒子状态
     * @param type      类型
     * @param cType     RFID映射类型
     * @return  
     */
    void _UpdateStatus(ConsumableBoxState status,quint8 uiType,RFIDConsumableType cType);

    /**
     * @brief _SortConsumableBoxList 更新锁和灯状态
     * @param type      类型
     * @return  
     */
    void _SortConsumableBoxList(quint8 uiType);
private:
    QList<quint8> m_qConsumableList[CT_MAX];//记录各类可用耗材盒索引List,当耗材为空或者耗材不可用时会被清除
    QMap<quint8, ConsumableBox> m_qConsumableMapList[CT_MAX];// key is  consumableIndex
    //FIXME: 这里的指针需要注意,最好删除或者更改为对象，非法移除时引起的野指针问题
    ConsumableBox* m_curUsedBox[CT_MAX];// 记录当前在用的耗材盒 
    quint8 m_curUsedListIndex[CT_MAX];

    quint16 m_uiCapacityInfo[CT_MAX][2];//记录不同耗材行列容量信息
    quint8 m_uiCatchType[CT_MAX];//单次抓取的耗材数量
    QMutex m_qMutex;
    bool m_bSystemBuildBusyStatus;// 系统构建状态(判断耗材是否需要上锁)
};
#endif // CONSUMABLES_H
