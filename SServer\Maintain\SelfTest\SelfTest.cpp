#include<qdebug.h>
#include<QTime>
#include<algorithm>
#include"SelfTest.h"
#include"publicconfig.h"
#include "consumables/consumables.h"
#include "control/coperationunit.h"
#include "module/devicemodule.h"
#include "datacontrol/ctiminginfodb.h"
#include "cglobalconfig.h"
#include "consumables/pcrresource.h"
#include "consumables/crecyclebin.h"
#include "HalSubSystem/HalSubSystem.h"
#include "affair/caffair.h"
#include "error/cerrorhandler.h"
#include "error/cerrornotify.h"
#define MAGIC_ENUM_RANGE_MAX 2550
#include "magic_enum/magic_enum.hpp"

CSelfTest::CSelfTest()
:m_pcrCatchModule(true, DEVICE_CATCH_TYPE)
{
    _init();
}

CSelfTest::~CSelfTest()
{

}

void CSelfTest::_init()
{
    const QList<quint16> listBoardZInitCondition{ Action_Board1ZInit_Condition,Action_Board3ZInit_Condition };                       // 第一阶段(检测Z轴复位的条件，满足后执行Z轴复位)
    const QList<quint16> listBoardZInit{ Action_Board1ZInit,Action_Board2ZInit,Action_Board3ZInit,Action_Board4ZInit };              // 第二阶段(板1Z初始化 + 板2Z初始化 + 板3z初始化)  
    const QList<quint16> listBoardXYInit1{ Action_Board1RemainPartInit,Action_Board4RemainInit };                                    // 第三阶段(板1其他电机初始化 + 板4其他电机初始化)
    const QList<quint16> listBoardXYInit2{ Action_Board2RemainInit,Action_Board3RemainPartInit };                                    // 第四阶段(板2其他电机初始化 + 板3其他电机初始化)
    const QList<quint16> listSingleClean1{ Action_CleanCentrifuge,Action_CloseCap };                                                 // 第五阶段(板3离心位置清理 + 板1样本盖盖)
    const QList<quint16> listSingleClean2{ Action_SampleBackHome,Action_Board4EjectMagTube };                                        // 第六阶段(板1样本清理放回 + 磁套清理(退磁))
    const QList<quint16> listMultiClean{ Action_AbandonPCR };                                                                        // 第七阶段(板3PCR区域清理)

    // 时序完成结果检查函数映射表
    m_stepValidators[Action_Board1ZInit_Condition] = [this](quint16 uiComplexID) { return _checkAllBoardZInitCondition(uiComplexID); };
    m_stepValidators[Action_Board3ZInit_Condition] = [this](quint16 uiComplexID) { return _checkAllBoardZInitCondition(uiComplexID); };
    m_stepValidators[Action_Board1ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board2ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board3ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board4ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board1RemainPartInit]  = [this](quint16 uiComplexID) { return _checkAllBoardXYInit1(uiComplexID); };
    m_stepValidators[Action_Board4RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit1(uiComplexID); };
    m_stepValidators[Action_Board2RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit2(uiComplexID); };
    m_stepValidators[Action_Board3RemainPartInit]  = [this](quint16 uiComplexID) { return _checkAllBoardXYInit2(uiComplexID); };
    m_stepValidators[Action_CleanCentrifuge]       = [this](quint16 uiComplexID) { return _checkSingleClean1(uiComplexID); };
    m_stepValidators[Action_CloseCap]              = [this](quint16 uiComplexID) { return _checkSingleClean1(uiComplexID); };//Action_CloseCap要特殊处理(左右、两个)
    m_stepValidators[Action_CloseCapDouble]        = [this](quint16 uiComplexID) { return _checkSingleClean1(uiComplexID); };
    m_stepValidators[Action_CloseCapLeft]          = [this](quint16 uiComplexID) { return _checkSingleClean1(uiComplexID); };
    m_stepValidators[Action_SampleBackHome]        = [this](quint16 uiComplexID) { return _checkSingleClean2(uiComplexID); };//Action_SampleBackHome(左右、两个)
    m_stepValidators[Action_SampleBackHomeDouble]  = [this](quint16 uiComplexID) { return _checkSingleClean2(uiComplexID); };
    m_stepValidators[Action_SampleBackHomeLeft]    = [this](quint16 uiComplexID) { return _checkSingleClean2(uiComplexID); };
    m_stepValidators[Action_Board4EjectMagTube]    = [this](quint16 uiComplexID) { return _checkSingleClean2(uiComplexID); };
    m_stepValidators[Action_AbandonPCR]            = [this](quint16 uiComplexID) { return _checkMultiClean(uiComplexID); };

    //时序执行函数映射表
    m_stepExecutors[Action_Board1ZInit_Condition]  = [this]() { _onBoard1ZInitCondition(); };
    m_stepExecutors[Action_Board3ZInit_Condition]  = [this]() { _onBoard3ZInitCondition(); };
    m_stepExecutors[Action_Board1ZInit]            = [this]() { _onBoard1ZInit(); };
    m_stepExecutors[Action_Board2ZInit]            = [this]() { _onBoard2ZInit(); };
    m_stepExecutors[Action_Board3ZInit]            = [this]() { _onBoard3ZInit(); };
    m_stepExecutors[Action_Board4ZInit]            = [this]() { _onBoard4ZInit(); };
    m_stepExecutors[Action_Board1RemainPartInit]   = [this]() { _onBoard1RemainPartInit(); };
    m_stepExecutors[Action_Board2RemainInit]       = [this]() { _onBoard2RemainInit(); };
    m_stepExecutors[Action_Board3RemainPartInit]   = [this]() { _onBoard3RemainPartInit(); };
    m_stepExecutors[Action_Board4RemainInit]       = [this]() { _onBoard4RemainInit(); };
    m_stepExecutors[Action_CleanCentrifuge]        = [this]() { _onCleanCentrifuge(); };
    m_stepExecutors[Action_CloseCap]               = [this]() { _onCloseCap(); };
    m_stepExecutors[Action_SampleBackHome]         = [this]() { _onSampleBackHome(); };
    m_stepExecutors[Action_Board4EjectMagTube]     = [this]() { _onBoard4EjectMagTube(); };
    m_stepExecutors[Action_AbandonPCR]             = [this]() { _onAbandonPCR(); };
    
    _initHash("listBoardZInitCondition",m_hashZInitCondition,listBoardZInitCondition);
    _initHash("listBoardZInit"         ,m_hashZInit         ,listBoardZInit);
    _initHash("listBoardXYInit1"       ,m_hashXYInit1       ,listBoardXYInit1);
    _initHash("listBoardXYInit2"       ,m_hashXYInit2       ,listBoardXYInit2);
    _initHash("listSingleClean1"       ,m_hashSingleClean1  ,listSingleClean1);   
    _initHash("listSingleClean2"       ,m_hashSingleClean2  ,listSingleClean2);
    _initHash("listMultiClean"         ,m_hashMultiClean    ,listMultiClean);

    //自检流程节点(支持并行)
    // 1、第一阶段(检测Z轴复位的条件，满足后执行Z轴复位)
    m_listRunningStep.append(listBoardZInitCondition);  
    // 2、第二阶段(板1Z初始化 + 板2Z初始化 + 板3z初始化)  
    m_listRunningStep.append(listBoardZInit);  
    // 3、第三阶段(板2其他电机初始化 + 板3其他电机初始化)
    m_listRunningStep.append(listBoardXYInit1);    
    // 4、第四阶段(板1其他电机初始化 + 板4其他电机初始化)
    m_listRunningStep.append(listBoardXYInit2);   
    // 5、第五阶段(板3离心位置清理 + 板1样本盖盖 )
    m_listRunningStep.append(listSingleClean1); 
    // 6、第六阶段(板1样本清理放回 + 磁套清理(退磁))
    m_listRunningStep.append(listSingleClean2); 
    // 7、第七阶段(板3PCR区域清理)
    m_listRunningStep.append(listMultiClean);            
}

void CSelfTest::_initHash(QString strName,QHash<quint16, bool>& hash,QList<quint16> list)
{
    hash.clear();
    for (auto id:list)
    {
        hash[id] = false;
    }
    qDebug()<<"_initHash"<<strName<<list.size();
}

void CSelfTest::_resetInitHash(QString strName, QHash<quint16, bool>& hash)
{
    for (auto& value : hash) {
        value = false;
    }
    qDebug()<<"_resetInitHash"<<strName<<hash.keys();
}

void CSelfTest::_onBoard1ZInitCondition()
{
    // 判断是否满足板卡1的Z轴复位
    CompAvrStatus status = CDevStatus::getInstance().getSampleGripperAvailability(CT_SAMPLE_GRIPPER);
    if (CAS_Unavailable == status)// 需要张开样本抓手(防止Z轴复位时抓起样本，夹紧电机处在夹紧状态)
    {
        // 发送张开样本抓手时序
    }
    _onBoardAction("_onBoard1ZInitCondition",Action_Board1ZInit_Condition,"");
    qDebug()<<"_onBoard1ZInitCondition";
}

void CSelfTest::_onBoard3ZInitCondition()
{
    // 判断是否满足板卡1的Z轴复位
    GrabStatus status = CDevStatus::getInstance().getPcrGripperGrabStatus();
    if (CAS_Unavailable == status)
    {

        // 发送张开pcr抓手时序
    }    
    _onBoardAction("_onBoard3ZInitCondition",Action_Board3ZInit_Condition,"");
    qDebug()<<"_onBoard3ZInitCondition";
}

bool CSelfTest::_checkBoard(QString str, QHash<quint16, bool> &hash, quint16 uiComplexID)
{
    if (!hash.contains(uiComplexID))
    {
        qDebug()<<str<<" not contains"<<uiComplexID;
        return false;
    }
    
    hash[uiComplexID] = true;
    auto values = hash.values();
    bool allTrue = std::all_of(values.begin(), values.end(), 
                               [](const bool& value) { return value; });
    
    qDebug()<<"_checkBoard:"<<uiComplexID<<str<<allTrue<<hash.keys();
    return allTrue;    
}

bool CSelfTest::_checkAllBoardZInitCondition(quint16 uiComplexID)
{
    return _checkBoard("_checkAllBoardZInitCondition",m_hashZInitCondition,uiComplexID);
}

bool CSelfTest::_checkAllBoardZInit(quint16 uiComplexID)
{
    return _checkBoard("_checkAllBoardZInit",m_hashZInit,uiComplexID);
}

bool CSelfTest::_checkAllBoardXYInit1(quint16 uiComplexID)   
{
    return _checkBoard("_checkAllBoardXYInit1",m_hashXYInit1,uiComplexID);
}

bool CSelfTest::_checkAllBoardXYInit2(quint16 uiComplexID)   
{
    return _checkBoard("_checkAllBoardXYInit2",m_hashXYInit2,uiComplexID);
}

bool CSelfTest::_checkSingleClean1(quint16 uiComplexID)
{
    return _checkBoard("_checkSingleClean1",m_hashSingleClean1,uiComplexID);
}

bool CSelfTest::_checkSingleClean2(quint16 uiComplexID)
{
    return _checkBoard("_checkSingleClean2",m_hashSingleClean2,uiComplexID);
}

bool CSelfTest::_checkMultiClean(quint16 uiComplexID)
{
    m_pcrCatchModule.SetState(false);
    return _checkBoard("_checkMultiClean",m_hashMultiClean,uiComplexID);
}

void CSelfTest::_clearRemainingSteps()
{
    m_uiCurrentStageIndex = 0;  
    m_allStepIds.clear();
    _onUpdateMiddleHostStatus();//异常后也要清理状态
    COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_UpperHost, 1); 
    qDebug()<<"_clearRemainingSteps"<<m_allStepIds.size();
}

void CSelfTest::_resetStep()
{
    m_uiCurrentStageIndex = 0; 
    m_allStepIds.clear();
    for (const auto& stage : m_listRunningStep) {
        m_allStepIds.unite(QSet<quint16>::fromList(stage));
    }
    _resetInitHash("hashZInitCondition",m_hashZInitCondition);
    _resetInitHash("hashZInit",m_hashZInit);
    _resetInitHash("hashXYInit1",m_hashXYInit1);
    _resetInitHash("hashXYInit2",m_hashXYInit2);
    _resetInitHash("hashSingleClean1",m_hashSingleClean1);
    _resetInitHash("hashSingleClean2",m_hashSingleClean2);
    _resetInitHash("hashMultiClean",m_hashMultiClean);
    m_qListCleanPCRACtion.clear();
    m_qListAbandonPCRACtion.clear();

    // 获取样本组件状态
    m_devCompRight = CDevStatus::getInstance().getSampleGripper(CI_RIGHT);
    m_devCompLeft  = CDevStatus::getInstance().getSampleGripper(CI_LEFT);   
    // 获取pcr抓手状态
    m_devCompPcr = CDevStatus::getInstance().getPcrGripper();
    m_pcrCatchModule.ClearTaskQueue();
    qDebug()<<"_resetStep"<<m_allStepIds.size();      
}

void CSelfTest::_executeCurrentStage()
{
    if (m_allStepIds.isEmpty()) {
        qDebug() << "Self-test completed: All stages finished";
        return;
    }

    for (size_t i = m_uiCurrentStageIndex; i < m_listRunningStep.size(); i++)
    {
        // 执行新的阶段
        for (auto step:m_listRunningStep[i])
        {
            m_stepExecutors[step]();
        }
        m_uiCurrentStageIndex++;
        break;        
    }
    qDebug()<<"_executeCurrentStage:"<<m_uiCurrentStageIndex;
}

void CSelfTest::_reportCurrentStage(quint16 uiComplexID,quint16 uiResult)
{
    // 需要把时序号改为自检步骤
    QString strAction = "";
    auto action = magic_enum::enum_cast<EnumAffairAction>(uiComplexID);
    if (action.has_value()) {
        auto action_name = magic_enum::enum_name(action.value());
        strAction = action_name.data();
    }
    QString strParams = QString("%1:%2").arg(m_uiSeqType).arg(strAction);
    COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost, uiResult);
    qDebug()<<"_reportCurrentStage "<<strParams<<uiResult;
}

void CSelfTest::_onBoardAction(const QString strName,quint16 uiComplexID, QString strParam)
{
    QString strCommandStr = QString::number(uiComplexID);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, strCommandStr+strParam, iMachineID);     
    qDebug()<<"_onBoardAction "<<strName<<uiComplexID<<strParam<<iMachineID;
}

void CSelfTest::_onBoard1ZInit()
{
    _onBoardAction("_onBoard1ZInit",Action_Board1ZInit,"");
}

void CSelfTest::_onBoard2ZInit()
{
    _onBoardAction("_onBoard2ZInit",Action_Board2ZInit,"");
}

void CSelfTest::_onBoard3ZInit()
{
    _onBoardAction("_onBoard3ZInit",Action_Board3ZInit,"");
}

void CSelfTest::_onBoard4ZInit()
{
    _onBoardAction("_onBoard4ZInit",Action_Board4ZInit,"");
}

void CSelfTest::_onBoard1RemainPartInit()
{
    _onBoardAction("_onBoard1RemainPartInit",Action_Board1RemainPartInit,"");
}

void CSelfTest::_onBoard2RemainInit()
{
    _onBoardAction("_onBoard2RemainInit",Action_Board2RemainInit,"");
}

void CSelfTest::_onBoard3RemainPartInit()
{
    _onBoardAction("_onBoard3RemainPartInit",Action_Board3RemainPartInit,"");
}

void CSelfTest::_onBoard4RemainInit()
{
    _onBoardAction("_onBoard4RemainInit",Action_Board4RemainInit,"");
}

void CSelfTest::_onCleanCentrifuge()
{
    QString strParam = "";
    int minIndex = 0;
    strParam += QString(",%1,%2,%3").arg(false).arg(false).arg(0);
    // 清空离心时，需要判断pcr抓手是否抓取PCR管和区域盖
    bool  bHasCatch = _checkMotor3PCRCatchAreaCap();
    if (m_devCompPcr.grabStatus == GS_HasGrabbed)
    {
        if(m_devCompPcr.uiGrabItemType == IT_PCRTube)// 如果抓取了PCR管，需要先丢弃PCR管
        {
            strParam = "";
            strParam += QString(",%1,%2,%3").arg(true).arg(false).arg(minIndex);
        }
        else if(m_devCompPcr.uiGrabItemType == IT_PCRCap && bHasCatch)// 如果抓取了PCR区域盖，需要先盖盖
        {
            // 需要计算盖盖位置
            const int bitOffset = 16;//PCR区域盖子光耦位偏移起始位
            QList<int> qList;
            for(int i=0;i<4;i++)//4个PCR区域盖子光耦
            {
                bool bStatus = (m_u32Motor3AllOptoStatus >> (bitOffset + i)) & 1;// PCR遮光盖1检测光耦
                qList.push_back(bStatus);
            }    

            //判断最小的光耦位为true的位数，即为盖盖位置
            minIndex = qList.indexOf(true);
            strParam = "";
            strParam += QString(",%1,%2,%3").arg(false).arg(true).arg(minIndex);
        }
    }
    else
    {
        // 如果没有抓取，但是光耦检测到少于4个盖子，则有盖子处在异常位置，需要上报故障，并停止自检
        if (bHasCatch)
        {
           CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SystemSelfCheckFailed, QString("PCR cap is not in the correct position."));
           _clearRemainingSteps();
           m_isAborted = true;           
           return;
        }
    }
    
    qDebug()<<"_onCleanCentrifuge"<<m_devCompPcr.grabStatus<<m_devCompPcr.uiGrabItemType<<bHasCatch;
    // 参数1：是否丢弃PCR管
    // 参数2：是否盖盖
    // 参数3：盖盖位置
    // 参数4：是否掉落检测
    strParam += QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
    _onBoardAction("_onCleanCentrifuge",Action_CleanCentrifuge,strParam);
}

void CSelfTest::_onCloseCap()
{
    EnumAffairAction action = Action_CloseCap;
    QString strParam = "";

    bool bSend = true;
    bool bRight = false;
    bool bLeft = false;
    // 检查右组件,如果夹紧状态为夹紧，并且夹紧类型为样本盖，则认为样本盖盖
    if (m_devCompRight.grabStatus == GS_HasGrabbed && m_devCompRight.uiGrabItemType == IT_SampleCap)
    {
        bRight = true;
    }

    // 检查左组件,如果夹紧状态为夹紧，并且夹紧类型为样本盖，则认为样本盖盖
    if (m_devCompLeft.grabStatus == GS_HasGrabbed && m_devCompLeft.uiGrabItemType == IT_SampleCap)
    {
        bLeft = true;
    }
    
    // 1、如果使用右组件，则需要发送Action_CloseCap
    // 2、如果使用左组件，则需要发送Action_CloseCapLeft
    // 3、如果使用左右组件，则需要发送Action_CloseCapDouble
    if (bRight && bLeft)
    {
        action = Action_CloseCapDouble;
    }
    else if (bRight)
    {
        action = Action_CloseCap;
    }
    else if (bLeft)
    {
        action = Action_CloseCapLeft;
    }
    else
    {
        bSend = false;// 如果左右组件都没有夹紧，则认为样本不需要盖盖
    }

    // 需要先移除其他类型的关盖(Action_CloseCap除外)
    const QList<quint16> listSingleClean1{ Action_CloseCapDouble,Action_CloseCapLeft };
    for (auto action : listSingleClean1)
    {
        if (m_hashSingleClean1.contains(action))
        {
            m_hashSingleClean1.remove(action);
        }
    }

    if (bSend)
    {
        // 添加样本盖盖检测标记
        strParam += QString(",%1").arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
        if(CGlobalConfig::getInstance().getSampleTubeExistFlag())
        {
            strParam += ",2";
        }
        else
        {
            strParam += ",3";
        }  
        strParam += QString(",%1,%2,%3").arg(0).arg(0).arg(1);//这三个参数默认使用(需要关盖)

        // 添加新的关盖选项
        if (action != Action_CloseCap)
        {
          m_hashSingleClean1[action] = false;// 添加一个新的选项
          m_hashSingleClean1[Action_CloseCap] = true;// 其他类型的关盖，Action_CloseCap则为true
          m_allStepIds.insert(action);// 添加选项到运行列表
        }
        _onBoardAction("_onCloseCap",action,strParam);
    }
    else
    {
        m_hashSingleClean1[Action_CloseCap] = true;// 如果样本不需要盖盖，则认为样本盖盖成功
        qDebug()<<"_onCloseCap"<<action<<strParam;
    }
}

void CSelfTest::_onSampleBackHome()
{
    EnumAffairAction action = Action_SampleBackHome;
    QString strParam = "";
    bool bSend = true;
    bool bRight = false;
    bool bLeft = false;
    // 检查右组件,如果夹紧状态为夹紧，并且夹紧类型为样本管或样本盖，则认为样本盖盖
    if (m_devCompRight.grabStatus == GS_HasGrabbed && (m_devCompRight.uiGrabItemType == IT_SampleTube || m_devCompRight.uiGrabItemType == IT_SampleCap))
    {
        bRight = true;
    }

    // 检查左组件,如果夹紧状态为夹紧，并且夹紧类型为样本管或样本盖，则认为样本盖盖
    if (m_devCompLeft.grabStatus == GS_HasGrabbed && (m_devCompLeft.uiGrabItemType == IT_SampleTube || m_devCompLeft.uiGrabItemType == IT_SampleCap))
    {
        bLeft = true;
    }
    
    // 1、如果使用右组件，则需要发送Action_SampleBackHome
    // 2、如果使用左组件，则需要发送Action_SampleBackHomeLeft
    // 3、如果使用左右组件，则需要发送Action_SampleBackHomeDouble
    if (bRight && bLeft)
    {
        action = Action_SampleBackHomeDouble;
    }
    else if (bRight)
    {
        action = Action_SampleBackHome;
    }
    else if (bLeft)
    {
        action = Action_SampleBackHomeLeft;
    }
    else
    {
        bSend = false;// 如果左右组件都没有抓到样本，则认为样本不需要放回
    }

    // 需要先移除其他类型的关盖(Action_SampleBackHome除外)
    const QList<quint16> listSingleClean2{ Action_SampleBackHomeDouble,Action_SampleBackHomeLeft };
    for (auto action : listSingleClean2)
    {
        if (m_hashSingleClean2.contains(action))
        {
            m_hashSingleClean2.remove(action);
        }
    }

    if (bSend)
    {
        // 添加新的放回选项
        if (action != Action_SampleBackHome)
        {
          m_hashSingleClean2[action] = false;// 添加一个新的选项
          m_hashSingleClean2[Action_SampleBackHome] = true;// 其他类型的放回，Action_SampleBackHome则为true
          m_allStepIds.insert(action);// 添加选项到运行列表
        }

        quint16 uiSafePosRight = CDevStatus::getInstance().getSampleGripperSafePos(CI_RIGHT).toUInt();
        quint16 uiSafePosLeft  = CDevStatus::getInstance().getSampleGripperSafePos(CI_LEFT).toUInt();
        strParam += QString(",%1,%2").arg(uiSafePosRight).arg(CGlobalConfig::getInstance().getSampleGripperDetectFlag());
        strParam += QString(",%1,%2").arg(0).arg(1);//这两个参数默认使用        
        _onBoardAction("_onSampleBackHome",action,strParam);
    }
    else
    {
        m_hashSingleClean2[Action_SampleBackHome] = true;// 如果样本不需要放回，则认为样本放回成功
        qDebug()<<"_onSampleBackHome"<<action<<strParam;
    }
}

void CSelfTest::_onBoard4EjectMagTube()
{
    _onBoardAction("_onBoard4EjectMagTube",Action_Board4EjectMagTube,"");
}

void CSelfTest::_onAbandonPCR()
{
    //1.查看pcr区域盖盖情况，如果有盖子未盖回，则先盖盖
    //判定是否有未盖回的盖子，需要通过管理版查看光耦状态
    QList<PCRResInfo> resInfos = PCRResource::getInstance().getAllNeedCleanPCRRes();
    if(resInfos.isEmpty())//没有需要清理的PCR管,则认为自检完成
    {
        _sendSelfTestResult(0);
        return;
    }
    _onAllNeedCleanPCR(resInfos);
    qDebug()<<"_onAbandonPCR"<<m_qListCleanPCRACtion.size(); 
}

void CSelfTest::_getMotor3PCROptoStatus(QList<int>& qList)
{
    //获取PCR区域盖状态 挡住为-0 未挡住-1
    if(m_u32Motor3AllOptoStatus !=0)
    {
        qList.clear();
        bool bPcrOpto5Statusbit16 = (m_u32Motor3AllOptoStatus >> 16) & 1;// PCR遮光盖1检测光耦
        bool bPcrOpto6Statusbit17 = (m_u32Motor3AllOptoStatus >> 17) & 1;// PCR遮光盖2检测光耦
        bool bPcrOpto5Statusbit18 = (m_u32Motor3AllOptoStatus >> 18) & 1;// PCR遮光盖3检测光耦
        bool bPcrOpto6Statusbit19 = (m_u32Motor3AllOptoStatus >> 19) & 1;// PCR遮光盖4检测光耦       
        bool bPcrOpto5Statusbit20 = (m_u32Motor3AllOptoStatus >> 20) & 1;// PCR遮光盖5检测光耦
        bool bPcrOpto6Statusbit21 = (m_u32Motor3AllOptoStatus >> 21) & 1;// PCR遮光盖6检测光耦
        
        // 先检查是否有盖子
        QList<int> qListArea1;
        if(!bPcrOpto5Statusbit20)// PCR遮光盖5有盖子,判断PCR遮光盖1和4检测光耦状态
        {
          if(bPcrOpto5Statusbit16)//第1个需要盖
          {
            qListArea1.push_back(0);
          }else if(bPcrOpto5Statusbit18 && qListArea1.isEmpty())
          {
            qListArea1.push_back(2);
          }
        }
        
        QList<int> qListArea2;
        if(!bPcrOpto6Statusbit21)// PCR遮光盖6有盖子,判断PCR遮光盖2和3检测光耦状态
        {
          if(bPcrOpto6Statusbit17)//第1个需要盖
          {
            qListArea2.push_back(1);
          }else if(bPcrOpto6Statusbit19 && qListArea2.isEmpty())
          {
            qListArea2.push_back(3);
          }
        }   
        qList.append(qListArea1);
        qList.append(qListArea2);

        qDebug()<<"MotorBoard3Clean Opto Status: "<<m_u32Motor3AllOptoStatus<<qList;     
        m_u32Motor3AllOptoStatus = 0;
    }
}

quint8 CSelfTest::_getPCRAreaIndexString(quint8 uiRowIndex, quint8 uiColumnIndex, QString &strParam)
{
    quint8 uiAreaIndex = uiRowIndex*PCR_COLUMN_SIZE+ uiColumnIndex;
    strParam = QString("%1").arg(uiAreaIndex);
    return uiAreaIndex;
}

void CSelfTest::_actionAddOpenPCRCapTask(bool bNeedOpenCap, quint8 uiRowIndex,
                                 quint8 uiColumnIndex, QString &strAreaIndexParam,PCRCatchTaskID taskID)
{
    quint8 uiAreaIndex = _getPCRAreaIndexString(uiRowIndex, uiColumnIndex, strAreaIndexParam);
    qDebug()<<"_actionAddOpenPCRCapTask PCR Area cap: "<<bNeedOpenCap<<uiRowIndex<<uiColumnIndex<<strAreaIndexParam<<uiAreaIndex<<taskID;    
    //如果需要打開PCR區域蓋子
    if(bNeedOpenCap)
    {
        QString strParam  = QString(",%1").arg(strAreaIndexParam) + QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
        _addPCRTubeExistFalgToParamStr(strParam);
        strParam += QString(",%1,%2").arg(uiAreaIndex+3).arg(CGlobalConfig::getInstance().getPCRAreaCapDetectFlag());
        m_pcrCatchModule.SlotAddSubTask(taskID, "", strParam);
        m_qListCleanPCRACtion.enqueue(Action_OpenPCRCap);
    }
}

void CSelfTest::_actionAddClosePCRCapTask(bool bNeedCloseCap, const QString &strAreaIndexParam,PCRCatchTaskID taskID)
{
    qDebug()<<"_actionAddClosePCRCapTask PCR Area cap: "<<bNeedCloseCap<<strAreaIndexParam<<taskID;
    if(bNeedCloseCap)
    {
        QString strParam  = QString(",%1").arg(strAreaIndexParam) + QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
        _addPCRTubeExistFalgToParamStr(strParam);
        strParam += QString(",%1,%2").arg(strAreaIndexParam.toInt()+3).arg(CGlobalConfig::getInstance().getPCRAreaCapDetectFlag());
        m_pcrCatchModule.SlotAddSubTask(taskID,"",strParam);     
        m_qListCleanPCRACtion.enqueue(Action_ClosePCRCap);           
    }
}

void CSelfTest::_actionAddAbandonPCRTubeTask(quint8 uiRowIndex, quint8 uiColumnIndex, const QString &strAreaParam)
{
    qDebug()<<"-------ActionAddAbandonPCRTubeTask---------";
    QString strParam = QString(",%1,%2").arg(PCRResource::getInstance().GetPCRSizeType()).arg(strAreaParam);
    strParam += QString(",%1,%2").arg(uiColumnIndex).arg(uiRowIndex);
    strParam += QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());//添加pcr抓手检测标记
    strParam += QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());//添加耗材丢弃位参数
    _addPCRTubeExistFalgToParamStr(strParam);
    m_pcrCatchModule.SlotAddSubTask(PCTI_ABANDON_TUBE,"",strParam);
    m_qListCleanPCRACtion.enqueue(Action_AbandonPCR);
    m_qListAbandonPCRACtion.enqueue(Action_AbandonPCR);
}

void CSelfTest::_addPCRTubeExistFalgToParamStr(QString &strParam)
{
    if(CGlobalConfig::getInstance().getPCRTubeExistFlag())
    {
        strParam += ",2";
    }
    else
    {
        strParam += ",3";
    }
}

void CSelfTest::_sendSelfTestResult(quint16 uiResult)
{
    if(uiResult == 0)
    {
        PCRResource::getInstance().ResetDBData();
        CDevStatus::getInstance().reset();// 重置设备状态
    }
    _onUpdateMiddleHostStatus();
    m_allStepIds.remove(Action_AbandonPCR);
    COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_UpperHost, uiResult); 
    HalSubSystem::getInstance().SetRunStatus(RunStat::RST_IDLE);
    qDebug()<<"_sendSelfTestResult:";
}

void CSelfTest::_onAllNeedCleanPCR(QList<PCRResInfo>& resInfos)
{
    QList<int> qList;
    _getMotor3PCROptoStatus(qList);    
    for (auto& index : qList)
    {
        QString strParam  = QString(",%1").arg(index) + QString(",%1").arg(CGlobalConfig::getInstance().getPCRGripperDetectFlag());
        _addPCRTubeExistFalgToParamStr(strParam);
        strParam += QString(",%1,%2").arg(index+3).arg(CGlobalConfig::getInstance().getPCRAreaCapDetectFlag()); 
        m_pcrCatchModule.SlotAddSubTask(PCTI_CLOSE_CAP, "", strParam);
        m_qListCleanPCRACtion.enqueue(Action_ClosePCRCap);      
    }
    //2.查看pcr区域状态执行清理动作
    for(int i= 0;i<resInfos.size();i++)
    {
        PCRResInfo resInfo = resInfos.at(i);
        QString strAreaIndexParam = "";
        _actionAddOpenPCRCapTask(true, resInfo.uiRowIndex, resInfo.uiColumnIndex, strAreaIndexParam);
        for(int i=0; i<resInfo.uiNextDoublePos;i=i+2)
        {
            quint8 uiRowIndex = PCRResource::getInstance().GetPCRSubAreaRowIndex(i);
            quint8 uiColumnIndex = PCRResource::getInstance().GetPCRSubAreaColumnIndex(i);
            _actionAddAbandonPCRTubeTask(uiRowIndex, uiColumnIndex, strAreaIndexParam);
            CRecycleBin::backBin().use(2);//TODO这里暂未进行精细化管理，理论上有可能一次只有丢弃一个PCR管，但概率比较低
        }
        _actionAddClosePCRCapTask(true, strAreaIndexParam);
    }     
    qDebug()<<"_onAllNeedCleanPCR:"<<m_qListCleanPCRACtion.size()<<"resInfos:"<<resInfos.size();
}

void CSelfTest::_onUpdateMiddleHostStatus()
{
    QString strAction = "";
    auto action = magic_enum::enum_cast<RunStat>(RST_IDLE);
    if (action.has_value()) {
        auto action_name = magic_enum::enum_name(action.value());
        strAction = action_name.data();
    }    
    COperationUnit::getInstance().sendStringData(Method_status, strAction, Machine_Middle_Host);
    qDebug()<<"_onUpdateMiddleHostStatus:";
}

void CSelfTest::_handleSelfTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    // 丢弃PCR管时序动作(特殊处理)
    if (!m_qListCleanPCRACtion.isEmpty() && m_qListCleanPCRACtion.contains(uiComplexID))
    {
        m_pcrCatchModule.SetState(false);
        _reportCurrentStage(uiComplexID,uiResult);
        m_qListCleanPCRACtion.dequeue();
        if (m_qListCleanPCRACtion.isEmpty() && uiResult == 0)
        {
            // 已经清除完成，回复上位机自检完成
            _sendSelfTestResult(uiResult);
            qDebug()<<"SelfTest completed";
        }
        return;
    }
    
    // 步骤不在运行列表中，忽略
    if (!m_allStepIds.contains(uiComplexID)) return;

    _reportCurrentStage(uiComplexID,uiResult);// 上报结果
    
    // 1. 检查步骤执行结果（uiResult非0表示失败）
    if (uiResult != 0) {
        qWarning() << "self step result failed" << uiComplexID;
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SystemSelfCheckFailed, QString("ComplexID %1 failed.").arg(uiComplexID));
        if (!m_isAborted)//只发送一次
        {
            _clearRemainingSteps();
        }
        m_isAborted = true;
        return;
    }

    bool isValidAll = false;
    // 2. 检查是否需要特殊验证（从映射表获取验证函数）
    if (m_stepValidators.contains(uiComplexID)) {
        isValidAll = m_stepValidators[uiComplexID](uiComplexID);  // 执行验证函数
    }

    // 3. 正常完成：从运行列表移除步骤
    m_allStepIds.remove(uiComplexID);

    qDebug() << "_handleSelfTimeseqReply step" << uiComplexID << "m_allStepIds.size:" << m_allStepIds.size()<<"m_uiCurrentStageIndex:"<<m_uiCurrentStageIndex<<"isValidAll:"<<isValidAll;    

    //执行下一阶段
    if (isValidAll)
    {
        _executeCurrentStage();
    }
    qDebug() << "_handleSelfTimeseqReply finished";  
}

bool CSelfTest::_checkMotor3PCRCatchAreaCap()
{
    //获取PCR区域盖状态 挡住为-0 未挡住-1
    const int bitOffset = 16;//PCR区域盖子光耦位偏移起始位
    QList<int> qList;
    for(int i=0;i<6;i++)//4个PCR区域盖子光耦 + 2个PCR区域盖放置位
    {
        bool bStatus = (m_u32Motor3AllOptoStatus >> (bitOffset + i)) & 1;// PCR遮光盖1检测光耦
        qList.push_back(bStatus);
    }
    // 如果存在未挡住的盖子，则认为pcr抓手有盖子(判断光耦数量未挡住数量超过2即为pcr抓手有盖子)
    int unBlockCount = qList.count(true);
    bool bHasCatch = unBlockCount > 2;  
    qDebug() << "_checkMotor3PCRCatchAreaCap qList:" << qList << "unBlockCount:" << unBlockCount << "bHasCatch:" << bHasCatch;
    return bHasCatch;
}

void CSelfTest::_handleCleanPCRTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    m_pcrCatchModule.SetState(false);
    // 1. 检查步骤执行结果（uiResult非0表示失败）
    if (uiResult != 0) {
        qWarning() << "clean step result failed" << uiComplexID;
        _onUpdateMiddleHostStatus();
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SystemSelfCheckFailed, QString("ComplexID %1 failed.").arg(uiComplexID));
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_UpperHost, uiResult);
        m_isAborted = true;
    }    
    else
    {
        if(!m_qListCleanPCRACtion.isEmpty())
        {
            m_qListCleanPCRACtion.dequeue();
        }
        
        if (uiComplexID == Action_AbandonPCR && !m_qListAbandonPCRACtion.isEmpty())
        {
            const quint8 uiHoleMax = PCR_SUB_AREA_SIZE2*PCR_MODULE_SIZE;
            quint8 uiTestState = uiHoleMax - (m_qListAbandonPCRACtion.size()*2);
            QString strParams = QString("%1:%2").arg(m_uiSeqType).arg(uiTestState);
            COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost, uiResult);             
            m_qListAbandonPCRACtion.dequeue();
        }
    }  

    if ((m_qListCleanPCRACtion.isEmpty() && uiResult == 0) || uiResult != 0)
    {
        // 已经清除完成，回复上位机自检完成
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_UpperHost, uiResult);
        _onUpdateMiddleHostStatus();
        qDebug()<<"CleanPCR completed";
    }     
    qDebug()<<"_handleCleanPCRTimeseqReply: ID=" << uiComplexID << "result=" << uiResult;
}

void CSelfTest::StartSelfTest(quint16 uiSeqType)
{
    m_uiSeqType = uiSeqType;
    m_isAborted = false;          // 重置异常标志 
    _resetStep();                 // 重置步骤
    SendElecMagneticLockCommand();//上锁
    _executeCurrentStage();       //开始执行自检第一阶段
    qDebug()<<"StartSelfTest:";
}

void CSelfTest::StartCleanPCR(quint16 uiSeqType)
{
    m_uiSeqType = uiSeqType;
    m_isAborted = false;     // 重置异常标志 
    _resetStep();            // 重置步骤
    QList<PCRResInfo> resInfos = PCRResource::getInstance().GetAllPCRRes();
    _onAllNeedCleanPCR(resInfos);
    qDebug()<<"StartCleanPCR:";
}

void CSelfTest::SendGainOptoStatusCommand()
{
    // 获取光耦状态
    COperationUnit::getInstance().sendStringData(Method_AllOptoStatus, "", Machine_Motor_1); // 获取板卡1全部光耦状态    
    COperationUnit::getInstance().sendStringData(Method_AllOptoStatus, "", Machine_Motor_3); // 获取板卡3全部光耦状态
    qDebug() << "SendGainOptoStatusCommand";
}

void CSelfTest::SendElecMagneticLockCommand()
{
    Consumables::getInstance().CheckConsumableBoxStatus();//检查试剂，准备上锁和灯
    qDebug() << "SendElecMagneticLockCommand";    
}

void CSelfTest::SetMotor3AllOptoStatus(quint16 u32Motor3AllOptoStatus)
{
    m_u32Motor3AllOptoStatus = u32Motor3AllOptoStatus;      
    qDebug()<<"SetMotor3AllOptoStatus:"<<u32Motor3AllOptoStatus;
}

void CSelfTest::HandleTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    qDebug() << "HandleTimeseqReply: ID=" << uiComplexID << "result=" << uiResult<<"aborted="<<m_isAborted;
    // 异常已触发，直接返回
    if (m_isAborted) return;

    switch(m_uiSeqType)
    {
        case ST_SELF_TEST:
            _handleSelfTimeseqReply(uiComplexID, uiResult);
            break;
        case ST_PCR_CLEAN:
            _handleCleanPCRTimeseqReply(uiComplexID, uiResult);
            break;
        default:
            break;
    }  
}