#include "pcrmodule.h"
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include "consumables/pcrresource.h"
#include "error/cerrorhandler.h"
#include "error/cerrornotify.h"

PCRModule::PCRModule(bool bUseThread, quint8 quCatchType)
    : DeviceModule("PCRModule", bUseThread), m_uiCatchType(quCatchType)
{

} // 在构造函数中进行初始化

void PCRModule::SetCatchType(quint8 quCatchType)
{
    m_uiCatchType = quCatchType; // 新增设置 m_uiCatchType 变量的函数
}

void PCRModule::SetPCRModuleIndex(quint8 uiModuleIndex)
{
    m_uiCurModuleIndex = uiModuleIndex;
}

void PCRModule::AddFLData(QString strFLData)
{
    qDebug()<<"PCRModule AddFLData"<<strFLData;
    if(m_bUseThread)
    {
        emit SignalAddFLData(strFLData);
    }
    else
    {
        SlotAddFLData(strFLData);
    }
}

void PCRModule::SendFLDataResultToUpperHost(const int iMethodID)
{
    if(m_bUseThread)
    {
        emit SignalSendFLDataResultToUpperHost(iMethodID);
    }
    else
    {
        SlotSendFLDataResultToUpperHost(iMethodID);
    }
}

void PCRModule::StopTimingTransmit()
{
    if(m_bUseThread)
    {
        emit SignalStopTimingTransmit();
    }
    else
    {
        SlotStopTimingTransmit();
    }
}

void PCRModule::TransTecTimeSeq()
{
    COperationUnit::getInstance().sendStringData(Method_TEC_TransmitTimingData,m_strContent, Machine_PCR_Ctrl+m_uiCurModuleIndex);
    qDebug()<<"TransTecTimeSeq: "<<m_strContent;     
}

void PCRModule::SlotAddSubTask(quint8 uiSubTaskID, const QString &strCommandStr, const QString &strParamStr)
{
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
    // 可以在这里添加子类特有的逻辑
   qDebug() << "PCRCatchModule adding task with specific logic (uiSubTaskID: " << uiSubTaskID << ", strCommandStr: " << strCommandStr << ", strParamStr: " << strParamStr << ")";

}

void PCRModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "PCRModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr <<"m_uiCurModuleIndex:"<<m_uiCurModuleIndex<<")";
        switch (task.uiSubTaskID)
        {
        case PTI_TEC_START:
        {
            _AddPCRStartOrStopTask(QString::number(PCR_START));
            break;
        }
        case PTI_TEC_STOP:
        {
            _AddPCRStartOrStopTask(QString::number(PCR_STOP));
            break;
        }
        case PTI_TEC_TIMING_TABLE_TRANS_REQ:
        {
            _AddPCRTimingTableTransReqTask(task.strParamStr,task.strCommandStr,false,true);
            break;
        }
        case PTI_TEC_TIMING_PCR_START_REQ:
        {
            _AddPCRTimingTableTransReqTask(task.strParamStr,task.strCommandStr,true,false);
            break;
        }        
        case PTI_TEC_TIMING_TABLE_TRANS_REQ_WITH_PCR_START:
        {
            _AddPCRTimingTableTransReqTask(task.strParamStr,task.strCommandStr,true,true);
            break;
        }
        default:
            break;
        }
        m_qWaitProcessSubTask.pop_front();
    }
}

void PCRModule::SlotAddTask(const CmdTask& task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "PCRCatchModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}

void PCRModule::SlotAddFLData(QString strFLData)
{
    //PCR返回数据格式 前20个数16位，后4个数8位
    //[1.index：扫描起始孔位索引(孔位索引由0开始)  2.H1_B    3.H2_B   4.H3_B  5.H4_B
    //6.index：扫描起始孔位索引(孔位索引由0开始)  7.H1_G  8.H2_G  9.H3_G   10.H4_G
    //11.index：扫描起始孔位索引(孔位索引由0开始)  12.H1_O  13.H2_O  14.H3_O  15.H4_O
    //16.index：扫描起始孔位索引(孔位索引由0开始)  17.H1_R   18.H2_R  19.H3_R  20.H4_R
    //21.cycle1 22.cycle2 23.cycle3 24.cycle4]
#if 1
    if (strFLData.isEmpty())
        return;

    QStringList qValueList = strFLData.split(',');
    if (qValueList.size() <26)
        return;
    const int iNumHolesPerGroup = 4;

    const int iCycleStartIndex = 20;
    const int iValStartIndices[] = {1, 6, 11, 16};
    const char chColorLetters[] = {'B', 'G', 'O', 'R'};
    
    // 第25个数据区分 扩增或者熔解 0:扩增 1:熔解
    int iPCRMode = qValueList[24].toInt();
    // 第26个数据熔解温度（下位机*100上发）
    int iPCRTemp = qValueList[25].toInt();    
    qDebug() << "PCR Mode: " << iPCRMode;
    for (int iGroup = 0; iGroup < 4; ++iGroup)
    {
        int iCycleValue = qValueList[iCycleStartIndex + iGroup].toInt();
        int iValStartIndex = iValStartIndices[iGroup];
        char chColorLetter = chColorLetters[iGroup];

        int iHoleStartIndex = qValueList[iValStartIndex - 1].toInt();
        if(iHoleStartIndex == 0)//0组代表无采光数据直接过滤
            continue;
        iHoleStartIndex = (iHoleStartIndex-1)*4;//下位机给的有效组索引是1-16，需要减掉1组
        for (int iHole = 0; iHole < iNumHolesPerGroup; ++iHole)
        {
            quint8 uiHoleIndex = iHoleStartIndex + iHole;// 序号由0开始
            FLData flData = {0,0,0,0,0};

            if (m_qFLDataMap.contains(uiHoleIndex))
                flData = m_qFLDataMap[uiHoleIndex];

            int iVal = qValueList[iValStartIndex + iHole].toInt();           
            flData.iCycle = iCycleValue;
            if (iPCRMode == 1)//1:熔解 ，需要替换温度值
            {
                flData.iCycle = iPCRTemp;
            }            

            switch (chColorLetter)
            {
            case 'B':
                flData.iBVal = iVal;
                break;
            case 'G':
                flData.iGVal = iVal;
                break;
            case 'O':
                flData.iOVal = iVal;
                break;
            case 'R':
                flData.iRVal = iVal;
                break;
            }

            m_qFLDataMap[uiHoleIndex] = flData;
            qDebug()<<"Set FLData Hole:"<<uiHoleIndex<<"B:"<<flData.iBVal<<"G:"<<flData.iGVal<<"O:"<<flData.iOVal<<"R:"<<flData.iRVal<<"Cycle:"<<flData.iCycle;
        }
    }

#endif

}

void PCRModule::SlotProcessTask(const CmdTask &task)
{
    qDebug() << "DeviceModule processing task (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ") in thread: " << QThread::currentThread();
    if (!m_bIsBusy)
    {
        //发送指令给下位机模块
        int iMethodID = task.strCommandStr.toInt();
        COperationUnit::getInstance().sendStringData(iMethodID, task.strParamStr, Machine_PCR_Ctrl+m_uiCurModuleIndex);
        qDebug()<<"PCR Module SendString Cmd: "<<task.strCommandStr+task.strParamStr;
        m_qTaskQueue.dequeue();
        // 具体的任务处理逻辑
        // 处理完成后设置状态为空闲
    }
    else
    {
        qDebug() << "PCR Module is busy, task will be processed when the device becomes idle.";
    }
}

void PCRModule::SlotInitialize()
{
    DeviceModule::SlotInitialize();
    connect(this, &PCRModule::SignalAddFLData, this, &PCRModule::SlotAddFLData);
    connect(this, &PCRModule::SignalSendFLDataResultToUpperHost, this, &PCRModule::SlotSendFLDataResultToUpperHost);
    connect(this, &PCRModule::SignalStopTimingTransmit, this, &PCRModule::SlotStopTimingTransmit);
    m_pReciveMsgTimer = new QTimer(this);
    m_pReciveMsgTimer->setSingleShot(false);//计时器循环此时true只循环一次，false无限循环
    connect(m_pReciveMsgTimer,SIGNAL(timeout()),this,SLOT(SlotSendToPCRDeviceTimer()));
    connect(this, &PCRModule::SignalStartTimer, this, &PCRModule::SlotStartTimer);
}


void PCRModule::SlotGenerateFLDataString(const int iMethodID)
{
    if (m_qFLDataMap.isEmpty())
    {
        m_strFLDataStr = "";
    }

    qint8 u8PCRSubAreaSize = PCRResource::getInstance().GetPCRSubAreaSize();
    u8PCRSubAreaSize = u8PCRSubAreaSize * PCR_MODULE_SIZE;//孔位
    //添加默认值，协议需要全部的孔位数据
    QStringList vFLDatas;
    for (qint8 i = 0; i < u8PCRSubAreaSize; i++)
    {
        vFLDatas.push_back(QString("%1,%2,%3,%4,%5,%6").arg(i).arg("0").arg("0").arg("0").arg("0").arg("0"));//默认全部为0(孔位除外)
    }

    if (Method_melt_result == iMethodID)// 熔解结果
    {
        QString strTemp = ""; //温度
        //孔1,循环数,BGOR;孔2,循环数,BGOR;......
        for (auto it = m_qFLDataMap.cbegin(); it != m_qFLDataMap.cend(); ++it)
        {
            quint8 holeIndex = it.key();
            const FLData& flData = it.value();
            strTemp = QString::number(((flData.iCycle*1.0f)/100.0f), 'f', 2); //温度时乘以100上发的，更新到上位机时除以100
            vFLDatas[holeIndex] = QString("%1,%2,%3,%4,%5,%6").arg(holeIndex).arg(strTemp)
                             .arg(flData.iBVal).arg(flData.iGVal).arg(flData.iOVal).arg(flData.iRVal);
        }        
    }
    else
    {
        //孔1,循环数,BGOR;孔2,循环数,BGOR;......
        for (auto it = m_qFLDataMap.cbegin(); it != m_qFLDataMap.cend(); ++it)
        {
            quint8 holeIndex = it.key();
            const FLData& flData = it.value();
            vFLDatas[holeIndex] = QString("%1,%2,%3,%4,%5,%6").arg(holeIndex).arg(flData.iCycle)
                             .arg(flData.iBVal).arg(flData.iGVal).arg(flData.iOVal).arg(flData.iRVal);
        }   
    }

    m_strFLDataStr =  vFLDatas.join(";");
    qDebug()<<"-----SlotGenerateFLDataString-----"<<m_strFLDataStr;
}

void PCRModule::SlotSendFLDataResultToUpperHost(const int iMethodID)
{
    SlotGenerateFLDataString(iMethodID);
    // Method_test_result,//测试结果
    // Method_melt_result,//熔解结果
    COperationUnit::getInstance().sendStringData( iMethodID, m_strFLDataStr, Machine_UpperHost);
    qDebug()<<"PCR Module SendString Cmd: "<<iMethodID;
}


void PCRModule::_AddPCRStartOrStopTask(QString strParam)
{
    CmdTask sTask;
    sTask.bSync = false;
    sTask.strCommandStr = QString::number(Method_TEC_PCR_StartOrStop);//轉移PCR管到混勻模塊並混勻
    sTask.strParamStr = strParam;
    DeviceModule::SlotAddTask(sTask);
}

void PCRModule::_AddPCRTimingTableTransReqTask(QString strParam,QString strCommand,bool bNeedStartPCR, bool bTransTec)
{
    m_bNeedStartPCR = bNeedStartPCR;
    auto[iPrograms, iSetps, strTimingContent] = CTimingDB::getInstance().getTecInfoFromName(strParam);
    m_strContent = strTimingContent;
    m_strCommand = strCommand;
    const QString strSpilt = "$";
    if (bTransTec)
    {
        QStringList strProgramsList = m_strContent.split(SPLIT_And, QString::SkipEmptyParts);
        QStringList strSendProgramContent;
        for (quint8 i = 0; i < strProgramsList.size(); i++)
        {
            if(!strProgramsList.at(i).isEmpty())
            {
                strSendProgramContent.append(QString::number(i) + SPLIT_And + strProgramsList.at(i));                                                     
            }
        }
        QString str = strSpilt + strSendProgramContent.join(strSpilt) + strSpilt;
        COperationUnit::getInstance().sendStringData(Method_TEC_RequestTransmitTimingTable,
            QString::number(iPrograms) + "," + QString::number(iSetps)+","+strCommand+str, Machine_PCR_Ctrl+m_uiCurModuleIndex);
        
        QString strParamStr = QString::number(iPrograms) + "," + QString::number(iSetps)+","+strCommand+str;
        m_queueParam.enqueue(strParamStr);// 中位机主动下发的需要重发缓存，完成后清空
    }
    qDebug()<<"PCRModule::_AddPCRTimingTableTransReqTask"<<strTimingContent<<iPrograms<<iSetps<<bNeedStartPCR<<m_uiCurModuleIndex<<bTransTec<<strCommand<<m_queueParam.size();
    // SlotDelaySendToPCRDeviceTimer();// 不启动定时器
}

void PCRModule::SlotDelaySendToPCRDeviceTimer()
{
    qDebug()<<"SlotDelaySendToPCRDeviceTimer"<<m_strContent;
    if(m_bUseThread)
    {
        emit SignalStartTimer();
    }
    else
    {
        m_iSendIndex = 0;
        m_strProgramsList = m_strContent.split(SPLIT_And);
        m_pReciveMsgTimer->start(100);
    }
 }

void PCRModule::SlotStopTimingTransmit()
{
    m_pReciveMsgTimer->stop();
    m_strProgramsList.clear();
    m_bNeedStartPCR = false;
    ReTransTecTimeSeq();//重发一次
    qDebug()<<"PCRModule::SlotStopTimingTransmit";
}

void PCRModule::SlotStartTimer()
{
    qDebug()<<"Slot Start PCRModule TimingFile Timer.";
    m_iSendIndex = 0;
    m_strProgramsList = m_strContent.split(SPLIT_And, QString::SkipEmptyParts);
    m_pReciveMsgTimer->start(100);
}

void PCRModule::SlotSendToPCRDeviceTimer()
{
    qDebug()<<"SlotSendToPCRDeviceTimer:"<<m_iSendIndex<<"ProgramList:"<<m_strProgramsList.length()<<"CurModuleIndex:"<<m_uiCurModuleIndex<<"m_iSendIndex:"<<m_iSendIndex;
    if(m_iSendIndex < m_strProgramsList.length())
    {
        if(!m_strProgramsList.at(m_iSendIndex).isEmpty())
        {
            m_strSendProgramContent =
                    QString::number(m_iSendIndex) + SPLIT_And + m_strProgramsList.at(m_iSendIndex);
            COperationUnit::getInstance().sendStringData(Method_TEC_TransmitTimingData,
                                                                     m_strSendProgramContent, Machine_PCR_Ctrl+m_uiCurModuleIndex);
            qDebug()<<"SlotSendToPCRDeviceTimer: SendProgramContent"<<m_strSendProgramContent;                                                       
        }
        m_iSendIndex++;
    }
    else
    {
        if(!m_bNeedStartPCR)
        {
            m_pReciveMsgTimer->stop();
            COperationUnit::getInstance().sendCmd(Method_TEC_TransmitTimingEnd, Machine_PCR_Ctrl+m_uiCurModuleIndex);
            qDebug()<<"SlotSendToPCRDeviceTimer: Method_TEC_TransmitTimingEnd index 0";                                                       
            m_strProgramsList.clear();
            m_iSendIndex = 0;
        }
        else
        {
            if(m_strProgramsList.length()>0)
            {
                COperationUnit::getInstance().sendCmd(Method_TEC_TransmitTimingEnd, Machine_PCR_Ctrl+m_uiCurModuleIndex);
                qDebug()<<"SlotSendToPCRDeviceTimer: Method_TEC_TransmitTimingEnd";                                                       
                m_strProgramsList.clear();
            }
            else
            {
                m_pReciveMsgTimer->stop();
                m_iSendIndex = 0;
                // 启动改由PCR接收完成tec时序成功之后启动
                // COperationUnit::getInstance().sendStringData(Method_TEC_PCR_StartOrStop, "1", Machine_PCR_Ctrl+m_uiCurModuleIndex);
                // qDebug()<<"SlotSendToPCRDeviceTimer: Method_TEC_PCR_StartOrStop StartPCR";  
                // m_bNeedStartPCR = false;
            }
        }
    }
}

void PCRModule::StartPCR()
{
    if (!m_bNeedStartPCR)
    {
        qDebug()<<"StartPCR: m_bNeedStartPCR is false, ignore"<<m_uiCurModuleIndex;
        return;
    }
    
    COperationUnit::getInstance().sendStringData(Method_TEC_PCR_StartOrStop, QString::number(PCR_START), Machine_PCR_Ctrl+m_uiCurModuleIndex);
    m_bNeedStartPCR = false;
    m_queueParam.clear();//tec时序已经下发成功，启动后清除缓存
    m_pReciveMsgTimer->stop();
    m_iSendIndex = 0;    
    qDebug()<<"StartPCR: "<<m_uiCurModuleIndex;      
}

void PCRModule::ReTransTecTimeSeq()
{
    if (m_queueParam.isEmpty())
    {
        qDebug()<<"ReTransTecTimeSeq: queueParam is empty, ignore";
        return;
    }
    QString strParam  = m_queueParam.head();
    COperationUnit::getInstance().sendStringData(Method_TEC_RequestTransmitTimingTable,strParam, Machine_PCR_Ctrl+m_uiCurModuleIndex);
    m_queueParam.dequeue();
    qDebug()<<"ReTransTecTimeSeq: "<<m_uiCurModuleIndex<<strParam;
}

void PCRModule::UpdateRunStatus(int iParam,quint32 uiResult)
{
    if (uiResult == 0)//成功
    {
        return;
    }
    switch(iParam)// 启动或者停止失败，故障提示
    {
       case PCR_START:// 启动失败
           CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStartFailed, "tec timeseq start failed.");
           break;
       case PCR_STOP:// 停止失败
           CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceStopFailed, "tec timeseq stop failed.");
           break;
       default:
           break;
    }    
}

void PCRModule::SetFLLedStatus(bool bOpen)
{
    // 参数1：0-通道设置 1-灯源设置 
    // 参数2：0-关闭    15-开启
    QString strParam = "1,";     
    if (bOpen)
    {
        strParam += "15";
    }
    else
    {
        strParam += "0";
    }  
    COperationUnit::getInstance().sendStringData(Method_FLLED,strParam, Machine_Fluorence);
    qDebug()<<"SetFLLedStatus: "<<bOpen<<strParam;
}

bool PCRModule::CheckReTransTecTimeSeq()
{
    bool bRet = m_queueParam.isEmpty();
    qDebug()<<"CheckReTransTecTimeSeq: "<<bRet;
    return bRet;
}

void PCRModule::StartMultiPCR(PCRModule pcrModule[5])
{
    for (size_t i = 1; i <=PCR_MODULE_SIZE; i++)
    {
        pcrModule[i].StartPCR();//启动pcr
        QThread::msleep(300);//启动延时
    }    
    qDebug()<<"StartMultiPCR:";
}

void PCRModule::ResetStartMultiPCRStatus(PCRModule pcrModule[5],QString strCommand)
{
    QStringList strParams = strCommand.split(",");
    if (strParams.isEmpty() || strParams.size() != PCR_MODULE_SIZE)
    {
        qDebug()<<"ResetStartMultiPCRStatus command is error"<<strCommand;
        return;
    }
    for (size_t i = 1; i <=PCR_MODULE_SIZE; i++)
    {
        if (strParams[i-1].toUInt() == 1)
        {
            pcrModule[i].m_bNeedStartPCR = true;//重置pcr启动状态
        }
    }    
    qDebug()<<"ResetStartMultiPCRStatus:"<<strCommand;
}

QString PCRModule::GetCommandStr()
{
    qDebug()<<"GetCommandStr:"<<m_strCommand;
    return m_strCommand;
}