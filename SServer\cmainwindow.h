/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description:  中位机核心业务类
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CMAINWINDOW_H
#define CMAINWINDOW_H

#include <QObject>

#include <QMutex>
#include<QDateTime>
#include "publicconfig.h"
#include "affair/caffairobject.h"
//#include "affair/caffairbase.h"
#include "affair/caffaircomplexcompose.h"
#include "affair/caffair.h"



class CMainWindow : public QObject
{
    Q_OBJECT
public:
    explicit CMainWindow(QObject *parent = nullptr);
    ~CMainWindow();

signals:
    void sigComplexComposeResult(QString strComplexID);
    void sigExtractAutoupgradeZipFile();
    void sigBoardVersionInfo(EnumMachineID ID,QString strVersion);
public slots:
    void slotAddReciveMsg(QByteArray qMsgBtyeArray);
    void slotReceiveUnitCondition(quint16 uiUnit, quint16 uiIndex);
    void slotExtractScanRs(QString strInfo);
    void slotStatusRequest();
    void slotBootSelfAutoTimeOut();
private:
    void _initNetwork();
    void _initData();
    void _initObject();

    void _loadConsumable(quint8 uiType, quint8 uiIndex, quint16 ui16Delay = 0);
    void _unloadConsumable(quint8 uiType, quint8 uiIndex);

private:
    static void* _CreateThreadHandleList(void* arg);// 处理中位机业务通讯
    void _HandleReceiveList();

private:

private:
    bool m_bThreadExit;
    QByteArray m_qSendMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;
    // 解析
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iCmdID;
    quint8 m_iSourceID;
    char *m_pFramePos;
    int m_iReadSeqNumber;
    QByteArray m_qPayloadByteArray;
    QString m_qPayloadString;
    QString m_strPayloadString;
    quint16 m_iReadPayloadLength;
    quint32 m_iReciveMachineID;
    quint8 m_iResult;
    // 流程调试
    QMutex m_qUnitConditionMutex;
    QMutex m_qExecConditionMutex;
    CAffairObject *m_pCAffairObject;
//    CAffairBase *m_pAffair;
    CAffair *m_pAffair;
    CAffairComplexCompose *m_pCAffairComplexCompose;// 业务组合
    QTimer* m_pStatusRequestTimer;
    QTimer* m_pBootSelfAutoTimer;// 开机自检定时器
};

#endif // CMAINWINDOW_H
