#include "cextractscannerthread.h"
#include <QDebug>
#include "publicfunction.h"
CExtractScannerThread::CExtractScannerThread(QString strSerialName, QString m_strBandRate, QObject *parent,QString strScannerName)
    : QObject(parent)
    , m_strSerialName((strSerialName)),
      m_strBandRate((m_strBandRate)),
      m_strScannerName(strScannerName)
{
    m_bOpenSerialPort = false;
    m_qReciveMessageByteArray = "";
    m_pThread = new QThread();
    m_pSerialPort = new QSerialPort();
    _InitPort(m_strSerialName,m_strBandRate);// 必须先初始化，后moveto

    this->moveToThread(m_pThread);
    m_pSerialPort->moveToThread(m_pThread);

    connect(m_pSerialPort, &QSerialPort::readyRead, this, &CExtractScannerThread::_slotReadyRead);



    m_pThread->start();
}

CExtractScannerThread::~CExtractScannerThread()
{
    if(m_pSerialPort != nullptr)
    {
        m_pSerialPort->close();
        m_pSerialPort->deleteLater();
        m_pSerialPort = nullptr;
    }
    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }

}

bool CExtractScannerThread::GetIsOpenSerialPort()
{
    return m_bOpenSerialPort;
}

void CExtractScannerThread::slotSendMessageNoList(QByteArray qSendMsgAarry)
{
    if(qSendMsgAarry.count() >= gk_iFrameLengthNotData)// 帧长
    {// 只做MethodID初步解析
//        m_pFramePos = qSendMsgAarry.data() + gk_iMethodIDPos;//指令执行ID
//        m_iMethodID = GetByte2Int(m_pFramePos);
//        m_iDestinationID = *((quint8*)qSendMsgAarry.data() + gk_iDestinationIDPos);
        m_pFramePos = qSendMsgAarry.data() + gk_iLengthPos;
        m_iReadPayloadLength = GetByte2Int(m_pFramePos);
        m_qPayloadByteArray = qSendMsgAarry.mid(gk_iFrameDataPos, m_iReadPayloadLength);
        m_qPayloadByteArray = m_qPayloadByteArray.replace("[", "");
        m_qPayloadByteArray = m_qPayloadByteArray.replace("]", "");
        qDebug()  <<  m_strScannerName<<"CExtractScannerThread "
                   << qSendMsgAarry
                   << m_qPayloadByteArray;
    }
    sendFrameData(QByteArray::fromHex(m_qPayloadByteArray));
}

void CExtractScannerThread::_slotReadyRead()
{
    m_qReciveMessageByteArray += m_pSerialPort->readAll();
    qDebug() <<m_strScannerName<< "extract scanner recive : " << m_qReciveMessageByteArray;
    emit sigReciveSeirlaData(m_qReciveMessageByteArray);
    m_qReciveMessageByteArray = "";

}

void CExtractScannerThread::_InitPort(QString strComName, QString strBandRate)
{
    QString strLog;
    if(m_pSerialPort->isOpen())
        m_pSerialPort->close();
    m_pSerialPort->setPortName(strComName);
    if(m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_pSerialPort->setBaudRate(strBandRate.toInt());
        m_pSerialPort->setDataBits(QSerialPort::Data8);
        m_pSerialPort->setParity(QSerialPort::NoParity);
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
        m_bOpenSerialPort = true;
        m_pSerialPort->setReadBufferSize(1024);
        strLog = QString("open sucess:") + strComName + " " + strBandRate;
    }
    else
    {
        m_bOpenSerialPort = false;
        strLog = QString("open failed:") + strComName + m_pSerialPort->errorString();
    }
    qDebug()<<m_strScannerName<<strLog;
}

void CExtractScannerThread::sendFrameData(const QByteArray &qSendMsgAarry)
{
    m_pSerialPort->write(qSendMsgAarry);
    qDebug() <<m_strScannerName<<"send frame data : " << qSendMsgAarry.toHex();
}