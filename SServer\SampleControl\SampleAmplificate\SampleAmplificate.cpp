#include<qdebug.h>
#include<QTime>
#include"SampleAmplificate.h"
#include "datacontrol/cprojectdb.h"

SampleAmplificate::SampleAmplificate(QObject *parent) : QObject(parent)
{

}

bool SampleAmplificate::ParseSampleInfo(QString &strBatchInfo)
{
    QMutexLocker qSysLocker(&m_qMutex);
    m_batchInfoList.clear();
    m_qBatchInfos.clear();
    m_qBatchInfosUsed.clear();
    m_qBatchInfosMap.clear();

    // 241021-154531;0^0,1^1,S241021-1545201--+--2024-10-21154533,M100;FluA/FluB/RSV
    qDebug() << "SampleAmplificate strBatchInfo" << strBatchInfo;
    QStringList batchFields = strBatchInfo.split(";");
    if(batchFields.size()>=2)
    {
        // 1.解析样本信息
        QSet<quint8> qAmplifyAreas;
        m_strBatchNo = batchFields[0];
        strTecName = batchFields[2];
        QStringList qSampleRecordList = batchFields[1].split("+$+", QString::SkipEmptyParts);
        //PCR管位置(0^0),扩增区域位置(0^0),样本编号,项目编号+$+PCR管位置(0^0),扩增区域位置(0^0),样本编号,项目编号
        for (const QString &strSampleRecord : qSampleRecordList) 
        {
            QStringList qSampleFields = strSampleRecord.split(",", QString::SkipEmptyParts);
            if(qSampleFields.size()<4)
            {
                continue;
            }
            AmplificateSampleInfo sampleInfo;
            QString strPCRPosition = qSampleFields[0];
            QString strAmplifyAreaPosition = qSampleFields[1];
            sampleInfo.strSampleID = qSampleFields[2];
            sampleInfo.strProjectID = qSampleFields[3];  

            //  解析PCR管位置和扩增区域位置   
            QStringList positionPartsPCR = strPCRPosition.split("^", QString::SkipEmptyParts);
            if (positionPartsPCR.size() == 2)
            {
                sampleInfo.uiTubeRowIndex = positionPartsPCR[0].toInt();
                sampleInfo.uiTubeColumnIndex = positionPartsPCR[1].toInt();
                qDebug() << "PCR pos: row:" << sampleInfo.uiTubeRowIndex << ", column:" << sampleInfo.uiTubeColumnIndex;
            }

            QStringList positionPartsAmplify = strAmplifyAreaPosition.split("^", QString::SkipEmptyParts);
            if (positionPartsAmplify.size() == 2)
            {
                sampleInfo.uiAmplifyRowIndex = positionPartsAmplify[0].toInt();
                sampleInfo.uiAmplifyColumnIndex = positionPartsAmplify[1].toInt();
                qDebug() << "Amplify pos: row:" << sampleInfo.uiAmplifyRowIndex << ", column:" << sampleInfo.uiAmplifyColumnIndex;
            }
            
            // 计算PCR管区域索引和扩增区域索引
            sampleInfo.uiTubeAreaIndex = _CalcPCRTubeAreaRowColumnIndexToArea(sampleInfo.uiTubeColumnIndex);
            _CalcAmplifyArea(sampleInfo);
            qDebug()<<"SampleAmplificate sampleInfo"<<sampleInfo.strSampleID<<sampleInfo.strProjectID<<sampleInfo.uiTubeAreaIndex<<sampleInfo.uiAmplifyAreaIndex;

            sampleInfo.bNeedCloseCap = false;
            sampleInfo.bNeedOpenCap = false;
            sampleInfo.bLastOneInCurTecBatch = false;

            sampleInfo.uiCompNum = CProjectInformation::getInstance().getTubeCountFromProjectLot(sampleInfo.strProjectID);


            m_batchInfoList.push_back(sampleInfo);

            qAmplifyAreas.insert(sampleInfo.uiAmplifyAreaIndex);
            if(m_qBatchInfosMap.contains(sampleInfo.uiAmplifyAreaIndex))
            {
                m_qBatchInfosMap[sampleInfo.uiAmplifyAreaIndex].append(sampleInfo);
            }
            else
            {
                QList<AmplificateSampleInfo> qList;
                qList.append(sampleInfo);
                m_qBatchInfosMap[sampleInfo.uiAmplifyAreaIndex] = qList;
            }
            
        }

        // 2.判断PCR区域盖的开关逻辑(需要判断当前区域是否添加完成，即可关闭)
        _CalcAmplifyAreaSampleDistribution(qAmplifyAreas);

        // 3.判断当前批次是否结束(全部的样本都已经处理完成)
        m_bRunning = true;// 开始运行
    }    
    return true;
}

bool SampleAmplificate::GetNextAmplificateSampleInfo(AmplificateSampleInfo &info)
{
    QMutexLocker qSysLocker(&m_qMutex);
    if (m_qBatchInfos.size() == 0)
    {
        qWarning() << "No more samples to get.";
        return false;
    }
    
    info = m_qBatchInfos.dequeue();
    qDebug()<<"GetNextAmplificateSampleInfo"<<info.strProjectID;
    return true;
}

bool SampleAmplificate::UpdateCurAmplificateSampleInfo(AmplificateSampleInfo &info)
{
    QMutexLocker qSysLocker(&m_qMutex);
    m_qBatchInfosUsed.append(info);
    return true;
}

quint8 SampleAmplificate::GetAmplificateSampleInfoSize()
{
    qDebug() << "GetAmplificateSampleInfoSize: " << m_qBatchInfos.size();
    return m_qBatchInfos.size();
}

quint8 SampleAmplificate::GetAmplificateSampleInfoUsedSize()
{
    qDebug() << "GetAmplificateSampleInfoUsedSize: " << m_qBatchInfosUsed.size();
    return m_qBatchInfosUsed.size();
}

bool SampleAmplificate::GetRunStatus()
{
    qDebug()<<"GetRunStatus"<<m_bRunning;
    return m_bRunning;
}

void SampleAmplificate::ResetRunStatus()
{
    m_bRunning = false;
    qDebug()<<"ResetRunStatus"<<m_bRunning;
}

QString SampleAmplificate::GetBatchNo()
{
    return m_strBatchNo;
}

qint8 SampleAmplificate::_CalcPCRTubeAreaRowColumnIndexToArea(quint8 &uiColumnIndex)
{
    // iRowIndex 在 0 到 5 之间，则 uiAreaIndex 被设置为 1；如果 uiRowIndex 在 6 到 11 之间，则 uiAreaIndex 被设置为 0
    qint8 uiAreaIndex = 1;// 区域索引默认为1
    const quint8 uiAreaColumnIndexEnd = 11;
    const quint8 uiAreaColumnIndexBegin = 5;
    if (uiColumnIndex >= 6 && uiColumnIndex <= uiAreaColumnIndexEnd) 
    {
        uiColumnIndex = uiAreaColumnIndexEnd - uiColumnIndex;
        uiAreaIndex = 0;
    }
    else
    {
        uiColumnIndex = uiAreaColumnIndexBegin - uiColumnIndex;
    }    
    qDebug()<<"_CalcPCRTubeAreaRowColumnIndexToArea "<<uiColumnIndex<<" "<<uiAreaIndex;
    return uiAreaIndex;
}

void SampleAmplificate::_CalcAmplifyArea(AmplificateSampleInfo& info)
{
    info.uiPcrPosRowIndex = info.uiAmplifyRowIndex;
    info.uiPcrPosColumnIndex = info.uiAmplifyColumnIndex;

    const quint8 uiColumnIndexHalf = 8;
    const quint8 uiRowIndexHalf = 2;

    qint8 uiAreaIndex = 0;

    if (info.uiAmplifyRowIndex < 2) 
    {
        if (info.uiAmplifyColumnIndex < uiColumnIndexHalf) 
        {
            uiAreaIndex = 0;
        } 
        else 
        {
            uiAreaIndex = 1;
            info.uiPcrPosColumnIndex = info.uiAmplifyColumnIndex - uiColumnIndexHalf;
        }
    } 
    else 
    {
        if (info.uiAmplifyColumnIndex < uiColumnIndexHalf) 
        {
            uiAreaIndex = 2;
            info.uiPcrPosRowIndex = info.uiAmplifyRowIndex - uiRowIndexHalf;
        } 
        else 
        {
            uiAreaIndex = 3;
            info.uiPcrPosRowIndex = info.uiAmplifyRowIndex - uiRowIndexHalf;
            info.uiPcrPosColumnIndex = info.uiAmplifyColumnIndex - uiColumnIndexHalf;
        }
    }

    switch (uiAreaIndex)
    {
        case 0:   // 区域0
            info.uiPcrAreaPosRowIndex = 0;
            info.uiPcrAreaPosColumnIndex = 0;
            break;
        case 1:   // 区域1
            info.uiPcrAreaPosRowIndex = 0;
            info.uiPcrAreaPosColumnIndex = 1;
            break;
        case 2:   // 区域2
            info.uiPcrAreaPosRowIndex = 1;
            info.uiPcrAreaPosColumnIndex = 0;
            break;
        case 3:   // 区域3
            info.uiPcrAreaPosRowIndex = 1;
            info.uiPcrAreaPosColumnIndex = 1;
            break;
    }   
    info.uiAmplifyAreaIndex = uiAreaIndex; 
    qDebug()<<"_CalcAmplifyAreaToArea: "<<info.uiPcrAreaPosRowIndex<<info.uiPcrAreaPosColumnIndex<<info.uiPcrPosRowIndex<<info.uiPcrPosColumnIndex<<uiAreaIndex;
}

void SampleAmplificate::_CalcAmplifyAreaSampleDistribution(QSet<quint8>& qAmplifyAreas)
{
        auto amplifyAreaslist = qAmplifyAreas.toList();
        std::sort(amplifyAreaslist.begin(), amplifyAreaslist.end(),[](quint8 a, quint8 b) { return a < b; });
        qDebug()<<"amplifyAreaslist: "<<amplifyAreaslist;
        for (const quint8 &area : amplifyAreaslist)
        {
            if(m_qBatchInfosMap.contains(area))
            {
                QList<AmplificateSampleInfo> &qList = m_qBatchInfosMap[area];
                if(!qList.isEmpty())// 更改当前区域的开关逻辑
                {
                    // 开启PCR管区域
                    auto& infoBegin = qList.first();
                    infoBegin.bNeedOpenCap = true;
                    infoBegin.bNeedCloseCap = false;
                    
                    // 关闭PCR管区域
                    auto& infoEnd = qList.last();                    
                    if(qList.size() > 1)
                    {
                       infoEnd.bNeedOpenCap = false; 
                       infoEnd.bNeedCloseCap = true; 
                    }else if (qList.size() == 1)
                    {
                        infoEnd.bNeedCloseCap = true; 
                    }

                    // 判断当前批次已完成(只有一个TEC时序)
                    if (amplifyAreaslist.last() == area)
                    {
                        infoEnd.bLastOneInCurTecBatch = true;
                    }   
                }

                // 更新测试队列信息
                for (auto& q : qList)
                {
                    qDebug()<<"m_qBatchInfos qList:"<<q.strSampleID<<q.strProjectID<<q.bNeedOpenCap<<q.bNeedCloseCap<<q.bLastOneInCurTecBatch<<area;
                    m_qBatchInfos.append(q);
                }
                                
            }
            else
            {
                qWarning() << "No sample in area: " << area;
            }
        }    
}
