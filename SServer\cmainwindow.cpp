#include "cmainwindow.h"
#include <QDebug>
#include <QString>
#include <QTime>
#include <QCoreApplication>
#include <unistd.h>
#include <QtMath>
#include "ccommunicationobject.h"
#include "publicfunction.h"
#include "datacontrol/ctiminginfodb.h"
#include "datacontrol/cprojectdb.h"
#include "datacontrol/CSystemDB.h"
#include "control/coperationunit.h"
#include "cglobalconfig.h"
#include "publicfunction.h"
#include "affair/cextractparse.h"
#include "error/cerrorhandler.h"
#include "affair/cdevStatus.h"
#include "datacontrol/errorcodedb.h"
#include "RFID/CRFIDCtrl.h"
#include "RFID/CRFIDMotionTask.h"
#include "consumables/cconsumablereservation.h"
#include "HalSubSystem/HalSubSystem.h"
#include "error/cerrornotify.h"
#include "SystemConfig/CpuMonitor.h"
#include "SystemConfig/SystemConfig.h"
#include<QRegularExpression>
#include "Maintain/MaintainSubSystem.h"
#include "consumables/pcrresource.h"
#include "log/cspdlogger.h"
#define MAGIC_ENUM_RANGE_MAX 2550
#include "magic_enum/magic_enum.hpp"
#include"Upgrade/UpgradeCtrl.h"
#define RFID_DELAY_TIME_MS 800 //RFID读取延时时间 
CMainWindow::CMainWindow(QObject *parent) : QObject(parent)
{
    qDebug() << "=================================";
    qDebug() << "tcp server window start v 0.03-20240108-1";
    qDebug() << "=================================";
#ifdef SServer_Version
    qDebug() << "APP_FULL_VERSION"<<QString(APP_FULL_VERSION);
#endif
    m_bThreadExit = true;
    QDir::setCurrent(QCoreApplication::applicationDirPath());
    _initData();
    _initNetwork();
    _initObject();
    CCommunicationObject::getInstance();

    connect(this, &CMainWindow::sigExtractAutoupgradeZipFile,
     &CUpgradeCtrl::getInstance(), &CUpgradeCtrl::slotExtractAutoupgradeZipFile);
    connect(this, &CMainWindow::sigBoardVersionInfo,
     &CUpgradeCtrl::getInstance(), &CUpgradeCtrl::slotBoardVersionInfo);

    connect(&CCommunicationObject::getInstance(), &CCommunicationObject::sigSendMessageToMainWindow,
            this, &CMainWindow::slotAddReciveMsg);

    m_pStatusRequestTimer = new QTimer(this);
    m_pStatusRequestTimer->setSingleShot(true);//计时器循环此时true只循环一次，false无限循环
    connect(m_pStatusRequestTimer,SIGNAL(timeout()),this,SLOT(slotStatusRequest()));
    m_pStatusRequestTimer->start(500);

    m_pBootSelfAutoTimer = new QTimer(this);
    m_pBootSelfAutoTimer->setSingleShot(true);
    connect(m_pBootSelfAutoTimer,SIGNAL(timeout()),this,SLOT(slotBootSelfAutoTimeOut()));
    m_pBootSelfAutoTimer->start(2000);

    pthread_t tid;
    pthread_create(&tid, NULL, _CreateThreadHandleList,this);
}

CMainWindow::~CMainWindow()
{
    m_bThreadExit = false;
    if(m_pCAffairObject)
        delete  m_pCAffairObject;
    if(m_pCAffairComplexCompose)
        delete  m_pCAffairComplexCompose;
    if(m_pStatusRequestTimer)
        delete m_pStatusRequestTimer;
    if(m_pBootSelfAutoTimer)
        delete m_pBootSelfAutoTimer;        
}

void CMainWindow::slotAddReciveMsg(QByteArray qMsgBtyeArray)
{    
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qSendMessageInfoList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
    #ifndef ShortOutPutLog
    // qDebug()<<"CMainWindow::slotAddReciveMsg: "<<m_iCurrentWriteIndex<<m_iReadIndex.load();
    #endif
}

void CMainWindow::slotReceiveUnitCondition(quint16 uiUnit, quint16 uiIndex)
{
    QMutexLocker locker(&m_qUnitConditionMutex);
    m_pAffair->slotAddReciveSTCondition(uiUnit, uiIndex);
}

void CMainWindow::slotStatusRequest()
{
    QDFUN_LINE<<"request Status to all sub devices----------------.";
    QString strStatus = QString("%1,%2").arg("0").arg("");//状态IDLE目前暂用0替代,information为空
    for(int i = Machine_Function_manager_Ctrl;i<Machine_GarbageStateCtrl;i++)
    {
        COperationUnit::getInstance().sendStringData(Method_unit_status, strStatus, i);
    }
}

void CMainWindow::slotBootSelfAutoTimeOut()
{
    if (CSystemDB::getInstance().getIntValueFromKey("Start_Reset") > 0)
    {
        QString str = QString("%1").arg(ST_RESET)+":"; 
        m_pAffair->StartProcess(str);
        qDebug()<<"slotBootSelfAutoTimeOut execute self test"<<str;        
    }
}

void CMainWindow::_initNetwork()
{// ch初始化默认IP地址
#ifdef Q_OS_LINUX
    system("ifconfig eth1 ************ netmask *************");
    //    CGlobalConfig::getInstance().initNetworkAddress();
#endif
}

void CMainWindow::_initData()
{
    QString strRootDir = QCoreApplication::applicationDirPath();
    CreateDir(strRootDir+ "/db/");
#ifdef Q_OS_ARM // 临时策略，首次迁移Timing.db 20250522
    // 如果strRootDir/db/中不存在Timing.db,则将strRootDir/clientdb/Timing.db拷贝到strRootDir/db/Timing.db
    if(!QFile::exists(strRootDir+"/db/Timing.db"))  
    {
        QFile::copy(strRootDir+"/clientdb/Timing.db", strRootDir+"/db/Timing.db");
        system("sync");
    }
#endif
    CTimingDB::getInstance().initDataBase();
    CTimingInfoDB::getInstance().initDataBase();
    CProjectInformation::getInstance().initDataBase();
    CSystemDB::getInstance().initDataBase();
    ErrorCodeDB::getInstance().initDataBase();
}

void CMainWindow::_initObject()
{
    m_pCAffairObject = new CAffairObject(Unit_Condition_Sample, this);
    m_pAffair = &CAffair::getInstance();
    connect(m_pAffair, &CAffair::sigExtractScanRs,this, &CMainWindow::slotExtractScanRs);
    connect(&CCommunicationObject::getInstance(), SIGNAL(sigZebraScannerRs(QString,int)),
            m_pAffair, SLOT(slotZebraScannerRs(QString,int))); //扫码器的结果及是否超时

    m_pAffair->start();
    m_pCAffairComplexCompose = new CAffairComplexCompose(this);
    connect(this, &CMainWindow::sigComplexComposeResult,
            m_pCAffairComplexCompose, &CAffairComplexCompose::slotReciveComplexResult);
    m_pCAffairComplexCompose->start();
    CErrorHandler::getInstance().start();
    CErrorNotify::getInstance().start();
    CpuMonitor::getInstance().Start(60 * 1000); 
}

void CMainWindow::slotExtractScanRs(QString strInfo)   //格式（同一个试剂条“，”，不同的“|”）：试剂条序号，扫码状态（2：Status_ScanFinish   3：Status_ScanFail），条码信息|
{
    QStringList listResult;
    for (size_t i = 0; i < STRIP_MAX_SIZE; i++)
    {
        listResult.append("");
    }
    
    QStringList extractFields = strInfo.split("|");
    for (auto& extractField : extractFields)
    {
        QStringList fieldList = extractField.split(",");
        if(fieldList.length() == 3 && !fieldList[0].isEmpty())
        {
            QString strBarcode = fieldList[2];//条码信息
            listResult[fieldList[0].toUInt()]=strBarcode;
        }
    }
    QString strResult = listResult.join(",");
    COperationUnit::getInstance().sendStringData(Method_extract_qrcode, strResult, Machine_UpperHost);

    //m_pAffair->ActionAddExtractScanMotorInit();// 无需复位，复位 动作在不应在结果复位
    qDebug()<<__FUNCTION__<<"ExtractScanRs="<<strInfo<<strResult;
}


void *CMainWindow::_CreateThreadHandleList(void *arg)
{
    CMainWindow* pCMainWindow = (CMainWindow*)arg;
    while(pCMainWindow->m_bThreadExit)
    {
        if(pCMainWindow->m_iReadIndex.load() != pCMainWindow->m_iWriteIndex.load())
        {
            pCMainWindow->_HandleReceiveList();
        }
        else
        {
            ::usleep(10*1000);
        }
    }
    return NULL;
}

void CMainWindow::_loadConsumable(quint8 uiType, quint8 uiIndex,quint16 ui16Delay)
{
    qDebug()<<"_loadConsumable:"<<uiType<<uiIndex;
    if(uiType<=CT_REAGENT)
    {
        RFIDConsumableType cType =  CRFIDCtrl::getInstance().ConverIdxToConsumableType(uiType, uiIndex);
        CRFIDTask task;
        task.iType = cType;
        task.iMethod = Method_rfid_read;
        task.ui16Delay = ui16Delay;
        CRFIDMotionTask::getInstance().AddTask(task);
    }
    else if(uiType == CT_RECYCLE_BIN)
    {
        CRecycleBin::frontBin().reload();
        CRecycleBin::backBin().reload();
    }
}

void CMainWindow::_unloadConsumable(quint8 uiType, quint8 uiIndex)
{
    qDebug()<<"_unloadConsumable:"<<uiType<<uiIndex;
    if(uiType<CT_MAX)
    {
        Consumables::getInstance().RemoveConsumableBox(uiType, uiIndex);
    }
    else if(uiType == CT_REAGENT)
    {
        Reagent::getInstance().RemoveReagentBox(uiIndex);
    }
    else if(uiType == CT_RECYCLE_BIN)
    {
        CRecycleBin::frontBin().unload();
        CRecycleBin::backBin().unload();
    }
}

void CMainWindow::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);
            m_iCmdID = *((quint8*)qMessage.data() + gk_iCmdIDPos);
            m_iSourceID = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_pFramePos = qMessage.data() + gk_iLengthPos;
            m_iReadPayloadLength = GetByte2Int(m_pFramePos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            if (Method_heart_beat != m_iMethodID)
            {
                qDebug() << "m_iMethodID" << m_iMethodID << qMessage.toHex(':').toUpper();
            }
            switch (m_iMethodID)
            {
            case Method_heart_beat:
            {
                COperationUnit::getInstance().sendResult(Method_heart_beat, Machine_UpperHost);
                break;
            }
            case Method_rtc:
            {
                QDateTime qSystemDateTIme = QDateTime::fromTime_t(m_qPayloadString.toUInt());
                CGlobalConfig::getInstance().setSystemTime(qSystemDateTIme);
                COperationUnit::getInstance().sendResult(Method_rtc, Machine_UpperHost);
                // 向下继续发送时间
                QStringList strRtcTimeList;
                strRtcTimeList << m_qPayloadString;
                COperationUnit::getInstance().sendDataList(Method_rtc, strRtcTimeList, Machine_Function_manager_Ctrl);
                COperationUnit::getInstance().sendDataList(Method_rtc, strRtcTimeList, Machine_Motor_1);
                COperationUnit::getInstance().sendDataList(Method_rtc, strRtcTimeList, Machine_Motor_2);
                COperationUnit::getInstance().sendDataList(Method_rtc, strRtcTimeList, Machine_Motor_3);
                COperationUnit::getInstance().sendDataList(Method_rtc, strRtcTimeList, Machine_PCR_MainCtrl);
                break;
            }
            case Method_upgrade_req:
            {// 上位机发送指令-中位机，中位机读取U盘文件进行升级操作
                // 当前为针对单独升级，一键升级后期再做考虑
                qDebug()<<"enter Method_upgrade_req,payload="<<m_qPayloadString;
                if(m_qPayloadString.toInt()==-1)  //-1 一键升级
                {
                    emit sigExtractAutoupgradeZipFile();
                }
                else
                {
                    CUpgradeCtrl::getInstance().UpgradeReq(m_qPayloadString,0);
                    COperationUnit::getInstance().sendStringResult(Method_upgrade_req, "", Machine_UpperHost, 0);
                }
                break;
            }
            case Method_upgrade_data:
            {// 升级数据
               // qDebug()<<"enter Method_upgrade_data,payload="<<m_qPayloadString;
                if(m_iCmdID ==0x01)
                {
                    CUpgradeCtrl::getInstance().UpgradData(m_iSourceID,m_qPayloadString);
                }
                break;
            }
            case Method_Stop_Upgrade:
            {
                qDebug()<<"enter Method_Stop_Upgrade,payload="<<m_qPayloadString;
                CUpgradeCtrl::getInstance().SetStopUpgradeFlag(true);
                break;
            }
            case Method_board_info:
            {
                qDebug()<<__FUNCTION__<<"enter mainwindow Method_board_info,payload="<<m_qPayloadString<<",m_iSourceID="<<m_iSourceID;
                if(m_iSourceID==Machine_UpperHost)
                {
                    // 获取中位机版本号
                    if (m_qPayloadString.toUInt() == Machine_Middle_Host)
                    {
                        QString strVersion = "unknown";
                    #ifdef SServer_Version
                        strVersion = QString(APP_FULL_VERSION);
                    #endif                        
                        QStringList strMsg = QStringList{QString::number(Machine_Middle_Host), strVersion};
                        COperationUnit::getInstance().sendDataList(Method_board_info, strMsg, Machine_UpperHost);
                        break;
                    }
                    //下发到下位机
                    COperationUnit::getInstance().sendStringData(Method_board_info, "", m_qPayloadString.toUInt());
                };
                if(m_iSourceID !=Machine_UpperHost)//不是上位机发来的
                {
                    //上传结果
                  //  QString strMsg= QString::number(m_iSourceID)+","+m_qPayloadString;
                    QStringList payloadlist = m_qPayloadString.split(",");
                   QString strVersion ;
                    if(payloadlist.size()==2)
                    {
                        strVersion = payloadlist[1];
                    }
                    else
                    {
                        strVersion =payloadlist[0];
                    }

                    QStringList strMsg = QStringList{QString::number(m_iSourceID), strVersion};
                    COperationUnit::getInstance().sendDataList(Method_board_info, strMsg, Machine_UpperHost);
                   emit sigBoardVersionInfo(EnumMachineID(m_iSourceID),strVersion);
                };
                break;
            }
            case Method_config_extract:
            {// 设置提取时序参数
                QStringList strConfigList = m_qPayloadString.split(",");
                if(strConfigList.length() > 1)
                {
                    CSystemDB::getInstance().addKeyValue("extract_h_value", strConfigList.at(0));// 提取高度H值
                    CSystemDB::getInstance().addKeyValue("extract_mix_percentage", strConfigList.at(1));// 混匀百分比
                }
                break;
            }
            case Method_extract_start:
            {// 提取开始
                QString strExtractUIContent =
                        CTimingDB::getInstance().getExtractTimingUIContentFromName(m_qPayloadString);
                m_pAffair->ExtractModuleDebugStart(strExtractUIContent);
                // // 解耦，应对UI-Motor规则改变，db没有改变
                // QString strExtractMotorContent = CExtractParse::getInstance().getExtractMotorContentFromUIContent(strExtractUIContent);//getExtractMotorContentFromUIContent(strExtractUIContent);
                // //下发保存新的提取时序给下位机
                // QString strExtractCmdContent = QString("%1,%2,Extract,NoCondtion;").arg(Machine_Motor_4).arg(Action_Extract) + strExtractMotorContent;
                // COperationUnit::getInstance().sendStringData(Method_set_comp_cmd, strExtractCmdContent, Machine_Motor_4);
                // qDebug()<<"Send Extract cmd:"<<strExtractCmdContent;
                // QThread::msleep(500);//延时500ms,下位机需要写入
                // //启动业务动作提取指组合令
                // QString strExtractCmd = QString::number(Action_Extract);
                // COperationUnit::getInstance().sendStringData(Method_comp_cmd, strExtractCmd, Machine_Motor_4);
                break;
            }
            case Method_extract_stop:
            {// 提取停止

                break;
            }
			case Method_strip_codescan_test:
            {
                if(m_qPayloadString=="1")//开始
                {
                    m_pAffair->ExtractScanTest_use();  //lxj
                }
                else
                {
                     m_pAffair->StopExtractScanTest_use();
                }
                break;
            }
            case Method_start_test:
            {
                break;
                //m_pAffair->ExtractScanTest();  //lxj
                //QString strRsData;
                // CCommunicationObject::getInstance().sendStartScanCmdToZebraScanner(strRsData);
                //m_pAffair->PCRMachineClear();
                // break;

                static bool bTestStart = false;
                if(bTestStart)
                {
                    CCommunicationObject::getInstance().StopSamplerCodeScanner();
                }
                else{
                    CCommunicationObject::getInstance().StartSamplerCodeScanner();
                }
                bTestStart = !bTestStart;
                break;
            }
            case Method_daily_monitor:
            {
                qDebug()<<__FUNCTION__<<"Method_daily_monitor,payload="<<m_qPayloadString;
                m_pAffair->StartDailyMonitor(m_qPayloadString);
                break;
            }
            case Method_daily_monitor_warn:
            {
                qDebug()<<__FUNCTION__<<"Method_daily_monitor_warn,payload="<<m_qPayloadString<<",m_iSourceID="<<m_iSourceID<<",m_iDestinationID="<<m_iDestinationID;
                if(m_iSourceID == Machine_UpperHost)  //设置的
                {
                    m_pAffair->DailyMonitorWarnSet(m_qPayloadString);
                }
               //上报结果在 Method_error_info分支
                break;
            }
            case Method_machine_clean:
            {
                int iTask = m_qPayloadString.toInt();
                qDebug()<<"enter Machine clean Cmd,m_qPayloadString="<<m_qPayloadString;
                if(iTask ==0 )
                {
                    qDebug()<<"enter PCR Cmd";
                }
                MaintainSubSystem::getInstance().SelfTestCleanPCR(ST_PCR_CLEAN);
                break;
            }
            case Method_uv_open:   //紫外灯的开关
            {
                qDebug()<<"enter Method_uv_open,m_qPayloadString="<<m_qPayloadString;
                QStringList strList = m_qPayloadString.split(",");
                if(strList.size()!=2)
                {
                    qDebug()<<"Method_uv_open error ,m_qPayloadString="<<m_qPayloadString;
                }
                else
                {
                    m_pAffair->UVLightCtrl(strList[0].toInt(), strList[1].toInt());
                }
                break;
            }
            case Method_extract_read:
            {// 读取提取数据
                qDebug() << "Method_extract_read "  << m_qPayloadByteArray;
                QString strReadDBString = CTimingDB::getInstance().getAllExtractTimingData();
                qDebug() << "strReadDBString " << strReadDBString;
                COperationUnit::getInstance().sendStringResult(Method_extract_read, strReadDBString,
                                                               Machine_UpperHost);
                break;
            }
            case Method_extract_write:
            {// 写提取数据库
                qDebug() << "Method_extract_write "  << m_qPayloadString;
                QStringList strPayloadList =  m_qPayloadString.split("+");
                int iPayloadListCount = strPayloadList.length();
                QStringList strWriteTimingList;
                quint8 quResult = 0x01;
                for(int i = 0; i < iPayloadListCount; ++i)
                {
                    strWriteTimingList = strPayloadList.at(i).split(":");
                    if(strWriteTimingList.length() == 2)
                    {
                        if(strWriteTimingList.at(1).isEmpty())
                        {
                            CTimingDB::getInstance().deleteExtractTimingFromName(strWriteTimingList.at(0));
                        }
                        else
                        {
                            CTimingDB::getInstance().addExtractTiming(strWriteTimingList.at(0),
                                                                          strWriteTimingList.at(1),
                                                                          CExtractParse::getInstance().getExtractMotorContentFromUIContent(strWriteTimingList.at(1)));
                        }
                        quResult = 0x00;
                    }
                }
                COperationUnit::getInstance().sendResult(Method_extract_write, Machine_UpperHost, quResult);
                break;
            }
            case Method_set_process_cmd:
            {// 保存下发流程时序
                qDebug() << "Method_set_process_cmd "  << m_qPayloadString;
                int iNameIndex = m_qPayloadString.indexOf(';');
                if(iNameIndex > 1 && iNameIndex<m_qPayloadString.size())
                {
                    QString strName = m_qPayloadString.left(iNameIndex);
                    QString strValue = m_qPayloadString.right(m_qPayloadString.size()-iNameIndex-1);
                    CTimingInfoDB::getInstance().addProcessTiming(strName,strValue);
                }
                break;
            }
            case Method_start:
            {
                quint8 uiIndex = m_qPayloadString.indexOf(':');
                qDebug() << "Method_start:" << m_qPayloadString << uiIndex;
                if(uiIndex > 0)
                {
                    quint8 uiSeqType = m_qPayloadString.left(uiIndex).toUInt();
                    if(ST_SAMPLE_SCAN == uiSeqType)
                    {
                        QString strParams = m_qPayloadString.right(m_qPayloadString.length() - uiIndex -1);
                        m_pAffair->PreProcess(strParams);
                    }
                    else if(ST_TEST_PRE == uiSeqType)
                    {
                        m_pAffair->ResetResult(m_iResult);
                    }
                    else
                    {
                        m_pAffair->StartProcess(m_qPayloadString);
                    }
                }
                break;
            }
            case Method_pos_debug:
            {
                qDebug() << "Method_pos_debug: "  << m_qPayloadString<<m_iCmdID;
                MaintainSubSystem::getInstance().MotorPosDebug(m_qPayloadString);
                break;
            }
            case Method_aging_test:
            {

                break;
            }
            case Method_Start_pos_debug:
            {
                int iStatus =m_pAffair->SetPosDebugStatus();
                qDebug() << "Start Method_Start_pos_debug,status="<<iStatus;
                if(iStatus==0){MaintainSubSystem::getInstance().SetPosDebugRun(1);}
                COperationUnit::getInstance().sendStringResult(Method_Start_pos_debug, QString::number(iStatus), Machine_UpperHost);
                break;
            }
            case Method_Stop_pos_debug:
            {
                int iStatus =m_pAffair->StopPosDebugStatus();
                qDebug() << "stop Method_Stop_pos_debug,status="<<iStatus;
                if(iStatus==0){MaintainSubSystem::getInstance().SetPosDebugRun(0);}
                COperationUnit::getInstance().sendStringResult(Method_Stop_pos_debug, QString::number(iStatus), Machine_UpperHost);
                break;
            }
            case Method_pause:
            case Method_pause_process_cmd:
            {// 暂停流程时序
                if(m_iCmdID == CmdType_Command)
                {
                    m_pAffair->PauseProcess();
                }
                else if(m_iCmdID == CmdType_Reply)
                {
                    m_pAffair->updateDeviceCmdExecState(m_iSourceID, m_iResult);
                }
                break;
            }
            case Method_stop:
            case Method_stop_process_cmd:
            {// 停止流程时序
                if(m_iCmdID == CmdType_Command)
                {
                    m_pAffair->StopProcess(true);
                }
                else if(m_iCmdID == CmdType_Reply)
                {
                    m_pAffair->updateDeviceCmdExecState(m_iSourceID, m_iResult);
                }
                break;
            }
            case Method_resume:
            {// 停止流程时序
                if(m_iCmdID == CmdType_Command)
                {
                    m_pAffair->ResumeProcess();
                }
                else if(m_iCmdID == CmdType_Reply)
                {
                    m_pAffair->updateDeviceCmdExecState(m_iSourceID, m_iResult);
                }
                break;
            }
            case Method_sample_process_cmd:
            {//  追加样本测试批次
                QStringList strPayloadList =  m_qPayloadString.split(",");
                int iPayloadListCount = strPayloadList.length();
                if(iPayloadListCount > 1)
                {
                    if(!strPayloadList[0].isEmpty() && !strPayloadList[1].isEmpty() )
                    {

                    }
                }
                break;
            }
            case Method_set_comp_cmd:
            {//
                qDebug() << "Method_set_comp_cmd "  << m_qPayloadString;
                int iNameIndex = m_qPayloadString.indexOf(';');
                if(iNameIndex > 1 && iNameIndex<m_qPayloadString.size())
                {
                    QString strIDName = m_qPayloadString.left(iNameIndex);
                    QStringList strIDNameList = strIDName.split(",");
                    QString strContent = m_qPayloadString.right(m_qPayloadString.size()-iNameIndex-1);
                    if(strIDNameList.length() >= 4)
                    {
                        CTimingInfoDB::getInstance().addComplexTiming(strIDNameList[0],
                                strIDNameList[3], strIDNameList[2], strIDNameList[1], strContent);
                    }
                }
                break;
            }
            case Method_comp_cmd_st:
            {//  业务动作-复合指令组合状态
                QStringList strUnitConditionList = m_qPayloadString.split(",");
                if(strUnitConditionList.length() >= 2)
                {
                    int uiUnit = strUnitConditionList.at(0).toInt();
                    int uiIndex = strUnitConditionList.at(1).toInt();
                    qDebug()<<"---------Recevie cmd state unit:"<<uiUnit<<" uiIndex:"<<uiIndex;
                    m_pAffair->slotAddReciveSTCondition(uiUnit, uiIndex);
                }
                break;
            }
            case Method_comp_cmd:
            {//  业务动作-复合指令-执行
                qDebug()<<"CMainWindow Deal with Method_comp_cmd:"<<m_qPayloadString;
                QStringList strParams = m_qPayloadString.split(",");
                if(strParams.length() >= 1)
                {
                    quint16 iCmdID = strParams.at(0).toInt();
                    qDebug()<<"---------Recevie comp cmd  id:"<<iCmdID
                           << m_pCAffairComplexCompose->getIsRunning()
                           << m_iResult;
                    qDebug()<<"ScanMode: "<<m_pAffair->GetExtractScanMode();
                    
                    m_pAffair->ExtractModuleDebugActionCmdReply(iCmdID, m_iResult);//单独执行提取动作
                    m_pAffair->slotAddActionCmdReply(iCmdID, m_iResult);
                    if(m_pCAffairComplexCompose->getIsRunning())
                    {
                        if(!m_iResult)
                        {
                            sigComplexComposeResult(strParams.at(0));
                        }
                        else
                        {
                            qWarning() << "complex run erro" << iCmdID
                                       << m_iResult << m_pCAffairComplexCompose->getIsRunning();
                            m_pCAffairComplexCompose->setFinished();
                            COperationUnit::getInstance().sendResultList(Method_complex_compose_run,
                                                                         QStringList({strParams.at(0)}), Machine_UpperHost,  m_iResult);
                        }
                    }
                }
                break;
            }
            case Method_complex_compose_run:
            {// 执行业务组合指令
                if(!m_pCAffairComplexCompose->getIsRunning())
                {
                    m_pCAffairComplexCompose->startComplexCompose(m_qPayloadString);
                }
                else
                {
                    qDebug() << "CAffairComplexCompose is running, please stop first!";
                }
                break;
            }
            case Method_complex_compose_stop:
            {// 执行业务组合指令i
                if(m_pCAffairComplexCompose->getIsRunning())
                {
                    m_pCAffairComplexCompose->stopComplexCompose();
                }
                break;
            }
            case Method_TEC_PCR_StartOrStop:
            case Method_TEC_PCR_SignalReport:
            case Method_FLCYEND:
            case Method_pcr_tec_table_req:
            case Method_pcr_tec_table_data:
            case Method_pcr_tec_table_end:
            case Method_TEC_RequestTransmitTimingTable:
            case Method_TEC_TransmitTimingData:   
            case Method_TEC_TransmitTimingEnd:
            case Method_FLGAIN:         
            {
                m_pAffair->slotPCRCmdReply(m_iSourceID,m_iMethodID, m_iResult, m_qPayloadString);
                break;
            }
            case Method_FLMDT:
            {
                m_pAffair->slotFLDataCmdReply(m_qPayloadByteArray);
                break;
            }
            case Method_tecdb_write:
            {
                QStringList strParams = m_qPayloadString.split("+");
                if(strParams.length() >= CTimingDB::getInstance().getTecFieldCount())
                {
                    CTimingDB::getInstance().addTecTimingFromList(strParams);
                }
                break;
            }
            case Method_tecdb_delete:
            {
                CTimingDB::getInstance().deleteTecContentFromName(m_qPayloadString);
                break;
            }
            case Method_projectdb_read:
            {
                QString strPorjectLot = m_qPayloadString;
                QStringList strProjectInfo = CProjectInformation::getInstance().getProjectInfoStringFromProjectLot(strPorjectLot);
                qDebug() << "strProjectInfo " << strProjectInfo
                         << strProjectInfo.join("+").toLocal8Bit();
                COperationUnit::getInstance().sendStringData(Method_projectdb_read,
                                                             strProjectInfo.join("+"), Machine_UpperHost);
                break;
            }
            case Method_projectdb_write:
            {
                QStringList strProjectInfoList = m_qPayloadString.split("+");
                CProjectInformation::getInstance().addOneProjectInfo(strProjectInfoList);
                break;
            }
            case Method_projectdb_delete:
            {
                QString strPorjectLot = m_qPayloadString;
                CProjectInformation::getInstance().deleteProjInfoFromProjectLot(strPorjectLot);
                break;
            }
            case Method_pcr_size_type:
            {
                //params:[type] type:PCR扩增孔类型，32孔= 0, 64孔 = 1
                quint8 uiSizeType = m_qPayloadString.trimmed().toUInt();
                PCRResource::getInstance().SetPCRSizeType(uiSizeType);
                break;
            }
            case Method_set_consumable:
            {
                //params:[type,index,remain]
                //type：耗材类型，有3个选项, TIP = 0, TUBE(扩增管) = 1, CAP(扩增管帽) = 2
                //index:耗材索引,有两个选项(0,1)
                //remain:耗材剩余数量（其中Tip最大96，另外两个最大48）
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 3)
                {
                    quint8 uiType = strParamsList[0].toUInt();
                    quint8 uiIndex = strParamsList[1].toUInt();
                    quint8 uiRemain = strParamsList[2].toUInt();
                    if(uiType<CT_MAX)
                    {
                        Consumables::getInstance().AddVirtualConsumable(uiType, uiIndex, uiRemain);
                    }
                    else if(uiType == CT_RECYCLE_BIN)
                    {
                        if(uiIndex == RBT_FRONT_BIN)
                        {
                            CRecycleBin::frontBin().setCapacity(uiRemain);
                        }
                        else if(uiIndex == RBT_BACK_BIN)
                        {
                            CRecycleBin::backBin().setCapacity(uiRemain);
                        }
                        else
                        {
                            QDFUN_LINE<<"Method_set_consumable recycle bin index params invalid :"<<uiIndex;
                        }
                    }

                }
                break;
            }
            case Method_set_reagent:
            {
                //params:[index, projId, componentNum, remain]
                //index:试剂列索引,有4个选项(0,1,2,3)
                //projId:项目ID，取自于当前已添加项目
                //componentNum:组分数量，根据选定的项目决定
                //remain:试剂剩余数量（单组分最大96，2组分最大48，3-4组分最大24，5组分以上最大12）
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 4)
                {
                    quint8 uiIndex = strParamsList[0].toUInt();
                    QString strProjId = strParamsList[1];
                    quint8 uiComponentNum = strParamsList[2].toUInt();
                    quint8 uiRemain = strParamsList[3].toUInt();
                    Reagent::getInstance().AddVirtualReagentBox(uiIndex, strProjId, uiComponentNum, uiRemain);
                }
                break;
            }
            case Method_load_consumable:
            {
                //params:[type,index]
                //type：耗材类型，有5个选项, TIP = 0, TUBE(扩增管) = 1, CAP(扩增管帽) = 2,  REAGENT（试剂） = 3，回收仓 = 4
                //index: 耗材索引，其中Tip，Tube，cap有2盒，Reagent有4条，回收仓有2个（0：Tip回收仓，1：PCR管回收仓）
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    quint8 uiType = strParamsList[0].toUInt();
                    quint8 uiIndex = strParamsList[1].toUInt();
                    _loadConsumable(uiType, uiIndex,RFID_DELAY_TIME_MS);
                }
                break;
            }
            case Method_unload_consumable:
            {
                //params:[type,index]
                //type：耗材类型，有3个选项, TIP = 0, TUBE(扩增管) = 1, CAP(扩增管帽) = 2,  REAGENT（试剂） = 3，回收仓 = 4
                //index: 耗材索引，其中Tip，Tube，cap有2盒，Reagent有4条，回收仓有2个（0：Tip回收仓，1：PCR管回收仓）
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() ==2)
                {
                    quint8 uiType = strParamsList[0].toUInt();
                    quint8 uiIndex = strParamsList[1].toUInt();
                    _unloadConsumable(uiType, uiIndex);
                }
                break;
            }
            case Method_enough_consumable:
            {
                //params:[项目1,项目2;项目1;项目1....]
                QList<QString> qSampleInfoList;
                QStringList strParamsList = m_qPayloadString.split(";");
                foreach(QString strParam, strParamsList)
                {
                    qSampleInfoList.append(strParam);
                }
                
                // 计算当前样本需要使用的PCR孔位数量
                PCRResource::getInstance().CalcCurSampleUsePCRHoles(qSampleInfoList);
                CConsumableReservation cConsumableReservation;
                QString strResult = cConsumableReservation.getReservateResult(qSampleInfoList);
                COperationUnit::getInstance().sendStringData(Method_enough_consumable, strResult, Machine_UpperHost);
                break;
            }   
            case Method_get_pcr_time:
            {
                quint8 u8HoleNum = PCRResource::getInstance().GetCalcCurSampleUsePCRHoles();//获取当前样本需要使用的PCR孔位数量(即使用pcr孔位)
                qint64 secondsRemaining =  PCRResource::getInstance().CaclNextBatchWaitTime(u8HoleNum);
                QString strInfo = PCRResource::getInstance().GetPCRAreaRemainTimeRecord();
                QString strResult = QString("%1:%2").arg(secondsRemaining).arg(strInfo);
                COperationUnit::getInstance().sendStringData(Method_get_pcr_time, strResult, Machine_UpperHost);
                break;
            }           
            case Method_ftp_file_sync:
            {// 覆盖
                qDebug() << "has ftp finished, to sync";
                break;
            }
            case Method_extract_heater_start:
            {
                qDebug()<<"Send heater start cmd to function board."<<m_qPayloadString;
                COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_StartHeating, m_qPayloadString, Machine_Function_manager_Ctrl);
                break;
            }
            case Method_extract_heater_stop:
            {
                qDebug()<<"Send heater stop cmd to function board."<<m_qPayloadString;
                COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_StopHeating, m_qPayloadString, Machine_Function_manager_Ctrl);
                break;
            }
            case Method_error_info:
            {
                //错误处理
                QDFUN_LINE<<"Method_error_info"<<m_qPayloadString;
                CErrorHandler::getInstance().slotAddReciveMsg(m_qPayloadString.toLocal8Bit());
                COperationUnit::getInstance().sendStringData(Method_error_info, m_qPayloadString, Machine_UpperHost);
                m_pAffair->ReportMonitorWarnError(m_qPayloadString);  //还要从ID 中筛选温度报警的，给上位机发送
                break;
            }
            case Method_systemdb_add:
            {
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    CSystemDB::getInstance().addKeyValue(strParamsList.at(0), strParamsList.at(1));// 設置公共配置key和value
                    if (strParamsList.at(0) == "Middle_Log_Terminal")
                    { // 中端日志终端
                        CSpdLogger::modifyConfig("console_output", strParamsList.at(1).toInt() > 0 ? "true" : "false");
                    }
                }
                break;
            }
            case Method_dev_comp_status:
            {
                CDevStatus::getInstance().setDevCompStatus(m_qPayloadString);
                break;
            }
            case Method_FeatMngBoard_TipTrayLockSensorStatus:
            {
                qDebug()<<"Method_FeatMngBoard_TipTrayLockSensorStatus "<<m_qPayloadString;
                quint8 uiType = CT_TIP;
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    quint8 uiIndex = strParamsList[0].toUInt();
                    quint8 uiState = strParamsList[1].toUInt();
                    if(uiState == 1)
                    {
                        _loadConsumable(uiType, uiIndex,RFID_DELAY_TIME_MS);                     
                    }
                    else
                    {
                        _unloadConsumable(uiType, uiIndex);
                    }
                }
                break;
            }
            case Method_FeatMngBoard_PCRTrayLockSensorStatus:
            {
                qDebug()<<"Method_FeatMngBoard_PCRTrayLockSensorStatus "<<m_qPayloadString;
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    quint8 uiIndex = strParamsList[0].toUInt();
                    quint8 uiState = strParamsList[1].toUInt();
                    if(uiState == 1)
                    {
                        _loadConsumable(CT_TUBE, uiIndex,RFID_DELAY_TIME_MS);
                        // _loadConsumable(CT_CAP, uiIndex,800); // 多次读取，回造成can重发                        
                    }
                    else
                    {
                        _unloadConsumable(CT_TUBE, uiIndex);
                        _unloadConsumable(CT_CAP, uiIndex);
                    }
                }
                break;
            }
            case Method_FeatMngBoard_ReagentBarLockSensorStatus:
            {
                qDebug()<<"Method_FeatMngBoard_ReagentBarLockSensorStatus "<<m_qPayloadString;
                quint8 uiType = CT_REAGENT;
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    quint8 uiIndex = strParamsList[0].toUInt();
                    quint8 uiState = strParamsList[1].toUInt();
                    // uiIndex光耦标记， 0-试剂条1（右边起），1-试剂条2，2-试剂条3，3-试剂条4
                    if(uiState == 1)
                    {
                        _loadConsumable(uiType, uiIndex,RFID_DELAY_TIME_MS);
                    }
                    else
                    {
                        _unloadConsumable(uiType, uiIndex);
                    }
                }
                break;
            }
            case Method_FeatMngBoard_NotifyLockStateChange:
            {
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 3)
                {
                    quint8 uiAreaType = strParamsList[0].toUInt();
                    quint8 uiIndex = strParamsList[1].toUInt();
                    quint8 uiState = strParamsList[2].toUInt();
                    //                    [区块号, 区块内索引号，光耦状态]
                    //                    区块号：1-样本架、2-TIP架、3-PCR管架、4-冷藏仓试剂架
                    //                    索引号：从右到左对应的不同类型索引号
                    //                    状态：0-无效（未触发），1-有效（触发）
                    if(uiAreaType>=AT_TIP && uiAreaType<=AT_REAGENT)
                    {
                        if(uiAreaType == AT_TUBECAP)
                        {
                            if(uiState == 1)
                            {
                                _loadConsumable(CT_TUBE, uiIndex);
                                _loadConsumable(CT_CAP, uiIndex);
                            }
                            else
                            {
                                _unloadConsumable(CT_TUBE, uiIndex);
                                _unloadConsumable(CT_CAP, uiIndex);
                            }
                        }
                        else if(uiAreaType == AT_TIP || uiAreaType == AT_REAGENT)
                        {
                            quint8 uiType = CT_TIP;
                            if(uiAreaType == AT_REAGENT)
                            {
                                uiType = CT_REAGENT;
                            }
                            if(uiState == 1)
                            {
                                _loadConsumable(uiType, uiIndex);
                            }
                            else
                            {
                                _unloadConsumable(uiType, uiIndex);
                            }
                        }
                    }
                }
                break;
            }
            case Method_FeatMngBoard_ExtractModuleLockSensorStatus:
            {
                //需要判断光耦变化
                qDebug()<<"Method_FeatMngBoard_ExtractModuleLockSensorStatus "<<m_qPayloadString;
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    // 参数1：光耦标记，0-提取模块
                    // 参数2：光耦状态，0-无效，1-有效   
                    bool bStatus = false;                 
                    quint8 uiType = strParamsList[0].toUInt();
                    quint8 uiStatus = strParamsList[1].toUInt();  
                    if (1 == uiStatus)// 光耦到位之后，需要先检测卡条光耦
                    {
                       bStatus = true;
                       COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_ExtractBarPositionStatusCheck, "", Machine_Function_manager_Ctrl);                       
                    }                                
                }
                break;
            }            
            case Method_FeatMngBoard_ExtractBarPositionStatusCheck:
            {
                //获取卡条光耦信息，提取条扫码由测试界面点击开始时，同时进行样本和提取条扫码
                qDebug()<<"Method_FeatMngBoard_ExtractBarPositionStatusCheck "<<m_qPayloadString;
                QStringList strParamsList = m_qPayloadString.split(",");
                uint16_t uiValue = strParamsList[0].toUInt();
                CStrip::getInstance().SetMagneticRodSleevePositionAll(uiValue);
                m_pAffair->SendSimulateExtractScanResult();//直接更新提取条信息
                break;
            }
            case Method_Disable_PCR:// PCR禁区域
            {
                QMetaEnum metaFieldType = QMetaEnum::fromType<SystemConfig::EnumPcrType>();
                QString strField = metaFieldType.valueToKey(static_cast<int>(SystemConfig::pcr_disable_area));                
                if (m_qPayloadString.isEmpty())//没发参数代表获取
                {
                    QString strValue = CSystemDB::getInstance().getStringValueFromKey(strField); 
                    COperationUnit::getInstance().sendStringData(Method_Disable_PCR, strValue, Machine_UpperHost); 
                }
                else
                {
                    PCRResource::getInstance().SetDisablePcrArea(m_qPayloadString);
                    CSystemDB::getInstance().addKeyValue(strField,m_qPayloadString);  
                }

                break;
            }
            case Method_Disable_PCR_HOLE: //PCR禁孔
            {
                QMetaEnum metaFieldType = QMetaEnum::fromType<SystemConfig::EnumPcrType>();
                QString strField = metaFieldType.valueToKey(static_cast<int>(SystemConfig::pcr_disable_area_hole));                
                if (m_qPayloadString.isEmpty())//没发参数代表获取
                {
                    QString strValue = CSystemDB::getInstance().getStringValueFromKey(strField); 
                    COperationUnit::getInstance().sendStringData(Method_Disable_PCR_HOLE, strValue, Machine_UpperHost); 
                }
                else
                {
                    CSystemDB::getInstance().addKeyValue(strField,m_qPayloadString);
                    PCRResource::getInstance().SetDisablePcrArea(m_qPayloadString); 
                    PCRResource::getInstance().LoadValidPcrAreaHole();  
                }
                break;
            }              
            case Method_AllOptoStatus:
            {
                if (m_iSourceID == 0x04)    //板卡3
                {
                    m_pAffair->SetMotor3AllOptoStatus(m_qPayloadString.toUInt());            
                }
                else if (m_iSourceID == 0x02)//板卡1
                {

                }
                else
                {
                    
                }
                break;
            }  
            case Method_pcr_program_version:
            {
//                bool bVersion = (m_qPayloadString == "1")?true:false;
//                m_pAffair->SetPcrProgramVersion(bVersion);
                break;
            } 
            case Method_RLCFG:
            {
                qDebug()<<"Method_RLCFG "<<m_qPayloadString;
                QStringList strParamsList = m_qPayloadString.split(",");
                if(strParamsList.size() == 2)
                {
                    // 参数1：光耦标记，0-提取模块
                    // 参数2：光耦状态，0-无效，1-有效   
                    bool bStatus = false;                 
                    quint8 uiType = strParamsList[0].toUInt();
                    quint8 uiStatus = strParamsList[1].toUInt();
                    if (Led::EnumMoudle::Strip_Box == static_cast<Led::EnumMoudle>(uiType))
                    {
                        Led::EnumTriggerState state = Led::EnumTriggerState::OFF;
                        qDebug()<<"EnumTriggerState "<<uiType<<uiStatus;
                        if (1 == uiStatus)// 光耦到位之后，需要先检测卡条光耦
                        {
                           bStatus = true;
                           state = Led::EnumTriggerState::ON;
                           COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_ExtractBarPositionStatusCheck, "", Machine_Function_manager_Ctrl);                       
                        }    
                        HalSubSystem::getInstance().SetLedTrigger(state,Led::EnumMoudle::Strip_Box,Machine_Function_manager_Ctrl);                               
                        break;
                    }
                    Led::EnumTriggerState state = Led::EnumTriggerState::ON;
                    Led::EnumMoudle module = Led::EnumMoudle::Sample2_Left;
                    if (0 == uiStatus)// 光耦到位之后，需要先检测卡条光耦
                    {
                        state = Led::EnumTriggerState::OFF;   
                    } 
                    if (1 == uiType)
                    {
                        module = Led::EnumMoudle::Sample1_Right;
                    }
                    HalSubSystem::getInstance().SetLedTrigger(state,module,Machine_Function_manager_Ctrl);                              
                }                
                break;
            }
            case Method_status:
            {
                qDebug()<<"Method_status "<<m_qPayloadString;
                // 需要通过magic_enum把枚举的字符串转为枚举RunStat，再通过switch判断系统状态
                auto runStatOpt = magic_enum::enum_cast<RunStat>(m_qPayloadString.toStdString());
                if (runStatOpt.has_value())
                {
                    RunStat runStat = runStatOpt.value();
                    m_pAffair->UpdateRunStat(runStat);
                }
                break;
            }        
            case Method_Notification_SampleExist:
            {
                qDebug()<<"Method_Notification_SampleExist "<<m_qPayloadString;
                CCommunicationObject::getInstance().SetSampleCodeScanAllTubeExist(m_qPayloadString);
                break;
            }            
            default:
            {
                qDebug()<<"default Method:"<<m_iMethodID<<" "<<m_qPayloadString;
            }
                break;
            }
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}


