#include "cvirtualwindow.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
#include "control/coperationunit.h"

CVirtualWindow::CVirtualWindow(QObject *parent) : CWindowObject(parent)/*,m_timer(new QTimer(this))*/
{
    //    connect(this, &CVirtualWindow::SigTimeoutSendResult, this,
    //            &CVirtualWindow::SlotTimeoutSendResult);
//    connect(m_timer, &QTimer::timeout, this, &CVirtualWindow::SlotTimeoutAction);
}

CVirtualWindow::~CVirtualWindow()
{
    m_bThreadExit = true;
}

void CVirtualWindow::SlotTimeoutSendResult(const QString &strParam, int iTimeoutMS)
{
    //    if(m_timer)
    //    {
    //        m_timer->setSingleShot(true);
    //        m_timer->start(iTimeoutMS);
    //        m_timer->setProperty("param", QVariant::fromValue(strParam)); // 存储参数供SlotTimeoutAction使用
    //    }

    QTimer::singleShot(iTimeoutMS, this, [this, strParam]() {
        // 这个lambda函数将在3秒后执行
        qDebug()<<"SlotTimeoutAction";
        // 执行延迟后的操作，发送结果
        qDebug() << "Timeout action triggered with param:" << strParam;
        COperationUnit::getInstance().sendStringData(this->m_iMethodID,
                                                     strParam, Machine_Middle_Host,
                                                     0);
    });
}

//void CVirtualWindow::SlotTimeoutAction()
//{
//    qDebug()<<"SlotTimeoutAction";
//    QString strParam = m_timer->property("param").toString(); // 获取存储的参数
//    // 执行延迟后的操作，发送结果
//    qDebug() << "Timeout action triggered with param:" << strParam;
//    COperationUnit::getInstance().sendStringData(m_iMethodID,
//                                                 strParam, Machine_Middle_Host,
//                                                 0);
//}



void CVirtualWindow::_HandleReceiveList()
{
    QDFUN_LINE << "CVirtualWindow " << m_iReadIndex.load()
               << m_iWriteIndex.load();
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            m_pFramePos = qMessage.data() + gk_iLengthPos;
            m_iReadPayloadLength = GetByte2Int(m_pFramePos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");
            CGlobalConfig::getInstance().printMessageInfo(qMessage,
                                                          "[virtual->server] ");
            // 转发--用哪种通信方式，获取DestinationID，不修改SourceID
            switch (m_iMethodID)
            {
            case Method_GMCLK:
            {//
                COperationUnit::getInstance().sendResult(m_iMethodID, Machine_UpperHost);
                COperationUnit::getInstance().sendResultList(m_iMethodID,
                                                             QStringList({"0", "1"}), Machine_UpperHost);
                break;
            }
            case Method_comp_cmd:
            {
                QStringList strParams = m_qPayloadString.split(",");
                if(strParams.length() >= 1)
                {
                    qDebug() << "virtual complex cmd " << strParams;
                    if(strParams.at(0).toInt() == Action_Extract)
                    {
//                        emit SigTimeoutSendResult(m_qPayloadString, 60000);
                        QMetaObject::invokeMethod(this, "SlotTimeoutSendResult", Qt::QueuedConnection,
                                                  Q_ARG(QString, m_qPayloadString),
                                                  Q_ARG(int, 1000));
                    }
                    else
                    {
                        COperationUnit::getInstance().sendResultList(m_iMethodID,
                                                                     QStringList({strParams.at(0)}), Machine_Middle_Host,
                                                                     0);
                    }

                }
            }
            case Method_rfid_write:
            {
                COperationUnit::getInstance().sendResult(m_iMethodID, Machine_Middle_Host);
            }
            default:
                break;
            }//end of switch
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}
