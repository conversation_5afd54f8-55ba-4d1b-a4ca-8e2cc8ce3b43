#include "ccanbusdevicethread.h"

#include <QCanBus>
#include <QVariant>
#include <QDebug>
#include <QDataStream>
#include <QtMath>
#include <QTime>
#include "publicfunction.h"
#include "cglobalconfig.h"
#include "SystemConfig/SystemConfig.h"

CCanBusDeviceThread::CCanBusDeviceThread(QString strInterfaceName, QObject *parent)
    : QObject(parent)
    , m_strInterfaceName(strInterfaceName)
{
    qRegisterMetaType<QCanBusDevice::CanBusError>("QCanBusDevice::CanBusError");
    qRegisterMetaType<QVector<QCanBusFrame> >("QVector<QCanBusFrame>");

    memset(m_bWaitAck, 0, BUFFER_SIZE);
    m_iSeqNumber = -1;
    m_pReadFramesTimer = new QTimer();
    m_pReadFramesTimer->setSingleShot(false);//计时器循环此时true只循环一次，false无限循环
    m_pResendTimer = new QTimer();
    m_pResendTimer->setSingleShot(false);

    //
    m_pThread = new QThread();
    if(InitCanBusDevice())
    {
        this->moveToThread(m_pThread);
        m_pCanBusDevice->moveToThread(m_pThread);
        m_pReadFramesTimer->moveToThread(m_pThread);
        m_pResendTimer->moveToThread(m_pThread);

        connect(m_pReadFramesTimer,SIGNAL(timeout()),this,SLOT(_slotReadFramesTimer()));
        connect(m_pResendTimer,SIGNAL(timeout()),this,SLOT(_slotReSendTimer()));
        connect(this, &CCanBusDeviceThread::sigReadTimer,
                this, &CCanBusDeviceThread::_SlotStartTimer, Qt::QueuedConnection);

        m_pThread->start();
        emit sigReadTimer();
        emit sigNewConnect(true);
    }
    else
    {
        emit sigNewConnect(false);
    }
}

CCanBusDeviceThread::~CCanBusDeviceThread()
{
    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }
}

void CCanBusDeviceThread::slotWaitACK(quint16 iFrameNumber)
{
    m_bWaitAck[iFrameNumber] = true;
}

void CCanBusDeviceThread::slotSendAckBack(QByteArray qSendMsgAarry)
{
    _sendFrameData(qSendMsgAarry, true);// 保证写在同一个线程
}

void CCanBusDeviceThread::slotSendMessage(QByteArray qMsgBtyeArray)
{    
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        emit sigError(FT_Comm_CacheFull, "");
        return;
    }
    m_qSendMessageList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
}

void CCanBusDeviceThread::_slotCanBusError(QCanBusDevice::CanBusError error)
{
    qWarning() << "CanBus Error 000" << error;
    emit sigError(FT_Comm_OtherError, QString("CanBusErrorID:%1").arg(error));
}

void CCanBusDeviceThread::_slotReadFramesTimer()
{
    if(m_pCanBusDevice == nullptr)
    {
        return;
    }
    if (QThread::currentThread() != m_pThread) 
    {
        qDebug() << "Wrong thread for _slotReadFramesTimer";
        // return;
    }
    // 不用信号与槽是因为有时候缓冲区有数据，但是信号不发送出来
    if(m_pCanBusDevice->framesAvailable())
    {
        QVector<QCanBusFrame> qCanFramsVector = m_pCanBusDevice->readAllFrames();
#ifndef ShortOutPutLog
        // qDebug()<<"readAllFrames: "<<qCanFramsVector.size();
#endif
        sigReciveMessage(qCanFramsVector);

        quint32 _iMaxCanID = 0x1F;//由板的最大ID决定
        quint32 _iCanID = 0;
        QVector<QByteArray> _qbReadBuffArray;
        _qbReadBuffArray.resize(_iMaxCanID);
        for(quint16 i = 0; i != _iMaxCanID; ++i)
        {
            _qbReadBuffArray[i] = "";
            _qbReadBuffArray[i].reserve(1024 * 4);
        }
        QList<quint32> listCanId;
        for(int i = 0; i < qCanFramsVector.length(); ++i)
        {
            _iCanID = qCanFramsVector[i].frameId() - 0x20;
            if (_iCanID > _iMaxCanID)
            {
                qDebug() << "Error qCanFramsVector[i].frameId()" << i << qCanFramsVector[i].frameId();
                continue;
            }
            _qbReadBuffArray[_iCanID] += qCanFramsVector[i].payload();
            if (!listCanId.contains(_iCanID))
            {
                listCanId.append(_iCanID);
            }
        }
        // 遍历listCanId，打印
        for(int i = 0; i < listCanId.length(); ++i)
        {
            QString strType = (_qbReadBuffArray[listCanId[i]].count() > 6 && _qbReadBuffArray[listCanId[i]][6] == static_cast<char>(CmdType_Ack)) ? "ack" : "data";
            CGlobalConfig::getInstance().printMessageInfo(_qbReadBuffArray[listCanId[i]],
                                                          "[can ->server:" + QString("%1").arg(listCanId[i], 2, 10, QChar('0')) + " " + strType + "]");
            _qbReadBuffArray[listCanId[i]] = "";
        }
    }
    // 写
    if(m_iReadIndex.load() != m_iWriteIndex.load())
    {
        m_sCurrentSendMessage = m_qSendMessageList[m_iReadIndex.load()];
        _reSetFrameNumber(m_sCurrentSendMessage);
        _sendFrameData(m_sCurrentSendMessage);
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        // 重发
        if(m_sCurrentSendMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 0x05不做重发
            m_iCmdID  = *((quint8*)m_sCurrentSendMessage.data() + gk_iCmdIDPos);
            if(m_iCmdID != CmdType_Bulletin)
            {
                MessageInfo messageInfo;
                messageInfo.qSendMessageDataByteArray = m_sCurrentSendMessage;
                messageInfo.timestamp = QDateTime::currentMSecsSinceEpoch();
                m_pFramePos = m_sCurrentSendMessage.data() + gk_iSeqPos;
                messageInfo.iSeqNumber =  GetByte2Int(m_pFramePos);
                //
                m_iRingCurrentWriteIndex = m_iRingWriteIndex.load();
                m_iRingNextWriteIndex = (m_iRingCurrentWriteIndex + 1) % BUFFER_SIZE;
                //
                if (m_iRingNextWriteIndex == m_iRingReadIndex.load()) {
                    // 原则上不可能有65535个重发存在，故而不做考虑
                    qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-resnd tcp^^^^^^^^^^^^^";
                }
                m_bWaitAck[messageInfo.iSeqNumber] = false;
                m_sRingMessageInfoList[m_iRingCurrentWriteIndex] = messageInfo;
                m_iRingWriteIndex.store(m_iRingNextWriteIndex);
            }
        }
    }
    if(m_iReSendReadIndex.load() != m_iReSendWriteIndex.load())
    {// 重发队列
        // qInfo() << "---------can resend -----------" << m_strInterfaceName;
        QByteArray& qMessage = m_qReSendMessageList[m_iReSendReadIndex.load()];
        // 环形队列
        m_iReSendReadIndex.store((m_iReSendReadIndex.load() + 1) % BUFFER_SIZE);
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            qDebug()  << "resend Message_" << m_strInterfaceName << qMessage.toHex(':').toUpper();
            _sendFrameData(qMessage);
            emit sigError(FT_Comm_Resend, QString("resend message method id %1").arg(GetByte2Int(qMessage.data() + gk_iMethodIDPos)));
        }
    }
}

void CCanBusDeviceThread::_slotReSendTimer()
{
    m_iRingCurrentReadIndex = m_iRingReadIndex.load();
    if (m_iRingCurrentReadIndex != m_iRingWriteIndex.load())
    {
        const MessageInfo& messageInfo = m_sRingMessageInfoList[m_iRingCurrentReadIndex];
        if(!m_bWaitAck[messageInfo.iSeqNumber])
        {// 重发
            const qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
            if ((currentTime - messageInfo.timestamp) >= gk_iWaitMsecs)
            { // If the message is old, resend it
                m_iReSendCurrentWriteIndex = m_iReSendWriteIndex.load();
                m_iReSendNextWriteIndex = (m_iReSendCurrentWriteIndex + 1) % BUFFER_SIZE;

                if (m_iReSendNextWriteIndex == m_iReSendReadIndex.load())
                { // 原则上不可能有65535个重发存在，故而不做考虑
                    qWarning() << "m_qReSendMessageInfoList^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
                    emit sigError(FT_Comm_CacheFull, "");
                    return;
                }
                m_qReSendMessageList[m_iReSendCurrentWriteIndex] = messageInfo.qSendMessageDataByteArray;
                m_iReSendWriteIndex.store(m_iReSendNextWriteIndex);

                // qDebug() << __FUNCTION__ << __LINE__ << m_iRingCurrentReadIndex << m_iRingWriteIndex.load();
                m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
            }
        }
        else
        {
            m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
        }
    }
}

void CCanBusDeviceThread::_sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend)
{
    if(qSendMsgAarry.count()  < gk_iFrameLengthNotData || m_pCanBusDevice == nullptr)
    {
        qWarning() << "^^^^^^^^^^ERROR^^^^^^^"
                << qSendMsgAarry << qSendMsgAarry.count();
        emit sigError(FT_Comm_PacketLengthAbnormal, QString("sendMsgArray len:%d").arg(qSendMsgAarry.count()));
        return;
    }
    m_iCurrentPOrtDataByteArraySize = qSendMsgAarry.size();
    m_iTotalFrameNumbers = m_iCurrentPOrtDataByteArraySize / 8;
    m_iLastFrameCount = m_iCurrentPOrtDataByteArraySize % 8;

    m_uiCanID = qSendMsgAarry[gk_iDestinationIDPos];
    m_uiCanID = m_uiCanID;//qPow(2, m_uiCanID);// 设置帧ID为2的N此方

    m_qFrameForSend.setFrameId(m_uiCanID);
    for(int i = 0; i < m_iTotalFrameNumbers; ++i)
    {
        m_qFrameForSend.setPayload(qSendMsgAarry.mid(i*8, 8));
        if(!m_pCanBusDevice->writeFrame(m_qFrameForSend))
        {
            qWarning() << "write frame error" << m_pCanBusDevice->errorString();
        }
    }
    if(m_iLastFrameCount)
    {
        m_qFrameForSend.setPayload(qSendMsgAarry.mid(m_iTotalFrameNumbers*8, m_iLastFrameCount));
        if(!m_pCanBusDevice->writeFrame(m_qFrameForSend))
        {
            qWarning() << "write frame error" << m_pCanBusDevice->errorString();
        }
    }

    QString strType = bACKSend ? "ack" : "data";
    CGlobalConfig::getInstance().printMessageInfo(qSendMsgAarry,
                                                  "[server->" + m_strInterfaceName
                                                  + ":" + QString("%1").arg(m_uiCanID, 2, 10, QChar('0')) + " " + strType + "]");
}

void CCanBusDeviceThread::_reSetFrameNumber(QByteArray &qByteArray)
{// 帧号
    m_iSeqNumber++;
    if(m_iSeqNumber > 0xFFFF)
    {
        m_iSeqNumber = 0;
    }
    // 将 quint16 值转换为字节序列，你可以选择使用大端或小端
    if(qByteArray.count() >= gk_iFrameLengthNotData)// 帧长
    {
        QByteArray qBlockByteArray;
        QDataStream qOutDataStream
                (&qBlockByteArray,QIODevice::ReadWrite);
        qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
        qOutDataStream << quint16(m_iSeqNumber);
        qByteArray = qByteArray.replace(gk_iSeqPos, 2,  qBlockByteArray);
        quint16 iCrc16 = GetCRC16(qByteArray.data(), qByteArray.count()-2, 0);
        QByteArray qBlockByteArrayCrc;
        QDataStream qOutDataStreamCrc
                (&qBlockByteArrayCrc,QIODevice::ReadWrite);
        qOutDataStreamCrc.setByteOrder(QDataStream::BigEndian);  // 设置xiao端格式
        qOutDataStreamCrc << quint16(iCrc16);
        qByteArray = qByteArray.replace(qByteArray.count()-2, 2,  qBlockByteArrayCrc);
    }
}

bool CCanBusDeviceThread::InitCanBusDevice()
{
    m_sSCanBusDeviceStruct.strPlugin = "socketcan";
    m_sSCanBusDeviceStruct.strInterfaceName = m_strInterfaceName;
    m_sSCanBusDeviceStruct.iBitRate = 500*1000;

    // 读取配置文件信息
    auto& config = SystemConfig::getInstance();
    QMetaEnum metaFieldType = QMetaEnum::fromType<SystemConfig::EnumConfigFieldType>();
    QMetaEnum metaType = QMetaEnum::fromType<SystemConfig::EnumCanConfigType>();
    QString strCan = config.GetMetaEnumFiledString(metaFieldType,SystemConfig::can);
    QString strValue = config.GetStringValue(strCan,config.GetMetaEnumFiledString(metaType,SystemConfig::bit_rate));
    if (!strValue.isEmpty())
    {
        quint32 u32Value = strValue.toInt();
        m_sSCanBusDeviceStruct.iBitRate = u32Value*1000;
    }
    qDebug()<<"InitCanBusDevice: "<<m_sSCanBusDeviceStruct.iBitRate;

    QString strErrorString;
    m_pCanBusDevice = QCanBus::instance()->createDevice(
                m_sSCanBusDeviceStruct.strPlugin, m_sSCanBusDeviceStruct.strInterfaceName, &strErrorString);
    if (!m_pCanBusDevice)
    {
        qDebug() << "bade" << strErrorString;
        emit sigConnectError(m_sSCanBusDeviceStruct.strInterfaceName, strErrorString);
        emit sigError(FT_Comm_OpenFail, "");
        return false;
    }
    connect(m_pCanBusDevice, &QCanBusDevice::errorOccurred, this, &CCanBusDeviceThread::_slotCanBusError);
    //connect(m_pCanBusDevice, &QCanBusDevice::framesReceived, this, &CCanBusDeviceThread::SlotFramesReceived);
    m_pCanBusDevice->setConfigurationParameter(QCanBusDevice::BitRateKey,m_sSCanBusDeviceStruct.iBitRate);

    if(!m_pCanBusDevice->connectDevice())
    {
        qDebug() << "can connect deviec erroe";
        emit sigConnectError(m_sSCanBusDeviceStruct.strInterfaceName, m_pCanBusDevice->errorString());
        emit sigError(FT_Comm_OtherError, QString("CanBusErrorID:%1").arg(QCanBusDevice::CanBusError::ConnectionError));
        delete m_pCanBusDevice;
        m_pCanBusDevice = nullptr;
        return false;
    }
    else
    {
        QVariant qBitRate = m_pCanBusDevice->configurationParameter(QCanBusDevice::BitRateKey);
        if (qBitRate.isValid())
        {
            QString strLog = (QString("11 Backend: %1, connected to %2 at %3 kBit/s")
                              .arg(m_sSCanBusDeviceStruct.strPlugin).arg(m_sSCanBusDeviceStruct.strInterfaceName)
                              .arg(qBitRate.toInt()/1000));
            qDebug() << __FUNCTION__ << strLog;
            return true;
        }
        else
        {
            qDebug() << "qBitRate can isValidc erroe";
            emit sigConnectError(m_sSCanBusDeviceStruct.strInterfaceName, "bad BitRate");
            emit sigError(FT_Comm_OtherError, QString("CanBusErrorID:%1").arg(QCanBusDevice::CanBusError::ConfigurationError));
            delete m_pCanBusDevice;
            m_pCanBusDevice = nullptr;
            return false;
        }
    }
    return true;
}

void CCanBusDeviceThread::_SlotStartTimer()
{
    m_pReadFramesTimer->start(10);//
    m_pResendTimer->start(100);
}
