#ifndef CSELFTEST_H
#define CSELFTEST_H

#include<QObject>
#include<QHash>
#include<QList>
#include<QSet>
#include<QQueue>
#include <functional>
#include "module/pcrcatchmodule.h"
#include "affair/cdevStatus.h"
#include "consumables/cpcrrespersister.h"

class CSelfTest : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief  StartSelfTest 开始自检
     * @param  uiSeqType 时序类型
     * @return  
     */
    void StartSelfTest(quint16 uiSeqType); 

    /**
     * @brief  StartCleanPCR 开始PCR区域清理
     * @param  uiSeqType 时序类型
     * @return  
     */
    void StartCleanPCR(quint16 uiSeqType); 

    /**
     * @brief  SendGainOptoStatusCommand 下发命令获取所有光耦当前状态
     * @param 
     * @return  
     */
    void SendGainOptoStatusCommand();   
    
    /**
     * @brief  SendElecMagneticLockCommand 下发组件上锁
     * @param 
     * @return  
     */
    void SendElecMagneticLockCommand();   

    /**
     * @brief  SetMotor3AllOptoStatus 设置光耦状态
     * @param  u32Motor3AllOptoStatus 光耦状态
     * @return  
     */
    void SetMotor3AllOptoStatus(quint16 u32Motor3AllOptoStatus);  

    /**
     * @brief  HandleTimeseqReply 接收时序执行结果
     * @param  uiComplexID 时序执行的id
     * @param  uiResult    时序结果
     * @return  
     */
    void HandleTimeseqReply(quint16 uiComplexID, quint16 uiResult);   
    
signals:

public slots:

public:
  CSelfTest();
  ~CSelfTest();
private:
    /**
     * @brief  _init 初始化
     * @param 
     * @return  
     */
    void _init(); 

    /**
     * @brief  _resetInitHash 初始化hash结构体
     * @param strName 名称
     * @param hash    初始化对象
     * @param list    hash的key
     * @return  
     */
    void _initHash(QString strName,QHash<quint16, bool>& hash,QList<quint16> list);

    /**
     * @brief  _resetInitHash 初始化hash结构体
     * @param strName 名称
     * @param hash    初始化对象
     * @return  
     */
    void _resetInitHash(QString strName,QHash<quint16, bool>& hash);

    /**
     * @brief  _resetStep 重置步骤
     * @param  
     * @return  
     */
    void _resetStep(); 

    /**
     * @brief  _checkBoard 检查板卡条件
     * @param  str  执行名称
     * @param  hash 验证对象
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkBoard(QString str, QHash<quint16, bool>& hash, quint16 uiComplexID);

    /**
     * @brief  _onBoard1ZInitCondition 检查板卡1的Z轴复位条件
     * @param  
     * @return  
     */
    void _onBoard1ZInitCondition(); 

    /**
     * @brief  _onBoard3ZInitCondition 检查板卡3的Z轴复位条件
     * @param  
     * @return  
     */
    void _onBoard3ZInitCondition();    

    /**
     * @brief  _checkAllBoardZInitCondition 检查所有板卡的Z轴复位条件是否满足
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardZInitCondition(quint16 uiComplexID); 

    /**
     * @brief  _checkAllBoardZInit 检查所有板卡的Z轴复位成功
     * @param uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardZInit(quint16 uiComplexID); 

    /**
     * @brief  _checkAllBoardXYInit1 检查非Z轴板卡复位成功
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardXYInit1(quint16 uiComplexID); 

    /**
     * @brief  _checkAllBoardXYInit2 检查非Z轴板卡复位成功
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkAllBoardXYInit2(quint16 uiComplexID); 

    /**
     * @brief  _checkSingleClean1 多板卡单清理(板3离心位置清理 + 板1样本盖盖 )
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkSingleClean1(quint16 uiComplexID); 

    /**
     * @brief  _checkSingleClean2 多板卡单清理(板1样本清理放回 + 磁套清理(退磁))
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkSingleClean2(quint16 uiComplexID); 
    
    /**
     * @brief  _checkBoard4StripRecover 板卡3(pcr区域清理)
     * @param  uiComplexID 执行时序
     * @return  是否验证成功
     */
    bool _checkMultiClean(quint16 uiComplexID);    

    /**
     * @brief  _clearRemainingSteps 清空步骤
     * @param 
     * @return  
     */    
    void _clearRemainingSteps();

    /**
     * @brief  _executeCurrentStage 执行最新步骤
     * @param 
     * @return  
     */    
    void _executeCurrentStage();   
    
    /**
     * @brief  _reportCurrentStage 上报最新执行结果
     * @param uiComplexID 执行时序
     * @param uiResult    执行结果
     * @return  
     */    
    void _reportCurrentStage(quint16 uiComplexID,quint16 uiResult);

    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param  uiComplexID     时序id
     * @param  strParam        时序参数
     * @return  
     */    
    void _onBoardAction(const QString strName,quint16 uiComplexID,QString strParam); 

    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard1ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard2ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard3ZInit(); 
    
    /**
     * @brief  _onBoard1ZInit  板卡1执行Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard4ZInit();     

    /**
     * @brief  _onBoard1RemainPartInit  板卡1执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard1RemainPartInit();

    /**
     * @brief  _onBoard2RemainInit  板卡2执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard2RemainInit();

    /**
     * @brief  _onBoard3RemainPartInit  板卡3执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard3RemainPartInit();

    /**
     * @brief  _onBoard4RemainInit  板卡4执行非Z轴初始化
     * @param 
     * @return  
     */    
    void _onBoard4RemainInit();

    /**
     * @brief  _onCleanCentrifuge  清理离心位置
     * @param 
     * @return  
     */    
    void _onCleanCentrifuge();

    /**
     * @brief  _onCloseCap  盖样本盖
     * @param 
     * @return  
     */    
    void _onCloseCap();

    /**
     * @brief  _onSampleBackHome  样本放回
     * @param 
     * @return  
     */    
    void _onSampleBackHome();

    /**
     * @brief  _onBoard4EjectMagTube  板卡4退磁套
     * @param 
     * @return  
     */    
    void _onBoard4EjectMagTube();

    /**
     * @brief  _onAbandonPCR  PCR区域清理
     * @param 
     * @return  
     */    
    void _onAbandonPCR();

    /**
     * @brief  _getMotor3PCROptoStatus  PCR区域盖状态
     * @param  qList 光耦状态列表
     * @return  
     */ 
    void _getMotor3PCROptoStatus(QList<int>& qList);

    /**
     * @brief  _getPCRAreaIndexString  PCR区域索引字符串
     * @param  uiRowIndex 行索引
     * @param  uiColumnIndex 列索引
     * @param  strParam 参数
     * @return  PCR区域索引
     */ 
    quint8 _getPCRAreaIndexString(quint8 uiRowIndex, quint8 uiColumnIndex, QString &strParam);

    /**
     * @brief _actionAddOpenPCRCapTask 打开pcr区域盖
     * @param bNeedOpenCap 是否开盖
     * @param uiRowIndex   行索引
     * @param uiColumnIndex 列索引
     * @param strAreaIndexParam pcr区域索引
     * @param taskID 任务id
     */    
    void _actionAddOpenPCRCapTask(bool bNeedOpenCap, quint8 uiRowIndex,
                                 quint8 uiColumnIndex, QString &strAreaIndexParam,PCRCatchTaskID taskID = PCTI_OPEN_CAP);

    /**
     * @brief _actionAddClosePCRCapTask 关闭pcr区域盖
     * @param bNeedOpenCap 是否开盖
     * @param strAreaIndexParam pcr区域索引
     * @param taskID 任务id
     */    
    void _actionAddClosePCRCapTask(bool bNeedCloseCap, const QString &strAreaIndexParam,PCRCatchTaskID taskID = PCTI_CLOSE_CAP);

    /**
     * @brief _actionAddAbandonPCRTubeTask 丢弃PCR管
     * @param uiRowIndex 行索引
     * @param uiColumnIndex 列索引
     * @param strAreaParam 区域参数
     */    
    void _actionAddAbandonPCRTubeTask(quint8 uiRowIndex, quint8 uiColumnIndex, const QString &strAreaParam);

    /**
     * @brief _addPCRTubeExistFalgToParamStr 添加PCR管存在标志
     * @param strParam 参数
     */    
    void _addPCRTubeExistFalgToParamStr(QString &strParam);

    /**
     * @brief _sendSelfTestResult 发送自检结果
     * @param uiResult 结果
     */    
    void _sendSelfTestResult(quint16 uiResult); 

    /**
     * @brief _onAllNeedCleanPCR 所有需要清理的PCR区域
     * @param resInfos 结果
     */    
    void _onAllNeedCleanPCR(QList<PCRResInfo>& resInfos); 

    /**
     * @brief _onUpdateMiddleHostStatus 更新中位机状态
     */    
    void _onUpdateMiddleHostStatus();  
    
    /**
     * @brief _handleSelfTimeseqReply 处理自检时序结果
     * @param uiComplexID 时序id
     * @param uiResult    时序结果
     */    
    void _handleSelfTimeseqReply(quint16 uiComplexID, quint16 uiResult);  

    /**
     * @brief _checkMotor3PCRCatchAreaCap 判断pcr抓手是否有盖子
     * @param 
     * @return  
     */    
    bool _checkMotor3PCRCatchAreaCap();    

    /**
     * @brief _handleCleanPCRTimeseqReply 处理PCR区域清理时序结果
     * @param uiComplexID 时序id
     * @param uiResult    时序结果
     */    
    void _handleCleanPCRTimeseqReply(quint16 uiComplexID, quint16 uiResult); 
             
private: 
    quint16 m_uiSeqType = 0;                                      // 自检的时序类型索引
    QHash<quint16, std::function<bool(quint16)>> m_stepValidators;// 步骤检验
    QHash<quint16, std::function<void()>> m_stepExecutors;        // 步骤执行
    QList<QList<quint16>> m_listRunningStep;                      // 运行步骤
    QSet<quint16> m_allStepIds;                                   // 所有的步骤
    bool m_isAborted;                                             // 异常停止标记
    quint16 m_uiCurrentStageIndex = 0;                            // 当前执行步骤
    QHash<quint16, bool> m_hashZInitCondition;                    // 板卡Z轴允许复位状态(执行完成后才能执行Z轴复位)  
    QHash<quint16, bool> m_hashZInit;                             // 板卡Z轴复位状态(判断Z轴复位成功)
    QHash<quint16, bool> m_hashXYInit1;                           // 非Z轴板卡复位状态(判断X、Y轴复位成功)
    QHash<quint16, bool> m_hashXYInit2;                           // 非Z轴板卡复位状态(判断X、Y轴复位成功)
    //板卡2暂时没有       
    QHash<quint16, bool> m_hashSingleClean1;                      // 多板卡单清理(板3离心位置清理 + 板1样本盖盖)
    QHash<quint16, bool> m_hashSingleClean2;                      // 多板卡单清理(板1样本清理放回 + 磁套清理(退磁))
    QHash<quint16, bool> m_hashMultiClean;                        // 板卡3(pcr区域清理)
    quint32 m_u32Motor3AllOptoStatus;                             // 板卡3光耦状态
    PCRCatchModule m_pcrCatchModule;                              // PCR抓取模块
    QQueue<quint16> m_qListAbandonPCRACtion;                      // 丢弃PCR管时序动作
    QQueue<quint16> m_qListCleanPCRACtion;                        // 清理PCR所有时序动作
    DevComponent m_devCompRight;                                  // 样本右组件
    DevComponent m_devCompLeft;                                   // 样本左组件
    DevComponent m_devCompPcr;                                    // pcr抓手组件
};

#endif // CSELFTEST_H
