/*****************************************************
  * Copyright: 万孚生物
  * Author: mflin
  * Date: 2024-3-4
  * Description:  业务流程管理
  * -----------------------------------------------------------------
  * History:
  *1.创建业务流程管理
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CAFFAIR_H
#define CAFFAIR_H
#include<QObject>
#include<QThread>
#include<QMap>
#include<atomic>
#include<QMutex>
#include"publicconfig.h"
#include "SampleControl/samplecontrol.h"
#include "consumables/cstrip.h"
#include "consumables/consumables.h"
#include "consumables/pcrresource.h"
#include "consumables/reagent.h"
#include "consumables/crecyclebin.h"
#include "module/samplemodule.h"
#include "module/extractmodule.h"
#include "module/pcrcatchmodule.h"
#include "module/gantrymodule.h"
#include "module/pcrmodule.h"
#include "module/switchmixmodule.h"
#include "TestPreprocessing.h"

enum ActionSeqType
{
    AST_BEGIN,//开始
    AST_PUNCH,//刺破
    AST_UNCAP,//开盖
    AST_ABOSRB_SAMPLE,//吸取样本
    AST_CAP,//关盖
    AST_SPIT_SAMPLE,//吐样
    AST_TRANS_CLEVAGE,//转移裂解液
    AST_EXTRACT,//提取
    AST_SUB_PACK_REAGENT,//分装试剂
    AST_REAGENT_MIX,//试剂混匀
    AST_TRANS_PURIFY,//转移提纯液
    AST_TRANS_RAFFIN_OIL,//转移石蜡油
    AST_TRANS_PCR_CUBE,//盖PCR管帽并转移
    AST_TRANS_AND_MIX,//转移PCR 并混匀
    AST_OPEN_PCR_CAP,//打开PCR区盖子
    AST_TRANS_TO_PCR,//转移到PCR区
    AST_PCR,//PCR动作
    AST_ABANDON,//丢弃PCR管
    AST_END,//结束
};

struct ActionInfo
{
    quint8 quAtcionSeq;//当前动作序列号
    quint8 quModuleID;
    quint8 quSubModuleSeqID;
    quint8 quComplexID;//复合动作ID
};

enum ActionType
{
    AT_NO_COND = 0,//无状态条件
    AT_SINGLE_COND,//单状态条件
    AT_MULTI_COND,//多状态联合条件
};

struct  SActionExtraInfo
{
    quint16 uiActionSeqNo;//流程动作序列号
    ActionType eType;//动作类型(无条件/单条件/多条件)
};

struct SExecCondition
{
    quint16 uiUnitID;//单元id
    quint16 uiConditionID;//状态id
    bool bState;//
};//执行条件

struct SCondition
{
    quint16 uiUnitID;//单元id
    quint16 uiConditionID;//状态id
};//状态条件

struct SExecAction
{
    QString strComplexID;//复合指令id
    bool bDstExepectST;//执行该指令的执行目标状态,true/false
    quint16 uiExecCondID;//执行条件id
};//执行条件关联动作

struct SActionData
{
    QList<SExecAction> qActions;
    bool bNeedExecCond;//是否包含执行条件
    bool bWaitExec;//是否处于状态条件已满足并等待执行条件满足，待执行
};

typedef enum //流程运行状态
{
    // RST_UNKNOWN     = 0,    // 未知状态
    // RST_INIT        = 1,    // 正常状态下的复位
    // RST_ERR_INIT    = 2,    // 错误状态下的复位
    RST_IDLE        = 3,    // 空闲状态
    RST_WAIT_IDLE   = 4,    // 等待进入空闲状态(过渡状态)
    // RST_WAIT_PARSE  = 5,    // 等待解析
    RST_WAIT_RUN    = 6,    // 任务已准备好，等待执行条件满足
    RST_RUN         = 7,    // 任务正在执行中
    RST_WAIT_RESUME = 8,    // 暂停后等待恢复执行(过渡状态)
    RST_RESUME      = 9,    // 从暂停状态恢复执行
    RST_WAIT_PAUSE  = 10,   // 等待暂停(过渡状态)
    RST_PAUSE       = 11,   // 暂停任务执行
    RST_WAIT_STOP   = 12,   // 等待停止任务执行(过渡状态)
    RST_STOP        = 13,   // 任务已停止
    // RST_ABORT       = 14,   // 任务被异常终止
    RST_ERR         = 15,   // 错误状态，需要处理异常
    RST_POSDEBUG    = 16,   // 位置调试模式
    RST_PCR_RUN     = 17,   // PCR扩增模块正在运行
} RunStat;

struct ActionCmdReply
{
    quint16 uiCmdId;
    quint16 uiResult;
};

struct PCRCmdReply
{
    quint16 uiSourceID;
    quint16 uiCmdId;
    quint16 uiResult;
    QString strParam;
};

struct MotorBoardInit
{
    bool bBoardZInit;
    bool bBoardOthMotorInit;
    bool bBoardOthCompInit;
    bool bBoardClean;
    quint8 uiCleanTotalStep;
    quint8 uiCleanDoneSteps;
    quint8 uiPCRCleanTotalStep;//  not include cover
    quint8 uiPCRCleanDoneSteps;//  not include cover
};

struct DeviceInit
{
    MotorBoardInit board1;
    MotorBoardInit board2;
    MotorBoardInit board3;
    MotorBoardInit board4;
};

enum SeqType
{
    ST_PERIODIC = 0,  //周期时序
    ST_EXTRACTION , // 单提取时序
    ST_AMPLIFICATION, //单扩增时序
    ST_RESET, //复位时序
    ST_SELF_TEST, //自检时序
    ST_SYSTEM_SELF_TEST, //系统自检时序
    ST_SAMPLE_SCAN, //旋转扫码单元
    ST_PCR_CLEAN,// PCR清理
    ST_MOTOR_DEBUG,// 电机调试
    ST_AGING_TEST, //老化测试
    ST_SAMPLE_EXIST, //检测样本有无
    ST_TEST_PRE, // 测试预处理
    ST_UNDEFINE,
};

//针对停止、暂停、恢复等指令的设备
struct DeviceCmdExecState
{
    bool bMotorBoard1Recv;
    bool bMotorBoard2Recv;
    bool bMotorBoard3Recv;
    bool bMotorBoard4Recv;
    bool bPCRBoardRecv;
    bool bMotorBoard1Result;
    bool bMotorBoard2Result;
    bool bMotorBoard3Result;
    bool bMotorBoard4Result;
    bool bPCRBoardResult;
};

class CAffair : public QThread
{
    Q_OBJECT
public:
   enum InternalStandardStatus
   {
       IS_NONE = 0,
       IS_STEP_WAIT,    // 等待添加内标
       IS_STEP_ACTION,  // 正在添加内标
       IS_STEP_ABANDON, // TIP1000 丢弃
       IS_STEP_DONE,    // 添加内标完成状态 (可能存在多个内标)
       IS_FINISH,       // 添加内标全部完成
   };

public:
    static CAffair &getInstance();
    int SetPosDebugStatus(); //设置设备状态为位置调试模式  0 成功：1 失败
    int StopPosDebugStatus();//设置设备状态为位置idle 0 成功：1 失败
    void StartProcess(QString strParams);
    void PauseProcess();
    void StopProcess(bool bManualStop);
    void ResumeProcess();

    void ResetProcess();
    void PeriodicProcess();

    void PreProcess(QString strParams);

    void ResetResult(int iResult);

    void SampleExistProcess(QString strParams);

    void SampleScanProcess();

    void AppendNewBatch(QString strProcessName);

    void AddToWaitExecList(QList<SExecAction> *qActions, quint16 uiActionSeqNo, ActionType eType);

    RunStat GetRunST();
    void SetExtractScanMode(bool bStatus);
    bool GetExtractScanMode() {return  m_bExtractScanMode;} 
    /**
     * @brief SendTestStateToUpperHost 反馈测试过程状态给上位机
     * @param uiTestState
     */
    void SendPeroidStateToUpperHost(quint8 uiTestState, quint8 uiCurExecST = 0, quint8 uiResult = 0,QString strBatchNoSpecial = "");

    /**
     * @brief SendSampeScanStateToUpperHost 反馈样本扫码过程状态给上位机
     * @param uiTestState
     */    
    void SendSampeScanStateToUpperHost(quint8 uiTestState, quint8 uiResult = 0,QString strResult ="");

    //---------------------Action reply---------------------------------//

    
    void HandleSpitSampleReply();

    void HandleCatchSampleToOpenCapReply();

    void HandleOpenCapReply();

    /**
     * @brief HandleSample1000TipReply 取tip1000(在样本放回样本架时，提前取tip)
     * @param 
     */ 
    void HandleSample1000TipReply();

    void HandleSamplingReply();

    void HandleCloseCapReply();

    void HandleSampleBackHomeReply();

    void HandleTransCleavageReply();

    void HandleStandardAbandonTip200Reply();

    void HandleSubpackReagentReply();

    void HandleSubPackPunchReply();

    void HandleGetTipReply();//响应获取TIP结果

    void HandleAbandonTipReply();//响应丢弃TIP结果

    void HandleTransReagentReply();

    void HandleTransPurifyReply();

    void HandleCapAndTransTubeReply();

    /**
     * @brief HandleSwitchTubeReply 转移模块移动到离心位时回复
     * @param 
     */ 
    void HandleSwitchTubeReply();

    /**
     * @brief HandleCentrifugeTubeReply 离心成功时回复
     * @param 
     */ 
    void HandleCentrifugeTubeReply();

    void HandleOpenPCRReply();

    void HandleTransPCRTubeToAmplifyAreaReply();

    void HandleClosePCRReply();

    /**
     * @brief HandleAbandonClosePCRReply 丢弃时pcr关盖
     * @param 
     */ 
    void HandleAbandonClosePCRReply();

    void HandleAbandonPCRReply();

    void HandlePunchReply();

    void ExtractScanCodeMotorMoveReply();
    void StripCarMovePosReply();

    void ExtractScanMotorInitReply();

    void HandleExtractReply();

    /**
     * @brief HandlePCREndReply pcr完成时回复
     * @param uiPCRIndex        pcr索引
     */    
    void HandlePCREndReply(quint16 uiPCRIndex);
    //---------------------------------------------end of action cmd reply---------------------------------

    void GetPCRResource(quint8 uiOpSize, PosInfo& pcrAreaPos,
                        PosInfo& pcrSubAreaPos, bool& bNeedOpenCap, QString& strTecName);

    quint8 GetPCRAreaIndexString(quint8 uiRowIndex, quint8 uiColumnIndex, QString &strParam);

    //------------------------start of add task -----------------------------

    void ActionAddCatchSampleTask();

    void ActionAddOpenSampleCapTask();

    void ActionAddSampingTask();

    void ActionAddCloseSampleCapTask();

    void ActionAddSpitSampleTask();

    /**
     * @brief ActionAddTransClevageTask 添加裂解液(已去掉，防止与样本单元干涉)
     * @param bRightMove 是否往右移动
     */
    void ActionAddTransClevageTask(bool bRightMove = false);//bRightMove 转移完后是否需要往右移动

    void ActionAddSampleBackHomeTask();

    void ActionAddPunchStripTask();        

    void ActionAddExtractTask();

    void ActionAddStandardAbandonTip200Task();

    /**
     * @brief ActionAddSample1000Tip 获取tip1000
     * @param seq 样本处在那个阶段
     */    
    void ActionAddSample1000Tip(const SeqExecST seq = SEST_WAIT_SAMPLING);

    void ActionAddSubpackReagentTask();

    void ActionAddTransReagentTask();

    void ActionAddTransPurifyTask();

    void ActionAddCapAndTransTubeTask();

    /**
     * @brief ActionAddAmplTransTubeTask 扩增例程转移（不盖pcr管盖）
     * @param 
     */  
    void ActionAddAmplTransTubeTask();
    
    /**
     * @brief ActionAddSwitchTubeTask 转移pcr管(不包含离心)
     * @param 
     */      
    void ActionAddSwitchTubeTask();

    /**
     * @brief ActionAddSwitchTubeTask 离心pcr管
     * @param 
     */   
    void ActionAddCentrifugeTubeTask();

    /**
     * @brief ActionAddOpenPCRCapTask 打开pcr区域盖
     * @param bNeedOpenCap 是否开盖
     * @param uiRowIndex   行索引
     * @param uiColumnIndex 列索引
     * @param strAreaIndexParam pcr区域索引
     * @param taskID 任务id
     */    
    void ActionAddOpenPCRCapTask(bool bNeedOpenCap, quint8 uiRowIndex,
                                 quint8 uiColumnIndex, QString &strAreaIndexParam,PCRCatchTaskID taskID = PCTI_OPEN_CAP);

    void ActionAddTransToAmplifyAreaTask(SystemBuildInfo& info,const QString& strAreaIndexParam, bool bLeft, bool bRight);

    /**
     * @brief ActionAddClosePCRCapTask 关闭pcr区域盖
     * @param bNeedOpenCap 是否开盖
     * @param strAreaIndexParam pcr区域索引
     * @param taskID 任务id
     */    
    void ActionAddClosePCRCapTask(bool bNeedCloseCap, const QString &strAreaIndexParam,PCRCatchTaskID taskID = PCTI_CLOSE_CAP);

    void ActionAddTransToPCRAmplifyAreaTask();

    void ActionAddStartPCRTask();

    void ActionAddAbandonPCRTask();//open cap + abondon + close cap

    void ActionAddAbandonPCRTubeTask(quint8 uiRowIndex, quint8 uiColumnIndex, const QString &strAreaParam);

    void SendExtractScanCodeMotorMove(int iStripIdx);

    void ActionAddStripPutDownPos();// 卡盒架移动到卡条放置位

    void ActionAddExtractScanMotorInit();// 扫码电机复位

    /**
     * @brief ActionAddSampleScanTubeExistTask 样本有无扫描
     * @param 
     */    
    void ActionAddSampleScanTubeExistTask();

    /**
     * @brief ActionAddSampleScanCodeStartTask 开始夹取样本扫码(单个扫码动作)
     * @param uiSampleIndex 扫码索引位置
     */    
    void ActionAddSampleScanCodeStartTask(quint8 uiSampleIndex);
    
    /**
     * @brief ActionAddSampleScanCodeRotateTask 旋转样本扫码(单个扫码动作)
     * @param bLeft   左侧样本抓手
     * @param bRight  右侧样本抓手
     * @param uiSampleIndex  样本在样本架位置索引
     */        
    void ActionAddSampleScanCodeRotateTask(bool bLeft,bool bRight,quint8 uiSampleIndex);
    
    /**
     * @brief ActionAddSampleScanCodeEndTask 夹取样本扫码结束(单个扫码动作)
     * @param uiSampleIndex  样本在样本架位置索引
     */     
    void ActionAddSampleScanCodeEndTask(quint8 uiSampleIndex);
    
    /**
     * @brief ActionAddSampleScanCodeInitTask 扫码结束后初始化动作(全部扫码动作完成)
     * @param 
     */        
    void ActionAddSampleScanCodeInitTask();

    /**
     * @brief ActionAddSampleScanCodeTubeCheck 扫码检查试管有无
     * @param uiType--左右样本管判断 Action_SampleCodeScanTubeCheckLeft:左 Action_SampleCodeScanTubeCheck:右
     */        
    void ActionAddSampleScanCodeTubeCheck(quint16 uiType);

    void ExtractScanTest();
    void ExtractScanTest_use();
    void StopExtractScanTest_use(); //停止扫码老化测试
    void StopExtractScan();
    void ActionBeginExtractScanTask(QList<int> ScanStripList);  //16 个 1 是需要扫码， 0 是不用

    void UVLightCtrl(int iType,int iSet);  //消毒灯控制     0:提取 1PCR  2:all      iset:0 关 1开
    void StartDailyMonitor(QString strParam);  //开启日常监控
    void DailyMonitorWarnSet(QString strParam);  //日常监控异常上下限设置
    void ReportMonitorWarnError(QString strParam);  //上报温度异常

    //电机板1初始化
    void ActionAddMotorBoard1InitTask();
    //电机板2初始化
    void ActionAddMotorBoard2InitTask();
    //电机板3初始化
    void ActionAddMotorBoard3InitTask();
    //--------------------------end of  add task-----------

    void periodicHandleCmdReply(quint16 uiComplexID);

    /**
     * @brief periodicErrorHandleCmdReply 周期时序异常处理
     * @param uiComplexID 复合指令id
     * @param uiResult 结果
     */  
    void periodicErrorHandleCmdReply(quint16 uiComplexID,quint16 uiResult);

    void scanCodeHandleCmdReply(quint16 uiComplexID,quint16 uiResult = 0);

    void SendStopCmdToDevice();

    void SendResumeCmdToDevice();

    void SendPauseCmdToDevice();

    void updateDeviceCmdExecState(quint8 iSourceID, quint8 iResult);

    void SendSimulateExtractScanResult(); //模拟提取条扫码的结果

    void SetMotor3AllOptoStatus(const quint32 u32Status);

    void GetMotor3PCROptoStatus(QList<int>& qList);  

    /**
     * @brief UpdateRunStat 更新流程运行状态
     * @param runStat 流程运行状态
     */        
    void UpdateRunStat(RunStat runStat);   

    /**
     * @brief ExtractModuleDebugActionCmdReply 提取单独模块调试动作回复
     * @param uiComplexID 复合指令id
     * @param uiResult 结果
     */        
    void ExtractModuleDebugActionCmdReply(quint16 uiComplexID, quint16 uiResult);  

    /**
     * @brief ExtractModuleDebugStart 提取单独模块调试开始
     * @param strExtractUIContent 提取模块调试内容
     */        
    void ExtractModuleDebugStart(QString strExtractUIContent);            
signals:
    void sigExtractScanRs(QString strInfo); //提取条扫码的结果 all

    void sigResetFinished(bool success, const QString& msg);
    void sigSampleExistFinished(bool success, const QString& msg);
    void sigSampleScanFinished(bool success, const QString& msg);

public slots:
    void slotAddReciveSTCondition(quint16 uiUnit, quint16 uiCondition);
    void slotAddActionCmdReply(quint16 uiCmdID, quint16 uiResult);
    void slotPCRCmdReply(quint16 iSourceID,quint16 uiCmdID, quint16 uiResult, QString strParam);
    void slotFLDataCmdReply(const QByteArray& qByteArray);
    void slotZebraScannerRs(QString dataRs,int iStatus);
    void slotStartPeriodicResetProcess();
protected:
    virtual void run();
    virtual void _HandleReceiveSTCondList();//处理接收到的状态条件信号
    virtual void _HandleReceiveCmdResultList();//处理接收到的复合
    virtual void _HandleReceivePCRCmdReplyList();//处理接收到的PCR回覆
    virtual void _HandleReceiveFLDataList();//处理接收到的荧光数据

private:
    explicit CAffair(QObject *parent = nullptr);
    ~CAffair();

    void _ClearData();//
    void _BeginPeriodicProcessActions(quint8 quWaitExecSampleSize);

    void _SetRunST(RunStat runSate, QString strFunc);
    void _SetSeqType(SeqType seqType);
    SeqType _GetSeqType();
    void _addSampleTubeExistFalgToParamStr(QString &strParam);
    void _addPCRTubeExistFalgToParamStr(QString &strParam);
    void _addSampleHeightStateToParam(QString &strParam);

    /**
     * @brief _addIsCapToParam 添加样本是否盖盖参数
     * @param
     * @return 
     */  
    void _addIsCapToParam(QString &strParam);

    /**
     * @brief _checkSampleScanTubeAndAction 检测样本有无
     * @param
     * @return 
     */      
    void _checkSampleScanTubeAndAction();

    /**
     * @brief _getCurBatchNo 获取当前批次号
     * @param uiCurExecST 当前执行状态
     * @return 批次号
     */      
    QString _getCurBatchNo(quint8 uiCurExecST);

    /**
     * @brief _calcArraySystemBuildInfo 计算pcr区域和孔位分配
     * @param 
     * @return 
     */       
    void _calcArraySystemBuildInfo();

	void _TransPurify();

    /**
     * @brief _sendRunSTErrorNotify 故障发送执行情况
     * @param 
     * @return 
     */       
    void _sendRunSTErrorNotify();

    QList<QString> listScanRsMsg;
protected:
    QVector<long> m_vecScanConutDiff;//用于提取条测试扫码统计，不同的个数
    bool m_bStartZebraScan;  //1 :扫码 0：关闭
    bool m_bZebraScanAging; //斑马扫码老化标记
    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    bool m_bRunSTChanged;// 记录状态改变，触发条件变量进入run循环
    QAtomicInt m_bWaitForStopResult;//是否在等待停止指令結果
    QAtomicInt m_bManualStop;//是否是上位机主动发起的停止，涉及到返回结果判定
    bool m_bPunchDone;
    bool m_bWaitPunchDone;
    bool m_bThreadAlive;//线程存续状态

    QAtomicInt m_sharedVariable; //流程运行状态
    QMutex m_qRunSTMutex;//流程运行状态锁

    QList<RunStat> m_sNextStatList;//待执行状态

    bool m_bExtractScanMode;// 是否在执行提取条扫码模式，为了截留信息

    InternalStandardStatus m_eInternalStandardStatus = IS_NONE; // 内标状态
    bool m_bInternalStandardRun = false;// 是否进入内标状态
    bool m_bStartMELT = false;// 判断是否开始熔解
    quint32 m_u32Motor3AllOptoStatus = 0;//板卡3的所有光耦状态
    bool m_bSampleScanTubeLeft = false; //左侧样本是否检测完成
    bool m_bSampleScanTubeRight = false;//右侧样本是否检测完成
    quint16 m_uiSampleScanTubeResultLeft = 0;//左侧样本是否检测结果
    quint16 m_uiSampleScanTubeResultRight = 0;//右侧样本是否检测结果

    /**
      *key 是复合指令id, 值代表当前正在执行该动作的样本索引集合,
      *其中样本索引=batchNo*MaxRow*MaxColumn + rowIndex*MaxColumn + columnIndex
      * */
    QMap<quint16, QVector<quint32>> m_qExecSampleMap;
    //    QVector< int> m_qPCRSampleVect;

    //ST cond message 状态条件接收无锁环形缓冲队列
    SCondition m_uiConditionInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex_Condition{0};
    std::atomic<int> m_iReadIndex_Condition{0};
    int m_iNextWriteIndex_Condition;
    int m_iCurrentWriteIndex_Condition;

    //complex message 复合指令无锁环形缓冲队列
    ActionCmdReply m_uiComplexCmdInfoList[BUFFER_SIZE];
    std::atomic<int> m_iCmdWriteIndex_Condition{0};
    std::atomic<int> m_iCmdReadIndex_Condition{0};
    int m_iCmdNextWriteIndex_Condition;
    int m_iCmdCurrentWriteIndex_Condition;

    //complex message PCR指令无锁环形缓冲队列
    PCRCmdReply m_pcrCmdReplyList[BUFFER_SIZE];
    std::atomic<int> m_iPCRCmdWriteIndex_Condition{0};
    std::atomic<int> m_iPCRCmdReadIndex_Condition{0};
    int m_iPCRCmdNextWriteIndex_Condition;
    int m_iPCRCmdCurrentWriteIndex_Condition;

    //荧光数据 无锁环形缓冲队列
    QByteArray m_flDataList[MIDDLE_BUFFER_SIZE];
    std::atomic<int> m_iFLDataCmdWriteIndex_Condition{0};
    std::atomic<int> m_iFLDataCmdReadIndex_Condition{0};
    int m_iFLDataCmdNextWriteIndex_Condition;
    int m_iFLDataCmdCurrentWriteIndex_Condition;

    //--------------------------样本耗材类管理资源--------------------------//
    SampleControl* m_pSampleControl;
    CStrip* m_pStrip;
    PCRResource* m_pPCRResource;
    Reagent* m_pReagent;
    CRecycleBin* m_pFrontBin;
    CRecycleBin* m_pBackBin;

    //----------------------------动作执行模块---------------------------//
    SampleModule m_sampleModule;
    ExtractModule m_extractModule;
    GantryModule m_gantryModule;
    SwitchMixModule m_switchMixModule;
    PCRCatchModule m_pcrCatchModule;
    PCRModule  m_pcrModule[PCR_MODULE_SIZE+1];//1个公共模块+N个真实扩增模块

    //---------------------时序类型------------------------------//
    QAtomicInt m_seqType;

    DeviceCmdExecState m_deviceCmdExecState;
    QAtomicInt m_curBatchPCREndDone;//当前批次PCR结束信号是否已处理，这里涉及到多块PCR扩增区域板，单批次只需处理一次结束信号

    bool m_bTransReagentFinishToWaitExtract; // 转移试剂完成，等待提取完成
    bool m_bWaitExtractFinishToTransReagent; // 提取完成，等待转移试剂完成

    bool m_bPeriodicInit; // 周期时序初始化
    QString m_strPeriodicParams; // 周期时序参数

    bool m_bReconstitutionWait; // 复溶后等待

    QString m_strCurProjID; // 当前项目id
    int m_iCurAmplifyCompIndex; // 当前扩增组件索引
    int m_iSystemBuildInfoSize; // 系统构建信息大小
    bool m_bGetTip; // 是否获取TIP

    TestPreprocessing *m_pTestPre;
};

#endif // CAFFAIR_H
