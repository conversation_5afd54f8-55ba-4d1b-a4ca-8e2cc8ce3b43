#include "ccommunicationobject.h"
#include <QDebug>
#include <unistd.h>
#include <QDataStream>
#include <QtEndian>
#include "control/coperationunit.h"
#include "publicfunction.h"
#include"RFID/CRFIDWaitRsThread.h"
#include"RFID/CRFIDMotionTask.h"
#include "cglobalconfig.h"
#include "error/errorconfig.h"
#include "error/cerrornotify.h"
#include "SystemConfig/SystemConfig.h"
#include "datacontrol/CSystemDB.h"
#include"Upgrade/UpgradeCtrl.h"

#define Send_By_Virtual 0
CCommunicationObject::CCommunicationObject(QObject *parent) : QObject(parent)
    , m_bEnableCan1(true) 
{
    //
    qRegisterMetaType<EConnectType> ("EConnectType");
    qRegisterMetaType<QHostAddress> ("QHostAddress");
    qRegisterMetaType<ErrorID>("ErrorID");
    //
    _initData();
    _initObject();
    _initCommunication();
}

CCommunicationObject::~CCommunicationObject()
{
}

CCommunicationObject &CCommunicationObject::getInstance()
{
    static CCommunicationObject cCCommunicationObject;
    return cCCommunicationObject;
}

void CCommunicationObject::_initCommunication()
{
    CRFIDMotionTask::getInstance();
    CRFIDWaitRsThread::getInstance();
    CUpgradeCtrl::getInstance();
    m_bInitServer = false;
    m_pRetryInitServerTimer = new QTimer(this);
    m_pRetryInitServerTimer->setSingleShot(false);//计时器循环此时true只循环一次，false无限循环
    connect(m_pRetryInitServerTimer,SIGNAL(timeout()),this,SLOT(_slotRetryInitServer()));
    //  first find 首次初始化
    m_qEth1Address = getInterfaceAddress("eth1");
    m_qWlan0Address = getInterfaceAddress("wlan0");
    qDebug() << "eth1: " << m_qEth1Address.toString()
             << "wlan0: " << m_qWlan0Address.toString();
    // TCP Server
    m_qTCPServerHostAddress = QHostAddress::Null;
    m_strTCPHostMacAddress = "";

    {// 保证优先连接有线网络(可配置)
        m_pRetryInitServerTimer->start(5000);
        qDebug() << "Server could not start!";
    }
    if(m_bInitServer)
    {//  无论是否链接整个，为了保证ip地址稳定后再次udp正确性
        m_bInitServer = false;
        m_pRetryInitServerTimer->start(5000);

    }

    if (CSystemDB::getInstance().getIntValueFromKey("Connect_127") > 0)
    {
        m_qTCPServerHostAddress = QHostAddress::LocalHost;//设置为localhost​
        qDebug()<<"_initCommunication ip address is "<<m_qTCPServerHostAddress.toString();
    }    
    
    // tcp udp
    m_pCUdpServerThread = new CUdpServerThread(30080, m_qTCPServerHostAddress, m_strTCPHostMacAddress);
    connect(this, &CCommunicationObject::sigRetryInitServer_0,
            m_pCUdpServerThread, &CUdpServerThread::slotReInitServer);
    connect(this, &CCommunicationObject::sigNewTcpServerConnect,
            m_pCUdpServerThread, &CUdpServerThread::slotServerConnect);
    // tcp server
    m_pCmdTcpServerThread_0 = new CTcpServerThread(30080, m_qTCPServerHostAddress, m_strTCPHostMacAddress);
    m_pCmdTcpAnalyzeThread_0 = new CTcpAnalyzeThread();
    m_pCClientWindow = new CClientWindow();
    connect(this, &CCommunicationObject::sigRetryInitServer_0,
            m_pCmdTcpServerThread_0, &CTcpServerThread::slotReInitServer);
    // 发送数据
    connect(this, &CCommunicationObject::sigSendTcpServerMessage_0,
            m_pCmdTcpServerThread_0, &CTcpServerThread::slotSendMessage);
    connect(m_pCmdTcpAnalyzeThread_0, &CTcpAnalyzeThread::sigWaitACK,
            m_pCmdTcpServerThread_0, &CTcpServerThread::slotWaitACK);
    connect(m_pCmdTcpAnalyzeThread_0, &CTcpAnalyzeThread::sigSendACKBack,
            m_pCmdTcpServerThread_0, &CTcpServerThread::slotSendAckBack);

    // 接收数据
    connect(m_pCmdTcpServerThread_0, &CTcpServerThread::sigReciveMessage,
            m_pCmdTcpAnalyzeThread_0, &CTcpAnalyzeThread::slotReciveOriginalMessageData);// 原始数据-解析
    connect(m_pCmdTcpAnalyzeThread_0, &CTcpAnalyzeThread::sigReciveMessage,
            m_pCClientWindow, &CClientWindow::slotAddReciveMsg);// 接受的数据直接发给对应的线程
    connect(m_pCmdTcpServerThread_0, &CTcpServerThread::sigNewNetworkConnect,
            this, &CCommunicationObject::slotNewConnectFromCmdNetwork_0);
    connect(m_pCmdTcpServerThread_0, &CTcpServerThread::sigACKOut,
            this, &CCommunicationObject::slotACKOutFromCmdNetwork_0);

    //错误处理
    connect(m_pCmdTcpServerThread_0, &CTcpServerThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_Upper_TCP_Comm, errorID);});
    connect(m_pCmdTcpAnalyzeThread_0, &CTcpAnalyzeThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_Upper_TCP_Comm, errorID);});

    // TCP PCR
    m_strTCPHostMacAddress = GetHostMac("eth1");
    m_pCmdTcpServerThread_1 = new CTcpServerThread(30090, m_qEth1Address, m_strTCPHostMacAddress);
    m_pCmdTcpAnalyzeThread_1 = new CTcpAnalyzeThread();
    m_pCPcrWindow = new CPcrWindow();
    connect(m_pCPcrWindow, &CPcrWindow::sigUpgradeEndMsg,
            &CUpgradeCtrl::getInstance(),&CUpgradeCtrl::slotUpgradeEndRs);
    // 发送数据
    connect(this, &CCommunicationObject::sigSendTcpPCRMessage_1,
            m_pCmdTcpServerThread_1, &CTcpServerThread::slotSendMessage);
    connect(m_pCmdTcpAnalyzeThread_1, &CTcpAnalyzeThread::sigWaitACK,
            m_pCmdTcpServerThread_1, &CTcpServerThread::slotWaitACK);
    connect(m_pCmdTcpAnalyzeThread_1, &CTcpAnalyzeThread::sigSendACKBack,
            m_pCmdTcpServerThread_1, &CTcpServerThread::slotSendAckBack);
    // 接收数据
    connect(m_pCmdTcpServerThread_1, &CTcpServerThread::sigReciveMessage,
            m_pCmdTcpAnalyzeThread_1, &CTcpAnalyzeThread::slotReciveOriginalMessageData);// 原始数据-解析
    connect(m_pCmdTcpAnalyzeThread_1, &CTcpAnalyzeThread::sigReciveMessage,
            m_pCPcrWindow, &CPcrWindow::slotAddReciveMsg);// 接受的数据直接发给对应的线程
    connect(m_pCmdTcpServerThread_1, &CTcpServerThread::sigNewNetworkConnect,
            this, &CCommunicationObject::slotNewConnectFromCmdNetwork_1);
    connect(m_pCmdTcpServerThread_1, &CTcpServerThread::sigACKOut,
            this, &CCommunicationObject::slotACKOutFromCmdNetwork_1);

    //错误处理
    connect(m_pCmdTcpServerThread_1, &CTcpServerThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_PCR_TCP_Comm, errorID);});
    connect(m_pCmdTcpAnalyzeThread_1, &CTcpAnalyzeThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_PCR_TCP_Comm, errorID);});

    // can0
    m_pCCanBusDeviceThread_0 = new CCanBusDeviceThread("can0");
    m_pCanBusAnalyzeThread_0 = new CCanAnalyzeThread();
    m_pCCan0Window = new CCan0Window();
    // 发送数据
    connect(this, &CCommunicationObject::sigSendCanbusMessage_0,
            m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::slotSendMessage);// 发送数据
    connect(m_pCanBusAnalyzeThread_0, &CCanAnalyzeThread::sigWaitACK,
            m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::slotWaitACK);
    connect(m_pCanBusAnalyzeThread_0, &CCanAnalyzeThread::sigSendACKBack,
            m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::slotSendAckBack);
    // 接收数据
    connect(m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::sigReciveMessage,
            m_pCanBusAnalyzeThread_0, &CCanAnalyzeThread::slotReciveOriginalMessageData);// 原始数据-解析
    connect(m_pCanBusAnalyzeThread_0, &CCanAnalyzeThread::sigReciveMessage,
            m_pCCan0Window, &CCan0Window::slotAddReciveMsg);// 接受的数据直接发给对应的线程
    connect(m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::sigACKOut,
            this, &CCommunicationObject::slotACKOutFromCanBus_0);
    connect(m_pCCan0Window, &CCan0Window::sigUpgradeEndMsg,
            &CUpgradeCtrl::getInstance(),&CUpgradeCtrl::slotUpgradeEndRs);

    //RFID
    connect(m_pCCan0Window, &CCan0Window::sigRFIDRsMsg,
            &CRFIDWaitRsThread::getInstance(), &CRFIDWaitRsThread::SlotMethodIDMotionRsMsg);
    connect( &CRFIDMotionTask::getInstance(),SIGNAL(sigError(QString , MidMachineSubmodule , ErrorID)),
             this,SLOT(handleError(QString , MidMachineSubmodule , ErrorID)));

    //错误处理
    connect(m_pCCanBusDeviceThread_0, &CCanBusDeviceThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_CAN0_Comm, errorID);});
    connect(m_pCanBusAnalyzeThread_0, &CCanAnalyzeThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_CAN0_Comm, errorID);});

    // can1
    if (m_bEnableCan1)
    {
        m_pCCanBusDeviceThread_1 = new CCanBusDeviceThread("can1");
        m_pCanBusAnalyzeThread_1 = new CCanAnalyzeThread();
        m_pCCan1Window = new CCan1Window();
        // 发送数据
        connect(this, &CCommunicationObject::sigSendCanbusMessage_1,
                m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::slotSendMessage);
        connect(m_pCanBusAnalyzeThread_1, &CCanAnalyzeThread::sigWaitACK,
                m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::slotWaitACK);
        connect(m_pCanBusAnalyzeThread_1, &CCanAnalyzeThread::sigSendACKBack,
                m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::slotSendAckBack);
        // 接收数据
        connect(m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::sigReciveMessage,
                m_pCanBusAnalyzeThread_1, &CCanAnalyzeThread::slotReciveOriginalMessageData);// 原始数据-解析
        connect(m_pCanBusAnalyzeThread_1, &CCanAnalyzeThread::sigReciveMessage,
                m_pCCan1Window, &CCan1Window::slotAddReciveMsg);// 接受的数据直接发给对应的线程

        connect(m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::sigACKOut,
                this, &CCommunicationObject::slotACKOutFromCanBus_1);

        connect(m_pCCan1Window, &CCan1Window::sigRFIDRsMsg,
                &CRFIDWaitRsThread::getInstance(), &CRFIDWaitRsThread::SlotMethodIDMotionRsMsg);
        connect(m_pCCan1Window, &CCan1Window::sigUpgradeEndMsg,
                &CUpgradeCtrl::getInstance(),&CUpgradeCtrl::slotUpgradeEndRs);

        //错误处理
        connect(m_pCCanBusDeviceThread_1, &CCanBusDeviceThread::sigError,
                this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_CAN1_Comm, errorID);});
        connect(m_pCanBusAnalyzeThread_1, &CCanAnalyzeThread::sigError,
                this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_CAN1_Comm, errorID);});
    }

    //  串口
    m_pCSerialDeviceThreadd = new CSerialDeviceThread("/dev/ttyS18","256000");
    m_pSerialAnalyzeThread = new CTcpAnalyzeThread();
    m_pCSerialWindow = new CSerialWindow();
    // 发送数据
    connect(this, &CCommunicationObject::sigSendSerialMessage,
            m_pCSerialDeviceThreadd, &CSerialDeviceThread::slotSendMessage);
    connect(m_pSerialAnalyzeThread, &CTcpAnalyzeThread::sigWaitACK,
            m_pCSerialDeviceThreadd, &CSerialDeviceThread::slotWaitACK);
    connect(m_pSerialAnalyzeThread, &CTcpAnalyzeThread::sigSendACKBack,
            m_pCSerialDeviceThreadd, &CSerialDeviceThread::slotSendAckBack);
    // 接收数据
    connect(m_pCSerialDeviceThreadd, &CSerialDeviceThread::sigReciveMessage,
            m_pSerialAnalyzeThread, &CTcpAnalyzeThread::slotReciveOriginalMessageData);// 原始数据-解析
    connect(m_pSerialAnalyzeThread, &CTcpAnalyzeThread::sigReciveMessage,
            m_pCSerialWindow, &CSerialWindow::slotAddReciveMsg);// 接受的数据直接发给对应的线程
    connect(m_pCSerialDeviceThreadd, &CSerialDeviceThread::sigACKOut,
            this, &CCommunicationObject::slotACKOutFromSerial);

    //  提取扫码
    m_pCZebraScannerCtrl = new CZebraScannerCtrl("/dev/ttyS8", "9600");//nulltptr;
    connect(m_pCZebraScannerCtrl, SIGNAL(sigZebraScannerRs(QString,int)),
             this, SLOT(slotRevZebraScannerRs(QString,int))); //扫码器的结果及是否超
    //错误处理
    connect(m_pCZebraScannerCtrl, &CZebraScannerCtrl::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_Barcode_Serial_Comm, errorID);});

    //  样本扫码
#if 0
    m_pSampleCodeScannerLeft = new CSampleScannerCtrl("/dev/ttyS9", "9600","m_pSampleCodeScannerLeft");
    m_pSampleCodeScannerRight = new CSampleScannerCtrl("/dev/ttyS4", "9600","m_pSampleCodeScannerRight");
#else //最新仪器
    m_pSampleCodeScannerLeft = new CSampleScannerCtrl("/dev/ttyS7", "9600","m_pSampleCodeScannerLeft");
    m_pSampleCodeScannerRight = new CSampleScannerCtrl("/dev/ttyS9", "9600","m_pSampleCodeScannerRight");
#endif

    // 虚拟通讯
    m_pCVirtualWindow = new CVirtualWindow();
    connect(this, &CCommunicationObject::sigSendVirtualMessage,
            m_pCVirtualWindow, &CVirtualWindow::slotAddReciveMsg);// 接受的数据直接发给对应的线程
    // ftp
    m_pCFtpServerThread  = new CFtpServerThread(30070);
    connect(this, &CCommunicationObject::sigSendFTPFile,
            m_pCFtpServerThread, &CFtpServerThread::slotSendFile);
    connect(m_pCFtpServerThread, &CFtpServerThread::sigReciveFileFinished,
            this, &CCommunicationObject::slotReciveFileFinished);
    //错误处理
    connect(m_pCFtpServerThread, &CFtpServerThread::sigError,
            this, [this](ErrorID errorID, QString strExtraInfo){handleError(strExtraInfo, Mid_Sub_FTP_Comm, errorID);});


}

void CCommunicationObject::slotNewConnectFromCmdNetwork_0(QString strIP, int iPort, bool bConnected)
{
    qDebug() << __FUNCTION__ << strIP << iPort;
    emit sigNewTcpServerConnect(bConnected);
    m_bClientConnected = bConnected;

    //存在客户端断开链接后，pcr没有重启(即没有断开链接)，需要重新发送PCR链接状态(如果PCR有重连，也不会影响正确的状态显示)
    QStringList strConnectFlag;
    strConnectFlag.push_back(QString::number(m_bPCRConnected));
    COperationUnit::getInstance().sendNotifyList(Method_connect_flag, strConnectFlag,  Machine_UpperHost);
}

void CCommunicationObject::slotACKOutFromCmdNetwork_0(QString strIP, int iPort)
{
    qDebug() << __FUNCTION__ << strIP << iPort;
}

void CCommunicationObject::slotNewConnectFromCmdNetwork_1(QString strIP, int iPort, bool bConnected)
{
    qDebug() << __FUNCTION__ << strIP << iPort;
    emit sigNewPCRServerConnect(bConnected);
    int iConnectFalg = bConnected ? 1 : 0;
    QStringList strConnectFlag;
    strConnectFlag.push_back(QString::number(iConnectFalg));
    COperationUnit::getInstance().sendNotifyList(Method_connect_flag, strConnectFlag,  Machine_UpperHost);
    m_bPCRConnected = bConnected;
}

void CCommunicationObject::slotACKOutFromCmdNetwork_1(QString strIP, int iPort)
{
    qDebug() << __FUNCTION__ << strIP << iPort;
}


void CCommunicationObject::slotACKOutFromCanBus_0()
{
    qDebug() << __FUNCTION__;
}

void CCommunicationObject::slotNewConnectFromCanBus_0(bool bConnect)
{
    qDebug() << __FUNCTION__ << bConnect;
}



void CCommunicationObject::slotACKOutFromCanBus_1()
{
    qDebug() << __FUNCTION__;
}

void CCommunicationObject::slotNewConnectFromCanBus_1(bool bConnect)
{
    qDebug() << __FUNCTION__ << bConnect;
}

void CCommunicationObject::slotACKOutFromSerial()
{
    qDebug() << __FUNCTION__;
}

void CCommunicationObject::slotNewConnectFromSerial(bool bConnect)
{
    qDebug() << __FUNCTION__ << bConnect;
}

void CCommunicationObject::slotReciveMessageFromExtractScanner(QByteArray qReciveMsgAarry)
{
    COperationUnit::getInstance().sendStringData(Method_ExScanner_barcodeData, QString::fromLocal8Bit(qReciveMsgAarry),
                                                 Machine_UpperHost, 0, 0x04);
}

void CCommunicationObject::sendMessageToMachine(const QByteArray & qSendByteArray,
                                                const quint8 & eMachineID)
{    
#if Send_By_Virtual
    switch (eMachineID)
    {
    case Machine_Middle_Host:
        emit sigSendMessageToMainWindow(qSendByteArray);
        break;
    case Machine_UpperHost:
        emit sigSendTcpServerMessage_0(qSendByteArray);
        break;
    case Machine_PCR_MainCtrl:
    case Machine_PCR_Ctrl:
    case Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    case Machine_Fluorence:
        emit sigSendTcpPCRMessage_1(qSendByteArray);
        break;
    default:
        emit sigSendVirtualMessage(qSendByteArray);
        break;
    }
#else
    switch (eMachineID)
    {
    case Machine_Middle_Host:
        emit sigSendMessageToMainWindow(qSendByteArray);
        break;
    case Machine_UpperHost:
        emit sigSendTcpServerMessage_0(qSendByteArray);
        break;
    case Machine_Motor_1:
    case Machine_Motor_2:
    case Machine_Motor_3:
    case Machine_Motor_4:
    case Machine_Power_Ctrl:
        emit sigSendCanbusMessage_0(qSendByteArray);
        break;
    case Machine_Function_manager_Ctrl:
    case Machine_Temp_Ctrl:
    case Machine_RFID_1:
    case Machine_RFID_2:
    case Machine_RFID_3:
        if (m_bEnableCan1) {
            emit sigSendCanbusMessage_1(qSendByteArray);
        } else {
            emit sigSendCanbusMessage_0(qSendByteArray);
        }
        break;
    case Machine_PCR_MainCtrl:
    case Machine_PCR_Ctrl:
    case Machine_PCR_Ctrl_1:
    case Machine_PCR_Ctrl_2:
    case Machine_PCR_Ctrl_3:
    case Machine_PCR_Ctrl_4:
    case Machine_Fluorence:
        emit sigSendTcpPCRMessage_1(qSendByteArray);
        break;
    case Machine_GarbageStateCtrl:
        emit sigSendSerialMessage(qSendByteArray);
        break;
    case Machine_Scan_Board_1:
        emit sigSendMessageToScanner(qSendByteArray);
        break;
    case Machine_Scan_Board_2:
        emit sigSendExtractScanner(qSendByteArray);
        break;
    default:
        break;
    }
#endif
}

void CCommunicationObject::slotReciveFileFinished(QString strFileName)
{
    if(!strFileName.isEmpty())
    {
        COperationUnit::getInstance().sendStringResult(Method_ftp_file_info, strFileName, Machine_UpperHost);
    }
}

int CCommunicationObject::sendStartScanCmdToZebraScanner()
{
    int iStatus= 0;
    if(m_pCZebraScannerCtrl)
    {
        m_pCZebraScannerCtrl->DoSingleScanCmd();
    }
    return  iStatus;
}

void CCommunicationObject::GetExtractScanLastRs(QString &strRs)
{
    if(m_pCZebraScannerCtrl)
    {
       strRs= m_pCZebraScannerCtrl->GetLastScanRs();
    }
}

 int CCommunicationObject::closeZebraScanner()
 {
     qDebug()<<"closeZebraScanner ";
     int iStatus= 0;
     if(m_pCZebraScannerCtrl)
     {
         iStatus = m_pCZebraScannerCtrl->CloseSingleScanCmd();
     }
     return  iStatus;
 }

 void CCommunicationObject::slotRevZebraScannerRs(QString dataRs,int iStatus)
 {
     emit sigZebraScannerRs(dataRs,iStatus);
 }

void CCommunicationObject::handleError(QString strExtraInfo, MidMachineSubmodule subModule, ErrorID errorID)
{
    CErrorNotify::getInstance().addErrorInfoItem(subModule, errorID, strExtraInfo);
}

void CCommunicationObject::StartSamplerCodeScanner()
{
    m_pSampleCodeScannerLeft->OpenDoSingleScanCmd();
    m_pSampleCodeScannerRight->OpenDoSingleScanCmd();
}

void CCommunicationObject::StartSingleSamplerCodeScanner(bool bLeft)
{
    if (bLeft)
    {
        m_pSampleCodeScannerLeft->OpenDoSingleScanCmd();
    }
    else
    {
        m_pSampleCodeScannerRight->OpenDoSingleScanCmd();
    }
}

void CCommunicationObject::StopSamplerCodeScanner()
{
    m_pSampleCodeScannerLeft->CloseSingleScanCmd();
    m_pSampleCodeScannerRight->CloseSingleScanCmd();
}

qint8 CCommunicationObject::GetSamplerScanNextPos()
{
    m_pSampleCodeScannerRight->GetSampleNextPos();
    return m_pSampleCodeScannerLeft->GetSampleNextPos();
}

qint8 CCommunicationObject::GetSamplerScanCurPos()
{
    return m_pSampleCodeScannerLeft->GetSampleCurPos();
}

bool CCommunicationObject::IsSamplerScanFinish()
{
    return m_pSampleCodeScannerLeft->IsScanFinish();
}

void CCommunicationObject::ResetSampleScanPos()
{
    m_pSampleCodeScannerLeft->ResetSamplePos();
    m_pSampleCodeScannerRight->ResetSamplePos();
}

void CCommunicationObject::GetSampleCodeScanLeftResult(QStringList &result)
{
    m_pSampleCodeScannerLeft->GetSampleCodeResult(result);
}

void CCommunicationObject::GetSampleCodeScanRightResult(QStringList &result)
{
    m_pSampleCodeScannerRight->GetSampleCodeResult(result);
}

QStringList CCommunicationObject::GetSampleCodeScanAllResult(QStringList &resultLeft, QStringList &resultRight)
{
    return m_pSampleCodeScannerLeft->GetSampleCodeAllResult(resultLeft,resultRight);
}

bool CCommunicationObject::GetSampleCodeScanSingleResult(qint8 uiIndex)
{
    QString strResultLeft = m_pSampleCodeScannerLeft->GetSampleCodeScanSingleResult(uiIndex);
    QString strResultRight = m_pSampleCodeScannerRight->GetSampleCodeScanSingleResult(uiIndex);
    if(strResultLeft.isEmpty() || strResultRight.isEmpty())
    {
        return false;
    }
    return true;
}

bool CCommunicationObject::GetSampleCodeScanInitStatus()
{
    bool bRet = false;
    bRet = m_pSampleCodeScannerLeft->GetInitStatus();
    bRet &= m_pSampleCodeScannerRight->GetInitStatus();  
    qDebug()<<"GetSampleCodeScanInitStatus bRet"<<bRet;  
    return bRet;
}

void CCommunicationObject::SetSampleCodeMaxSampleRow(const quint8 uiRow)
{
    m_pSampleCodeScannerLeft->SetMaxSampleRowPos(uiRow);
    m_pSampleCodeScannerRight->SetMaxSampleRowPos(uiRow);  
    qDebug()<<"SetSampleCodeMaxSampleRow: "<<uiRow;  
}

void CCommunicationObject::SetSampleCodeScanTubeExist(bool bLeft, bool bExist)
{
    if (bLeft)
    {
        m_pSampleCodeScannerLeft->SetSampleCodeScanTubeExist(bExist);
    }
    else
    {
        m_pSampleCodeScannerRight->SetSampleCodeScanTubeExist(bExist); 
    }
}

void CCommunicationObject::SetSampleCodeScanAllTubeExist(const QString strParam)
{
    QStringList strList = strParam.split(",");
    if (strList.size() != SAMPLE_MAX_SIZE)
    {
        qDebug()<<"SetSampleCodeScanAllTubeExist size error"<<strList;
        return;
    }

    const int halfSize = SAMPLE_MAX_SIZE / 2;
    QString strRight = strList.mid(0, halfSize).join(",");
    QString strLeft = strList.mid(halfSize).join(",");

    QStringList sampleList;
    for (size_t i = 0; i < halfSize; i++)
    {
        sampleList <<  strList[i + halfSize] << strList[i];
    }
    m_strSampleCodeScanLastRs = sampleList.join(",");
    
    m_pSampleCodeScannerLeft->SetSampleCodeScanAllTubeExist(strLeft);
    m_pSampleCodeScannerRight->SetSampleCodeScanAllTubeExist(strRight); 
}

// 获取扫码结果
QString CCommunicationObject::GetSampleCodeScanResult()
{
    return m_strSampleCodeScanLastRs;
}

bool CCommunicationObject::GetSampleCodeScanTubeExist(bool bLeft)
{
    bool bRet = false;
    if (bLeft)
    {
        bRet = m_pSampleCodeScannerLeft->GetSampleCodeScanTubeExist();
    }
    else
    {
        bRet = m_pSampleCodeScannerRight->GetSampleCodeScanTubeExist();
    }
    qDebug()<<"GetSampleCodeScanTubeExist:"<<bLeft;
    return bRet;
}

void CCommunicationObject::_slotRetryInitServer()
{
    if(m_bInitServer)
    {
        m_pRetryInitServerTimer->stop();
    }
    else
    {
        m_qEth1Address = getInterfaceAddress("eth1");
        m_qWlan0Address = getInterfaceAddress("wlan0");
        qDebug() << "re eth1: " << m_qEth1Address.toString()
                 << "re wlan0: " << m_qWlan0Address.toString();
        // TCP Server
        m_strTCPHostMacAddress = "";
        if (!m_qWlan0Address.isNull() && !m_qWlan0Address.toString().contains("169.254."))
        {// 169.254.是网络设备无法从DHCP服务器获取IP地址时自动分配一个临时的IP地址，所以不认为是有效地址
            m_qTCPServerHostAddress = m_qWlan0Address;
            m_strTCPHostMacAddress = GetHostMac("wlan0");
            qDebug() << "Server re started on wlan0!";
            m_bInitServer = true;
        }
        else
        {
            CGlobalConfig::getInstance().initNetworkAddress();
            qDebug() << "Server re initNetworkAddress!";
        }
        if(m_bInitServer)
        {
            m_pRetryInitServerTimer->stop();
            if (SystemConfig::getInstance().GetBoolValue(SystemConfig::network,SystemConfig::local))
            {
                m_qTCPServerHostAddress = QHostAddress::LocalHost;//设置为localhost​
                qDebug()<<"_slotRetryInitServer ip address is "<<m_qTCPServerHostAddress.toString();
            }             
            emit sigRetryInitServer_0(m_qTCPServerHostAddress, m_strTCPHostMacAddress);
        }
    }
}

void CCommunicationObject::_slotRetryEth0InitServer()
{
    if(m_bInitServer)
    {
        m_pRetryInitServerTimer->stop();
    }
    else
    {
        CGlobalConfig::getInstance().initNetworkAddress();
        m_qWlan0Address = getInterfaceAddress("wlan0");
        qDebug()<< "wlan0: " << m_qWlan0Address.toString();
        // TCP Server
        m_strTCPHostMacAddress = "";
        if(m_bInitServer)
        {
            m_pRetryInitServerTimer->stop();
            if (SystemConfig::getInstance().GetBoolValue(SystemConfig::network,SystemConfig::local))
            {
                m_qTCPServerHostAddress = QHostAddress::LocalHost;//设置为localhost​
                qDebug()<<"_slotRetryEth0InitServer ip address is "<<m_qTCPServerHostAddress.toString();
            }              
            emit sigRetryInitServer_0(m_qTCPServerHostAddress, m_strTCPHostMacAddress);
        }
    }
}

bool CCommunicationObject::getClientConnect()
{
    return m_bClientConnected;
}

bool CCommunicationObject::getPCRConnect()
{
    return m_bPCRConnected;
}

void CCommunicationObject::senFTPFile(QString strFileName)
{
    emit sigSendFTPFile(strFileName);
}

void CCommunicationObject::_initData()
{
    m_bClientConnected = false;
    m_bPCRConnected = false;
    if (!SystemConfig::getInstance().GetStringValue("can", "enable_can1").isEmpty())
    {
        m_bEnableCan1 = SystemConfig::getInstance().GetStringValue("can", "enable_can1").toInt();
    }
}

void CCommunicationObject::_initObject()
{

}

