#ifndef PCRRESOURCE_H
#define PCRRESOURCE_H
#include <qobject.h>
#include <QMutex>
#include "publicconfig.h"
#include "cpcrrespersister.h"
#include <QVector>
#include <QHash>
#include <QQueue>
#include "./SampleControl/samplecontrol.h"

enum PCRSizeType
{
    PCR_SIZE_32,
    PCR_SIZE_64,
};

enum PCRState
{
    PCRST_IDLE = 0,//空闲可用
    PCRST_ADDING,//添加中，可在该区域空闲位置增加测试
    PCRST_USING,//测试中，该区域不可中途添加新测试
    PCRST_INVALID,//异常不可用
    PCRST_LOCK,//提前锁定
};

struct PCRResourceTimeInfo
{
   quint8 uiAreaIndex;  // pcr区域索引
   QString strTecName;  // 测试时序名称
   QDateTime startTime; // 开始时间
   QDateTime endTime;   // 结束时间
};


/**
 * @brief The PCRResource class PCR资源会被管理起来，按结构区域划分成几个资源，其中因为涉及到单次一个资源占用，以及单次两个PCR资源占用
 * 所以PCR资源会根据一次性使用的资源数划分，其中双数使用正序开始，单数使用逆序开始，直至资源用光，或者需要使用双数，只余下单数资源。
 * PCR资源有4种状态，区域内全部未使用为IDLE，部分被添加为ADDING，开启PCR扩增为USING，PCR资源如果故障或别的情况导致不可用为VALID。
 */
class PCRResource
{
public:
    static PCRResource &getInstance();

    bool IsPCRResourceEnough(quint8 uiTotalUseSize);

    /**
     * @brief GetNextSingleIdlePCRPos 获取下一个单独测试可用的PCR位置
     * @param uiRowIndex 当前可用的PCR区所在行位置索引
     * @param uiColumnIndex 当前可用PCR区所在列位置索引
     * @param subAreaRowIndex 当前可用PCR区域内可用位置行索引
     * @param subAreaColumnIndex 当前可用PCR区域内可用位置列索引
     * @param strTecName 目标TEC时序名称
     * @return
     */
    bool GetNextSingleIdlePCRPos(quint8& uiRowIndex, quint8& uiColumnIndex,
                                 quint8& subAreaRowIndex, quint8& subAreaColumnIndex,
                                 bool& bNeedOpenCap, QString &strTecName);
    /**
     * @brief GetNextDoubleIdlePCRPos 获取下两个测试可用的PCR位置
     * @param uiRowIndex 当前可用的PCR区所在行位置索引
     * @param uiColumnIndex 当前可用PCR区所在列位置索引
     * @param subAreaRowIndex 当前可用PCR区域内可用位置行索引
     * @param subAreaColumnIndex 当前可用PCR区域内可用位置列索引
     * @param strTecName 目标TEC时序名称
     * @return
     */
    bool GetNextDoubleIdlePCRPos(quint8& uiRowIndex, quint8& uiColumnIndex,
                                 quint8& subAreaRowIndex, quint8& subAreaColumnIndex,
                                 bool& bNeedOpenCap, QString &strTecName);

    /**
     * @brief FreePCRSubArea 释放特定行列的PCR区所有PCR资源
     * @param uiRowIndex
     * @param uiColumnIndex
     */
    bool FreePCRSubArea(quint8 uiRowIndex, quint8 uiColumnIndex);

    /**
     * @brief FreeAllPCR 释放全部PCR资源
     */
    void FreeAllPCR();

    /**
       * @brief UseDoublePCR 使用某连续两个PCR资源
       * @param uiRowIndex 该PCR资源所在行位置
       * @param uiColumnIndex 该PCR资源所在的列位置
       * @param qVect        样本信息
       */
    void UsePCR(quint8 uiRowIndex, quint8 uiColumnIndex, bool & bNeedCloseCap,const QVector<SystemBuildInfo> qVect);

    /**
       * @brief GetNeedCloseCap 获取PCR区域盖是否需要关盖
       * @param uiRowIndex 该PCR资源所在行位置
       * @param uiColumnIndex 该PCR资源所在的列位置
       */
    void GetNeedCloseCap(quint8 uiRowIndex, quint8 uiColumnIndex, bool & bNeedCloseCap);

    /**
       * @brief SetPCRST 设置特定区域PCR状态
       * @param uiRowIndex
       * @param uiColumnIndex
       * @param uiPCRST
       */
    void SetPCRST(quint8 uiRowIndex, quint8 uiColumnIndex, quint8 uiPCRST );

    /**
    * @brief SetPCRST   设置特定区域PCR状态
    * @param uiPCRIndex PCR索引
    * @param uiPCRST    PCR状态
    */
   void SetPCRST(quint8 uiPCRIndex, quint8 uiPCRST );

    /**
       * @brief GetPCRSizeType 返回PCR 大小类型，32孔还是64孔
       * @return
       */
    quint8 GetPCRSizeType();

    quint8 GetPCRSubAreaSize();

    void SetPCRSizeType(quint8 uiSizeType);

    /**
     * @brief ResetDBData 数据库中PCR的资源恢复到初始化状态
     */
    void ResetDBData();

    QList<PCRResInfo> getAllNeedCleanPCRRes();

    quint8 GetPCRSubAreaRowIndex(quint8 uiPosIndex);
    quint8 GetPCRSubAreaColumnIndex(quint8 uiPosIndex);

    void setAllPCRAreaNeedClean();

    /**
     * @brief SetDisablePcrArea 需要屏蔽的PCR区域
     * @param params 屏蔽数量
     * @return 
     */
    void SetDisablePcrArea(const QString &strParam); 

    /**
     * @brief LoadValidPcrArea 加载可用pcr区域
     * @return 
     */    
    void LoadValidPcrArea(); 

    /**
       * @brief SetPCRST 设置特定区域PCR孔位状态
       * @param strParam  禁用信息
       */
    void SetDisablePcrAreaHole(const QString &strParam); 

    /**
     * @brief LoadValidPcrAreaHole 加载可用pcr区域孔位
     * @return 
     */    
    void LoadValidPcrAreaHole();

    /**
     * @brief _IsDoubleHole   判断孔位是否充足(pcr禁用孔和区域)
     * @param uiRowIndex      区域行索引
     * @param uiColumnIndex   区域列索引
     */
    bool IsDoubleHole(quint8 uiRowIndex, quint8 uiColumnIndex);

    /**
     * @brief ResetAllAreaNextPos    重置全部区域可用孔位起点索引
     */
    void ResetAllAreaNextPos();  
    
    /**
     * @brief CalcSamplePCRHoleInfo   预分配孔位
     * @param qCalcSystemBuildInfo    分配信息
     */
    void CalcSamplePCRHoleInfo(QQueue<QVector<SystemBuildInfo>>& qCalcSystemBuildInfo);   

    /**
     * @brief AddWaitCapSampleInfo   添加待盖样本信息
     * @param qDstQueue  样本信息
     */
    void AddWaitCapSampleInfo(QQueue<QVector<SystemBuildInfo>>& qQueue); 

    /**
     * @brief CalcCurSampleUsePCRHoles   计算当前批次所需要PCR孔位数量
     * @param qSampleInfoList  项目信息
     * @return 孔位数量
     */
    quint8 CalcCurSampleUsePCRHoles(QList<QString> qSampleInfoList);   

    /**
     * @brief CheckCurPatchHolesEnough    检查当前批次是否有足够的孔位
     * @param qSampleInfoList  项目信息
     * @return 是否有足够的孔位
     */
    bool CheckCurPatchHolesEnough(QList<QString> qSampleInfoList);  

    /**
     * @brief AddRecordPCRAreaDuration    记录pcr区域启动时间和结束时间
     * @param strBatch                 批次信息
     * @param qPCRTimeInfoList         使用区域信息
     * @return 
     */
    void AddRecordPCRAreaDuration(const QString& strBatch,QVector<PCRResourceTimeInfo>& qPCRTimeInfoList);

    /**
     * @brief DelRecordPCRAreaDuration   移除pcr区域启动时间和结束时间
     * @param strBatch                    批次信息
     * @return 
     */
    void DelRecordPCRAreaDuration(const QString& strBatch); 

    /**
     * @brief CaclNextBatchWaitTime    计算下一个批次等待时间
     * @param uiTotalUseSize           使用数量
     * @return 返回等待时间(可能是负数)
     */
    qint64 CaclNextBatchWaitTime(quint8 uiTotalUseSize); 
    
    /**
     * @brief GetPCRAreaRemainTimeRecord     获取pcr区域剩余时间
     * @param 
     * @return 返回批次对应的剩余时间(每个区域)
     */
    QString GetPCRAreaRemainTimeRecord();
    
    /**
     * @brief ResetPCRResourceAreaAtatus    重置全部区域可用孔位起点索引
     * @param uiAreaIndex                   区域索引
     */
    void ResetPCRResourceAreaStatus(quint8 uiAreaIndex);     

    /**
     * @brief ResetPCRResourceHoletatus    重置区域可用孔位状态
     * @param uiRowIndex                   PCR区域行索引
     * @param uiColumnIndex                PCR区域列索引
     * @param uiHoleIndex                  孔位索引
     */
    void ResetPCRResourceHoletatus(quint8 uiRowIndex, quint8 uiColumnIndex, quint8 uiHoleIndex);   

    /**
     * @brief AddRecordPCRAreaBatchInfo    记录pcr区域对应批次信息
     * @param uiPCRIndex                   区域索引
     * @param strBatchInfo                 批次信息
     */    
    void AddRecordPCRAreaBatchInfo(const quint8 uiPCRIndex,const QString strBatchInfo);

    /**
     * @brief GetRecordPCRAreaBatchInfo    记录pcr区域对应批次信息
     * @param uiPCRIndex                   区域索引
     * @return 返回批次信息
     */    
    QString GetRecordPCRAreaBatchInfo(const quint8 uiPCRIndex);  

    /**
     * @brief CheckRecordPCRAreaDuration    检查pcr区域对应批次信息
     * @param strBatch                     批次信息
     * @return 返回批次信息
     */        
    bool CheckRecordPCRAreaDuration(const QString& strBatch);
    
    /**
     * @brief GetAllRecordPCRAreaBatchInfo    记录所有pcr区域批次信息
     * @return 返回批次信息
     */    
    QSet<QString> GetAllRecordPCRAreaBatchInfo(); 

    /**
     * @brief GetCalcCurSampleUsePCRHoles    返回最新计算需要的孔位数量
     * @return 孔位数量
     */     
    quint8 GetCalcCurSampleUsePCRHoles();

    /**
     * @brief IsCurBatchAllPCRIndexValid    判断当前批次pcr区域是否清除完成
     * @param uiPCRIndex                     区域索引
     * @return 是否完成
     */     
    bool IsCurBatchAllPCRIndexValid(const quint8 uiPCRIndex); 

    /**
     * @brief GetAllPCRRes    获取全部pcr区域资源
     * @return 全部pcr区域资源
     */     
    QList<PCRResInfo> GetAllPCRRes();       
protected:
    /**
     * @brief InitPCRResInfo 初始化/释放某个PCR区域资源
     * @param pResInfo
     * @param uiRowIndex
     * @param uiColumnIndex
     */
    void _InitPCRResInfo(PCRResInfo *pResInfo, quint8 uiRowIndex, quint8 uiColumnIndex);

    /**
     * @brief _IsDoubleHole    判断孔位是否充足(pcr禁用孔和区域)
     * @param uiRowIndex       区域行索引
     * @param uiColumnIndex    区域列索引
     * @param uiNextDoublePos  准备使用的孔位
     */
    bool _IsDoubleHole(quint8 uiRowIndex, quint8 uiColumnIndex,quint8& uiNextDoublePos);

    /**
     * @brief _GetRegionIndex  根据区域索引获取区域行索引和列索引
     * @param uiAreaIndex      区域索引
     * @param uiRowIndex       区域行索引
     * @param uiColumnIndex    区域列索引
     */
    void _GetRegionIndex(quint8 uiAreaIndex,quint8& uiRowIndex, quint8& uiColumnIndex);

    /**
     * @brief _GetRegionIndexByRowColumn  根据区域行索引和列索引获取区域索引
     * @param uiRowIndex                  区域行索引
     * @param uiColumnIndex               区域列索引
     */
    quint8 _GetRegionIndexByRowColumn(const quint8& uiRowIndex,const quint8& uiColumnIndex);

    /**
     * @brief _GetRegionList   获取区域行索引和列索引
     * @param list             区域索引
     */
    void _GetRegionList(QList<QPair<int, int>>& list);

    /**
     * @brief _GetValidNextPos       获取最新可用的位置
     * @param uiRowIndex             区域行索引
     * @param uiColumnIndex          区域列索引
     * @return 最新的可用的位置索引
     */
    quint8 _GetValidNextPos(quint8& uiRowIndex, quint8& uiColumnIndex);

    /**
     * @brief _SortPCRHoleInfo    pcr孔位信息排序
     * @return 
     */
    void _SortPCRHoleInfo();

    /**
     * @brief _GetSubAreaInfo       获取pcr孔位信息
     * @param uiSubAreaIndex        子区域索引
     * @param uiSubAreaRowIndex     区域行索引
     * @param uiSubAreaColumnIndex  区域列索引
     * @return 是否是连续孔位
     */
    bool _GetSubAreaInfo(quint8 uiSubAreaIndex,quint8& uiSubAreaRowIndex,quint8& uiSubAreaColumnIndex);

    /**
     * @brief _GetInvalidSubAreaInfo       获取pcr单个孔位信息
     * @param uiSubAreaIndex               子区域索引
     * @param uiSubAreaRowIndex            区域行索引
     * @param uiSubAreaColumnIndex         区域列索引
     * @return 是否获取成功--不设置禁孔(应当返回false)
     */
    bool _GetInvalidSubAreaInfo(quint8 uiSubAreaIndex,quint8& uiSubAreaRowIndex,quint8& uiSubAreaColumnIndex);

    /**
     * @brief _UpdateSubAreaInfo    更新pcr孔位信息
     * @param uiRowIndex            区域行索引
     * @param uiColumnIndex         区域列索引
     * @param bSingleHole           是否是单个孔位
     * @return 
     */
    void _UpdateSubAreaInfo(quint8 uiRowIndex, quint8 uiColumnIndex,bool bSingleHole = false);

    /**
     * @brief _GetSamplePCRHoleInfo   获取预分配孔位
     * @param qCalcSystemBuildInfo    分配信息
     * @param qVectHoleRemain         剩余孔位
     * @param infoCur                 样本信息
     * @param uiAreaIndexLast         上一个区域索引
     */
    void _GetSamplePCRHoleInfo(QQueue<QVector<SystemBuildInfo>>& qCalcSystemBuildInfo,QVector<quint8>& qVectHoleRemain,QVector<SystemBuildInfo> &infoCur,qint8& uiAreaIndexLast);   
       
private:
    explicit PCRResource();
    void _Init();

private:
    PCRResInfo m_pcrResInfo[PCR_ROW_SIZE][PCR_COLUMN_SIZE];
    quint8 m_uiPCRSizeType;
    quint8 m_uiPCRTotalSize;
    quint8 m_uiPCRSubAreaSize;
    quint8 m_uiPCRSubAreaRowSize;
    quint8 m_uiPCRSubAreaColumnSize;

    QMutex m_qMutex;

    CPCRResPersister m_persister;
    PCRResInfo m_pcrResHoleInfo[PCR_ROW_SIZE][PCR_COLUMN_SIZE][PCR_SUB_AREA_SIZE2];// PCR孔位信息
    QHash<quint8,QQueue<QPair<qint8,qint8>>> m_qHashHoleDoubleValidInfo;           // pcr可用连续孔位(key：模块0~3，value：孔位对0~15)
    QHash<quint8,QQueue<QPair<qint8,qint8>>> m_qHashHoleDoubleInvalidInfo;         // pcr不可用连续孔位
    QVector<quint8> m_qVectHoleRemain;                                             // pcr孔位剩余数量
    QQueue<QVector<SystemBuildInfo>> m_qCalcSystemBuildInfo;                       // 预计算孔位样本信息
    quint8 m_u8HoleNum = 0;                                                        // 孔位数量
    QHash<QString,QVector<PCRResourceTimeInfo>> m_qPCRAreaTimeRecord;              // pcr区域启动和结束时间(参数1为批次)
    QHash<quint8,QString> m_qPCRAreaBatchInfoRecord;                               // pcr区域对应的批次信息
};

#endif // PCRRESOURCE_H
