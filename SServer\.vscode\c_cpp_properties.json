{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/build/**", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/**", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtCore", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtCore/5.12.8", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtCore/5.12.8/QtCore", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtGui", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtGui/5.12.8", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtGui/5.12.8/QtGui", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtWidgets", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtWidgets/5.12.8", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtWidgets/5.12.8/QtWidgets", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQml", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQml/5.12.8", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQml/5.12.8/QtQml", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQuick", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQuick/5.12.8", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtQuick/5.12.8/QtQuick", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtNetwork", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtOpenGL", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtSql", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtXml", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/QtTest", "D:/QT/Qt5.12.8/Tools/mingw730_64/include", "D:/QT/Qt5.12.8/Tools/mingw730_64/x86_64-w64-mingw32/include", "D:/QT/Qt5.12.8/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include", "D:/QT/Qt5.12.8/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "QT_NETWORK_LIB", "QT_DEPRECATED_WARNINGS", "WIN32", "_WIN32", "__MINGW32__", "__MINGW64__"], "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "compilerPath": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/gcc.exe", "compilerArgs": ["-Wall", "-Wextra", "-fPIC"], "browse": {"path": ["${workspaceFolder}/**", "${workspaceFolder}/build/**", "D:/QT/Qt5.12.8/5.12.8/mingw73_64/include/**", "D:/QT/Qt5.12.8/Tools/mingw730_64/include/**"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}], "version": 4}