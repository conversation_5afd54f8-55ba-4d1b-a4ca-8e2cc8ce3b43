#ifndef CFTPSERVERTHREAD_H
#define CFTPSERVERTHREAD_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTcpServer>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QTimer>
#include <QTime>
#include <QQueue>
#include <atomic>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CFtpServerThread : public QObject
{
    Q_OBJECT
public:
    explicit CFtpServerThread(QObject *parent = nullptr);    
    explicit CFtpServerThread(int iPort, QHostAddress strHostIP = QHostAddress::Any,
                              QString strHostMac = "",
                              QObject *parent = nullptr);
    ~CFtpServerThread();
signals:
    void sigInitServer();
    void sigNewNetworkConnect(QString strIP, int iPort, bool bConnect);
    void sigNewNetworkDisConnect(QString strIP, int iPort);
    void sigReciveFileFinished(QString strFileName);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);
public slots:
    void slotReInitServer(QHostAddress qHostAddress, QString strHostMac );
    void slotSendFile(QString strFilePathName);
private slots:
    void _slotInitServer();
    void slot_newconnect(); //建立新连接的槽
    void slot_disconnect(); //取消连接的槽
    void slot_recvmessage();
private:
    void _toListen();

private:

    QTimer *m_pReadFramesTimer;
    QThread *m_pThread;
    QTcpServer *m_pTCPServer; //QTcpServer服务器
    QTcpSocket *m_pConnectTcpSocket; //与客户端连接套接字，只连接一个
    int m_iConnected;// 0: no 1: one
    int m_iPort;
    QHostAddress m_qHostAddress;
    QString m_strHostMacAddress;


    QString m_strFileName;
    QByteArray m_byReceivedData;
    quint64 m_quiFileSize = 0;
    bool m_bMetaDataReceived = true;

};

#endif // CFTPSERVERTHREAD_H
