﻿#ifndef CPROJECTDB_H
#define CPROJECTDB_H

#include <QObject>
#include <QMap>
#include <QMutex>
#include <QVariant>
#if Q_OS_QML
#include <QQmlEngine>
#endif
#include "cdbobject.h"

// 前向声明
class CProjectDBImpl;


enum eProjectType
{
    ProjectType_PN, // 定性，阴阳性
    ProjectType_Density, // 定量，浓度
    ProjectType_Melting,// 熔解
};
// 项目信息结构体
struct SProjectInformationStruct
{
    QString projectLot = "";               // 项目货号
    QString projectName = "";              // 项目名称
    QString projectType = "";              // 项目类型:定性0/绝对定量1；标准溶解曲线2/高分辨率溶解曲线3（HRM）
    QString ablationExpiration = "";       // 复溶效期
    QString sampleCapacity = "";           // 样本容量
    QString internalStandardCapacity = ""; // 内标容量
    QString extractCapacity = "";          // 纯化物量
    QString pcrReagentCapacity = "";       // 扩增容量
    QString complexSolutionCapacity = "";  // 复溶容量
    QString extractName = "";              // 提取时序
    QString tecName = "";                  // 扩增时序
    QString tubeCount = "";                // 项目所用试管数量
    QString tubeInfo = "";                 // 项目信息
    QString calculation = "0";              // 曲线算法绝对荧光0/相对荧光线性1/相对荧光log2
    QString autoThreshold = "0";            // 阈值（自动0/手动1）
    QString thresholdValue = "";           // 手动阈值，与tubeInfo中项目对应，空白或者0表示自动阈值
    QString peakHeightValue = "10";          // 峰高阈值，每个通道一个，逗号分割B,G,Y,R
    QString meltingValue = "";             // 熔解值，参照【51-57,59-63;51-57,61-67;50-53;61-65】
    QString crossInterference = "";        // 干扰系数
    QString ctThresholdValue = "";         // 判定阈值，范围如10-40
    QString ISThresholdValue = "";         // 内标阈值
    QString ISConcentrationValue = "";     // 内标浓度
    QString liftValue = "30-30-30-30";                // 抬升值++,每个通道一个,-号分割 
    QString ISCapacity = "";               // 内标总量
    QString QCCapacity = "";               // 质控总量
    QString QCTValueRange = "";            // 强阳CT值范围,28-31
    QString QConcentrationRange = "";      // 强阳浓度范围0.23-1.22
    QString PCTValueRange = "";            // 弱阳CT值范围,28-31
    QString PConcentrationRange = "";      // 弱阳浓度范围,28-31
    QString QCNegativeCTValue = "";        // 质控阴性CT值
    QString QCInternalCtCTValue = "";      // 质控内标CT值
    QString QCInternalCtConcentrationValue = ""; // 质控内标浓度
    QString sampleCalibrationCapacity = ""; // 样本校准容量
    QString internalStandardCalibrationCapacity = ""; // 内标校准容量
    QString extractCalibrationCapacity = ""; // 提取校准容量
    QString pcrReagentCalibrationCapacity = ""; // PCR试剂校准容量
    QString complexSolutionCalibrationCapacity = ""; // 复溶液校准容量
    QString addTime = "";                  // 添加时间
    QString remark = "";                   // 备注
    QString remarks1 = "";                 // 备注1
    QString remarks2 = "";                 // 备注2
    QString remarks3 = "";                 // 备注3

    // 从字符串列表转换为结构体
    static SProjectInformationStruct fromStringList(const QStringList& list, const QStringList& fieldNames) {
        SProjectInformationStruct info;
        for (int i = 0; i < fieldNames.size() && i < list.size(); ++i) {
            const QString& fieldName = fieldNames[i];
            const QString& value = list[i];
            
            if (fieldName == "projectLot") info.projectLot = value;
            else if (fieldName == "projectName") info.projectName = value;
            else if (fieldName == "projectType") info.projectType = value;
            else if (fieldName == "ablationExpiration") info.ablationExpiration = value;
            else if (fieldName == "sampleCapacity") info.sampleCapacity = value;
            else if (fieldName == "internalStandardCapacity") info.internalStandardCapacity = value;
            else if (fieldName == "extractCapacity") info.extractCapacity = value;
            else if (fieldName == "pcrReagentCapacity") info.pcrReagentCapacity = value;
            else if (fieldName == "complexSolutionCapacity") info.complexSolutionCapacity = value;
            else if (fieldName == "extractName") info.extractName = value;
            else if (fieldName == "tecName") info.tecName = value;
            else if (fieldName == "tubeCount") info.tubeCount = value;
            else if (fieldName == "tubeInfo") info.tubeInfo = value;
            else if (fieldName == "calculation") info.calculation = value;
            else if (fieldName == "autoThreshold") info.autoThreshold = value;
            else if (fieldName == "thresholdValue") info.thresholdValue = value;
            else if (fieldName == "peakHeightValue") info.peakHeightValue = value;
            else if (fieldName == "meltingValue") info.meltingValue = value;
            else if (fieldName == "crossInterference") info.crossInterference = value;
            else if (fieldName == "ctThresholdValue") info.ctThresholdValue = value;
            else if (fieldName == "ISThresholdValue") info.ISThresholdValue = value;
            else if (fieldName == "ISConcentrationValue") info.ISConcentrationValue = value;
            else if (fieldName == "liftValue") info.liftValue = value;
            else if (fieldName == "ISCapacity") info.ISCapacity = value;
            else if (fieldName == "QCCapacity") info.QCCapacity = value;
            else if (fieldName == "QCTValueRange") info.QCTValueRange = value;
            else if (fieldName == "QConcentrationRange") info.QConcentrationRange = value;
            else if (fieldName == "PCTValueRange") info.PCTValueRange = value;
            else if (fieldName == "PConcentrationRange") info.PConcentrationRange = value;
            else if (fieldName == "QCNegativeCTValue") info.QCNegativeCTValue = value;
            else if (fieldName == "QCInternalCtCTValue") info.QCInternalCtCTValue = value;
            else if (fieldName == "QCInternalCtConcentrationValue") info.QCInternalCtConcentrationValue = value;
            else if (fieldName == "sampleCalibrationCapacity") info.sampleCalibrationCapacity = value;
            else if (fieldName == "internalStandardCalibrationCapacity") info.internalStandardCalibrationCapacity = value;
            else if (fieldName == "extractCalibrationCapacity") info.extractCalibrationCapacity = value;
            else if (fieldName == "pcrReagentCalibrationCapacity") info.pcrReagentCalibrationCapacity = value;
            else if (fieldName == "complexSolutionCalibrationCapacity") info.complexSolutionCalibrationCapacity = value;
            else if (fieldName == "addTime") info.addTime = value;
            else if (fieldName == "remark") info.remark = value;
            else if (fieldName == "remarks1") info.remarks1 = value;
            else if (fieldName == "remarks2") info.remarks2 = value;
            else if (fieldName == "remarks3") info.remarks3 = value;
        }
        return info;
    }

    // 从结构体转换为字符串列表
    QStringList toStringList(const QStringList& fieldNames) const {
        QStringList list;
        for (const QString& fieldName : fieldNames) {
            if (fieldName == "projectLot") list << projectLot;
            else if (fieldName == "projectName") list << projectName;
            else if (fieldName == "projectType") list << projectType;
            else if (fieldName == "ablationExpiration") list << ablationExpiration;
            else if (fieldName == "sampleCapacity") list << sampleCapacity;
            else if (fieldName == "internalStandardCapacity") list << internalStandardCapacity;
            else if (fieldName == "extractCapacity") list << extractCapacity;
            else if (fieldName == "pcrReagentCapacity") list << pcrReagentCapacity;
            else if (fieldName == "complexSolutionCapacity") list << complexSolutionCapacity;
            else if (fieldName == "extractName") list << extractName;
            else if (fieldName == "tecName") list << tecName;
            else if (fieldName == "tubeCount") list << tubeCount;
            else if (fieldName == "tubeInfo") list << tubeInfo;
            else if (fieldName == "calculation") list << calculation;
            else if (fieldName == "autoThreshold") list << autoThreshold;
            else if (fieldName == "thresholdValue") list << thresholdValue;
            else if (fieldName == "peakHeightValue") list << peakHeightValue;
            else if (fieldName == "meltingValue") list << meltingValue;
            else if (fieldName == "crossInterference") list << crossInterference;
            else if (fieldName == "ctThresholdValue") list << ctThresholdValue;
            else if (fieldName == "ISThresholdValue") list << ISThresholdValue;
            else if (fieldName == "ISConcentrationValue") list << ISConcentrationValue;
            else if (fieldName == "liftValue") list << liftValue;
            else if (fieldName == "ISCapacity") list << ISCapacity;
            else if (fieldName == "QCCapacity") list << QCCapacity;
            else if (fieldName == "QCTValueRange") list << QCTValueRange;
            else if (fieldName == "QConcentrationRange") list << QConcentrationRange;
            else if (fieldName == "PCTValueRange") list << PCTValueRange;
            else if (fieldName == "PConcentrationRange") list << PConcentrationRange;
            else if (fieldName == "QCNegativeCTValue") list << QCNegativeCTValue;
            else if (fieldName == "QCInternalCtCTValue") list << QCInternalCtCTValue;
            else if (fieldName == "QCInternalCtConcentrationValue") list << QCInternalCtConcentrationValue;
            else if (fieldName == "sampleCalibrationCapacity") list << sampleCalibrationCapacity;
            else if (fieldName == "internalStandardCalibrationCapacity") list << internalStandardCalibrationCapacity;
            else if (fieldName == "extractCalibrationCapacity") list << extractCalibrationCapacity;
            else if (fieldName == "pcrReagentCalibrationCapacity") list << pcrReagentCalibrationCapacity;
            else if (fieldName == "complexSolutionCalibrationCapacity") list << complexSolutionCalibrationCapacity;
            else if (fieldName == "addTime") list << addTime;
            else if (fieldName == "remark") list << remark;
            else if (fieldName == "remarks1") list << remarks1;
            else if (fieldName == "remarks2") list << remarks2;
            else if (fieldName == "remarks3") list << remarks3;
        }
        return list;
    }
};

// 业务层类
class CProjectInformation : public QObject
{
    Q_OBJECT
public:
    explicit CProjectInformation(QObject *parent = nullptr);
#if Q_OS_QML
    static QObject *qmlSingletonInstance(QQmlEngine *engine, QJSEngine *scriptEngine)
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static CProjectInformation &getInstance();

signals:
    void sigProjectInfomationChanged(); // 数据有更新，需要主动更新

public:
    Q_INVOKABLE void initDataBase();
    Q_INVOKABLE int getFieldLength();
    void updateDBFile(const QString &strNewFilePath);

    // 从CProjectInformation合并的函数
    Q_INVOKABLE void initData();
    SProjectInformationStruct getInfoFromLot(const QString &kstrProjectLot);
    SProjectInformationStruct getInfoFromName(const QString &kstrProjectName);
    Q_INVOKABLE QString getProjectLotFromProjectName(const QString &kstrProjectName);
    Q_INVOKABLE QString getProjectTypeFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getProjectNameFromLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getProjectLotFromQCCode(const QString &kstrWFQCCode);
    Q_INVOKABLE QString getProjectLotFromISCode(const QString &kstrWFQCCode);
    Q_INVOKABLE QString getProjectLotFromFLID(const QString &kstrFLID);
    Q_INVOKABLE QStringList getAllProjectNames();
    Q_INVOKABLE QStringList getAllProjectLots();

    // 获取判定阈值
    std::tuple<int, int> getCTThresholdValueRangeFromProjectLot(const QString &kstrProjectLot); // 定量CT值范围
    std::tuple<int, int> getISThresholdValueRangeFromProjectLot(const QString &kstrProjectLot); // 内标阈值范围
    std::tuple<int, int> getISConcentrationValueRangeFromProjectLot(const QString &kstrProjectLot); // 内标浓度范围
    // 获取质控判定阈值，强阳、弱阳、阴性、内标 及浓度
    std::tuple<int, int> getQCTValueRangeFromProjectLot(const QString &kstrProjectLot); // 强阳CT值范围
    std::tuple<int, int> getQConcentrationRangeFromProjectLot(const QString &kstrProjectLot); // 强阳浓度范围
    std::tuple<int, int> getPCTValueRangeFromProjectLot(const QString &kstrProjectLot); // 弱阳CT值范围
    std::tuple<int, int> getPConcentrationRangeFromProjectLot(const QString &kstrProjectLot); // 弱阳浓度范围
    std::tuple<int, int> getQCNegativeCTValueFromProjectLot(const QString &kstrProjectLot); // 质控阴性CT值
    std::tuple<int, int> getQCInternalCtCTValueFromProjectLot(const QString &kstrProjectLot); // 质控内标CT值
    std::tuple<int, int> getQCInternalCtConcentrationValueFromProjectLot(const QString &kstrProjectLot); // 质控内标浓度
    
    

    // 原有的get方法
    Q_INVOKABLE QStringList getProjectInfoStringFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getTubeInfoFromProjectLot(const QString &kstrProjectLot);    
    Q_INVOKABLE QString getTecNameFromProjectLot(const QString &kstrProjectLot);
    
    Q_INVOKABLE int getTubeCountFromProjectLot(const QString &kstrProjectLot);

    Q_INVOKABLE QString getTecNameFromProjectName(const QString &kstrProjectName);
    Q_INVOKABLE QString getExtractNameFromProjectName(const QString &kstrProjectName);
    Q_INVOKABLE QString getAblationExpirationFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getSampleCapacityFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getInternalStandardCapacityFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getExtractCapacityFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE QString getPcrReagentCapacityFromProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE std::tuple<int, QString, QString> getHistoryShowDateFromeProjectLot(const QString &kstrProjectLot);
    Q_INVOKABLE float getFloatFiledFromProjectLot(const QString &kstrProjectLot, const QString &kstrField);
    Q_INVOKABLE QString getStringFiledFromProjectLot(const QString &kstrProjectLot, const QString &kstrField);

    // update
    Q_INVOKABLE bool addOneProjectInfo(const QStringList &kstrDataList);
    Q_INVOKABLE bool updateTubeCountFromProjectLot(const QString &kstrProjectLot, const QString &kstrTubeCount);
    Q_INVOKABLE bool updateTubeInfoFromProjectLot(const QString &kstrProjectLot, const QString &kstrTubeInfo);
    Q_INVOKABLE bool updateProjTypeFromProjectLot(const QString &kstrProjectLot, const QString &kstrProjType);
    Q_INVOKABLE bool updateProjNameFromProjectLot(const QString &kstrProjectLot, const QString &kstrProjName);
    Q_INVOKABLE bool updateCalculationFromProjectLot(const QString &kstrProjectLot, const QString &kstrCalculation);
    Q_INVOKABLE bool updateAblationExpirationFromProjectLot(const QString &kstrProjectLot, const QString &kstrAblationExpiration);
    Q_INVOKABLE bool updateSampleCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrSampleCapacity);
    Q_INVOKABLE bool updateInternalStandardCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrInternalStandardCapacity);
    Q_INVOKABLE bool updateExtractCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrExtractCapacity);
    Q_INVOKABLE bool updatePcrReagentCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrPcrReagentCapacity);

    // delete
    Q_INVOKABLE bool deleteProjInfoFromProjectLot(const QString &kstrProjectLot);

private:
    void loadAllProjects();
    void _assignValuesToStruct(SProjectInformationStruct& projectInfo, const QStringList& strValuesList);

private:
    QMap<QString, SProjectInformationStruct> m_mapProjects;  // 内存中的项目缓存
    QMap<QString, SProjectInformationStruct> m_qProjectNameInformationMap; // projectName - map
    QStringList m_strProjectNameList;
    QStringList m_strProjectLotList;

    QMutex m_mutex;  // 用于线程安全
    CProjectDBImpl& m_dbImpl;  // 实际的数据库操作对象
};

// 数据库操作类
class CProjectDBImpl : public CDBObject
{
    Q_OBJECT
public:
    explicit CProjectDBImpl(QObject *parent = nullptr);
    static CProjectDBImpl& getInstance();

protected:
    QString getDatabasePath() const override {
        return CGlobalConfig::getInstance().getProjectDBDir();
    }
    QString getConnectionName() const override {
        return gk_strProjectDBReadConnect;
    }
    WriteConnectionPool *getWriteConnectionPool() override {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }

public:
    void initDataBase();
    int getFieldLength();
    void updateDBFile(const QString &strNewFilePath);

    // get
    QStringList getAllProjectLots();
    QStringList getProjectInfoFromProjectLot(const QString &kstrProjectLot);
    
    // update
    bool addOneProjectInfo(const QStringList &strDataList);
    bool updateTubeCountFromProjectLot(const QString &kstrProjectLot, const QString &kstrTubeCount);
    bool updateTubeInfoFromProjectLot(const QString &kstrProjectLot, const QString &kstrTubeInfo);
    bool updateProjTypeFromProjectLot(const QString &kstrProjectLot, const QString &kstrProjType);
    bool updateProjNameFromProjectLot(const QString &kstrProjectLot, const QString &kstrProjName);
    bool updateCalculationFromProjectLot(const QString &kstrProjectLot, const QString &kstrCalculation);
    bool updateAblationExpirationFromProjectLot(const QString &kstrProjectLot, const QString &kstrAblationExpiration);
    bool updateSampleCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrSampleCapacity);
    bool updateInternalStandardCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrInternalStandardCapacity);
    bool updateExtractCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrExtractCapacity);
    bool updatePcrReagentCapacityFromProjectLot(const QString &kstrProjectLot, const QString &kstrPcrReagentCapacity);

    // delete
    bool deleteProjInfoFromProjectLot(const QString &kstrProjectLot);

public:  // 改为public以允许CProjectDB访问
    struct FieldName_ProjectInfo {
        static inline const QString projectLot = "projectLot"; // 项目编号
        static inline const QString projectName = "projectName"; // 项目名称
        static inline const QString projectType = "projectType"; // 项目类型
        static inline const QString ablationExpiration = "ablationExpiration"; // 复溶效期
        static inline const QString sampleCapacity = "sampleCapacity"; // 样本容量
        static inline const QString internalStandardCapacity = "internalStandardCapacity"; // 内标容量
        static inline const QString extractCapacity = "extractCapacity"; // 提取容量
        static inline const QString pcrReagentCapacity = "pcrReagentCapacity"; // PCR试剂容量        
        static inline const QString complexSolutionCapacity = "complexSolutionCapacity"; // 复溶液容量
        static inline const QString extractName = "extractName"; // 提取名称
        static inline const QString tecName = "tecName"; // 扩增名称
        static inline const QString tubeCount = "tubeCount"; // 管数
        static inline const QString tubeInfo = "tubeInfo"; // 管信息
        static inline const QString calculation = "calculation"; // 计算方法
        static inline const QString autoThreshold = "autoThreshold"; // 自动阈值
        static inline const QString thresholdValue = "thresholdValue"; // 阈值-手动阈值
        static inline const QString peakHeightValue = "peakHeightValue"; // 峰高值
        static inline const QString meltingValue = "meltingValue"; // 熔解值
        static inline const QString crossInterference = "crossInterference"; // 交叉干扰
        static inline const QString ctThresholdValue = "ctThresholdValue"; // CT阳性判读阈值范围 10-40
        static inline const QString ISThresholdValue = "ISThresholdValue"; // 内标阈值
        static inline const QString ISConcentrationValue = "ISConcentrationValue"; // 内标浓度
        static inline const QString liftValue = "liftValue"; // 抬升值,每个通道一个,逗号分割
        static inline const QString ISCapacity = "ISCapacity"; // 内标总量
        static inline const QString QCCapacity = "QCCapacity"; // 质控总量
        static inline const QString QCTValueRange = "QCTValueRange"; // 强阳CT值范围,28-31
        static inline const QString QConcentrationRange = "QConcentrationRange"; // 强阳浓度范围0.23-1.22
        static inline const QString PCTValueRange = "PCTValueRange"; // 弱阳CT值范围,28-31 或者定性的阳性
        static inline const QString PConcentrationRange = "PConcentrationRange"; // 弱阳浓度范围0.001-0.01
        static inline const QString QCNegativeCTValue = "QCNegativeCTValue"; // 质控阴性CT值
        static inline const QString QCInternalCtCTValue = "QCInternalCtCTValue"; // 质控内标CT值
        static inline const QString QCInternalCtConcentrationValue = "QCInternalCtConcentrationValue"; // 质控内标浓度
        static inline const QString addTime = "addTime"; // 添加时间
        static inline const QString sampleCalibrationCapacity = "sampleCalibrationCapacity"; // 样本校准容量
        static inline const QString internalStandardCalibrationCapacity = "internalStandardCalibrationCapacity"; // 内标校准容量
        static inline const QString extractCalibrationCapacity = "extractCalibrationCapacity"; // 提取校准容量
        static inline const QString pcrReagentCalibrationCapacity = "pcrReagentCalibrationCapacity"; // PCR试剂校准容量
        static inline const QString complexSolutionCalibrationCapacity = "complexSolutionCalibrationCapacity"; // 复溶液校准容量
        static inline const QString remark = "remark"; // 备注
        static inline const QString remarks1 = "remarks1"; // 备注1
        static inline const QString remarks2 = "remarks2"; // 备注2
        static inline const QString remarks3 = "remarks3"; // 备注3
    };

    QString m_strTableProjectInfoName;
    FieldName_ProjectInfo FILED_ProjectInfo;
    QStringList m_strFieldNameProjectList;
};

#endif // CPROJECTDB_H
