#ifndef CRFIDWAITRSTHREAD_H
#define CRFIDWAITRSTHREAD_H

#include<QThread>
#include <QMutex>

class CRFIDWaitRsThread:public QObject
{
    Q_OBJECT
public:
    explicit CRFIDWaitRsThread(QObject *parent = nullptr);
    static CRFIDWaitRsThread &getInstance();
    void  InitMethodIDRsList();

 signals:
    void SignFeedBack(int iMethodID,int iStatus,int iType);//   //0 ok ,1  false,  -1:outtime
    void SignReadRFIDData(int iType,QString strData);
public slots:
    /**
     * @brief SlotMethodIDMotionRsMsg   解释回复的结果及Payload并压入list
     * @param qSendMsgAarry
     */
    void SlotMethodIDMotionRsMsg(QByteArray qSendMsgAarry);
    /**
     * @brief WaitStatusFeedBack  等待MethodID回复结果
     * @param iMethodID
     * @param strRs   payload
     * @param iMaxTime 超时时间
     * @return    //0 ok ,1  false,  -1:outtime
     */
    int SlotWaitStatusFeedBack(int iMethodID,int iType);

private:
    QMutex m_qMethodIDRsMutex;
    char *m_pFramePos;
    QByteArray m_qPayloadByteArray;
    QString m_qPayloadString;
    quint16 m_iReadPayloadLength;
    QThread *m_thread;
    QList<int>  m_MethodIDRsList;  //RC:0 代表成功  RC:1 代表失败
    QList<QString>  m_PayLoadList;
    QList<int>  m_SeqIDRsList;  //

};


#endif // CRFIDWAITRSTHREAD_H
