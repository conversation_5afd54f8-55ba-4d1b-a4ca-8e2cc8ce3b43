#ifndef CERRORHANDLER_H
#define CERRORHANDLER_H

#include <functional>
#include <QString>
#include <QMap>
#include <QThread>
#include <QMutex>
#include <atomic>
#include "affair/caffairbase.h"

/*
 * @author:mflin
 * @created:2024-6
 * @brief CErrorHandler 异常消息接收及处理类
 */

class CErrorHandler : public CAffairBase
{
    Q_OBJECT
public:
    static CErrorHandler& getInstance();

 signals:
    void sigStopProcess(bool bManualStop); //高等级异常需要发送停止时序信号

protected:
     void _HandleReceiveList() override; //异常处理
    //  void run() override;// 使用基类

private:
    explicit CErrorHandler(QObject *parent = nullptr);
    ~CErrorHandler();

private:
    QMap<QString, std::function<void(const QString&)>> m_specialHandlers;

};


#endif // CERRORHANDLER_H
