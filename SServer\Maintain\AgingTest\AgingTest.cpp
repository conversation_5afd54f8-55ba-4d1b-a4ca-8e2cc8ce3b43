#include<qdebug.h>
#include<QTime>
#include "publicconfig.h"
#include"affair/caffair.h"
#include "AgingTest.h"
#include "AgingConfigMgr.h"
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include"ccommunicationobject.h"
AgingTest::AgingTest()
{
    m_pAgingConfigMgr = &AgingConfigMgr::getInstance();
}

AgingTest::~AgingTest()
{

}

void AgingTest::AgingTestOperation(const QString strParams)
{
    QStringList listParams = strParams.split(":");
    if(listParams.size()!=2)  { qDebug()<<"Param error ,param = "<<strParams; return ;}
    if(listParams[0].toInt()!=ST_AGING_TEST) { qDebug()<<"Param error ,param = "<<strParams;return ; }

    QStringList sublistParams = listParams[1].split(",");
    if(listParams[1]=="")  { qDebug()<<"Param error ,param = "<<strParams;return ; }

    int iSubMethodID= sublistParams[0].toInt();

    switch (iSubMethodID)
    {
    case Method_AgingTest_AskActionList:
    {
        AskAgingTestActionList();
        break;
    }
    default:
    {
        qDebug()<<"Param Error,iSubMethodID = "<<iSubMethodID;
        break;
    }
    }
}

void AgingTest::HandleCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult)
{

}

void AgingTest::AskAgingTestActionList()
{
    QString strAction= m_pAgingConfigMgr->GetAgingActionList();
    QString strParams = QString("%1:%2,%3").arg(ST_AGING_TEST).arg(Method_AgingTest_AskActionList).arg(strAction);
    qDebug()<<"sendStringResult AskAgingTestActionList strParams="<<strParams;
    COperationUnit::getInstance().sendStringResult(Method_start, strParams, Machine_UpperHost);
}




