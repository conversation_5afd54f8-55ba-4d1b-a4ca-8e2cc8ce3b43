#ifndef ELECMAGNETICLOCK_H
#define ELECMAGNETICLOCK_H


#include<QObject>
#include<QVector>
#include <QMetaEnum> 
#include "publicconfig.h"

class ElecMagneticLock : public QObject
{
    Q_OBJECT
public:
    enum EnumLockState //0-解锁状态，1-上锁状态
    {
        Unlock,
        Lock,
        MotorUnlock, // 电机板锁(v0.4)
        MotorLock,   // 电机板锁(v0.4)
    };

    //0-所有区块的电磁锁、1-样本架、2-TIP架、3-PCR管架、4-冷藏仓试剂架、5-提取模块
    enum EnumLockType//锁类型
    {
        All,
        // Sampler, // v0.4版本去除
        Tip,
        PCR,
        // Reagent, // v0.4版本去除
        // Extract, // v0.4版本去除
    };   

    enum EnumMotorLockType//锁类型
    {
        MotorAll,
        MotorSampler,
        MotorExtract,
    };

    Q_ENUM( EnumLockType)

    enum EnumSamplerHolder
    {
        SamplerAll,
        Sampler1 = 1,//1-样本架1（左边起），2-样本架2
        Sampler2,
    }; 
    Q_ENUM( EnumSamplerHolder)

    enum EnumTipBox
    {
        Tip1 = 1,//1-Tip架1（右边起），2-Tip架2
        Tip2,
    }; 
    Q_ENUM( EnumTipBox)

    enum EnumTubeBox
    {
        Tube1 = 1,//1-PCR管架1（右边起），2-PCR管架2
        Tube2,
    };   
    Q_ENUM( EnumTubeBox)  

    enum EnumReagent
    {
        Strip1 = 1,//1-试剂条1（右边起），2-试剂条2，3-试剂条，4-试剂条4
        Strip2,
        Strip3,
        Strip4,
    };  
    Q_ENUM( EnumReagent) 
    
    enum EnumModule
    {
        ModuleExtract = 1,//1-提取模块
    };  
    Q_ENUM( EnumModule)            
public:
    explicit ElecMagneticLock(QObject *parent = nullptr);
public:
    /**
     * @brief SetRFIDElecMagneticLock 设置锁状态
     * @param state      锁状态
     * @param uiLockType 锁类型
     * @param uiIndex    类型对应的索引
     * @param machineID  机器ID，默认功能管理板
     * @return  
     */
    void SetRFIDElecMagneticLock(ElecMagneticLock::EnumLockState state,quint16 uiLockType,quint16 uiIndex,EnumMachineID machineID = Machine_Function_manager_Ctrl);

    /**
     * @brief ConvertRFIDConsumableType 根据耗材类型，转换到对应的索引
     * @param uiRFIDConsumableType     耗材类型
     * @return  
     */
    quint16 ConvertRFIDConsumableType(quint16 uiRFIDConsumableType);   

    /**
     * @brief SetAllElecMagneticUnlock 全部电磁锁解锁
     * @param 
     * @return  
     */
    void SetAllElecMagneticUnlock();     

    /**
     * @brief SetAllElecMagneticLock 全部电磁锁上锁
     * @param 
     * @return  
     */
    void SetAllElecMagneticLock();            

signals:

public slots:

private:

private:

};


#endif // ELECMAGNETICLOCK_H
