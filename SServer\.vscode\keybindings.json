[{"key": "ctrl+b", "command": "workbench.action.tasks.runTask", "args": "⚡ Fast Incremental Build", "when": "!inDebugMode"}, {"key": "ctrl+r", "command": "workbench.action.tasks.runTask", "args": "🏃 Build & Run", "when": "!inDebugMode"}, {"key": "ctrl+shift+b", "command": "workbench.action.tasks.runTask", "args": "make-debug", "when": "!inDebugMode"}, {"key": "ctrl+shift+r", "command": "workbench.action.tasks.runTask", "args": "🚀 Quick Run", "when": "!inDebugMode"}, {"key": "f5", "command": "workbench.action.tasks.runTask", "args": "🏃 Build & Run", "when": "!inDebugMode"}, {"key": "ctrl+shift+c", "command": "workbench.action.tasks.runTask", "args": "clean"}, {"key": "ctrl+`", "command": "terminal.focus"}, {"key": "ctrl+shift+`", "command": "workbench.action.terminal.new"}, {"key": "ctrl+shift+up", "command": "editor.action.moveLinesUpAction", "when": "editorTextFocus && !editorR<PERSON>only"}, {"key": "ctrl+shift+down", "command": "editor.action.moveLinesDownAction", "when": "editorTextFocus && !editorR<PERSON>only"}]