#ifndef CSAMPLESCANNERCTRL_H
#define CSAMPLESCANNERCTRL_H

#include <QMutex>
#include"cextractscannerthread.h"
#include "error/errorconfig.h"
#include <QHash> 

#define MAX_SACN_SAMPLE_ROW 8
class CSampleScannerCtrl:public QObject
{
    Q_OBJECT
public:
    explicit CSampleScannerCtrl(QString strSerialName,QString strBandRate,QString strScannerName);
    ~CSampleScannerCtrl();

    /**
     * @brief OpenDoSingleScanCmd 打开扫码
    */      
    int OpenDoSingleScanCmd();

    /**
     * @brief CloseSingleScanCmd 关闭扫码
    */      
    int CloseSingleScanCmd();  

    /**
     * @brief GetSampleNextPos 获取下一步扫码位置(调用会累加)
    */  
    qint8 GetSampleNextPos();

    /**
     * @brief GetSampleCurPos 获取当前扫码位置
    */       
    qint8 GetSampleCurPos();

    /**
     * @brief IsScanFinish 扫码是否结束
    */      
    bool IsScanFinish();

    /**
     * @brief ResetSamplePos 重置样本位置和缓存数据
    */      
    void ResetSamplePos();

    /**
     * @brief GetSampleCodeResult 获取整个器扫码结果
     * @param result  整个器扫码结果
    */     
    void GetSampleCodeResult(QStringList &result);

    /**
     * @brief GetSampleCodeAllResult 获取扫码结果(合并左右扫码结果)
     * @param resultLeft  获取左侧扫码结果
     * @param resultRight 获取右侧扫码结果
    */    
    QStringList GetSampleCodeAllResult(QStringList &resultLeft, QStringList &resultRight);

    /**
     * @brief GetSampleCodeResult 获取指定扫码结果
     * @param result  位置索引
    */     
    QString GetSampleCodeScanSingleResult(qint8 uiIndex); 

    /**
     * @brief GetInitStatus 获取初始化扫码结果
     * @return  返回初始化状态
    */         
     bool GetInitStatus();

    /**
     * @brief SetMaxSampleRowPos 设置最大样本行数
     * @param uiRow  行数
    */   
    void SetMaxSampleRowPos(const quint8 uiRow);

    /**
     * @brief SetSampleCodeScanTubeExist 设置样本管是否存在
     * @param bExist  是否存在
    */     
    void SetSampleCodeScanTubeExist(bool bExist);   
    
    /**
     * @brief SetSampleCodeScanAllTubeExist 设置样本管是否存在
     * @param strParam  当前扫码器样本管有无信息
    */     
   void SetSampleCodeScanAllTubeExist(const QString strParam);    
   
    /**
     * @brief GetSampleCodeScanTubeExist 获取样本有无状态
     * @param
    */     
   bool GetSampleCodeScanTubeExist();     
signals:
    /**
     * @brief sigWriteData 触发写信号
     * @param qByteArray 写入数据
     */
    void sigWriteData(QByteArray qByteArray);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    /**
     * @brief slotReceSerialData 异常信号
     * @param data 接收数据
     */
    void slotReceSerialData(QByteArray data);

    /**
     * @brief InitScanCodeSetting
     * 初始样本扫码器的操作
     */
    void InitScanCodeSetting();

private:
    /**
     * @brief _doSendCmd 发送数据到串口
     * @param qSendData  发送数据
     */     
    void _doSendCmd(QByteArray qSendData);

private:
    CExtractScannerThread  *pExtractScannerThread;                     // 串口线程(名称后期修改)
    QThread                *m_pThreadForInit;                          // 初始化线程
    QString                 m_strScannerName;                          // 扫码器名称(区分左右扫码器)
    qint8                   m_uiCountSamplePos;                        // 当前扫码样本位置
    QHash<qint8,QString>    m_hashSampleCodeResult;                    // 扫码结果保存
    qint8                   m_uiMaxsamplePos = MAX_SACN_SAMPLE_ROW;    // 位置最大为8
    QMutex                  m_qMutex;
    bool                    m_bInitOk;                                 // 初始化是否成功(使用版本号判断)
    QHash<qint8,bool>       m_hashSampleCodeExist;                     // 样本是否存在
};
#endif // CSAMPLESCANNERCTRL_H
