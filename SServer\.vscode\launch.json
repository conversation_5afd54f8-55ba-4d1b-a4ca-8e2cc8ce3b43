{"version": "0.2.0", "configurations": [{"name": "Debug Qt Application", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}\\build\\debug\\${workspaceFolderBasename}.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\build\\debug", "environment": [{"name": "PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"}, {"name": "QT_QPA_PLATFORM_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QT_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QML2_IMPORT_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml"}, {"name": "QT_LOGGING_RULES", "value": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true"}, {"name": "QT_FORCE_STDERR_LOGGING", "value": "1"}, {"name": "QT_MESSAGE_PATTERN", "value": "[%{time h:mm:ss.zzz}] %{category}: %{message}"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "Skip loading symbols from system libraries", "text": "-gdb-set auto-solib-add off", "ignoreFailures": true}, {"description": "Set max value size for gdb", "text": "-gdb-set max-value-size unlimited", "ignoreFailures": true}, {"description": "Set pagination off", "text": "-gdb-set pagination off", "ignoreFailures": true}], "preLaunchTask": "make-debug", "logging": {"engineLogging": false, "moduleLoad": false, "programOutput": true, "trace": false}, "console": "integratedTerminal"}, {"name": "Release Qt Application", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}\\build\\release\\${workspaceFolderBasename}.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\build\\release", "environment": [{"name": "PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"}, {"name": "QT_QPA_PLATFORM_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QT_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QML2_IMPORT_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml"}, {"name": "QT_LOGGING_RULES", "value": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true"}, {"name": "QT_FORCE_STDERR_LOGGING", "value": "1"}, {"name": "QT_MESSAGE_PATTERN", "value": "[%{time h:mm:ss.zzz}] %{category}: %{message}"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "make-release", "logging": {"engineLogging": false, "moduleLoad": false, "programOutput": true, "trace": false}, "console": "integratedTerminal"}, {"name": "Run Qt Application (No Debug)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}\\build\\debug\\${workspaceFolderBasename}.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\build\\debug", "environment": [{"name": "PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin;${env:PATH}"}, {"name": "QT_QPA_PLATFORM_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QT_PLUGIN_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins"}, {"name": "QML2_IMPORT_PATH", "value": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "D:/QT/Qt5.12.8/Tools/mingw730_64/bin/gdb.exe", "setupCommands": [{"description": "Start without debug symbols", "text": "-gdb-set auto-solib-add off", "ignoreFailures": true}], "preLaunchTask": "make-debug", "logging": {"engineLogging": false, "moduleLoad": false, "programOutput": true, "trace": false}, "console": "integratedTerminal"}, {"name": "🚀 Direct Run (推荐)", "type": "process", "request": "launch", "command": "${workspaceFolder}\\build\\debug\\${workspaceFolderBasename}.exe", "cwd": "${workspaceFolder}\\build\\debug", "env": {"PATH": "${env:PATH};D:/QT/Qt5.12.8/5.12.8/mingw73_64/bin;D:/QT/Qt5.12.8/Tools/mingw730_64/bin", "QT_QPA_PLATFORM_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins", "QT_PLUGIN_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/plugins", "QML2_IMPORT_PATH": "D:/QT/Qt5.12.8/5.12.8/mingw73_64/qml", "QT_LOGGING_RULES": "default.debug=true;qt.*.debug=false;*.warning=true;*.critical=true;*.fatal=true", "QT_FORCE_STDERR_LOGGING": "1", "QT_MESSAGE_PATTERN": "[%{time h:mm:ss.zzz}] %{category}: %{message}"}, "preLaunchTask": "make-debug", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}}]}