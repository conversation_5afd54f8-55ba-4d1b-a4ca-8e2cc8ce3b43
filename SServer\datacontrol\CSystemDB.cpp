#include "CSystemDB.h"
#include <QDebug>
#include <QSqlError>
#include <QString>
#include "cglobalconfig.h"
#include "publicfunction.h"
#include "cmainwindow.h"

// CSystemDB 实现
CSystemDB::CSystemDB(QObject *parent)
    : QObject(parent)
    , m_dbImpl(CSystemDBImpl::getInstance())
{
}

CSystemDB& CSystemDB::getInstance()
{
    static CSystemDB instance;
    return instance;
}

void CSystemDB::initDataBase()
{
    m_dbImpl.initDataBase();
    m_dbImpl.setDefaultValue();
    loadAllConfigs();
}

void CSystemDB::updateDBFile(const QString &strNewFilePath)
{
    m_dbImpl.updateDBFile(strNewFilePath);
    m_dbImpl.setDefaultValue();
    loadAllConfigs();
}

void CSystemDB::loadAllConfigs()
{
    QMutexLocker locker(&m_mutex);
    m_mapConfigs = m_dbImpl.getAllConfigs();
}

QVariant CSystemDB::getValueFromKey(QVariant qKey)
{
    QMutexLocker locker(&m_mutex);
    return m_mapConfigs.value(qKey.toString());
}

int CSystemDB::getIntValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toInt();
}

float CSystemDB::getFloatValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toFloat();
}

bool CSystemDB::getBoolValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toBool();
}

QString CSystemDB::getStringValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toString();
}

bool CSystemDB::addKeyValue(QVariant qKey, QVariant qValue)
{
    bool bResult = m_dbImpl.addKeyValue(qKey, qValue);
    if (bResult)
    {
        QMutexLocker locker(&m_mutex);
        m_mapConfigs[qKey.toString()] = qValue;
    }
    return bResult;
}

// CSystemDBImpl 实现
CSystemDBImpl::CSystemDBImpl(QObject *parent) : CDBObject(parent)
{
    m_strTableConfigName = "config";
    m_strFieldNameConfigList << FLIED_Config.key << FLIED_Config.value;
    m_iConfigTableColumnCount = m_strFieldNameConfigList.length();
}

CSystemDBImpl &CSystemDBImpl::getInstance()
{
    static CSystemDBImpl instance;
    return instance;
}

void CSystemDBImpl::initDataBase()
{
    QList<QStringList> strFieldNameLists;
    strFieldNameLists << m_strFieldNameConfigList;
    QStringList strTableNameList;
    strTableNameList << m_strTableConfigName;

    QVector<QPair<QString, QString>> fieldsAndTypes;
    QStringList strFieldNameList;
    for (int i = 0; i != strFieldNameLists.length(); ++i)
    {
        fieldsAndTypes.clear();
        strFieldNameList = strFieldNameLists.at(i);
        for (auto strField : strFieldNameList)
        {
            fieldsAndTypes.push_back({strField, "VARCHAR"});
        }

        if (this->createDBTable(strTableNameList.at(i), fieldsAndTypes))
        {
            QDFUN_LINE << strTableNameList.at(i) << "Table created successfully";
        }
        else
        {
            QDFUN_LINE << strTableNameList.at(i) << "Failed to create table";
        }
    }
}

void CSystemDBImpl::updateDBFile(const QString &strNewFilePath)
{
    // 获取旧数据库文件路径
    QString oldDbPath = CGlobalConfig::getInstance().GetSystemDBDir();

    try
    {
        // 首先关闭已有的数据库连接
        closeAllConnections();

        // 把新的数据库文件复制到旧的位置，先确定存在新文件，否则不执行删除
        if (QFile::exists(strNewFilePath))
        {
            if (QFile::exists(oldDbPath))
            { // 如果旧的数据库文件存在，删除它
                QFile::remove(oldDbPath);
            }
            QFile::copy(strNewFilePath, oldDbPath);
        }
        else
        {
            QDFUN_LINE << "Failed to verify new database connection";
        }
        //建立新连接
        initDataBase();
    }
    catch (const std::exception &e)
    {
        qWarning() << "Failed to verify new database connection";
    }
}

void CSystemDBImpl::setDefaultValue()
{
    QVector<std::pair<QString, QVariant>> strDefaultValueList =
    {
        {"Connect_127", "0"}, // 默认没有连接127.0.0.1
        {"Start_Reset", "0"}, // 默认没有开机自检
    };
    for (const auto& item : strDefaultValueList)
    {
        const QString& key = item.first;
        const QVariant& defaultValue = item.second;

        if (!_getKayIsExist(key))
        {
            addKeyValue(key, defaultValue);
        }
    }
}

QVariant CSystemDBImpl::getValueFromKey(QVariant qKey)
{
    QString strKey = qKey.toString();
    QVariant strContent = "";
    if (strKey == "")
    {
        return strContent;
    }
    QMap<QString, QString> strConditionMap =
        {{FLIED_Config.key, strKey}};

    strContent = this->getOneRecordOneField(m_strTableConfigName, FLIED_Config.value, strConditionMap).toString();
    return strContent;
}

int CSystemDBImpl::getIntValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toInt();
}

float CSystemDBImpl::getFloatValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toFloat();
}

bool CSystemDBImpl::getBoolValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toBool();
}

QString CSystemDBImpl::getStringValueFromKey(QVariant qKey)
{
    return getValueFromKey(qKey).toString();
}

bool CSystemDBImpl::addKeyValue(QVariant qKey, QVariant qValue)
{
    QStringList strVauleList = {qKey.toString(), qValue.toString()};
    bool bResult = this->addOneDBRecord(m_strTableConfigName,
                                      m_strFieldNameConfigList, strVauleList, FLIED_Config.key);
    QDFUN_LINE << m_strTableConfigName
              << m_strFieldNameConfigList << strVauleList;
    return bResult;
}

bool CSystemDBImpl::_getKayIsExist(QVariant qKey)
{
    QVariant vValue = getValueFromKey(qKey);
    if (vValue.isNull())
    {
        return false;
    }
    return true;
}

QMap<QString, QVariant> CSystemDBImpl::getAllConfigs()
{
    QMap<QString, QVariant> mapConfigs;
    QStringList strFiledList = {FLIED_Config.key, FLIED_Config.value};
    QList<QMap<QString, QString>> strTableData = this->getMoreRecordsMoreFieldsMap(m_strTableConfigName,
                                                                                    strFiledList);
    for (const auto& item : strTableData)
    {
        mapConfigs[item[FLIED_Config.key]] = item[FLIED_Config.value];
    }
    return mapConfigs;
}
