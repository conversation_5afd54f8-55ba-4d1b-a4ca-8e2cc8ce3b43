#include <QCoreApplication>
#include <QSharedMemory>
#include <QDebug>
#include "cmainwindow.h"

#ifdef Q_OS_LINUX
#include <execinfo.h>
#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <csignal>
#include <execinfo.h>
#include <pthread.h>
#endif

#include "log/cspdlogger.h"
#include "affair/caffairbase.h"
#include"./error/errorconfig.h"
QSharedMemory *shareMem;

#ifdef Q_OS_LINUX
// 段错误信息输出
void signalHandler(int signum) {
    // Get the current thread ID
        pthread_t threadId = pthread_self();

        // Log the thread ID
        qCritical() <<  "Thread ID: {}"<< threadId;

        // Get and log the backtrace (stack trace) for the current thread
        void* callstack[128];
        int frames = backtrace(callstack, sizeof(callstack) / sizeof(callstack[0]));
        char** strs = backtrace_symbols(callstack, frames);
        if (strs) {
            for (int i = 0; i < frames; ++i) {
                qCritical() << "Stack trace: " << i << strs[i];
            }
            free(strs);
        }
        if (shareMem->isAttached()) {
            shareMem->detach();
        }
        // 还原默认信号处理器
        signal(signum, SIG_DFL);

        // 重新发送信号：此时内核会触发 core dump
        raise(signum);
}
void setupSignalHandlers() {
        signal(SIGINT, signalHandler);
        signal(SIGILL, signalHandler);
        signal(SIGABRT, signalHandler);
        signal(SIGFPE, signalHandler);
        //    signal(SIGTERM, signalHandler);
        signal(SIGHUP, signalHandler);
        signal(SIGQUIT, signalHandler);
        signal(SIGTRAP, signalHandler);
        signal(SIGPIPE, signalHandler);
        signal(SIGALRM, signalHandler);
        // signal(SIGKILL, handleSignal); // 无法捕获
        signal(SIGSEGV, signalHandler); // Segmentation Fault
        // 可以继续添加更多的信号处理
}
#endif

int main(int argc, char *argv[])
{

#ifdef Q_OS_LINUX
    // Register the signalHandler for SIGSEGV (Segmentation Fault)
    setupSignalHandlers();
#endif

    shareMem = new QSharedMemory("SServer");

    // Check if the shared memory already exists
    if (shareMem->attach(QSharedMemory::ReadOnly)) {
        qDebug() << "SServer ReadOnly";
        // If it can be attached, it means it exists but not in use by another process
        shareMem->detach();
    }
    if (!shareMem->create(1))//创建大小1b的内存
    {
        qDebug() << "SServer is exist";
        qApp->quit(); //创建失败，说明已经有一个程序运行，退出当前程序
        return -1;
    }
    QCoreApplication a(argc, argv);
    qRegisterMetaType<MidMachineSubmodule>("MidMachineSubmodule");
     qRegisterMetaType<EnumMachineID>("EnumMachineID");
    // In your main function, before a.exec()
    QObject::connect(&a, &QCoreApplication::aboutToQuit, []() {
        if (shareMem && shareMem->isAttached()) {
            qDebug() << "SServer quit";
            shareMem->detach();
        }
    });
    CSpdLogger::init("s_spdlog.ini");

    CMainWindow cMainWindow;

    return a.exec();
}
