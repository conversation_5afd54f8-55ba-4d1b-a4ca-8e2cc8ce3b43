#ifndef CCOMMUNICATIONOBJECT_H
#define CCOMMUNICATIONOBJECT_H

#include <QObject>

#include "control/ctcpanalyzethread.h"
#include "control/ccananalyzethread.h"
#include "control/ctcpserverthread.h"
#include "control/ccanbusdevicethread.h"
#include "control/cserialdevicethread.h"
#include "control/cftpserverthread.h"
#include "control/cudpserverthread.h"
#include "window/ccan0window.h"
#include "window/ccan1window.h"
#include "window/cclientwindow.h"
#include "window/cpcrwindow.h"
#include "window/cserialwindow.h"
#include "window/cvirtualwindow.h"
#include "CodeScan/cextractscannerthread.h"
#include "CodeScan/CZebraScannerCtrl.h"
#include "error/errorconfig.h"
#include "CodeScan/CSampleScannerCtrl.h"

class CCommunicationObject : public QObject
{
    Q_OBJECT
public:
    explicit CCommunicationObject(QObject *parent = nullptr);
    ~CCommunicationObject();
    static CCommunicationObject &getInstance();

signals:
    void sigSendMessageToMainWindow(QByteArray qSendByteArray);// 发送至中位机处理
    // udp
    void sigNewTcpServerConnect(bool bConected);
    void sigNewPCRServerConnect(bool bConected);
    // 发送数据至
    void sigSendTcpServerMessage_0(QByteArray qSendMsgAarry);
    void sigSendTcpPCRMessage_1(QByteArray qSendMsgAarry);
    void sigRetryInitServer_0(QHostAddress qHostAddress, QString strHostMac);

    void sigSendCanbusMessage_0(QByteArray qSendMsgAarry);
    void sigSendCanbusMessage_1(QByteArray qSendMsgAarry);

    void sigSendSerialMessage(QByteArray qSendMsgAarry);
    void sigSendMessageToScanner(QByteArray qSendMsgAarry);
    void sigSendExtractScanner(QByteArray qSendMsgAarry);
    // 虚拟调试
    void sigSendVirtualMessage(QByteArray qSendMsgAarry);
    // 流程调试
    // ftp
    void sigSendFTPFile(QString strFileName);
    void sigZebraScannerRs(QString dataRs,int iStatus);

public slots:
    // TCP Server
    void slotNewConnectFromCmdNetwork_0(QString strIP, int iPort, bool bConnected);
    void slotACKOutFromCmdNetwork_0(QString strIP, int iPort);
    // TCP PCR
    void slotNewConnectFromCmdNetwork_1(QString strIP, int iPort, bool bConnected);
    void slotACKOutFromCmdNetwork_1(QString strIP, int iPort);

    // Can0
    void slotACKOutFromCanBus_0();
    void slotNewConnectFromCanBus_0(bool bConnect);

    // Can1
    void slotACKOutFromCanBus_1();
    void slotNewConnectFromCanBus_1(bool bConnect);
    // 串口
    void slotACKOutFromSerial();
    void slotNewConnectFromSerial(bool bConnect);
    // 提取条扫码
    void slotReciveMessageFromExtractScanner(QByteArray qReciveMsgAarry);
    //
    void sendMessageToMachine(const QByteArray &qSendByteArray, const quint8 &eMachineID);
    // ftp
    void slotReciveFileFinished(QString strFileName);

    //发送触发扫码动斑马扫码器，会有超时回复等指令
    int sendStartScanCmdToZebraScanner();
    int closeZebraScanner();
    void slotRevZebraScannerRs(QString dataRs,int iStatus);
    //处理异常信号，并组成异常通知相应
    void handleError(QString strExtraInfo, MidMachineSubmodule subModule, ErrorID errorID);
    
    /**
     * @brief StartSamplerCodeScanner 开启样本扫码器(两个)
     * @param 
     */        
    void StartSamplerCodeScanner();

    /**
     * @brief StartSingleSamplerCodeScanner 开启样本扫码器(单个)
     * @param bLeft 是否是左侧扫码器
     */        
    void StartSingleSamplerCodeScanner(bool bLeft=true);

    /**
     * @brief StopSamplerCodeScanner 停止样本扫码器(两个)
     * @param 
     */      
    void StopSamplerCodeScanner();

    /**
     * @brief GetSamplerScanNextPos 获取下一个样本位置
     * @return 返回样本位置
     */      
    qint8 GetSamplerScanNextPos();

    /**
     * @brief GetSamplerScanCurPos 获取当前样本位置
     * @return 返回当前位置
     */     
    qint8 GetSamplerScanCurPos();

    /**
     * @brief IsSamplerScanFinish 扫码是否完成
     * @return 扫码完成状态
     */        
    bool IsSamplerScanFinish();  

    /**
     * @brief ResetSampleScanPos 重置扫码状态
     * @return 
     */      
    void ResetSampleScanPos(); 

    /**
     * @brief GetSampleCodeScanLeftResult 获取左边样本扫码结果
     * @param result 扫码结果
     */        
    void GetSampleCodeScanLeftResult(QStringList &result);

    /**
     * @brief GetSampleCodeScanRightResult 获取右边样本扫码结果
     * @param uiSampleIndex 扫码结果
     */        
    void GetSampleCodeScanRightResult(QStringList &result);
    
    /**
     * @brief GetSampleCodeScanAllResult 获取全部扫码全部结果(扫码完成)
     * @param resultLeft 扫码左边结果
     * @param resultRight 扫码右边结果
     * @return 返回扫码全部结果
     */        
    QStringList GetSampleCodeScanAllResult(QStringList &resultLeft, QStringList &resultRight); 
    
    /**
     * @brief GetSampleCodeScanSingleResult 获取单个扫码结果
     * @param uiIndex 扫码结果索引
     */        
    bool GetSampleCodeScanSingleResult(qint8 uiIndex);
    
    /**
     * @brief GetInitStatus 获取初始化扫码结果
     * @return  返回初始化状态
    */         
     bool GetSampleCodeScanInitStatus();

    /**
     * @brief SetSampleCodeMaxSampleRow 设置最大样本行数
     * @param uiRow  行数
    */  
     void SetSampleCodeMaxSampleRow(const quint8 uiRow);

    /**
     * @brief SetSampleCodeScanTubeExist 设置样本有无
     * @param bLeft  左样本管，true:左，false:右
     * @param bExist  存在状态
    */  
     void SetSampleCodeScanTubeExist(bool bLeft = true, bool bExist = true);

    /**
     * @brief GetSampleCodeScanTubeExist 获取位置样本有无
     * @param bLeft  左样本管，true:左，false:右
     * @return  存在状态
    */  
     bool GetSampleCodeScanTubeExist(bool bLeft = true);

    /**
     * @brief SetSampleCodeScanAllTubeExist 设置全部样本有无
     * @param bLeft  左样本管，true:左，false:右
     * @param bExist  存在状态
    */  
     void SetSampleCodeScanAllTubeExist(const QString strParam);     

    /**
     * @brief GetSampleCodeScanResult 获取扫码结果
     * @return  返回扫码结果
     */
     QString GetSampleCodeScanResult();

     void GetExtractScanLastRs(QString &strRs);//取出提取扫码最后的结果


private slots:
    void _slotRetryInitServer();
    void _slotRetryEth0InitServer();
public:
    bool getClientConnect();
    bool getPCRConnect();
    void senFTPFile(QString strFileName);
private:
    void _initCommunication();
    void _initData();
    void _initObject();
private:
    QByteArray m_qbReadBuffTempArray;
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    char *m_pFramePos;
    // TCP UDP
    CUdpServerThread *m_pCUdpServerThread;
    // TCP Server
    CTcpServerThread *m_pCmdTcpServerThread_0;
    CTcpAnalyzeThread *m_pCmdTcpAnalyzeThread_0;
    CClientWindow *m_pCClientWindow;
    bool m_bClientConnected;
    // TCP PCR
    CTcpServerThread *m_pCmdTcpServerThread_1;
    CTcpAnalyzeThread *m_pCmdTcpAnalyzeThread_1;
    CPcrWindow *m_pCPcrWindow;
    bool m_bPCRConnected;
    // Can0
    CCanBusDeviceThread *m_pCCanBusDeviceThread_0;
    CCanAnalyzeThread *m_pCanBusAnalyzeThread_0;
    CCan0Window *m_pCCan0Window;
    // Can1
    CCanBusDeviceThread *m_pCCanBusDeviceThread_1;
    CCanAnalyzeThread *m_pCanBusAnalyzeThread_1;
    CCan1Window *m_pCCan1Window;
    // 串口
    CSerialDeviceThread *m_pCSerialDeviceThreadd;
    CTcpAnalyzeThread *m_pSerialAnalyzeThread;
    CSerialWindow *m_pCSerialWindow;

    // 提取扫码
    //CExtractScannerThread *m_pCExtractScannerThread;
    CZebraScannerCtrl *m_pCZebraScannerCtrl;

    // 样本扫码(左右)
    CSampleScannerCtrl *m_pSampleCodeScannerLeft;
    CSampleScannerCtrl *m_pSampleCodeScannerRight;
    QString m_strSampleCodeScanLastRs; //保存扫码结果

    // 虚拟通讯业务
    CVirtualWindow *m_pCVirtualWindow;
    // ftp
    CFtpServerThread *m_pCFtpServerThread;
    // retry
    QTimer *m_pRetryInitServerTimer;
    bool m_bInitServer;
    QHostAddress m_qEth1Address;
    QHostAddress m_qWlan0Address;
    QHostAddress m_qTCPServerHostAddress;
    QString m_strTCPHostMacAddress;
    QMutex m_DeviceOperateMutex;

    bool m_bEnableCan1;
};

#endif // CCOMMUNICATIONOBJECT_H
