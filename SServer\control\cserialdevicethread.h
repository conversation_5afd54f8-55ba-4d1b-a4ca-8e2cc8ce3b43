#ifndef CSERIALDEVICETHREAD_H
#define CSERIALDEVICETHREAD_H

#include <QObject>
#include <QThread>
#include <QSerialPort>
#include <QTimer>
#include <QTime>
#include <atomic>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CSerialDeviceThread : public QObject
{
    Q_OBJECT
public:
    explicit CSerialDeviceThread(QString strSerialName,
                                 QString m_strBandRate,QObject *parent = nullptr);
    ~CSerialDeviceThread();

signals:
    void sigReadTimer();
    void sigResetCom(QString,QString);// 打开串口
    void sigReciveMessage(QByteArray qByteArray);//
    void sigACKOut();
    void sigNewConnect(bool bOpen);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);
public slots:
    void slotWaitACK(quint16 iFrameNumber);
    void slotSendAckBack(QByteArray qSendMsgAarry);//
    void slotSendMessage(QByteArray qMsgBtyeArray);//
    void slotSendMessageNoList(QByteArray qSendMsgAarry);//
private slots:
    void _slotResetCom(QString,QString);
    void _slotReadFramesTimer();
    void _slotStartTimer();
    void _slotReSendTimer();
public:
    void ReSetCom(QString strComName,QString strBandRate);// 重新打开新串口
private:
    void _InitPort(QString strComName,QString strBandRate);
    void _sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend = false);
    void _reSetFrameNumber(QByteArray & qByteArray);// 重置发送帧号及CRC
private:
    QTimer *m_pReadFramesTimer;
    QThread *m_pThread;
    // 串口
    QString m_strSerialName;
    QSerialPort *m_pSerialPort;
    bool m_bOpenSerialPort;
    QString m_strBandRate;
    // 数据
    QByteArray m_sCurrentSendMessage;
    QByteArray m_qReadFrameByteArray;    //
    QByteArray m_qSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;
    // 重发队列
    QByteArray m_qReSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iReSendWriteIndex{0};
    std::atomic<int> m_iReSendReadIndex{0};
    int m_iReSendNextWriteIndex;
    int m_iReSendCurrentWriteIndex;
    // 重发机制中的环形队列
    bool m_bWaitAck[BUFFER_SIZE];
    QTimer *m_pResendTimer;
    MessageInfo m_sRingMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iRingWriteIndex{0};
    std::atomic<int> m_iRingReadIndex{0};
    int m_iRingCurrentWriteIndex;
    int m_iRingNextWriteIndex;
    int m_iRingCurrentReadIndex;
    quint16 m_iSeqNumber;// 帧号 0-65535
    char *m_pFramePos;
    //
    quint8 m_iCmdID;
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iSourceID;
    quint8 m_iSync;
};

#endif // CSERIALDEVICETHREAD_H
