

#include "DSBCL95.h"
#include <map>
#include <QByteArray>
#include <QDebug>

std::map<int, bclItem_t> kBCLCmdMap = {
    //BCL_CMD_INFOS
    {BCL_CMD_VERSION, {"V", BC<PERSON>_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_OPEN_SINGLE_TRIG, {"+", BCL_CMD_TYPE_NORMAL, 5, false}},
    {BCL_CMD_CLOSE_SINGLE_TRIG, {"-", BCL_CMD_TYPE_NORMAL, 5, false}},
    {BCL_CMD_OPEN_CONT_TRIG, {"C+", BCL_CMD_TYPE_NORMAL, 5, false}},
    {BCL_CMD_CLOSE_CONT_TRIG, {"C-", BCL_CMD_TYPE_NORMAL, 5, false}},
    {BCL_CMD_RESET, {"H", BCL_CMD_TYPE_NORMAL, 5, true}},
    {BCL_CMD_ROLL_BACK_FACTORY, {"PC20", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_OPEN_AUTO_TRIG, {"PT00203401", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_CLOSE_AUTO_TRIG, {"PT00203400", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_READ_TRIG_STATE, {"PR00034001", BCL_CMD_TYPE_DATA, 5, true}},

    {BCL_CMD_READ_ONCE, {"PT0020351", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_READ_BARCOE_NUM, {"PT00203202", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_OPEN_NO_DUPLICATE, {"PT002086A0", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_CLOSE_NO_DUPLICATE, {"PT00208620", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_ITF, {"PT00200000", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_ITF, {"PT00200001", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_Code_39, {"PT00200400", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_Code_39, {"PT00200402", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_Code_128, {"PT00200800", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_Code_128, {"PT00200808", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_UPC, {"PT00201200", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_UPC, {"PT00201206", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_EAN, {"PT00201600", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_EAN, {"PT00201607", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_Codebar, {"PT00202000", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_Codebar, {"PT0020200B", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_CLOSE_Code_93, {"PT00202400", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_OPEN_Code_93, {"PT0020240C", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},

    {BCL_CMD_FIX_NUM_Code_ITF, {"PT0020010A", BCL_CMD_TYPE_SET_NO_PARAM, 5, true}},
    {BCL_CMD_MIN_NUM_Code_ITF, {"PT00200141", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_Code_ITF, {"PT0020021E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_Code_39, {"PT00200541", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_Code_39, {"PT0020061E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_Code_128, {"PT00200941", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_Code_128, {"PT0020101E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_UPC, {"PT00201341", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_UPC, {"PT0020141E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_EAN, {"PT00201741", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_EAN, {"PT0020181E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_Codebar, {"PT00202141", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_Codebar, {"PT0020221E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_MIN_NUM_Code_93, {"PT00202541", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},
    {BCL_CMD_MAX_NUM_Code_93, {"PT0020261E", BCL_CMD_TYPE_SET_WITH_PARAM, 5, true}},

    {BCL_CMD_READ_Code_ITF_Data_Info, {"PR00001002", BCL_CMD_TYPE_DATA, 5, true}},
    //{BCL_CMD_READ_Code_ITF_Data_Info, {"PR00000003", BCL_CMD_TYPE_DATA, 5, true}},
   {BCL_CMD_READ_Code_39_Data_Info, {"PR00005002", BCL_CMD_TYPE_DATA, 5, true}},
    //{BCL_CMD_READ_Code_39_Data_Info, {"PR00004003", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_32_Data_Info, {"PR00029002", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_128_Data_Info, {"PR00009002", BCL_CMD_TYPE_DATA, 5, true}},
    //{BCL_CMD_READ_Code_128_Data_Info, {"PR00008003", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_UPC_Data_Info, {"PR00013002", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_EAN_Data_Info, {"PR00017002", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_Codebar_Data_Info, {"PR00021002", BCL_CMD_TYPE_DATA, 5, true}},
    {BCL_CMD_READ_Code_93_Data_Info, {"PR00025002", BCL_CMD_TYPE_DATA, 5, true}},

};

bclItem_t* GetBCLCmdItem(uint8_t cmdId)
{
    bclItem_t *item = nullptr;
    if(kBCLCmdMap.find(cmdId) != kBCLCmdMap.end())
    {
        item = &kBCLCmdMap[cmdId];
    }
    return item;
}


/*
DSBCL User Guide
Host Interface Signal Descriptions
    Signal          Description
    DOWNLOAD_IN_N   Flash download.
    VCC             Power.
    GND             Power.
    RxD             Input. Receive data
    TxD             Output. Transmit data
    CTS             Input. Clear to Send signal
    RTS             Output. Request to Send data
    POWER_DOWN      Output. When high, the decoder SE27XX is in low power mode.
    BEEPER_OUT_N    Output. Beeper: Low current beeper PWM output.
    HOST_DEC_LED_N  Output. Decode LED: Low current decode LED output.
    AIM_WAKE_IN_N   Input. Wake Up: In low power mode, pulse this pin low for 500 nsec to awaken the SE2707.
    TRIGGER_IN_N    Input. Drive this pin low to start a scan and decode session.

Host Serial Response Timeout
    SSI # 9Bh
    How long the scanner waits for an ACK or NAK before resending.
Host Character Timeout
    SSI # EFh
    The maximum time the scanner waits between characters transmitted by the host before discarding the received data and declaring an error.

Event Reporting
    Event Class     |   Event                   | Code Reported
    Decode Event    | Non-parameter decode      | 0x01
    Boot Up Event   | System power-up           | 0x03
    Parameter Event | Parameter entry error     | 0x07
                    | Parameter stored          | 0x08
                    | Defaults set              | 0x0A
                    | Number expected           | 0x0F
*/
#define kMaxResponseLen 50
uint8_t recvBuf[kMaxResponseLen];

BCL::BCL()
{
    _bufPrefix = recvBuf;
    _bufTail = _bufSuffix = _bufPrefix;
    _detected = false;
    _stat = ePrefix;
}

void BCL::init()
{

}

char ChangeInt2Char(int a)      //整型转换为字符型
{
    char b = 0x00;
    switch (a)
    {
    case 0:b = '0'; break;
    case 1:b = '1'; break;
    case 2:b = '2'; break;
    case 3:b = '3'; break;
    case 4:b = '4'; break;
    case 5:b = '5'; break;
    case 6:b = '6'; break;
    case 7:b = '7'; break;
    case 8:b = '8'; break;
    case 9:b = '9'; break;
    case 10:b = 'A'; break;
    case 11:b = 'B'; break;
    case 12:b = 'C'; break;
    case 13:b = 'D'; break;
    case 14:b = 'E'; break;
    case 15:b = 'F'; break;
    default:break;
    }
    return b;
}

bool BCL::getCmdData(uint8_t cmdId, char *cmd, uint8_t& cmdLen, uint8_t settingParam/* = 0*/)
{
    bool rc = false;
    bclItem_t* item = GetBCLCmdItem(cmdId);
    if(item)
    {
        int dataLen = strlen(item->cmd);
        cmdLen = dataLen + kFramePrefixLength + kFrameSuffixLength;//
        cmd[0] = kBCLPrefix1;
        char *suffix = &cmd[cmdLen-2];
        if (dataLen > 0) {
            memcpy(&cmd[kFramePrefixLength], item->cmd, cmdLen);
        }
        if(item->cmdType == BCL_CMD_TYPE_SET_WITH_PARAM)
        {
            //char paramHex[2] = {0};
            //paramHex[0] = settingParam/16;
            //paramHex[1] = settingParam%16;
            //cmd[kFramePrefixLength+cmdLen-2] = paramHex[0];
            //cmd[kFramePrefixLength+cmdLen-1] = paramHex[1];
            cmd[cmdLen-4] = ChangeInt2Char((int)settingParam/16);
            cmd[cmdLen-3] = ChangeInt2Char((int)settingParam%16);
            qDebug()<<__FUNCTION__<<"settingParam="<<settingParam;
            qDebug()<<__FUNCTION__<<"cmd[cmdLen-4]="<<cmd[cmdLen-4];
            qDebug()<<__FUNCTION__<<"cmd[cmdLen-3]="<<cmd[cmdLen-3];
             qDebug()<<__FUNCTION__<<"cmd="<<cmd;
        }
        *suffix++ = kBCLSuffix1;
        *suffix++ = kBCLSuffix2;
        rc= true;
    }
    return rc;
}

bool BCL::isCmdHaveResponse(uint8_t cmdId)
{
    bool rc = false;
    bclItem_t* item = GetBCLCmdItem(cmdId);
    if(item)
    {
        rc = item->bHaveResponse;
    }
    return rc;
}

uint16_t BCL::getCmdTimeOutValue(uint8_t cmdId)
{
    uint16_t timeout = 0;
    bclItem_t* item = GetBCLCmdItem(cmdId);
    if(item)
    {
        timeout = item->timeout;
    }
    return timeout;
}

bool BCL::parse(QByteArray data)
{
    bool rc = false;
    for(int i=0;i<data.size();i++)
    {
        _detected = dataIn(data[i]);
    }
    if(_detected)
    {
        //解析数据结果
        bcl_frame* frame;
        frame = (bcl_frame*)_bufPrefix;
        if(frame->data[0] == 'P'&&frame->data[1] == 'S')
        {
            uint8_t cmdExecST = frame->data[2];
            if(cmdExecST == '0')
                rc = true;
        }
    }
    return _detected;
}

void BCL::setContinueScan(bool bContinueScan)
{
    _bContinueScan = bContinueScan;
}

QVector<QString> BCL::getAllBarcode()
{
    return _barcodeVect;
}

void BCL::clearAllBarcode()
{
    _barcodeVect.clear();
}

QString BCL::getLatestResult()
{
    return  _latestResult;
}

bool BCL::dataIn(uint8_t rx)
{
    timeoutDetect();
    if (rx == kBCLPrefix1&&_stat!=ePrefix)
        reset();
    switch (_stat) {
    case ePrefix:
        if (rx == kBCLPrefix1) {
            _bufTail = _bufPrefix;
            *_bufTail++ = rx;
            _stat = eBody;
            _detected = false;
        }
        break;
    case eBody:
        *_bufTail++ = rx;
        if (rx == kBCLSuffix1) {
            _stat = eSuffix;
            _bufSuffix = _bufTail-1;
        }
        break;
    case eSuffix:
        *_bufTail++ = rx;
        if (rx == kBCLSuffix2)
        {
            _detected = true;
            QByteArray array;
            array = QByteArray((char*)_bufPrefix+1, _bufTail-_bufPrefix-kFramePrefixLength-kFrameSuffixLength);
            _latestResult = array.data();
            if(_bContinueScan && _latestResult.left(2)!= "PS")
            {
                _barcodeVect.push_back(_latestResult);
                qDebug()<<"Receive New Barcode:"<<_latestResult;
            }
            else
                qDebug()<<"Receive New Scanner Result:"<<_latestResult;
        }
        break;
    default:
        break;
    }
    return _detected;
}


void BCL::reset()
{
    memset(recvBuf,0, sizeof(recvBuf));
    _bufTail = _bufPrefix = recvBuf;
    _bufSuffix = _bufPrefix;
    _stat = ePrefix;
    _detected = false;

}

void BCL::timeoutDetect()
{

}

int BCL::getBarCodeSize()
{
    return _barcodeVect.size();
}

bool BCL::IsDataCmdType(int cmdId)
{
    bool rc = false;
    bclItem_t* item = GetBCLCmdItem(cmdId);
    if(item)
    {
        if(item->cmdType == BCL_CMD_TYPE_DATA)
            rc = true;
    }
    return rc;
}
