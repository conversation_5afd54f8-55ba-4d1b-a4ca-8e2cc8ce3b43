#include "cspdlogger.h"
#include <QDate>
#include <QFileInfo>
#include <QFile>
#include <QDir>
#include <QDebug>
#include <unistd.h>
#include <vector>
#include <QThread> 
#include <QRegularExpression>
#include "spdlog/sinks/stdout_sinks.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/common.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include <sys/types.h>
#ifdef __linux__
#include <sys/syscall.h>
#endif
#include "compressed_daily_file_sink.h"

bool CSpdLogger::m_bThreadExit = false;
std::shared_ptr<spdlog::logger> CSpdLogger::async_logger_d; // 初始化静态成员变量
std::shared_ptr<spdlog::logger> CSpdLogger::async_logger_i; // 初始化静态成员变量
std::shared_ptr<spdlog::logger> CSpdLogger::async_logger_w; // 初始化静态成员变量
std::shared_ptr<spdlog::logger> CSpdLogger::async_logger_cf; // 初始化静态成员变量
pthread_t CSpdLogger::log_file_check_thread;
pthread_t CSpdLogger::log_daily_monitoring_thread;
quint32 CSpdLogger::m_iMaxFileCount = 3; // 最多3个当天文件
quint32 CSpdLogger::m_iMaxFileSize = 200;// 单个文件最大200MB
bool CSpdLogger::m_bConsole = false;
QString CSpdLogger::m_strLogLevel = "debug";
quint32 CSpdLogger::m_iThreadPoolSize = 8192;
quint32 CSpdLogger::m_iThreadPoolThreads = 2;
quint32 CSpdLogger::m_iFlushIntervalMs = 100;
QString CSpdLogger::m_strLogDirectory = "logs/";
quint32 CSpdLogger::m_iSaveDays = 90;
const QString log_fix_name = ".log_";
const QString log_prefix_name_path = "/server_";//区分上位机日志文件
const QString log_prefix_name = "server_";//区分上位机日志文件
const QString compress_date_format = "yyyyMMdd";
const QString compress_time_format = "HHmmss";
moodycamel::ConcurrentQueue<QString> CSpdLogger::m_qZipPath;
bool CSpdLogger::m_bReadyZip = true;
std::mutex CSpdLogger::m_mtxZip;// zip压缩
std::condition_variable CSpdLogger::m_cvZip; // zip压缩 
QString CSpdLogger::m_strConfigFile = "spdlog.ini";

// TODO，分离配置选项
void CSpdLogger::init(const QString &strConfigFile)
{
    m_strConfigFile = strConfigFile;
    loadConfig(strConfigFile);
    initLogger();
    qInstallMessageHandler(messageHandler); // 将Qt日志重定向到spdlog
    // 启动检查日志文件大小的线程
    pthread_create(&log_file_check_thread, NULL, &CSpdLogger::checkLogFileSize, NULL);
    // 压缩及清理过期文件
    pthread_create(&log_daily_monitoring_thread, NULL, &CSpdLogger::dailyMonitoring, NULL);
}

void CSpdLogger::shutdown()
{
    m_bThreadExit = true;
    // 等待线程结束
    pthread_join(log_file_check_thread, NULL);
    pthread_join(log_daily_monitoring_thread, NULL);
}

void CSpdLogger::initLogger()
{
    // 设置异步日志的参数
    spdlog::init_thread_pool(m_iThreadPoolSize, m_iThreadPoolThreads); // 队列大小和线程数
    QString current_date = QDateTime::currentDateTime().toString("yyyy-MM-dd");

    // 创建每日文件sink-----debug
    int max_index = findMaxIndexForDate("debug", current_date);
    initLogger_debug(m_strLogDirectory + "debug"+log_prefix_name_path+"debug.log_" + QString::number(max_index));

    // 创建每日文件sink-----info
    max_index = findMaxIndexForDate("info", current_date);
    initLogger_info(m_strLogDirectory + "info"+log_prefix_name_path+"info.log_" + QString::number(max_index));

    // 创建每日文件sink-----warn
    max_index = findMaxIndexForDate("warn", current_date);
    initLogger_warn(m_strLogDirectory + "warn"+log_prefix_name_path+"warn.log_" + QString::number(max_index));
    // 创建每日文件sink-----critical
    max_index = findMaxIndexForDate("critical", current_date);
    initLogger_warn(m_strLogDirectory + "critical"+log_prefix_name_path+"critical.log_" + QString::number(max_index));
         // 设置为默认日志记录器
    if(m_strLogLevel.contains("debug"))
    {
        spdlog::set_default_logger(async_logger_d);
    }
    else if(m_strLogLevel.contains("info"))
    {
        spdlog::set_default_logger(async_logger_i);
    }
    else if(m_strLogLevel.contains("warn"))
    {
        spdlog::set_default_logger(async_logger_w);
    }
    else if(m_strLogLevel.contains("critical"))
    {
        spdlog::set_default_logger(async_logger_cf);
    }
    else
    {
        spdlog::set_default_logger(async_logger_d);
    }

    spdlog::flush_every(std::chrono::milliseconds(m_iFlushIntervalMs));  // 设置自动刷新间隔
}

void CSpdLogger::messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    try {
#ifdef __linux__
        pid_t tid = (pid_t) syscall(SYS_gettid);
#endif
        QString threadId = QString("%1").arg(quintptr(QThread::currentThreadId()),0,16);
#ifdef __linux__    
        threadId =  QString("%1").arg(tid);
#endif    
        QString fileName = QString(context.file).section('/', -1);
        fileName = fileName.section('\\', -1);
        QString context_info = QString("[%1][%2:%3]  ").arg(threadId).arg(fileName).arg(context.line) + msg;
        QByteArray localMsg = context_info.toLocal8Bit();

        switch (type) {
        case QtDebugMsg:
            if (async_logger_d) async_logger_d->debug("{}", localMsg.constData());
            break;
        case QtInfoMsg:
            if (async_logger_d) async_logger_d->debug("{}", localMsg.constData());
            if (async_logger_i) async_logger_i->info("{}", localMsg.constData());
            break;
        case QtWarningMsg:
            if (async_logger_w) async_logger_w->warn("{}", localMsg.constData());
            break;
        case QtCriticalMsg:
            if (async_logger_cf) async_logger_cf->error("{}", localMsg.constData());
            break;
        case QtFatalMsg:
            if (async_logger_cf) async_logger_cf->critical("{}", localMsg.constData());
            break;
        default:
            if (async_logger_cf) async_logger_cf->critical("{}", localMsg.constData());
            break;
        }
    } catch (const std::exception& e) {
        fprintf(stderr, "CSpdLogger::messageHandler exception: %s\n", e.what());
    } catch (...) {
        fprintf(stderr, "CSpdLogger::messageHandler unknown exception\n");
    }
}

void *CSpdLogger::checkLogFileSize(void *args)
{
    try {
        CSpdLogger *pCSpdLogger = (CSpdLogger*)args;
        const qint64 max_size = m_iMaxFileSize * 1024 * 1024; // 当天存储单个文件大小最大为200MB - 200 * 1024 * 1024
        QString current_date = "";
        QString _current_date = "";
        QString _lastCheckedDate = "";
        QString lastCheckedDate = QDateTime::currentDateTime().toString("yyyy-MM-dd"); // 初始化为当前日期
        
        // 添加前缀server，防止与上位机日志文件冲突
        const QString log_file_base_debug = m_strLogDirectory + "debug"+log_prefix_name_path+"debug";
        const QString log_file_base_info = m_strLogDirectory + "info"+log_prefix_name_path+"info";
        const QString log_file_base_warn = m_strLogDirectory + "warn"+log_prefix_name_path+"warn";
        const QString log_file_base_critical = m_strLogDirectory + "critical"+log_prefix_name_path+"critical";
        quint32 max_index_debug, max_index_info, max_index_warn, max_index_critical;
        QString new_log_file, current_log_file_debug, current_log_file_info, current_log_file_warn, current_log_file_critical, oldest_log_file;
        // 检查文件大小及日期
        while (!pCSpdLogger->m_bThreadExit)
        {
            current_date = QDateTime::currentDateTime().toString("yyyy-MM-dd");
            _current_date = "_" + current_date;
            _lastCheckedDate = "_" + lastCheckedDate;
            // 检查日期是否变化
            if (current_date != lastCheckedDate) {
                // 日期变化，重置日志记录器 索引默认改为0
                // debug
                // max_index_debug = pCSpdLogger->findMaxIndexForDate("debug", current_date);
                new_log_file = log_file_base_debug + log_fix_name + QString::number(0);
                pCSpdLogger->updateLogger_debug(new_log_file);
                // info
                // max_index_info = pCSpdLogger->findMaxIndexForDate("info", current_date);
                new_log_file = log_file_base_info + log_fix_name + QString::number(0);
                pCSpdLogger->updateLogger_info(new_log_file);
                // warn
                // max_index_warn = pCSpdLogger->findMaxIndexForDate("warn", current_date);
                new_log_file = log_file_base_warn + log_fix_name + QString::number(0);
                pCSpdLogger->updateLogger_warn(new_log_file);
                // critical
                // max_index_critical = pCSpdLogger->findMaxIndexForDate("critical", current_date);
                new_log_file = log_file_base_critical + log_fix_name + QString::number(0);
                pCSpdLogger->updateLogger_critical(new_log_file);

                qDebug() << "new day " << max_index_info << current_date << lastCheckedDate;
                
                // 删除旧文件(spdlog不自动删除文件)
                // debug
                max_index_debug = pCSpdLogger->findMaxIndexForDate("debug", lastCheckedDate);
                oldest_log_file = log_file_base_debug + _lastCheckedDate + log_fix_name + QString::number(max_index_debug);            
                m_qZipPath.enqueue(oldest_log_file);
                // info
                max_index_info = pCSpdLogger->findMaxIndexForDate("info", lastCheckedDate);
                oldest_log_file = log_file_base_info + _lastCheckedDate + log_fix_name +QString::number(max_index_info);            
                m_qZipPath.enqueue(oldest_log_file);
                // warn
                max_index_warn = pCSpdLogger->findMaxIndexForDate("warn", lastCheckedDate);
                oldest_log_file = log_file_base_warn + _lastCheckedDate + log_fix_name + QString::number(max_index_warn);            
                m_qZipPath.enqueue(oldest_log_file);
                // critical
                max_index_critical = pCSpdLogger->findMaxIndexForDate("critical", lastCheckedDate);
                oldest_log_file = log_file_base_critical + _lastCheckedDate + log_fix_name +QString::number(max_index_critical);            
                m_qZipPath.enqueue(oldest_log_file);

                m_bReadyZip = true;
                m_cvZip.notify_all();

                lastCheckedDate = current_date;
            }
            else
            {  // debug
                max_index_debug = pCSpdLogger->findMaxIndexForDate("debug", current_date);
                current_log_file_debug = log_file_base_debug + _current_date + log_fix_name +
                        QString::number(max_index_debug);
                QFileInfo fileInfo_debug(current_log_file_debug);
                if (fileInfo_debug.exists() && fileInfo_debug.size() > max_size)// 删除最旧的文件
                { 
                    // 创建新的日志文件
                    max_index_debug++;
                    QString new_log_file = log_file_base_debug + log_fix_name + QString::number(max_index_debug);
                    qDebug() << "new debug file " << log_file_base_debug << max_index_debug << current_date << new_log_file;
                    // 更新日志记录器
                    pCSpdLogger->updateLogger_debug(new_log_file);

                    m_qZipPath.enqueue(current_log_file_debug);// 需要压缩和删除的文件
                    m_bReadyZip = true;
                    m_cvZip.notify_all();
                }
                // info
                max_index_info = pCSpdLogger->findMaxIndexForDate("info", current_date);
                current_log_file_info = log_file_base_info + _current_date + log_fix_name +
                        QString::number(max_index_info);
                QFileInfo fileInfo_info(current_log_file_info);
                if (fileInfo_info.exists() && fileInfo_info.size() > max_size)// 删除最旧的文件
                {
                    // 创建新的日志文件
                    max_index_info++;
                    QString new_log_file = log_file_base_info + log_fix_name + QString::number(max_index_info);
                    qDebug() << "new info file " << log_file_base_info << max_index_info << current_date << new_log_file;
                    // 更新日志记录器
                    pCSpdLogger->updateLogger_info(new_log_file);

                    m_qZipPath.enqueue(current_log_file_info);// 需要压缩和删除的文件
                    m_bReadyZip = true;
                    m_cvZip.notify_all(); 
                }
                // warn
                max_index_warn = pCSpdLogger->findMaxIndexForDate("warn", current_date);
                current_log_file_warn = log_file_base_warn + _current_date + log_fix_name +
                        QString::number(max_index_warn);
                QFileInfo fileInfo_warn(current_log_file_warn);
                if (fileInfo_warn.exists() && fileInfo_warn.size() > max_size)// 删除最旧的文件
                { 
                    // 创建新的日志文件
                    max_index_warn++;
                    QString new_log_file = log_file_base_warn + log_fix_name + QString::number(max_index_warn);
                    qDebug() << "new warn file " << log_file_base_warn << max_index_warn << current_date << new_log_file;
                    // 更新日志记录器
                    pCSpdLogger->updateLogger_warn(new_log_file);

                    m_qZipPath.enqueue(current_log_file_warn);// 需要压缩和删除的文件
                    m_bReadyZip = true;
                    m_cvZip.notify_all();                
                }
                // critical
                max_index_critical = pCSpdLogger->findMaxIndexForDate("critical", current_date);
                current_log_file_critical = log_file_base_critical + _current_date + log_fix_name +
                        QString::number(max_index_critical);
                QFileInfo fileInfo_critical(current_log_file_critical);
                if (fileInfo_critical.exists() && fileInfo_critical.size() > max_size)// 删除最旧的文件
                { 
                    // 创建新的日志文件
                    max_index_critical++;
                    QString new_log_file = log_file_base_critical + log_fix_name + QString::number(max_index_critical);
                    qDebug() << "new critical file " << log_file_base_critical << max_index_critical << current_date << new_log_file;
                    // 更新日志记录器
                    pCSpdLogger->updateLogger_critical(new_log_file);
                    
                    m_qZipPath.enqueue(current_log_file_critical);// 需要压缩和删除的文件
                    m_bReadyZip = true;
                    m_cvZip.notify_all();                  
                }
            }
            sleep(60); // 每1秒检查一次
        }
    } catch (const std::exception& e) {
        fprintf(stderr, "CSpdLogger::checkLogFileSize exception: %s\n", e.what());
    } catch (...) {
        fprintf(stderr, "CSpdLogger::checkLogFileSize unknown exception\n");
    }
    return NULL;
}

void *CSpdLogger::dailyMonitoring(void *args)
{
    try {
        auto funcExtractBeforeSeparator = [](const QString& filePath)-> QString{
            // 使用QRegularExpression匹配日志级别
            QRegularExpression regex(R"(logs/(\w+)/)");
            QRegularExpressionMatch match = regex.match(filePath);
        
            if (match.hasMatch()) {
                // 返回匹配到的第一个捕获组（即日志级别）
                return match.captured(1);
            } else {
                // 匹配失败，返回一个空字符串
                return QString();
            }
        };
        
        auto funcRemoveFile = [] (const QString &filePath) -> bool {
            QFile file(filePath);
            // 检查文件是否存在
            if (!file.exists()) {
                qDebug() << "File does not exist:" << filePath;
                return false;
            }
            // 尝试删除文件
            if (file.remove()) {
                return true;
            } else {
                qDebug() << "Could not remove file:" << filePath;
                return false;
            }        
        };

        auto funcCheckFile = [] (const QString &filePath) -> bool {
            QFile file(filePath);
            // 检查文件是否存在
            if (!file.exists()) {
                qDebug() << "File does not exist:" << filePath;
                return false;
            }
            return true;     
        };

        std::unique_lock<std::mutex> uniqueLock(m_mtxZip);
        m_bReadyZip = false;
        CSpdLogger *pCSpdLogger = (CSpdLogger*)args;
        while (!pCSpdLogger->m_bThreadExit)
        {
            qDebug()<<"dailyMonitoring m_bReadyZip: "<<m_bReadyZip;
            m_cvZip.wait(uniqueLock, [] {
                return m_bReadyZip;
            });
            
            QString strZipPath = "";
            QString strLogPath = "";
            QStringList strFilesList;
            QString strLevel = "";
            QString strZipDate = "";
            QString strLastLogPath = "";
            qDebug()<<"dailyMonitoring size: "<<m_qZipPath.size_approx();
            while (m_qZipPath.size_approx() > 0)
            {
                bool bFound = m_qZipPath.try_dequeue(strLogPath);
                if(strLastLogPath == strLogPath)//连续相同文件，不压缩
                {
                    continue;
                }

                if(!funcCheckFile(strLogPath))
                {
                    qDebug()<<"dailyMonitoring log path not exist: "<<strLogPath;
                    continue;
                }

                if (bFound)
                {
                    strLevel = funcExtractBeforeSeparator(strLogPath);
                    strZipDate = "_" + QDateTime::currentDateTime().toString(compress_date_format) +"_"+QTime::currentTime().toString(compress_time_format);
                    strZipPath = m_strLogDirectory+strLevel+log_prefix_name_path + strLevel + strZipDate +".zip";//使用当前压缩时间来命名文件(方便定位输出日志最后时间)
                    qDebug()<<"dailyMonitoring strLogPath:"<<strLogPath<<strZipPath;
                    // 压缩和删除文件
                    strFilesList.append(strLogPath);
                    compressFilesToZip(strFilesList,strZipPath);
                    funcRemoveFile(strLogPath);//是否删除文件
                    strFilesList.clear();
                    strLastLogPath = strLogPath;
                }
            }
            m_bReadyZip = false;        
            // 删除 
            pCSpdLogger->compressLAndClearogs(false);
            // sleep(86400); // 每24小时运行一次 86400
        }
    } catch (const std::exception& e) {
        fprintf(stderr, "CSpdLogger::dailyMonitoring exception: %s\n", e.what());
    } catch (...) {
        fprintf(stderr, "CSpdLogger::dailyMonitoring unknown exception\n");
    }
    return NULL;
}

int CSpdLogger::findMaxIndexForDate(const QString &strBase, const QString &strDate)
{ 
    QDir dir(m_strLogDirectory + strBase);
    QStringList nameFilter;
    nameFilter << log_prefix_name + strBase + "_" + strDate + ".log*";
    QStringList logFiles = dir.entryList(nameFilter, QDir::Files);
    if (logFiles.isEmpty()) {
        return 0;
    }
    int maxIndex = 0;
    for (const QString& file : logFiles) {
        // 匹配新的文件命名格式
        QRegExp rx(strBase + "_" + strDate + ".log_(\\d+)");
        if (rx.indexIn(file) != -1) {
            int index = rx.cap(1).toInt();
            if (index > maxIndex) {
                maxIndex = index;
            }
        }
    }
    return maxIndex;
}

void CSpdLogger::updateLogger_debug(const QString &strNewLogFile)
{
    initLogger_debug(strNewLogFile);
    // 步骤4：注册新logger
    spdlog::register_logger(async_logger_d);
}

void CSpdLogger::updateLogger_info(const QString &strNewLogFile)
{
    initLogger_info(strNewLogFile);
    // 步骤4：注册新logger
    spdlog::register_logger(async_logger_i);
}

void CSpdLogger::updateLogger_warn(const QString &strNewLogFile)
{
    initLogger_warn(strNewLogFile);
    // 步骤4：注册新logger
    spdlog::register_logger(async_logger_w);
}

void CSpdLogger::updateLogger_critical(const QString &strNewLogFile)
{
    initLogger_critical(strNewLogFile);
    // 步骤4：注册新logger
    spdlog::register_logger(async_logger_cf);
}

void CSpdLogger::initLogger_debug(const QString &strNewLogFile)
{
    // === 原子性更新，避免悬空指针 ===
    // 步骤1：创建新logger    
    // 创建每日文件sink
    auto daily_sink_d = std::make_shared<spdlog::sinks::compressed_daily_file_sink_mt>(strNewLogFile.toStdString(),
                                                                            0, 0, false, m_iMaxFileCount);
    // 创建控制台sink
    auto console_sink_d = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    // 组合sinks
    std::vector<spdlog::sink_ptr> sinks_d {daily_sink_d};
    if(m_bConsole)
    {
        sinks_d.push_back(console_sink_d);// 是否在控制台输出
    }
    // 创建异步日志记录器
    auto newLogger = std::make_shared<spdlog::async_logger>("async_logger_d", sinks_d.begin(), sinks_d.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);
    newLogger->set_level(spdlog::level::debug);
    newLogger->flush_on(spdlog::level::debug);
    newLogger ->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%L] %v");

    // 步骤2：保存旧logger引用并原子性替换
    auto oldLogger = async_logger_d;
    async_logger_d = newLogger;
    
    // 步骤3：强化旧logger清理
    if (oldLogger) {
        // 多次确认flush完成
        for(int i = 0; i < 3; ++i) {
            oldLogger->flush();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        spdlog::drop("async_logger_d");
        oldLogger.reset();
        
        // 额外等待，确保底层文件句柄释放
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}

void CSpdLogger::initLogger_info(const QString &strNewLogFile)
{
    // === 原子性更新，避免悬空指针 ===
    // 步骤1：创建新logger  
    // 创建每日文件sink
    auto daily_sink_i = std::make_shared<spdlog::sinks::compressed_daily_file_sink_mt>(strNewLogFile.toStdString(),
                                                                            0, 0, false, m_iMaxFileCount);
    // 创建控制台sink
    auto console_sink_i = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    // 组合sinks
    std::vector<spdlog::sink_ptr> sinks_i {daily_sink_i};
//    if(m_bConsole)
//    {
//        sinks_i.push_back(console_sink_i);// 是否在控制台输出
//    }
    // 创建异步日志记录器
    auto newLogger = std::make_shared<spdlog::async_logger>("async_logger_i", sinks_i.begin(), sinks_i.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);
    newLogger->set_level(spdlog::level::info);
    newLogger->flush_on(spdlog::level::info);
    newLogger ->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%L] %v");

    // 步骤2：保存旧logger引用并原子性替换
    auto oldLogger = async_logger_i;
    async_logger_i = newLogger;
    
    // 步骤3：强化旧logger清理
    if (oldLogger) {
        // 多次确认flush完成
        for(int i = 0; i < 3; ++i) {
            oldLogger->flush();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        spdlog::drop("async_logger_i");
        oldLogger.reset();
        
        // 额外等待，确保底层文件句柄释放
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}

void CSpdLogger::initLogger_warn(const QString &strNewLogFile)
{
   // === 原子性更新，避免悬空指针 ===
    // 步骤1：创建新logger    
    // 创建每日文件sink
    auto daily_sink_wcf = std::make_shared<spdlog::sinks::compressed_daily_file_sink_mt>(strNewLogFile.toStdString(),
                                                                              0, 0, false, m_iMaxFileCount);
    // 创建控制台sink
    auto console_sink_wcf = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    // 组合sinks
    std::vector<spdlog::sink_ptr> sinks_wcf {daily_sink_wcf};
    if(m_bConsole)
    {
        sinks_wcf.push_back(console_sink_wcf);// 是否在控制台输出
    }
    // 创建异步日志记录器
    auto newLogger = std::make_shared<spdlog::async_logger>("async_logger_wcf", sinks_wcf.begin(), sinks_wcf.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);
    newLogger->set_level(spdlog::level::warn);
    newLogger->flush_on(spdlog::level::warn);
    newLogger ->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%L] %v");

    // 步骤2：保存旧logger引用并原子性替换
    auto oldLogger = async_logger_w;
    async_logger_w = newLogger;
    
    // 步骤3：强化旧logger清理
    if (oldLogger) {
        // 多次确认flush完成
        for(int i = 0; i < 3; ++i) {
            oldLogger->flush();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        spdlog::drop("async_logger_wcf");
        oldLogger.reset();
        
        // 额外等待，确保底层文件句柄释放
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}

void CSpdLogger::initLogger_critical(const QString &strNewLogFile)
{
    // === 原子性更新，避免悬空指针 ===
    // 步骤1：创建新logger    
    // 创建每日文件sink
    auto daily_sink_cf = std::make_shared<spdlog::sinks::compressed_daily_file_sink_mt>(strNewLogFile.toStdString(),
                                                                             0, 0, false, m_iMaxFileCount);
    // 创建控制台sink
    auto console_sink_cf = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    // 组合sinks
    std::vector<spdlog::sink_ptr> sinks_cf {daily_sink_cf};
    if(m_bConsole)
    {
        sinks_cf.push_back(console_sink_cf);// 是否在控制台输出
    }
    // 创建异步日志记录器
     auto newLogger = std::make_shared<spdlog::async_logger>("async_logger_cf", sinks_cf.begin(), sinks_cf.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);
    newLogger->set_level(spdlog::level::critical);
    newLogger->flush_on(spdlog::level::critical);
    newLogger ->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%L] %v");

    // 步骤2：保存旧logger引用并原子性替换
    auto oldLogger = async_logger_cf;
    async_logger_cf = newLogger;
    
    // 步骤3：强化旧logger清理
    if (oldLogger) {
        // 多次确认flush完成
        for(int i = 0; i < 3; ++i) {
            oldLogger->flush();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        spdlog::drop("async_logger_cf");
        oldLogger.reset();
        
        // 额外等待，确保底层文件句柄释放
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
}

void CSpdLogger::saveDefaultConfig(const QString &strConfigFile,
                                   const QMap<QString, QString> &strConfigMap)
{
    QFile qConfigFile(strConfigFile);
    if (!qConfigFile.open(QIODevice::Append | QIODevice::Text))
    {
        fprintf(stderr, "Could not open config file for writing: %s\n", qPrintable(strConfigFile));
        return;
    }
    QTextStream qTextStreamOut(&qConfigFile);
    for (auto it = strConfigMap.begin(); it != strConfigMap.end(); ++it)
    {
        qTextStreamOut << "\n" << it.key() << ":" << it.value() << ";";
    }
    qConfigFile.flush();
    qConfigFile.close();
}

void CSpdLogger::modifyConfig(const QString &strKey, const QString &strValue)
{
    QFile qConfigFile(m_strConfigFile);
    bool bKeyFound = false;
    QString strNewContent;
    
    // 首先读取现有配置
    if (qConfigFile.exists() && qConfigFile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream qTextStreamIn(&qConfigFile);
        QString strLine;
        
        while (!qTextStreamIn.atEnd())
        {
            strLine = qTextStreamIn.readLine();
            
            // 跳过空行
            if (strLine.trimmed().isEmpty())
            {
                strNewContent += strLine + "\n";
                continue;
            }
            
            // 解析每一行
            int iDelimiterPos = strLine.indexOf(':');
            if (iDelimiterPos != -1)
            {
                QString strCurrentKey = strLine.left(iDelimiterPos).trimmed();
                
                // 如果找到匹配的key，则替换该行
                if (strCurrentKey == strKey)
                {
                    strNewContent += strKey + ":" + strValue + ";\n";
                    bKeyFound = true;
                }
                else
                {
                    strNewContent += strLine + "\n";
                }
            }
            else
            {
                strNewContent += strLine + "\n";
            }
        }
        qConfigFile.close();
    }
    
    // 如果key不存在，则追加到文件末尾
    if (!bKeyFound)
    {
        strNewContent += strKey + ":" + strValue + ";\n";
    }
    
    // 重写配置文件
    if (qConfigFile.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QTextStream qTextStreamOut(&qConfigFile);
        qTextStreamOut << strNewContent;
        qConfigFile.flush();
        qConfigFile.close();
    }
    else
    {
        fprintf(stderr, "无法打开配置文件进行写入：%s\n", qPrintable(m_strConfigFile));
    }
}

void CSpdLogger::loadConfig(const QString &strConfigFile)
{
    QFile qConfigFile(strConfigFile);
    QMap<QString, QString> strConfigMap;
    QMap<QString, QString> strConfigToSaveMap;

    // 读取配置文件，如果失败则设置needToSaveDefaults为true
    if (qConfigFile.exists() && qConfigFile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream qTextSteamIn(&qConfigFile);
        QString strConfigText = qTextSteamIn.readAll();
        qConfigFile.close();
        strConfigMap = parseConfig(strConfigText);
    }
    // 设置默认值并检查是否需要保存
    if (!strConfigMap.contains("max_file_count"))
    {
        strConfigToSaveMap["max_file_count"] = QString::number(m_iMaxFileCount);
    }
    else
    {
        m_iMaxFileCount = strConfigMap["max_file_count"].toInt();
    }
    if (!strConfigMap.contains("max_file_size"))
    {
        strConfigToSaveMap["max_file_size"] = QString::number(m_iMaxFileSize);
    }
    else
    {
        m_iMaxFileSize = strConfigMap["max_file_size"].toInt();
    }
    if (!strConfigMap.contains("console_output"))
    {
        strConfigToSaveMap["console_output"] = m_bConsole ? "true" : "false";
    }
    else
    {
        m_bConsole = (strConfigMap["console_output"] == "true");
    }
    if (!strConfigMap.contains("log_level"))
    {
        strConfigToSaveMap["log_level"] = m_strLogLevel;
    }
    else
    {
        m_strLogLevel = strConfigMap["log_level"];
    }
    if (!strConfigMap.contains("thread_pool_size"))
    {
        strConfigToSaveMap["thread_pool_size"] = QString::number(m_iThreadPoolSize);
    }
    else
    {
        m_iThreadPoolSize = strConfigMap["thread_pool_size"].toUInt();
    }
    if (!strConfigMap.contains("thread_pool_threads"))
    {
        strConfigToSaveMap["thread_pool_threads"] = QString::number(m_iThreadPoolThreads);
    }
    else
    {
        m_iThreadPoolThreads = strConfigMap["thread_pool_threads"].toUInt();
    }
    if (!strConfigMap.contains("flush_interval_ms"))
    {
        strConfigToSaveMap["flush_interval_ms"] = QString::number(m_iFlushIntervalMs);
    }
    else
    {
        m_iFlushIntervalMs = strConfigMap["flush_interval_ms"].toUInt();
    }
    if (!strConfigMap.contains("log_directory"))
    {
        strConfigToSaveMap["log_directory"] = m_strLogDirectory;
    }
    else
    {
        m_strLogDirectory = strConfigMap["log_directory"];
    }
    if (!strConfigMap.contains("save_days"))
    {
        strConfigToSaveMap["save_days"] = QString::number(m_iSaveDays);
    }
    else
    {
        m_iSaveDays = strConfigMap["save_days"].toUInt();
        if(m_iSaveDays < 1)
        {
            m_iSaveDays = 1;// 最少存储1天
        }
    }
    // 如果需要保存默认配置，则将默认值写回配置文件
    if (!strConfigToSaveMap.isEmpty()) {
        saveDefaultConfig(strConfigFile, strConfigToSaveMap);
    }
}

QMap<QString, QString> CSpdLogger::parseConfig(const QString &strConfigText)
{    
    QMap<QString, QString> strConfigMap;
    QStringList strLineList = strConfigText.split('\n', QString::SkipEmptyParts);

    for (QString strLine : strLineList)
    {
        strLine = strLine.trimmed();
        if (strLine.isEmpty() || strLine.startsWith('#'))
        {
            continue;
        }
        int iDelimiterPos = strLine.indexOf(':');
        if (iDelimiterPos != -1)
        {
            QString strKey = strLine.left(iDelimiterPos).trimmed();
            QString strValue = strLine.mid(iDelimiterPos + 1).trimmed();
            // 移除行内注释
            int iCommentPos = strValue.indexOf('#');
            if (iCommentPos != -1)
            {
                strValue = strValue.left(iCommentPos).trimmed();
            }
            // 移除结尾的分号
            if (strValue.endsWith(';'))
            {
                strValue.chop(1);
            }
            strConfigMap[strKey] = strValue;
            // 禁止递归日志
            // qDebug() << strKey << strValue;
        }
    }
    return strConfigMap;
}

// 日志存储格式总是 level_YYYY-MM-dd.log_index
void CSpdLogger::compressLAndClearogs(bool bCompress)
{
    QStringList strLogLevelsList = {"debug", "info", "warn", "critical"};
    for (const QString& strLogLevel : strLogLevelsList)
    {
        compressLogsForLevel(m_strLogDirectory, strLogLevel);
        deleteOldLogsForLevel(m_strLogDirectory, strLogLevel);
    }
}

void CSpdLogger::compressLogsForLevel(const QString& strLogDirectory, const QString& strLogLevel)
{
    QDir qLevelDir(strLogDirectory + "/" + strLogLevel);
    if (!qLevelDir.exists())
    {
        qWarning() << "Log level directory does not exist:" << qLevelDir.absolutePath();
        return;
    }

    QDate qTodayDate = QDate::currentDate();
    QStringList strFiltersList;
    strFiltersList << "*.log_*";
    qLevelDir.setNameFilters(strFiltersList);

    QFileInfoList qFileInfoFileList = qLevelDir.entryInfoList(QDir::Files);

    QMap<QDate, QStringList> qFilesByDateMap;
    for (const QFileInfo& qFileInfo : qFileInfoFileList)
    {
        QString strFileName = qFileInfo.fileName();
        QStringList strPartsList = strFileName.split('_');
        if (strPartsList.size() < 4)
        {
            continue;
        }
        QString dateWithExt = strPartsList[2] + "_" + strPartsList[3];
        QStringList dateParts = dateWithExt.split('.');
        QDate qFileDate = QDate::fromString(dateParts[0], "yyyy-MM-dd");

        if (qFileDate.isValid() && qFileDate < qTodayDate)
        {
            qFilesByDateMap[qFileDate].append(qFileInfo.absoluteFilePath());
        }
    }

    for (auto it = qFilesByDateMap.begin(); it != qFilesByDateMap.end(); ++it)
    {
        const QDate& qLogDate = it.key();
        const QStringList& strLogFilesList = it.value();

        QString strZipFilePath = qLevelDir.absoluteFilePath(log_prefix_name+strLogLevel + "_" + qLogDate.toString(compress_date_format) +"_"+QTime::currentTime().toString(compress_time_format) + ".zip");
        compressFilesToZip(strLogFilesList, strZipFilePath);
        for (const QString& logFile : strLogFilesList)
        {
            QFile::remove(logFile);
        }
    }
    qDebug()<<"compressLogsForLevel: "<<qFileInfoFileList.size()<<qFilesByDateMap.size();
}

void CSpdLogger::compressFilesToZip(const QStringList& strFilesList, const QString& strZipFilePath)
{
   int iError = 0;
   zip_t* pZip = zip_open(strZipFilePath.toStdString().c_str(), ZIP_CREATE | ZIP_TRUNCATE, &iError);
   if (!pZip)
   {
       fprintf(stderr, "Failed to open zip file: %s\n", qPrintable(strZipFilePath));
       return;
   }

   for (const QString& strFilePath : strFilesList)
   {
       zip_source_t* pZipSource = zip_source_file(pZip, strFilePath.toStdString().c_str(), 0, 0);
       if (!pZipSource)
       {
           fprintf(stderr, "Failed to create zip source for file: %s\n", qPrintable(strFilePath));
           zip_close(pZip);
           return;
       }
       if (zip_file_add(pZip, QFileInfo(strFilePath).fileName().toStdString().c_str(), pZipSource, ZIP_FL_OVERWRITE) < 0)
       {
           fprintf(stderr, "Failed to add file to zip: %s\n", qPrintable(strFilePath));
           zip_source_free(pZipSource);
           zip_close(pZip);
           return;
       }
   }
   if (zip_close(pZip) < 0)
   {
       fprintf(stderr, "Failed to close zip file: %s\n", qPrintable(strZipFilePath));
   }
   // 禁止递归日志
   // qDebug()<<"compressFilesToZip "<<strFilesList<<strZipFilePath;
}

void CSpdLogger::deleteOldLogsForLevel(const QString& strLogDirectory, const QString& strLogLevel)
{
    QDir qLevelDir(strLogDirectory + "/" + strLogLevel);
    if (!qLevelDir.exists())
    {
        qWarning() << "Log level directory does not exist:" << qLevelDir.absolutePath();
        return;
    }

    QDate qTodayDate = QDate::currentDate();
    QDate qThirtyDaysAgo = qTodayDate.addDays(-(qint64)m_iSaveDays);

    QStringList strFiltersList;
    strFiltersList << "*.zip";
    qLevelDir.setNameFilters(strFiltersList);

    QFileInfoList qFileInfoList = qLevelDir.entryInfoList(QDir::Files);
    for (const QFileInfo& qFileInfo : qFileInfoList)
    {
        QString strFileName = qFileInfo.fileName();
        QStringList strPartsList = strFileName.split('_');
        if (strPartsList.size() < 4)
        {
            continue;
        }

        QString strDatePart = strPartsList[2];
        QDate qfileDate = QDate::fromString(strDatePart, compress_date_format);

        if (qfileDate.isValid() && qfileDate < qThirtyDaysAgo)
        {
            QFile::remove(qFileInfo.absoluteFilePath());
            qDebug() << "remove log: " << qFileInfo.absoluteFilePath();
        }
    }
}
