#ifndef UPGRADECTRL_H
#define UPGRADECTRL_H

#include<QObject>
#include"publicconfig.h"
#include <QTimer>
#include<QSet>
#include<QMap>
#include<QQueue>
#include<QProcess>

struct UpdateMsgStruct
{
    QSet<int> m_PackageReq;//已经请求的包序号
    QDateTime m_lastReqDataTime;//最后询问的时间
    //  QTimer *m_Timer;
};

struct UpdateBoardCheck
{
    EnumMachineID ID;
    QString strVersion;
    int iCheck;  //是否已经检验  -1 没校验 0 正在校验 1校验正确
    int iRepeatTime ; //重复发送次数，从0开始， 升级失败两次就失败
};

class CUpgradeCtrl : public QObject
{
    Q_OBJECT
public:
    explicit CUpgradeCtrl(QObject *parent = nullptr);
    static CUpgradeCtrl &getInstance();


    void UpgradeReq(QString qPayloadByteArray,int iIsExtractPath);
    void UpgradData(quint8 iSourceID,QString qPayloadString);
    void AutoUpgrade();
    void MakeUpgradeBoardInfo(int iIsExtractPath); //从U盘遍历需要 的板卡
    int ReadUpgradeBinaryFileAndVerSion(EnumMachineID _ID, QString strfilePath,int iPackSize,QString &strVersion,int &iPackNum);
    int CalUpgradeFile(EnumMachineID _ID,int iIsExtractPath,QString &strVersion,int &iPackNum);
    int ReadJsonFile(EnumMachineID _ID,QString &strVersion);
   // void UpgradeUpperHost();//升级上位机
    void DoShFile(); //执行sh文件升级上位机及中位机
    void SetStopUpgradeFlag(bool bFlag);
    void ResetUpgradeStatus(); //清空自动的列表及版本信息
    void StopAllUpgradeTimer();
signals:
    void sigUpgradeTimeOut(int _ID);
    void sigKillUpgradeTimer(int _ID);
public slots:
    void slotUpgradeEndRs(EnumMachineID ID,int iRs);
    void slotCheckUpgradeStatus(EnumMachineID _ID);// 检查功能管理板的升级状态，每10s触发一次;
    void slotUpgradeTimeOut(int _ID);
    void slotExtractFinished(int exitCode, QProcess::ExitStatus exitStatus);  //解压完成的处理
    void slotExtractAutoupgradeZipFile();
    void slotBoardVersionInfo(EnumMachineID ID,QString strVersion);
    void slotKillUpgradeTimer(int _ID);
private:
    void _packageMsg(quint16 qID,QByteArray &RsMsg);
    void _addMD5(QByteArray &RsMsg);
    void _checkUpgradeStatus(EnumMachineID _ID,QDateTime lastReqDataTime,QList<QByteArray> PackagesUpgradeList,QSet<int> UpgradePackageReq);

private:
    // 升级
    QList<QByteArray> m_qPCRMainUpgradePackagesList;//
    QList<QByteArray> m_qBoardFunctionUpgradePackagesList;//
    QList<QByteArray> m_qBoardMotorUpgradePackagesList;//
    QList<QByteArray> m_qTECPackagesUpgradeList;//
    QList<QByteArray> m_qFluorenceUpgradePackagesList;//
    QList<QByteArray> m_qRFIDUpgradePackagesList;//
    QList<QByteArray> m_qBoardPowerUpgradePackagesList;//

    UpdateMsgStruct m_UpdateMsgStruct_PCRMain;
    UpdateMsgStruct m_UpdateMsgStruct_BoardFunction;
    UpdateMsgStruct m_UpdateMsgStruct_BoardMotor;
    UpdateMsgStruct m_UpdateMsgStruct_TEC;
    UpdateMsgStruct m_UpdateMsgStruct_Fluorence;
    UpdateMsgStruct m_UpdateMsgStruct_RFID;
    UpdateMsgStruct m_UpdateMsgStruct_BoardPower;

    QQueue<EnumMachineID> m_queUpgradeBoard;  //升板子的列表
    QList <UpdateBoardCheck> m_ListBoardVersion;  //升板子的版本 用于校验
    QProcess *m_extractProcess;// 解压进程
    bool m_bAutoupgrade;
    bool m_bStopUpgrade; //停止升级
    QMap<int,QTimer *>m_timer;// 各个板子的定时器指针
};




#endif // UPGRADECTRL_H
