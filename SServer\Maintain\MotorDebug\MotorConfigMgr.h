#ifndef MotorConfigMgr_H
#define MotorConfigMgr_H

#include<QObject>
#include <QMetaEnum> 


// 公共配置结构体
struct CommonConfig {
    QString strInitCmd;  //初始化的命令,可能有多个  BoardIDX &MethodIDX | BoardIDY &MethodIDY
    QString strVerifyCmd;  //验证的命令     BoardIDX &MethodIDX | BoardIDY &MethodIDY
    double dOrdIdx;   //顺序
    int iBoardIdx;  //所属的板卡Id'
    QString strParam;  //附带信息，“|”分割
    QString strCNName;
};

struct MotorConfigInfo
{
    QString strDeviceType;// 设备类型 DeviceType_Motor =0  //电机 ，DeviceType_Claw =1    //夹爪，DeviceType_ADP =2    //移液器
    QString strSubOrdIdx;// 当前的电机位于第几个调试顺序
    QString strName;      // 电机名称
    bool    bDirect;      // 方向 false(步数-) true(步数+)
    QString strLimit;
    QString strUnloadCmd; // 卸载命令
    int iIsNeedSave;// 是否让UI显示保存 1 ，是  0 否
    int     iStep;        // 步数(上位机下发)
    //  int iBoardIdx;  //该电机在下位机所属的电机板号
    int iMotorIdx; //该电机在下位机所属的电机号
    int iPosID;  //该动作在下位机所属的保存号码，下位机同事提供
    QString strFlag;  //在使用“Method_DebugPos_AskDeviceList”询问所有电机的时候已经返回的标记   “OK” “NO”
    QString strParam; //附带的信息  // 用在了下夹爪的sendmethodID
};

struct ModuleConfig {
    CommonConfig m_CommonCfg;  // 公共配置
    QHash<QString,MotorConfigInfo>  m_hashDeviceCfg;  // 设备配置
};

class MotorConfigMgr : public QObject
{
    Q_OBJECT
public:
    enum UnitModuleFiled
    {
        ModuleFiled_Sample, // 样本模块
        ModuleFiled_Extract,  // 提取模块
        ModuleFiled_ADP,// 移液魔模块
        ModuleFiled_PCR,     // pcr模块
        ModuleFiled_SampleScan,
        ModuleFiled_StripScan,
        ModuleFiled_LiquidTest =6,
        ModuleFiled_LiquidCalSuck =7, //移液校准
    };
    Q_ENUM( UnitModuleFiled)

    enum MotorActionMethod
    {
        Action_Reset=0, // 复位
        Action_Unload,  // 卸载
        Action_Recovery,// 恢复
        Action_IncreaseStep, // 增加步数
        Action_DecreaseStep, //减少步数
        Action_GoTestPos=6, //走到测试位置
        Action_ClawCatch=7,//夹爪夹紧
        Action_ClawRelease=8,//夹爪松开
        Action_OpenScanLight=9,//
        Action_CloseScanLight=10,//
        Action_Rotate=11, //旋转
        Action_ScanRs=12,//扫码结果
        Action_TakeOffTip =13, //退Tip
        Action_SendMethodID=14, //直接给下位机发送MethodID
    };

    enum ScanMotorIdx
    {
        Motor_SampleScan_Left=1,   //样本左边扫码器
        Motor_SampleScan_Right,   //样本左边扫码器
        Motor_ExtractScan          //提取扫码器
    };

    enum  EnumDeviceType
    {
        DeviceType_Motor =0,  //电机
        DeviceType_Claw =1 ,   //夹爪
        DeviceType_ADP =2,   //移液器
        DeviceType_SampleScan =3,   //样本扫码
        DeviceType_Null=4, //空白框
        DeviceType_SendMethodID =5 ,//自定义  发送的methodID在addParam
    };
    Q_ENUM(EnumDeviceType)

    enum CommonSetType
    {
        initcmd, //初始化的命令
        ordidx,  //顺序 浮点数，便于插入扩增
        BoardIdx,
        verifycmd,
        param,
        CNName,
    };
    Q_ENUM( CommonSetType)

    struct structOrdSubAction {   //用于子动作的排序
        double dOrder;      // 排序依据字段
        QString strSubActionName;
    };

    enum MotorInfoType
    {
        DeviceType, //设备类型
        SubOrdIdx,   //调试顺序
        name,    // 名称
        direct,  // 方向
        limit, //范围
        unload,  // 卸载命令
        addparam,   // 附带信息
        IsNeedSave,// 是否让UI显示保存 1 ，是  0 否
        MotorIdx,   //该电机在下位机所属的电机号
        PosID, //该动作在下位机所属的序号，保存的时候用
    };
    Q_ENUM( MotorInfoType)
public:
    static MotorConfigMgr &getInstance();

    /**
     * @brief GetStringValue 获取字符串配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    QString GetStringValue(QString field,QString type);

    /**
     * @brief GetStringValue 获取字符串配置信息
     * @param type      类型
     * @return  对应区域的配置信息
     */
    QString GetStringValue(QString type);

    /**
     * @brief GetBoolValue 获取布尔配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    bool GetBoolValue(UnitModuleFiled field,MotorInfoType type);

    /**
     * @brief GetIntValue 获取整形配置信息
     * @param field     区域名称
     * @param type      类型
     * @return  对应区域的配置信息
     */
    int GetIntValue(UnitModuleFiled field,MotorInfoType type);

    /**
     * @brief GetActionMotorNum 根据动作名称返回涉及设备个数及名称
     * @param strTitle       动作名称，需要与配置档案 的对应
     * @return  非0 成功
     */
    int  GetActionDeviceListNumAndName(QString strModuleName,QString strActionName,QStringList &strlistName); //add



    /**
     * @brief  获取电机配置信息
     * @param strTitlefield       配置档某个动作的名字，如ModuleFiled_Sample-catch
     * @param strName     电机名称
     * @return  对应区域的配置信息
     */
    //0 OK 1FAIL
    int GetMotorConfigInfo(QString strModuleName,QString strActionName,QString strMotorName,MotorConfigInfo &RsStructInfo,int &iRsBoardID);

    int SetMotorConfigInfoFlag(QString strModuleName,QString strActionName,QString strMotorName,QString strFlag);
    int SetAllSubActionMotorFlag(QString strModuleName,QString strActionName,QString strFlag);
    // strListMethodID 0: 初始化ID， 1  验证ID
    int GetActionPrepareBoardIDAndMethodID(QString strModuleName,QString strActionName,int &RsBoardID,QStringList &strListMethodID,QString &strAddParam);

    //查询调试模块的的分解动作集合 ，动作集合用&连接输出 为QString类型
    //strName 为配置档里面的，注意配置档某个项目的名字+“-”不能包含另外一个项目，
    //如Sample 与ScanSample 要修改为SampleScan
    QString GetDebugProjectActionList(QString strModuleName);

private:
    /**
     * @brief _Init 获取配置信息
     * @param
     * @return
     */
    bool _Init();

    /**
     * @brief _Init 获取配置信息
     * @param
     * @return
     */
    bool _InitMotorConfigInfoHash();

    void _readDataToMotorConfigInfo(MotorConfigInfo &motorInfo);
    void _readDataToCommonSet(CommonConfig &commonInfo);

private:
    QString m_strFilePath; // 配置文件路径
    bool    m_bLoadStatus; // 配置文件加载状态
    // QHash<QString,QHash<QString,MotorConfigInfo>> m_mapDebugProject; //调试项目
    QHash<QString,ModuleConfig> m_mapDebugProject;
    //QHash<QString,QString> m_mapDebugProjectInit; //调试项目初始化时序
signals:

public slots:

private:
    MotorConfigMgr();
    ~MotorConfigMgr();
private:
    Q_DISABLE_COPY(MotorConfigMgr);
};


#endif // MotorConfigMgr_H
