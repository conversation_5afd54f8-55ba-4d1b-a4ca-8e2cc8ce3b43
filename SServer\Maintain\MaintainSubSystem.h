#ifndef MaintainSubSystem_H
#define MaintainSubSystem_H

#include<QObject>
#include<QVector>
#include "MotorDebug/MotorDebug.h"
#include"publicconfig.h"
#include"AgingTest/AgingTest.h"
#include "SelfTest/SelfTest.h"
#include "ResetTest/ResetTest.h"

class MaintainSubSystem : public QObject
{
    Q_OBJECT
public:
    static MaintainSubSystem &getInstance();

public:
    /**
     * @brief MotorPosDebug 开始调试(在CAffair::StartProcess调用为了进入运行状态，仪器处在繁忙)
     * @param strParams 调试组件参数
     * @return  
     */
    void MotorPosDebug(const QString strParams); 

    void AgingTestDebug(const QString strParams);
    /**
     * @brief HandleCmdReply 命令执行结果返回
     * @param uiComplexID 执行命令,可以为单指令
     * @param uiResult    执行结果
     * @return  
     */
    void HandleCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult);
    void HandleAgingTestCmdReply(quint16 uiComplexID,QString strpayload,quint16 uiResult);
     void SetPosDebugRun(int _iRun);

#pragma region 仪器自检 {
    /**
     * @brief  SelfTestStart 开始自检
     * @param  uiSeqType 时序类型
     * @return  
     */
    void SelfTestStart(quint16 uiSeqType); 

    /**
     * @brief  SelfTestCleanPCR 开始PCR区域清理
     * @param  uiSeqType 时序类型
     * @return  
     */
    void SelfTestCleanPCR(quint16 uiSeqType); 

    /**
     * @brief  SelfTestSetMotor3AllOptoStatus 设置光耦状态
     * @param  u32Motor3AllOptoStatus 光耦状态
     * @return  
     */
    void SelfTestSetMotor3AllOptoStatus(quint16 u32Motor3AllOptoStatus);  

    /**
     * @brief  SelfTestSendGainOptoStatusCommand 下发命令获取所有光耦当前状态
     * @param 
     * @return  
     */
    void SelfTestSendGainOptoStatusCommand(); 

    /**
     * @brief  SelfTestHandleTimeseqReply 接收时序执行结果
     * @param  uiComplexID 时序执行的id
     * @param  uiResult    时序结果
     * @return  
     */
    void SelfTestHandleTimeseqReply(quint16 uiComplexID, quint16 uiResult);   
#pragma endregion 仪器自检}  

#pragma region 仪器复位 {
    /**
     * @brief  ResetTestStart 开始复位
     * @param  uiSeqType 时序类型
     * @return  
     */
    void ResetTestStart(quint16 uiSeqType); 

    /**
     * @brief  ResetTestHandleTimeseqReply 接收时序执行结果
     * @param  uiComplexID 时序执行的id
     * @param  uiResult    时序结果
     * @return  
     */
    void ResetTestHandleTimeseqReply(quint16 uiComplexID, quint16 uiResult);   
#pragma endregion 仪器复位}  

private:
    MotorDebug *m_pMotorDebug; // 电机调试组件
    CSelfTest  *m_pSelfTest;   // 自检组件
    AgingTest *m_AgingTest;
    CResetTest *m_pResetTest;  // 复位组件
private:
    /**
     * @brief _Init 初始化配置信息
     * @return  
     */
    void _Init();    
signals:

public slots:

private:
    MaintainSubSystem();
    ~MaintainSubSystem();
private:
    Q_DISABLE_COPY(MaintainSubSystem);
    int iRun;  //1 设置了posdebug 模式  0 关闭
};


#endif // MaintainSubSystem_H
