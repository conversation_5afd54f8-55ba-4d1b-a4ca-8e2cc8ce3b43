#ifndef EXTRACTMODULE_H
#define EXTRACTMODULE_H
#include "module/devicemodule.h"

enum ExtractTaskID
{
    ETI_PUNCH = 0,//刺破
    ETI_EXTRACT, //提取
    ETI_EXTRACT_START, //提取开始
    ETI_EXTRACT_RUN, //正在提取
    ETI_EXTRACT_END, //提取结束
    ETI_EJECT_MAG_TUBE,//退磁套并初始化
};

enum ExtractScanCodeStatus
{
    Status_NoScan,
    Status_Scanning,
    Status_ScanFinish,
    Status_ScanFail,
};

struct ExtractScanCodeInfo
{
    int iIdx;
    ExtractScanCodeStatus iStatus;
    QString strCodeMsg;  //二维码信息
};

class ExtractModule : public DeviceModule {
    Q_OBJECT
public:
    ExtractModule(bool bUseThread = false); // 添加默认值
    void AddScanCodeStripIdx(QList<int> StripIdxList);  //lxj 还有有一个完成的时候清空reset的函数
    void ResetScanCodeStrip();
    void UpdataScanCodeInfo(QString strCodeData,ExtractScanCodeStatus iStatus);  //查找第一个status = Scanning  的 压入信息
    void SetExtractScanStatus(int iStripIdxList,ExtractScanCodeStatus iStatus); //设置扫描状态
    void FindNextScanStripIdxList(int &iStripIdxList);   //iStripIdxList =-1 已经全部完成
    void PackageScanStripScanRsInfo(QString &strInfo);//打包扫描条码信息    //格式（同一个试剂条“，”，不同的“|”）：试剂条序号，扫码状态（2：Status_ScanFinish   3：Status_ScanFail），条码信息|
    int JudgeScanRs(QList<QString> listMsg,QVector<long> &vecDiffCnt);//返回比对fail的个数，16个比对

    /**
     * @brief SetDebugStatus 设置提取模块调试状态
     * @param bRun 是否运行
     */      
    void SetDebugStatus(bool bRun);

    /**
     * @brief DebugActionCmdReply 提取模块调试动作回复
     * @param uiComplexID 复合指令id
     * @param uiResult 结果
     */      
    void DebugActionCmdReply(quint16 uiComplexID, quint16 uiResult);

    /**
     * @brief SetExtractHoleStatus 设置提取模块孔位裂解加热状态
     * @param uiHoleStatus 设置状态
     */      
    void SetExtractClevageHoleStatus(quint16 uiHoleStatus);
signals:
    void SignalAddSendExtractSeqTask(QString strParam);

public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr="", const QString& strParamStr = "") override;
    void SlotAddTask(const CmdTask& task) override;
protected:
    void SlotInitialize() override;

private:
    void _ProcessSubTask() override;

protected:
    QList<ExtractScanCodeInfo> ScanCodeInfoList;
    bool m_bDebug = false;
};

#endif // EXTRACTMODULE_H
