#include "pcrcatchmodule.h"
#include "consumables/pcrresource.h"

PCRCatchModule::PCRCatchModule(bool bUseThread, quint8 quCatchType)
    : DeviceModule("PCRCatchModule",bUseThread), m_uiCatchType(quCatchType)
{
} // 在构造函数中进行初始化

void PCRCatchModule::SetCatchType(quint8 quCatchType)
{
    m_uiCatchType = quCatchType; // 新增设置 m_uiCatchType 变量的函数
}

qint8 PCRCatchModule::GetCurSubTaskID()
{
    qDebug()<<"GetCurSubTaskID:"<<m_iCurSubTaskID;
    return m_iCurSubTaskID;
}

void PCRCatchModule::SetCurSubTaskID()
{
    m_iCurSubTaskID = -1;
    qDebug()<<"SetCurSubTaskID:"<<m_iCurSubTaskID;
}

quint8 PCRCatchModule::GetTaskQueueSize()
{
    qDebug()<<"GetTaskQueueSize:"<<m_qTaskQueue.size();
    return m_qTaskQueue.size();
}

bool PCRCatchModule::GetIsBusy()
{
    qDebug()<<"GetIsBusy:"<<m_bIsBusy;
    return m_bIsBusy;
}

void PCRCatchModule::SetIsBusy(bool bIsBusy)
{
    m_bIsBusy = bIsBusy;
    qDebug()<<"SetIsBusy:"<<m_bIsBusy;
}

void PCRCatchModule::ClearTaskQueue()
{
    emit SignalInitData();
    qDebug()<<"ClearTaskQueue:";
}

void PCRCatchModule::SlotAddSubTask(quint8 uiSubTaskID, const QString &strCommandStr, const QString &strParamStr)
{
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
    // 可以在这里添加子类特有的逻辑
    //    qDebug() << "PCRCatchModule adding task with specific logic (uiSubTaskID: " << uiSubTaskID << ", strCommandStr: " << strCommandStr << ", strParamStr: " << strParamStr << ")";
    
}

void PCRCatchModule::SlotAddTask(const CmdTask& task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "PCRCatchModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}

void PCRCatchModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "PCRCatchModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";

        m_iCurSubTaskID = task.uiSubTaskID;
        switch (task.uiSubTaskID)
        {
        case PCTI_SWITCH_TUBE:
        {
            _AddSubTask(task.strParamStr, Action_SwitchTube);
            break;
        }
        case PCTI_MIX_TUBE:
        {
            _AddSubTask(task.strParamStr, Action_CentrifugeTube);
            break;
        }        
        case PCTI_OPEN_CAP:
        {
            _AddSubTask(task.strParamStr, Action_OpenPCRCap);
            break;
        }
        case PCTI_CLOSE_CAP:
        {
            _AddSubTask(task.strParamStr, Action_ClosePCRCap);
            break;
        }
        case PCTI_ABANDON_OPEN_CAP:
        {
            _AddSubTask(task.strParamStr, Action_AbandonOpenPCRCap);
            break;
        }
        case PCTI_ABANDON_CLOSE_CAP:
        {
            _AddSubTask(task.strParamStr, Action_AbandonClosePCRCap);
            break;
        }        
        case PCTI_TRANS_TO_PCR_AMPLIFY_AREA:
        {
            _AddSubTask(task.strParamStr, Action_TransPCRTube);
            break;
        }
        case PCTI_ABANDON_TUBE:
        {
            _AddSubTask(task.strParamStr, Action_AbandonPCR);
            break;
        }
        case PCTI_CLEAN_CENTIFUGE_AND_SWITCH: // 清理离心区域和转移区域
        {
             _AddSubTask(task.strParamStr, Action_CleanCentrifuge);
            break;
        }
        case PCTI_GRIPPER_ABANDON_AND_INIT: // 爪子移动到丢弃口，爪子复位，电机复位
        {
             _AddSubTask(task.strParamStr, Action_Board3GripperAbandonAndInit);
            break;
        }
        case PCTI_GRIPPER_INIT: // 爪子初始化
        {
             _AddSubTask(task.strParamStr, Action_Board3GripperInit);
            break;
        }
        case PCTI_Z_INIT: // Z轴初始化
        {
             _AddSubTask(task.strParamStr, Action_Board3ZInit);
            break;
        }
        case PCTI_EXCEPT_Z_MOTOR_INIT: // 除Z轴外的电机初始化
        {
             _AddSubTask(task.strParamStr, Action_Board3ExceptZMotorInit);
            break;
        }
        case PCTI_REAMIN_INIT: // 其他剩余初始化
        {
             _AddSubTask(task.strParamStr, Action_Board3RemainInit);
            break;
        }
        default:
            break;
        }
        m_qWaitProcessSubTask.pop_front();
    }
}

