#include<qdebug.h>
#include<QTime>
#include <QFile>
#include <QVariantMap>
#include <QDir>
#include <string_view>
#include"SystemConfig.h"
#include "toml/toml.hpp"

namespace SystemConfigTomlSingleton 
{
    // 定义在头文件的话，引用的是toml库，编译速度较慢
    toml::value config;
};


SystemConfig::SystemConfig()
{
    m_strFilePath = "./ServerConfig/ServerConfig.toml";// 系统配置文件路径
}

QString SystemConfig::GetStringValue(QString field, QString type)
{
    if (m_bLoadStatus == false)
    {
        qDebug() <<"ServerConfig.toml file load failed!!! "<<m_bLoadStatus;  
        return "";
    }
    
    QString strRetValue = "";
    auto& config = SystemConfigTomlSingleton::config;
    if (!config.contains(field.toStdString()))
    {
       qDebug() <<"SystemConfig::GetStringValue field not exist"<<field;    
       return strRetValue;
    }
    
    auto table = toml::find(config, field.toStdString());
    if (table.is_table() && table.contains(type.toStdString()))
    {
        auto value = toml::find<std::string>(table, type.toStdString());
        strRetValue = QString::fromStdString(value);
    }
    qDebug() <<"SystemConfig::GetStringValue"<<field<<type<<strRetValue;    
    return strRetValue;
}

void SystemConfig::GetStringValue(QString& value,QString field,QString type)
{
    QString str = GetStringValue(field,type);
    if (!str.isEmpty())
    {
        value = str;
    }
}

bool SystemConfig::GetBoolValue(QString field, QString type)
{
    QString strValue = GetStringValue(field,type);
    return strValue == "1";
}

bool SystemConfig::GetBoolValue(EnumConfigFieldType field,EnumNetworkType type)
{
    QMetaEnum metaConfigFieldType = QMetaEnum::fromType<EnumConfigFieldType>();
    QString strConfigField = metaConfigFieldType.valueToKey(static_cast<int>(field));

    QMetaEnum metaType = QMetaEnum::fromType<EnumNetworkType>();
    QString strType = metaType.valueToKey(static_cast<int>(field));

    qDebug()<<"GetBoolValue: "<<strConfigField<<strType;
    return GetBoolValue(strConfigField,strType);
}

int SystemConfig::GetIntValue(EnumConfigFieldType field, EnumResetType type)
{
    qint32 value = 100;//最小100ms
    QMetaEnum metaConfigFieldType = QMetaEnum::fromType<EnumConfigFieldType>();
    QString strConfigField = metaConfigFieldType.valueToKey(static_cast<int>(field));

    QMetaEnum metaType = QMetaEnum::fromType<EnumResetType>();
    QString strType = metaType.valueToKey(static_cast<int>(field));

    QString strValue = GetStringValue(strConfigField,strType);
    if (strValue.isEmpty())
    {
        return value;
    }
    value = strValue.toInt();

    qDebug()<<"GetIntValue: "<<strConfigField<<strType<<strValue;
    return value;
}

QString SystemConfig::GetMetaEnumFiledString(const QMetaEnum metaField,const quint8 type)
{
    QString strField = metaField.valueToKey(static_cast<int>(type));
    return strField;
}

bool SystemConfig::Load()
{
    QFile file(m_strFilePath);
    if (!file.exists()) {
        m_bLoadStatus = false;
        qWarning() << "ServerConfig.toml file does not exist" << m_strFilePath;
        return false;
    }
    
    SystemConfigTomlSingleton::config = toml::parse( m_strFilePath.toStdString());
    m_bLoadStatus = true;
    qDebug()<<"SystemConfig::Load";
    return true;
}

SystemConfig::~SystemConfig()
{

}

SystemConfig &SystemConfig::getInstance()
{
    static SystemConfig obj;
    return obj;
}
