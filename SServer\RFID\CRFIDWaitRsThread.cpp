#include <QCoreApplication>
#include<qdebug.h>
#include "CRFIDWaitRsThread.h"
#include"publicconfig.h"
#include"publicfunction.h"
#include"CRFIDMotionTask.h"
#include"./RFID/CRFIDCtrl.h"
CRFIDWaitRsThread::CRFIDWaitRsThread(QObject *parent) : QObject(parent)
{
    m_thread = new QThread();

    InitMethodIDRsList();
    connect(this,SIGNAL(SignFeedBack(int,int,int)),&CRFIDMotionTask::getInstance(),SLOT(SlotFeedBack(int,int,int)));
    connect(this,SIGNAL(SignReadRFIDData(int,QString)),&CRFIDMotionTask::getInstance(),SLOT(SlotRFIDReadData(int ,QString)));
   this->moveToThread(m_thread);
   m_thread->start();
}

void  CRFIDWaitRsThread::InitMethodIDRsList()
{
    m_MethodIDRsList.clear();
    m_SeqIDRsList.clear();
    m_PayLoadList.clear();
    for(int i =0;i<gk_iMaxMethodIDSize;i++)
    {
        m_MethodIDRsList.push_back(-1);
        m_SeqIDRsList.push_back(-1);
        m_PayLoadList.push_back("");
    }
}

CRFIDWaitRsThread &CRFIDWaitRsThread::getInstance()
{
    static CRFIDWaitRsThread cCRFIDThread;
    return cCRFIDThread;
}

int CRFIDWaitRsThread::SlotWaitStatusFeedBack(int iMethodID,int iType)
{
    QString strRs ="";   //0712 
   int  iMaxTime =6000;
    int iSleep    = 50;
    int iSleepSum = iMaxTime / iSleep;
    int iCnt =0;

    while(iCnt <iSleepSum)
    {
        if(m_MethodIDRsList[iMethodID] ==0)
        {
            m_qMethodIDRsMutex.lock();
            m_MethodIDRsList[iMethodID]   =   -1;   //reset
            strRs  = m_PayLoadList[iMethodID];
            m_PayLoadList[iMethodID]="";
            qDebug()<<__FUNCTION__<<"Method_rfid_read";
            if(iMethodID == Method_rfid_read)
            {
                qDebug()<<__FUNCTION__<<"emit SignReadRFIDData";
               emit  SignReadRFIDData(iType,strRs);
            }
            m_qMethodIDRsMutex.unlock();
            qDebug()<<__FUNCTION__<<"!!!!iCnt="<<iCnt;
            emit SignFeedBack(iMethodID,0,iType);
            return 0;
        }
        if(m_MethodIDRsList[iMethodID] ==1)
        {
            m_qMethodIDRsMutex.lock();
            m_MethodIDRsList[iMethodID]   =   -1;   //reset
            strRs  = m_PayLoadList[iMethodID];
            m_PayLoadList[iMethodID]="";
            m_qMethodIDRsMutex.unlock();
            emit SignFeedBack(iMethodID,1,iType);
            qWarning() << "CRFIDWaitRsThread::SlotWaitStatusFeedBack failed"<<iMethodID<<iType<<iCnt;
            return 1;
        }
        QCoreApplication::processEvents(QEventLoop::AllEvents,iSleep);
        QThread::msleep(iSleep);
        iCnt++;
    }

    if(iCnt>=iSleepSum)
    {
        qDebug()<<__FUNCTION__<<"iMethodID=:"<<iMethodID<<"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!TimeOut";
        emit SignFeedBack(iMethodID,-1,iType);
        return  -1;
    }

    return  -1;
}

void CRFIDWaitRsThread::SlotMethodIDMotionRsMsg(QByteArray qSendMsgAarry)
{
    // qDebug()<<"SlotMethodIDMotionRsMsg -----";
    quint16 m_iMethodID;
    quint16 m_iSeqID;
    m_iSeqID         =  GetByte2Int(qSendMsgAarry.data() + gk_iSeqPos);
    m_iMethodID = GetByte2Int(qSendMsgAarry.data() + gk_iMethodIDPos);
    quint8 iRs= *((quint8*)qSendMsgAarry.data() + gk_iResultPos);

    m_pFramePos = qSendMsgAarry.data() + gk_iLengthPos;
    m_iReadPayloadLength = GetByte2Int(m_pFramePos);
    m_qPayloadByteArray = qSendMsgAarry.mid(gk_iFrameDataPos, m_iReadPayloadLength);
    m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
    m_qPayloadString = m_qPayloadString.replace("[", "");
    m_qPayloadString = m_qPayloadString.replace("]", "");

    qDebug()<<__FUNCTION__<<"@@@@@ lock in !!!!!!!!m_iMethodID ="<<m_iMethodID<<",iRs="<<iRs<<",m_iSeqID="<<m_iSeqID;
    m_qMethodIDRsMutex.lock();
    if(m_iMethodID < gk_iMaxMethodIDSize)
    {
        if(m_SeqIDRsList[m_iMethodID]  != m_iSeqID)
        {
            m_MethodIDRsList[m_iMethodID] = iRs;
            m_PayLoadList[m_iMethodID]  = m_qPayloadString;
            m_SeqIDRsList[m_iMethodID]  = m_iSeqID;
        }
    }
    m_qMethodIDRsMutex.unlock();

    // qDebug()<<__FUNCTION__<<"@@@@@lock out!!!!!!!!m_iMethodID ="<<m_iMethodID;

}
