#ifndef CMODULESEQMAPPER_H
#define CMODULESEQMAPPER_H

#include<QMap>
#include<QVector>

enum SeqExecST
{
    SEST_WAIT_SAMPLE_CATCH = 0,
    SEST_WAIT_OPEN_SAMPLE_CAP,
    SEST_WAIT_CLOSE_SAMPLE_CAP,
    SEST_WAIT_SAMPLE_BACK_HOME,
    SEST_WAIT_SAMPLING,
    SEST_WAIT_SPIT_SAMPLING,
    SEST_WAIT_TRANS_CLEVAGE,
    SEST_SAMPLE_FINISH,//样本结束(第一阶段中的样本转移结束)
    SEST_WAIT_EXTRACT,
    SEST_WAIT_SUB_PACK_REAGENT,
    SEST_WAIT_TRANS_REAGENT,
    SEST_WAIT_TRANS_PURIFY,
    SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE,// 由耗材盒转移到转移模块上
    SEST_WAIT_PCR_SWITCH,// 转移模块转走到离心位置(抓手抓起pcr管)
    SEST_WAIT_PCR_MIX,// 开始离心
    SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA,
    SEST_WAIT_CLOSE_PCR,
    SEST_WAIT_PCR_AMPLIFY,
    SEST_WAIT_ABANDON,
    SEST_FINISH,//全部流程结束
    SEST_ERROR,
};

static const char *kObjectID[] = {
    [SEST_WAIT_SAMPLE_CATCH] ="SAMPLE_CATCH",
    [SEST_WAIT_OPEN_SAMPLE_CAP] ="OPEN_SAMPLE_CAP",
    [SEST_WAIT_CLOSE_SAMPLE_CAP] ="CLOSE_SAMPLE_CAP",
    [SEST_WAIT_SAMPLE_BACK_HOME] ="SAMPLE_BACK_HOME",
    [SEST_WAIT_SAMPLING] ="SAMPLING",
    [SEST_WAIT_SPIT_SAMPLING] ="SPIT_SAMPLING",
    [SEST_WAIT_TRANS_CLEVAGE] ="TRANS_CLEVAGE",
    [SEST_SAMPLE_FINISH] ="SAMPLE_FINISH",
    [SEST_WAIT_EXTRACT] ="EXTRACT",
    [SEST_WAIT_SUB_PACK_REAGENT] ="SUB_PACK_REAGENT",
    [SEST_WAIT_TRANS_REAGENT] ="TRANS_REAGENT",
    [SEST_WAIT_TRANS_PURIFY] ="TRANS_PURIFY",
    [SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE] ="CAP_AND_TRANS_TUBE",
    [SEST_WAIT_PCR_SWITCH] ="PCR_SWITCH",
    [SEST_WAIT_PCR_MIX] ="PCR_MIX",
    [SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA] ="TRANS_TO_PCR_AMPLIFY_AREA",
    [SEST_WAIT_CLOSE_PCR] ="CLOSE_PCR",
    [SEST_WAIT_PCR_AMPLIFY] ="PCR_AMPLIFY",
    [SEST_WAIT_ABANDON] ="ABANDON",
    [SEST_FINISH] ="SEST_FINISH",
    [SEST_ERROR] = "SEST_ERROR",
};

enum LogicalModule //逻辑模块
{
    LM_GLOBAL = 0, //全局模块
    LM_SAMPLE = 1, //样本模块
    LM_EXTRACT, //提取模块
    LM_GANTRY,//龙门架模块
    LM_TRANS_AND_MIX, //转移混匀模块
    LM_PCR_CATCHER, //PCR抓手模块
    LM_PCR, //PCR模块
};


enum ComponentUnit
{
    CU_SAMPLE_RIGHT_CATCH = 0x41, //样本右抓手
    CU_SAMPLE_LEFT_CATCH, //样本左抓手
    CU_GANTRY_RIGHT_PUMP, //龙门架右移液泵
    CU_GANTRY_LEFT_PUMP, //龙门架左移液泵
};


class CModuleSeqMapper {
public:
    // 获取单例实例的访问点
    static CModuleSeqMapper& getInstance();
    // 查询逻辑模块关联的时序状态列表
    QVector<quint8> getSequences(const quint8 uiLogicalModule) const;
private:
    void _init();

private:
    explicit CModuleSeqMapper(); // 私有构造函数
    CModuleSeqMapper(const CModuleSeqMapper&) = delete; // 禁止拷贝构造
    CModuleSeqMapper& operator=(const CModuleSeqMapper&) = delete; // 禁止赋值操作符
    QMap<quint8, QVector<quint8>> m_seqMap; // 成员变量

};

#endif // CMODULESEQMAPPER_H
