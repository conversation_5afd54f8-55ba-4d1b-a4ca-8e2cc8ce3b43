#include "affair/cdevStatus.h"
#include <QtDebug>

#include "datacontrol/CSystemDB.h"
#include "control/coperationunit.h"
#include "publicconfig.h"

// GripperPumpModule 类的实现文件

const QString kSampleGripperGrabKey = "SampleGripper_Grab_";
const QString kSampleGripperAvailKey = "SampleGripper_Avail_";
const QString kSampleGripperGrabItemKey = "SampleGripper_Item_";
const QString kSampleGripperSafePosKey = "SampleGripper_Pos_";

const QString kGantryPumpGrabKey = "GantryPump_Grab_";
const QString kGantryPumpAvailKey = "GantryPump_Avail_";
const QString kGantryPumpGrabItemKey = "GantryPump_Item_";
const QString kGantryPumpSafePosKey = "GantryPump_Pos_";

const QString kPCRGripperGrabKey = "PCRGripper_Grab";
const QString kPCRGripperAvailKey = "PCRGripper_Avail";
const QString kPCRGripperGrabItemKey = "PCRGripper_Item";
const QString kPCRGripperSafePosKey = "PCRGripper_Pos";

const QString kSampleGripperPrefix = "SampleGripper";
const QString kGantryPumpPrefix = "GantryPump";
const QString kPCRGripperPrefix = "PCRGripper";

const QString kGrabStatusNames[]=
{
    "GS_NotGrabbed" ,//!< 未抓取
    "GS_HasGrabbed", //!< 已抓取
};

const QString kCompTypeNames[CT_MAX] =
{
    "CT_SAMPLE_GRIPPER",
    "CT_GANTRY_PUMP",
    "CT_PCR_GRIPPER",
};

const QString kCompIndexNames[CI_MAX]=
{
    "CI_RIGHT", //右组件
    "CI_LEFT",//左组件
};

const QString kItemTypeNames[IT_MAX] =
{
    "IT_Unknown",
    "IT_SampleTube",       //!< 样本管
    "IT_SampleCap",        //!< 样本盖
    "IT_Tip200",           //!< 200ul吸头
    "IT_Tip1000",          //!< 1000ul吸头
    "IT_PCRTube",          //!< PCR扩增管
    "IT_PCRCap",           //!< PCR扩增盖
    "IT_PCRPlateCover",    //!< PCR扩增区域盖子
    "IT_PCRTubeAndCap", //盖了盖的PCR扩增管
    // 可以继续添加其他类型
};

void GripperPumpModule::Init()
{
    // 初始化组件状态，默认为可用且未抓取
    for(int i = CI_START;i<CI_MAX;i++)
    {
        m_compList[i] ={CAS_Available, GS_NotGrabbed, IT_Unknown, ""};
    }
}

GripperPumpModule::GripperPumpModule(QObject *parent)
    : QObject(parent)
{
    Init();
}

GripperPumpModule::~GripperPumpModule()
{
}

// 设置指定组件的可用状态
void GripperPumpModule::setComponentAvailability(int iCompIndex, CompAvrStatus avrStatus)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX)
    {
        m_compList[iCompIndex].avrStatus = avrStatus;
    }
}

// 设置指定组件的抓取状态
void GripperPumpModule::setComponentGrabStatus(int iCompIndex, GrabStatus grabStatus)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX) {
        m_compList[iCompIndex].grabStatus = grabStatus;
    }
}

void GripperPumpModule::setItemType(int iCompIndex, quint8 uiItemType)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX) {
        m_compList[iCompIndex].uiGrabItemType = uiItemType;
    }
}

quint8 GripperPumpModule::getItemType(int iCompIndex)
{
    quint8 uiItemType = IT_Unknown;
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX)
    {
        uiItemType = m_compList[iCompIndex].uiGrabItemType;
    }
    return  uiItemType;
}

void GripperPumpModule::setSafePos(int iCompIndex, QString strSafePos)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX) {
        m_compList[iCompIndex].strSafePos = strSafePos;
    }
}

QString GripperPumpModule::getSafePos(int iCompIndex)
{
    QString strSafePos = "0";
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX)
    {
        strSafePos = m_compList[iCompIndex].strSafePos;
    }
    return strSafePos;
}


void GripperPumpModule::setItemProperty(int iCompIndex, quint8 uiItemType, QString strSafePos)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX) {
        m_compList[iCompIndex].uiGrabItemType = uiItemType;
        m_compList[iCompIndex].strSafePos = strSafePos;
    }
}

void GripperPumpModule::getItemProperty(int iCompIndex, quint8& uiItemType, QString& strSafePos)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX)
    {
        uiItemType = m_compList[iCompIndex].uiGrabItemType;
        strSafePos = m_compList[iCompIndex].strSafePos;
    }
    else
    {
        uiItemType = IT_Unknown;
        strSafePos = "0";
    }
}

// 获取指定组件的可用状态
CompAvrStatus GripperPumpModule::getComponentAvailability(int iCompIndex) const
{
    return (iCompIndex >= CI_START && iCompIndex < CI_MAX) ? m_compList[iCompIndex].avrStatus : CAS_Unavailable;
}

// 获取指定组件的抓取状态
GrabStatus GripperPumpModule::getComponentGrabStatus(int iCompIndex) const
{
    return (iCompIndex >= CI_START && iCompIndex < CI_MAX) ? m_compList[iCompIndex].grabStatus : GS_NotGrabbed;
}


void GripperPumpModule::setComponentStatus(int iCompIndex, CompAvrStatus avrStatus,
                                           GrabStatus grabStatus, quint8 uiItemType, QString strSafePos)
{
    if (iCompIndex >= CI_START && iCompIndex < CI_MAX)
    {
        m_compList[iCompIndex] = {avrStatus, grabStatus, uiItemType, strSafePos};
    }
}

DevComponent GripperPumpModule::getDevComponent(quint8 uiCompIndex)
{
    DevComponent devComp;
    if(uiCompIndex>=CI_START && uiCompIndex<CI_MAX)
    {
        devComp = m_compList[uiCompIndex];
    }
    qDebug()<<"GripperPumpModule::getDevComponent"<<uiCompIndex<<devComp.strSafePos<<devComp.uiGrabItemType<<devComp.grabStatus;
    return  devComp;
}

int GripperPumpModule::availableCompCount() const
{
    int iAvailCount = 0;
    for(int i=CI_START;i<CI_MAX;i++)
    {
        if(m_compList[i].avrStatus == CAS_Available)
            iAvailCount++;
    }
    return iAvailCount;
}

int GripperPumpModule::firstAvailableIndex() const
{
    for (size_t i = CI_START; i < CI_MAX; ++i)
    {
        if (m_compList[i].avrStatus == CAS_Available)
        {
            return static_cast<int>(i);
        }
    }
    return -1; // 没有可用组件时的返回值
}

CDevStatus &CDevStatus::getInstance()
{
    static CDevStatus cDevStatus;
    return cDevStatus;
}

void CDevStatus::reset()
{
    setSampleGripperAvailability(0, CAS_Available);
    setSampleGripperAvailability(1, CAS_Available);
    setSampleGripperGrabStatus(0, GS_NotGrabbed);
    setSampleGripperGrabStatus(1, GS_NotGrabbed);
    setSampleGripperItemType(0, IT_Unknown);
    setSampleGripperItemType(1, IT_Unknown);
    setSampleGripperSafePos(0, "0");
    setSampleGripperSafePos(1, "0");

    setGantryPumpAvailability(0, CAS_Available);
    setGantryPumpAvailability(1, CAS_Available);
    setGantryPumpGrabStatus(0, GS_NotGrabbed);
    setGantryPumpGrabStatus(1, GS_NotGrabbed);
    setGantryPumpItemType(0, IT_Unknown);
    setGantryPumpItemType(1, IT_Unknown);
    setGantryPumpSafePos(0, "0");
    setGantryPumpSafePos(1, "0");

    setPcrGripperStatus(CAS_Available, GS_NotGrabbed, IT_Unknown, "0");
}

// CDevStatus 类的实现文件
void CDevStatus::_initPCRGripper()
{
    QMutexLocker locker(&m_mutex);
    m_pcrGripper = {CAS_Available, GS_NotGrabbed, IT_Unknown, 0};
}

CDevStatus::CDevStatus(QObject *parent)
    : QObject(parent)
{
    // 初始化PCR抓手为默认状态：可用且未抓取
    _initPCRGripper();
    //    tstInit();
    loadFromDB();
    m_bUpdate.store(true);
}

CDevStatus::~CDevStatus()
{

}
// params:[comp_type,comp_index,grab_status,grab_item_type, safe_pos]
// comp_type:设备组件类型,其中 样本抓手=0, 龙门架移液泵 = 1, pcr 抓手 = 2
// comp_index:组件索引, 其中右组件 =0, 左组件= 1
// grab_status:抓取状态，未抓取 = 0， 抓取 = 1
// grab_item_type:    
//     IT_SampleTube=1,       //!< 样本管      
//     IT_SampleCap =2,        //!< 样本盖      
//     IT_Tip200 = 3 ,           //!< 200ul吸头  
//     IT_Tip1000 = 4,          //!< 1000ul吸头 
//     IT_PCRTube =5,          //!< PCR扩增管   
//     IT_PCRCap = 6,           //!< PCR扩增盖   
//     IT_PCRPlateCover = 7,    //!< PCR扩增区域盖子
void CDevStatus::setDevCompStatus(QString strParamsStr)
{
    QStringList strParamsList = strParamsStr.split(",");
    if(strParamsList.size() == 5 && m_bUpdate.load())
    {
        quint8 uiCompType = strParamsList.at(0).toInt();
        quint8 uiCompIndex = strParamsList.at(1).toInt();
        quint8 uiGrabStatus = strParamsList.at(2).toInt();
        quint8 uiGrabItemType = strParamsList.at(3).toInt();
        QString strSafePos = strParamsList.at(4);

        qDebug()<<"setDevCompStatus"<<strParamsStr
                <<"Type:"<<kCompTypeNames[uiCompType]
                <<"index"<<kCompIndexNames[uiCompIndex]
                <<"grabStatus:"<<kGrabStatusNames[uiGrabStatus]
                <<"type:"<<kItemTypeNames[uiGrabItemType];

        switch (uiCompType)
        {
        case CT_SAMPLE_GRIPPER:
        {
            _onPropertySet(getSampleGripperGrabKey(uiCompIndex), uiGrabStatus);
            _onPropertySet(getSampleGripperItemKey(uiCompIndex), uiGrabItemType);
            _onPropertySet(getSampleGripperPosKey(uiCompIndex), strSafePos);
            break;
        }
        case CT_GANTRY_PUMP:
        {
            _onPropertySet(getGantryPumpGrabKey(uiCompIndex), uiGrabStatus);
            _onPropertySet(getGantryPumpItemKey(uiCompIndex), uiGrabItemType);
            _onPropertySet(getGantryPumpPosKey(uiCompIndex), strSafePos);
            break;
        }
        case CT_PCR_GRIPPER:
        {
            _onPropertySet(kPCRGripperGrabKey, uiGrabStatus);
            _onPropertySet(kPCRGripperGrabItemKey, uiGrabItemType);
            _onPropertySet(kPCRGripperSafePosKey, strSafePos);
            break;
        }
        default:
            QDFUN_LINE<<"error comp type";
        }
    }
}

// 样本抓手模块的设置和获取方法实现
void CDevStatus::setSampleGripperAvailability(int iCompIndex, CompAvrStatus avrStatus)
{
    _onPropertySet(getSampleGripperAvailKey(iCompIndex),avrStatus);
}

CompAvrStatus CDevStatus::getSampleGripperAvailability(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_sampleGripper.getComponentAvailability(iCompIndex);
}

void CDevStatus::setSampleGripperGrabStatus(int iCompIndex, GrabStatus grabStatus)
{
    _onPropertySet(getSampleGripperGrabKey(iCompIndex), grabStatus);
}

GrabStatus CDevStatus::getSampleGripperGrabStatus(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_sampleGripper.getComponentGrabStatus(iCompIndex);
}

int CDevStatus::sampleGripperAvailableCount()
{
    QMutexLocker locker(&m_mutex);
    return m_sampleGripper.availableCompCount();
}

int CDevStatus::sampleGripperFirstAvailableIndex()
{
    QMutexLocker locker(&m_mutex);
    return m_sampleGripper.firstAvailableIndex();
}

// 龙门架泵模块的操作方法实现，参考样本抓手模块的实现模式，只需替换m_sampleGripper为m_gantryPump
void CDevStatus::setGantryPumpAvailability(int iCompIndex, CompAvrStatus avrStatus)
{
    _onPropertySet(getGantryPumpAvailKey(iCompIndex), avrStatus);
}

CompAvrStatus CDevStatus::getGantryPumpAvailability(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.getComponentAvailability(iCompIndex);
}

void CDevStatus::setGantryPumpGrabStatus(int iCompIndex, GrabStatus grabStatus)
{
    _onPropertySet(getGantryPumpGrabKey(iCompIndex), grabStatus);
}

GrabStatus CDevStatus::getGantryPumpGrabStatus(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.getComponentGrabStatus(iCompIndex);
}

int CDevStatus::gantryPumpAvailableCount()
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.availableCompCount();
}

int CDevStatus::gantryPumpFirstAvailableIndex()
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.firstAvailableIndex();
}

void CDevStatus::setSampleGripperItemType(int iCompIndex, quint8 uiItemType)
{
    _onPropertySet(getSampleGripperItemKey(iCompIndex), uiItemType);
}

void CDevStatus::setSampleGripperSafePos(int iCompIndex, QString strSafePos)
{
    _onPropertySet(getSampleGripperPosKey(iCompIndex), strSafePos);
}

quint8 CDevStatus::getSampleGripperItemType(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_sampleGripper.getItemType(iCompIndex);
}

QString CDevStatus::getSampleGripperSafePos(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return  m_sampleGripper.getSafePos(iCompIndex);
}

DevComponent CDevStatus::getSampleGripper(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    DevComponent comp = m_sampleGripper.getDevComponent(iCompIndex);
    return comp;
}

void CDevStatus::setGantryPumpItemType(int iCompIndex, quint8 uiItemType)
{
    _onPropertySet(getGantryPumpItemKey(iCompIndex), uiItemType);
}

void CDevStatus::setGantryPumpSafePos(int iCompIndex, QString strSafePos)
{
    _onPropertySet(getGantryPumpPosKey(iCompIndex), strSafePos);
}

quint8 CDevStatus::getGantryPumpItemType(int iCompIndex)
{
    return m_gantryPump.getItemType(iCompIndex);
}

QString CDevStatus::getGantryPumpSafePos(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.getSafePos(iCompIndex);
}

DevComponent CDevStatus::getGantryPump(int iCompIndex)
{
    QMutexLocker locker(&m_mutex);
    return m_gantryPump.getDevComponent(iCompIndex);
}

void CDevStatus::setPcrGripperItemType(quint8 uiItemType)
{
    _onPropertySet(kPCRGripperGrabItemKey, uiItemType);
}

DevComponent CDevStatus::getPcrGripper()
{
    QMutexLocker locker(&m_mutex);
    return  m_pcrGripper;
}

void CDevStatus::setPcrGripperGrabStatus(GrabStatus grabStatus)
{
    _onPropertySet(kPCRGripperGrabKey, grabStatus);
}

quint8 CDevStatus::getPcrGripperItemType()
{
    QMutexLocker locker(&m_mutex);
    return m_pcrGripper.uiGrabItemType;
}

QString CDevStatus::getPcrGripperSafePos()
{
    QMutexLocker locker(&m_mutex);
    return m_pcrGripper.strSafePos;
}

// PCR抓手的操作方法实现
void CDevStatus::setPcrGripperStatus(CompAvrStatus avrStatus, GrabStatus grabStatus, quint8 uiItemType, QString strSafePos)
{
    _onPropertySet(kPCRGripperAvailKey, avrStatus);
    _onPropertySet(kPCRGripperGrabKey, grabStatus);
    _onPropertySet(kPCRGripperGrabItemKey, uiItemType);
    _onPropertySet(kPCRGripperSafePosKey, strSafePos);
}

void CDevStatus::setPcrGripperAvailStatus(CompAvrStatus avrStatus)
{
    _onPropertySet(kPCRGripperAvailKey, avrStatus);
}

CompAvrStatus CDevStatus::getPcrGripperAvailability()
{
    QMutexLocker locker(&m_mutex);
    return m_pcrGripper.avrStatus;
}

GrabStatus CDevStatus::getPcrGripperGrabStatus()
{
    QMutexLocker locker(&m_mutex);
    return m_pcrGripper.grabStatus;
}

QString CDevStatus::getSampleGripperAvailKey(int iCompIndex)
{
    QString strKey = kSampleGripperAvailKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getSampleGripperGrabKey(int iCompIndex)
{
    QString strKey = kSampleGripperGrabKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getSampleGripperItemKey(int iCompIndex)
{
    QString strKey = kSampleGripperGrabItemKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getSampleGripperPosKey(int iCompIndex)
{
    QString strKey = kSampleGripperSafePosKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getGantryPumpGrabKey(int iCompIndex)
{
    QString strKey = kGantryPumpGrabKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getGantryPumpItemKey(int iCompIndex)
{
    QString strKey = kGantryPumpGrabItemKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getGantryPumpAvailKey(int iCompIndex)
{
    QString strKey = kGantryPumpAvailKey + QString::number(iCompIndex);
    return strKey;
}

QString CDevStatus::getGantryPumpPosKey(int iCompIndex)
{
    QString strKey = kGantryPumpSafePosKey + QString::number(iCompIndex);
    return strKey;
}

void CDevStatus::_loadSampleGripperAttrFromDB()
{
    QVariant value;
    CSystemDB& db = CSystemDB::getInstance();
    QMutexLocker locker(&m_mutex);
    for (int i = CI_START; i < CI_MAX; ++i)
    {
        value = db.getIntValueFromKey(getSampleGripperAvailKey(i));
        if (value.isValid())
        {
            m_sampleGripper.setComponentAvailability(i, static_cast<CompAvrStatus>(value.toInt()));
        }

        value = db.getIntValueFromKey(getSampleGripperGrabKey(i));
        if (value.isValid())
        {
            m_sampleGripper.setComponentGrabStatus(i, static_cast<GrabStatus>(value.toInt()));
        }

        value = db.getIntValueFromKey(getSampleGripperItemKey(i));
        if (value.isValid())
        {
            m_sampleGripper.setItemType(i, value.toInt());
        }

        value = db.getIntValueFromKey(getSampleGripperPosKey(i));
        if (value.isValid())
        {
            m_sampleGripper.setSafePos(i, value.toString());
        }
    }
}

void CDevStatus::_loadGantryPumpAttrFromDB()
{
    QVariant value;
    CSystemDB& db = CSystemDB::getInstance();
    QMutexLocker locker(&m_mutex);
    // 龙门架泵
    for (int i = CI_START; i < CI_MAX; ++i)
    {
        value = db.getIntValueFromKey(getGantryPumpAvailKey(i));
        if (value.isValid())
        {
            m_gantryPump.setComponentAvailability(i, static_cast<CompAvrStatus>(value.toInt()));
        }

        value = db.getIntValueFromKey(getGantryPumpGrabKey(i));
        if (value.isValid())
        {
            m_gantryPump.setComponentGrabStatus(i, static_cast<GrabStatus>(value.toInt()));
        }

        value = db.getIntValueFromKey(getGantryPumpItemKey(i));
        if (value.isValid())
        {
            m_gantryPump.setItemType(i, value.toInt());
        }

        value = db.getStringValueFromKey(getGantryPumpPosKey(i));
        if (value.isValid())
        {
            m_gantryPump.setSafePos(i, value.toString());
        }
    }
}

void CDevStatus::_loadPCRGripperAttrFromDB()
{
    QVariant value;
    CSystemDB& db = CSystemDB::getInstance();
    QMutexLocker locker(&m_mutex);
    value = db.getIntValueFromKey(kPCRGripperAvailKey);
    if (value.isValid())
    {
        m_pcrGripper.avrStatus = static_cast<CompAvrStatus>(value.toInt());
    }
    value = db.getIntValueFromKey(kPCRGripperGrabKey);
    if (value.isValid())
    {
        m_pcrGripper.grabStatus = static_cast<GrabStatus>(value.toInt());
    }
    value = db.getIntValueFromKey(kPCRGripperGrabItemKey);
    if (value.isValid())
    {
        m_pcrGripper.uiGrabItemType =  value.toInt();
    }
    value = db.getIntValueFromKey(kPCRGripperSafePosKey);
    if (value.isValid())
    {
        m_pcrGripper.strSafePos =  value.toString();
    }
}

void CDevStatus::tstInit()
{
    setSampleGripperAvailability(0, CAS_Available);
    setSampleGripperAvailability(1, CAS_Available);
    setSampleGripperGrabStatus(0, GS_HasGrabbed);
    setSampleGripperGrabStatus(1, GS_HasGrabbed);
    setSampleGripperItemType(0, IT_SampleCap);
    setSampleGripperItemType(1, IT_SampleCap);
    setSampleGripperSafePos(0, "0");
    setSampleGripperSafePos(1, "0");

    setGantryPumpAvailability(0, CAS_Available);
    setGantryPumpAvailability(1, CAS_Available);
    setGantryPumpGrabStatus(0, GS_HasGrabbed);
    setGantryPumpGrabStatus(1, GS_HasGrabbed);
    setGantryPumpItemType(0, IT_PCRCap);
    setGantryPumpItemType(1, IT_PCRCap);
    setGantryPumpSafePos(0, "0&0&0&");
    setGantryPumpSafePos(1, "0&1&0&");

    setPcrGripperStatus(CAS_Available, GS_HasGrabbed, IT_PCRPlateCover, "0");

}

void CDevStatus::setUpdateFlag(bool bUpdate)
{
    m_bUpdate.store(bUpdate);
}

bool CDevStatus::getUpdateFlag()
{
    return m_bUpdate.load();
}

void CDevStatus::loadFromDB()
{
    // 样本抓手
    _loadSampleGripperAttrFromDB();
    //龙门架泵
    _loadGantryPumpAttrFromDB();
    // PCR抓手
    _loadPCRGripperAttrFromDB();

}

void CDevStatus::saveToDB()
{
    QMutexLocker locker(&m_mutex);
    CSystemDB& db = CSystemDB::getInstance();
    for(int i = CI_START; i<CI_MAX; i++)
    {
        //样本抓手+龙门架泵
        db.addKeyValue(getSampleGripperAvailKey(i), m_sampleGripper.getComponentAvailability(i));
        db.addKeyValue(getSampleGripperGrabKey(i), m_sampleGripper.getComponentGrabStatus(i));
        db.addKeyValue(getSampleGripperItemKey(i), m_sampleGripper.getItemType(i));
        db.addKeyValue(getSampleGripperPosKey(i), m_sampleGripper.getSafePos(i));

        db.addKeyValue(getGantryPumpAvailKey(i), m_gantryPump.getComponentAvailability(i));
        db.addKeyValue(getGantryPumpGrabKey(i), m_gantryPump.getComponentGrabStatus(i));
        db.addKeyValue(getGantryPumpItemKey(i), m_gantryPump.getItemType(i));
        db.addKeyValue(getGantryPumpPosKey(i), m_gantryPump.getSafePos(i));
    }
    // 龙门架泵和PCR抓手的持久化操作
    db.addKeyValue(kPCRGripperAvailKey, m_pcrGripper.avrStatus);
    db.addKeyValue(kPCRGripperGrabKey, m_pcrGripper.grabStatus);
    db.addKeyValue(kPCRGripperGrabItemKey, m_pcrGripper.uiGrabItemType);
    db.addKeyValue(kPCRGripperSafePosKey, m_pcrGripper.strSafePos);

}

void CDevStatus::_onPropertySet(const QString &strKey, const QVariant &value)
{
    QMutexLocker locker(&m_mutex);
    if(strKey.startsWith(kSampleGripperPrefix))
    {
        int iStartIndex = strKey.indexOf('_');
        int iLastIndex = strKey.lastIndexOf('_');
        int iIndex = strKey.right(1).toInt();
        if(iLastIndex<=iStartIndex)
        {
            QDFUN_LINE<<"invalid property key";
            return;
        }
        QString strAction = strKey.mid(iStartIndex+1, iLastIndex-iStartIndex-1); // 提取动作（Avail或Grab）
        if(strAction == "Avail")
        {
            m_sampleGripper.setComponentAvailability(iIndex, static_cast<CompAvrStatus>(value.toInt()));
        }
        else if(strAction == "Grab")
        {
            m_sampleGripper.setComponentGrabStatus(iIndex, static_cast<GrabStatus>(value.toInt()));
        }
        else if(strAction == "Item")
        {
            m_sampleGripper.setItemType(iIndex, value.toInt());
        }
        else if(strAction == "Pos")
        {
            m_sampleGripper.setSafePos(iIndex, value.toString());
        }
        saveSinglePropertyToDB(strKey, value); // 单独持久化此属性
    }
    else if(strKey.startsWith(kGantryPumpPrefix))
    {
        int iStartIndex = strKey.indexOf('_');
        int iLastIndex = strKey.lastIndexOf('_');
        int iIndex = strKey.right(1).toInt();
        if(iLastIndex<=iStartIndex)
        {
            QDFUN_LINE<<"invalid property key";
            return;
        }
        QString strAction = strKey.mid(iStartIndex+1, iLastIndex-iStartIndex-1);
        if(strAction == "Avail")
        {
            m_gantryPump.setComponentAvailability(iIndex, static_cast<CompAvrStatus>(value.toInt()));
        }
        else if(strAction == "Grab")
        {
            m_gantryPump.setComponentGrabStatus(iIndex, static_cast<GrabStatus>(value.toInt()));
        }
        else if(strAction == "Item")
        {
            m_gantryPump.setItemType(iIndex, value.toInt());
        }
        else if(strAction == "Pos")
        {
            m_gantryPump.setSafePos(iIndex, value.toString());
        }
        saveSinglePropertyToDB(strKey, value); // 单独持久化此属性
    }
    else if(strKey.startsWith(kPCRGripperPrefix))
    {
        if(strKey == kPCRGripperAvailKey)
        {
            m_pcrGripper.avrStatus = static_cast<CompAvrStatus>(value.toInt());
        }
        else if(strKey == kPCRGripperGrabKey)
        {
            m_pcrGripper.grabStatus = static_cast<GrabStatus>(value.toInt());
        }
        else if(strKey == kPCRGripperGrabItemKey)
        {
            m_pcrGripper.uiGrabItemType = value.toInt();
        }
        else if(strKey == kPCRGripperSafePosKey)
        {
            m_pcrGripper.strSafePos = value.toString();
        }
        saveSinglePropertyToDB(strKey, value); // 单独持久化此属性
    }
}

void CDevStatus::saveSinglePropertyToDB(const QString &strKey, const QVariant &value)
{
    CSystemDB& db = CSystemDB::getInstance();
    db.addKeyValue(strKey, value);
}