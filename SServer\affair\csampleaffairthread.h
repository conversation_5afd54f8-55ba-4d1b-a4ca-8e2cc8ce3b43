#ifndef CSAMPLEAFFAIRTHREAD_H
#define CSAMPLEAFFAIRTHREAD_H

#include <QObject>
#include "caffairobject.h"

class CSampleAffairThread : public CAffairObject
{
    Q_OBJECT
public:
    explicit CSampleAffairThread(quint16 uiUnit, QObject *parent = nullptr);
    ~CSampleAffairThread();

protected:
    virtual void run();
    virtual void _HandleReceiveList();
    virtual void initUnitData();
};

#endif // CSAMPLEAFFAIRTHREAD_H
