/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description: 全局公共资源
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CGLOBALCONFIG_H
#define CGLOBALCONFIG_H

#include <QObject>
#include <QJsonObject>
#include <QMap>
#if Q_OS_QML
#include <QQmlEngine>
#endif
#include <QDateTime>
#include <QVariant>
#include "publicconfig.h"
class CGlobalConfig : public QObject
{
    Q_OBJECT
public:
    explicit CGlobalConfig(QObject *parent = nullptr);

signals:
    void sigRunLog(QString strLog, RunLogLevel runLogLevel);
    void sigTextFiledInfo(int x,int y,int height);
    void sigLogStandardOut(bool bOut);
    void sigUpdateComplexIDNameList(QStringList strComplexIDNameList);
public:
#if Q_OS_QML
    static QObject* qmlSingletonInstance( QQmlEngine* engine, QJSEngine* scriptEngine )
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static CGlobalConfig &getInstance();

    Q_INVOKABLE QString getCurrentDateTimeMin();
    Q_INVOKABLE QString getCurrentDateTime();
    Q_INVOKABLE bool hasUDisk();
    Q_INVOKABLE QString getUDiskPath();
    Q_INVOKABLE bool hasUDiskFile(QString strUDiskFile);
    Q_INVOKABLE QString getTimingInfoDBName();

    Q_INVOKABLE void setSystemTime(QDateTime qTime);
    Q_INVOKABLE QList<QVariant> getSystemTime();
    Q_INVOKABLE QString getRegisterItemValue(QString strIhold,
                                             QString strIrun, QString strIholddelay);
    Q_INVOKABLE QVariantList getRegisterItemValueFromData(QString strValue);

    Q_INVOKABLE QString getChopconfItemValue(QVariantList strValueList);
    Q_INVOKABLE QVariantList getChopconfItemValueFromData(QString strValue);

    Q_INVOKABLE void setRunning(bool bRunning);
    Q_INVOKABLE bool getRunning();
    Q_INVOKABLE void setMachineStatus(int eMachineStatus);
    Q_INVOKABLE int getMachineStatus();


    Q_INVOKABLE void setSoftUpdating(bool bSoftUpdating);
    Q_INVOKABLE bool getSoftUpdating();

    Q_INVOKABLE void setTextFiledInfo(int x,int y,int width);
    Q_INVOKABLE QString getPlainAesWord(QString strEncryptData);
    //

    QString GetResourcesDir();
    //日志配置路径
    QString GetLogJsonFilePath();
    //日志保存路径
    QString GetLogSaveDir();
    bool SetLogSaveDir(QString strDir);
    QStringList GetLogDirList();
    QString GetVersionPath();
    QString GetAutoVersionPath();
    QString GetMethodName(int eMethod);
    Q_INVOKABLE int getMethodID(QString strMethodName);
    Q_INVOKABLE int getCmdIDFromTimingComposeIndex(int iIndex);
    Q_INVOKABLE int getCmdIDFromComplexOrderIndex(int iIndex);
    Q_INVOKABLE QString getCmdIDFromProcessComplexOrderIndex(int iIndex);
    Q_INVOKABLE QString getRealTimingContent(QString strTiming);
    Q_INVOKABLE QString getRealComplexContent(QString strTiming);
    Q_INVOKABLE QString getRealProcessComplexContent(QString strTiming);

    void AddUnitItemIP(int iUnitItemID, QString strIP);
    QString GetUnitItemIP(int iUnitItemID);
    int GetUnitItemIDFromIP(QString strIP);

    int GetPayloadMaxLength();
    void SetPayloadMaxLength(int iMax);

    void recordRunLog(QString strLog, RunLogLevel eRunLogLevel = LEVEL_DEBUG);
    void initNetworkAddress();
    // win
    Q_INVOKABLE bool getIsWin();
    Q_INVOKABLE bool getIsReadAllMotor();
    Q_INVOKABLE void setIsReadAllMotor(bool bRead);
    Q_INVOKABLE int getUserPower();
    Q_INVOKABLE void setUserPower(int iPower);
    Q_INVOKABLE void setLogStandardOut(bool bOut);
    Q_INVOKABLE void setLanguageFlag(int iLanguage);
    Q_INVOKABLE int getLanguageFlag();
    Q_INVOKABLE void delayMSec(int iMSecTime);
    Q_INVOKABLE bool writeJsonMap(QString strParamsType,
                                  QString strKey, QString strValue);
    Q_INVOKABLE  QString readJsonMap(QString strParamsType,
                                     QString strKey);

    Q_INVOKABLE void shutdownSystem();

    Q_INVOKABLE void setUpdateOnePieceLen(int iLength);
    Q_INVOKABLE int getUpdateOnePieceLen();
    // ftp
    QString getFTPFilePath();

    // db
    Q_INVOKABLE QString getDBDir();
    Q_INVOKABLE QString getTimingInfoDBDir();
    Q_INVOKABLE QString getTimingDBDir();
    QString GetMotorInfoDBDir();
    QString GetHistoryDBDir();
    QString GetSystemDBDir();
    QString GetMotorDBDir();
    QString GetGraphDBDir();
    QString GetClientDBDir();
    QString getProjectDBDir();
    QString GetErrorCodeDBDir();
    QString GetKeyboardLibDir();
    Q_INVOKABLE QString getFaultCodeXlsxPath();
    Q_INVOKABLE QString getServerIPAddress();

    Q_INVOKABLE QStringList getMotorComposeIDNameListFromDB();
    int GetMotorComposeIndex(QString strIDName);
    QString GetMotorComposeIDFromListIndex(int index);
    int GetListIndexFromMotorComposeID(int index);
    Q_INVOKABLE void setInterferenceValue(qreal fValue);// B-G
    Q_INVOKABLE qreal getInterferenceValue();
    Q_INVOKABLE void setInterferenceValueGY(qreal fValue);// G-Y
    Q_INVOKABLE qreal getInterferenceValueGY();

    int GetTimingIndexByCmdID(int iCmdID);
    Q_INVOKABLE int getComplexIndexByCmdID(int iCmdID);
    int GetProcessComplexIndexByCmdID(QString iCmdIDName);

    Q_INVOKABLE void setMotorNumber(int iMotorNumber);
    int getMotorNumber(int iMotorBoardIndex);
    //chartData
    Q_INVOKABLE QString getTECChartDataDir();
    Q_INVOKABLE QString getContinuLightingChartDataDir();
    Q_INVOKABLE QString getSportsLightingDataDir();
public:
    Q_INVOKABLE QString getUDiskDir();
    Q_INVOKABLE QString getUDiskAppFileUpdatePath();
    Q_INVOKABLE QString getUDiskMiddleAppFileUpdatePath();
    Q_INVOKABLE QString getUDiskAlgorithmFileUpdatePath();
    Q_INVOKABLE QString getUDiskMyShFileUpdatePath();
    Q_INVOKABLE QString getUDiskSlaveFileUpdatePath();
    //iExtractPath     是否解压的路径   0 不是  1是读取解压出来的路径
    Q_INVOKABLE QString getUDiskPCRFileUpdatePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskTECUpgradePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskFLUpgradePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskRFIDUpgradePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskBoardFunctionUpdatePath(int iExtractPath=0);  //iAutoPath =0     是否自动升级的路径
    Q_INVOKABLE QString getUDiskBoardMotorUpdatePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskBoardPowerUpdatePath(int iExtractPath=0);
    Q_INVOKABLE QString getUDiskAutoUpgradeZipPath();
    Q_INVOKABLE QString getUpdateBoardVersionCheckPath();  //找升级比对版本的json路径

    Q_INVOKABLE bool isDirExist(QString strDirPath);
    Q_INVOKABLE bool isFileExist(QString strFilePath);
    Q_INVOKABLE bool copyFile(QString strOldPath,QString strNewPath);

    Q_INVOKABLE void setMotorMaxNumber(int iNumber);
    Q_INVOKABLE int getMotorMaxNumber();



    Q_INVOKABLE void updateProcessComplexIDMap();
    void printMessageInfo(QByteArray & qMessage, const QString &strTitle = "Message_");

    //獲取老化模式狀態
    bool getAgingMode();
    void  setAgingMode(bool bAging);
    //获取液面探测标记
    bool getLiquidDetect();
    bool getSampleGripperDetectFlag();
    bool getPumpDetectFlag();
    bool getPCRGripperDetectFlag();
    bool getSampleTubeExistFlag();
    bool getPCRTubeExistFlag();
    bool getMBoard1OptDetectFlag();
    bool getMBoard2OptDetectFlag();
    bool getMBoard3OptDetectFlag();
    bool getPCRAreaCapDetectFlag();
    bool getCentrifugeOptDetectFlag();
    bool getMagnetTubeDetectFlag();//磁套光耦检测标记
    bool getBlockedNeedleDetectFlag();//空吸/堵针检测
private:
    void _InitMethodIDNameMap();
    void _InitTimingIDMap();
    void _InitComplexIDMap();
    bool _WriteJsonMap(QString strFileName, QString strParamsType,
                       QString strKey, QString strValue);
    QString _ReadJsonMap(QString strFileName,QString strParamsType,
                         QString strKey);
private:
    // 定义一个结构体来保存ID和描述信息
    struct SCmdIDStruct {
        quint8 id;
        QString strDescription;
    };
    struct SDestinationIDStruct {
        quint8 id;
        QString strDescription;
    };


private:
    QString m_strUDiskDir;
    QMap<int, QString> m_qMachineIPInfoMap;
    int m_iMaxPayloadLength;// 一帧最大长度
    QList<int>  m_intMotorNumberList;
    QStringList m_strMBMethodNameList;
    QStringList m_strNormalMotorMethodNameList;
    QStringList m_strMotorMCHKMethodNameList;
    QStringList m_strMotorSCMPMethodNameList;
    QStringList m_strPCRMethodNameList;
    QStringList m_strFLLightMethodNameList;
    QStringList m_strHTMethodNameList;
    QStringList m_strUSMethodNameList;
    QStringList m_strVTMethodNameList;
    QStringList m_strClawNameList;
    QStringList m_strPipettingPumpNameList;
    QMap<int,QString> m_strMethodIDNameMap;
    QMap<int,int> m_strTimingID2MethodIDNameMap;
    QMap<int,int> m_strComplexID2MethodIDNameMap;
    QMap<int,QString> m_strProcessComplexID2MethodIDNameMap;// 流程调试中的复合指令ID

    QString m_strLogSaveDir;
    bool m_bIsWin;
    bool m_bSoftUpdating;
    int m_iConnectTypeApp;// 0: none, 1:serial, 2:network


    bool m_bSwithcLight;
    bool m_bSwithcFan;
    bool m_bRunning;
    EMachineStatus m_eMachineStatus;
    bool m_bUVLighting;
    int m_iDoorStatus;// 仓门状态，1：开盖子，0：关闭盖子, -1：异常
    //
    QString m_strPubKey;
    QString m_strPrivateKey;
    bool m_bIsReadAllMotor;// 一键读取电机参数
    int m_iUserPower;// 0：普通工厂，1：生产，2：研发
    int m_iLanguageFlag;// 语言
    QString m_strMachineSN;
    QString m_strFaultCodeFileName;
    QString m_strTimingInfoDBName;

    int m_iUpdateOnePieceLen;

    QStringList m_strBGRYList;

    qreal m_dMeltingSmooth;

    QStringList m_strMotorComposeIDNameList;
    qreal m_fInterferenceValue;// c串扰因子 G-B
    qreal m_fInterferenceValueGY;// c串扰因子G-Y

    int m_iMotroMaxNumber;// 电机最大数量
    // 帧信息
    char *m_pFramePos;
    quint8 m_iCmdID;
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iSourceID;
    quint8 m_iSync;
    quint8 m_iResult;
    quint16 m_iSeqNumber;
    QString m_strCmdIDName[256];// 最大0xFF
    QString m_strDestinationIDName[256];
    //老化模式
    bool m_bAging;//是否開啓老化模式，老化模式下耗材
};

#endif // CGLOBALCONFIG_H
