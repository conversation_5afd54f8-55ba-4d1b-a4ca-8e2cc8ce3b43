/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2024-04-22
  * Description:  虚拟业务类，用于通讯内循环测试
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CVIRTUALWINDOW_H
#define CVIRTUALWINDOW_H

#include "cwindowobject.h"

class CVirtualWindow : public CWindowObject
{
    Q_OBJECT
public:
    explicit CVirtualWindow(QObject *parent = nullptr);
    ~CVirtualWindow();
signals:
//    void SigTimeoutSendResult(QString strParam, int iTimeoutMS);
public slots:
    void SlotTimeoutSendResult(const QString &strParam, int iTimeoutMS);
//    void SlotTimeoutAction();
protected:
    void _HandleReceiveList() override ;
private:
//     QTimer *m_timer;
};

#endif // CVIRTUALWINDOW_H
