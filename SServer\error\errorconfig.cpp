#include "errorconfig.h"
#include <QMap>

QMap<quint8, QString> kDevSubModuleStrMap =
{
    { Mid_Sub_Upper_TCP_Comm, "0000"},      // 00 上位机 TCP通信
    { Mid_Sub_PCR_TCP_Comm, "0001"},        // 01 PCR TCP通信
    { Mid_Sub_CAN0_Comm,  "0002"},          // 02 CAN0 通信
    { Mid_Sub_CAN1_Comm,  "0003"},          // 03 CAN1 通信
    { Mid_Sub_Barcode_Serial_Comm, "0004"}, // 04 提取扫码串口通信
    { Mid_Sub_Cons, "0005"},                // 05 中位机耗材
    { Mid_Sub_RFID, "0006"},                // 06 中位机RFID
    { Mid_Sub_BarcodeScan, "0007"},         // 07 中位机卡条扫码
    { Mid_Sub_Db, "0008"},                  // 08 中位机数据库
    { Mid_Sub_Timing, "0009"},              // 09 中位机时序
    { Mid_Sub_Sys, "000A"},                 // 0A 中位机系统
    { Mid_Sub_PwrSupply, "000B"},           // 0B 中位机电源
    { Mid_Sub_Mem, "000C"},                 // 0C 中位机内存
    { Mid_Sub_Cons_Recycling1, "000D"},     // 0D 中位机耗材回收1
    { Mid_Sub_Cons_Recycling2, "000E"},     // 0E 中位机耗材回收2
    { Mid_Sub_Upgrade, "000F"},             // 0F 中位机升级
    { Mid_Sub_Params, "0010"},              // 10 中位机参数
    { Mid_Sub_FTP_Comm, "0011"},            // 11 FTP 通信
    { Mid_Sub_Sample_Barcode, "0012"},      // 12 样本扫码串口通信
    { Mid_Sub_Test_Process, "0013"}         // 13 测试流程
};


QMap<QString, DevSubModuleItem> kDevSubModuleItemMap = {
    //中位机板
    // 全局级别项
    {"0000", {IS_GlobalLevel, LM_GLOBAL}}, // 上位机 TCP通信
    {"0001", {IS_GlobalLevel, LM_GLOBAL}}, // PCR TCP通信
    {"0002", {IS_GlobalLevel, LM_GLOBAL}}, // CAN0 通信
    {"0003", {IS_GlobalLevel, LM_GLOBAL}}, // CAN1 通信
    {"0004", {IS_GlobalLevel, LM_GLOBAL}}, // 提取扫码串口通信
    {"0005", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机耗材
    {"0006", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机RFID
    {"0007", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机卡条扫码
    {"0008", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机数据库
    {"0009", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机时序
    {"000A", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机系统
    {"000B", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机电源
    {"000C", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机内存
    {"000F", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机升级
    {"0010", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机参数
    {"0011", {IS_GlobalLevel, LM_GLOBAL}}, // FTP 通信
    {"0012", {IS_GlobalLevel, LM_GLOBAL}}, // 样本扫码串口通信
    // 中位机模块级别项
    {"000D", {IS_ModuleLevel, LM_GANTRY}}, // 假设中位机耗材回收1属于提取模块
    {"000E", {IS_ModuleLevel, LM_PCR_CATCHER}}, // 假设中位机耗材回收2属于样本模块

    //电机板1
    // 新增映射项
    {"0200", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机CAN通信
    {"0201", {IS_ComponentLevel, CU_SAMPLE_RIGHT_CATCH}}, // 右抓手
    {"0202", {IS_ComponentLevel, CU_SAMPLE_LEFT_CATCH}}, // 左抓手
    {"0203", {IS_ComponentLevel, CU_SAMPLE_RIGHT_CATCH}}, // 开盖Z电机1
    {"0204", {IS_ComponentLevel, CU_SAMPLE_LEFT_CATCH}}, // 开盖Z电机2
    {"0205", {IS_ModuleLevel, LM_SAMPLE}}, // 开盖横轴电机
    {"0206", {IS_ModuleLevel, LM_SAMPLE}}, // 开盖夹紧电机
    {"0207", {IS_ModuleLevel, LM_SAMPLE}}, // 卡盒架移动电机
    {"0208", {IS_ModuleLevel, LM_EXTRACT}}, // 刺破电机
    {"0209", {IS_ModuleLevel, LM_EXTRACT}}, // 磁棒电机
    {"020A", {IS_ModuleLevel, LM_EXTRACT}}, // 磁套电机

    //电机板2
    {"0300", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机CAN通信
    {"0301", {IS_ComponentLevel, CU_GANTRY_RIGHT_PUMP}}, // 龙门架右移液泵
    {"0302", {IS_ComponentLevel, CU_GANTRY_LEFT_PUMP}}, // 龙门架左移液泵
    {"0303", {IS_ModuleLevel, LM_GANTRY}}, // 移液泵X轴电机
    {"0304", {IS_ModuleLevel, LM_GANTRY}}, // 移液泵Y轴电机
    {"0305", {IS_ComponentLevel, CU_GANTRY_RIGHT_PUMP}}, // 移液泵Z1轴电机
    {"0306", {IS_ComponentLevel, CU_GANTRY_LEFT_PUMP}}, // 移液泵Z2轴电机
    {"0307", {IS_ComponentLevel, CU_GANTRY_LEFT_PUMP}}, // 移液变距电机
    {"0308", {IS_ModuleLevel, LM_GANTRY}}, // 试剂冷藏盖开合电机
    {"0309", {IS_ModuleLevel, LM_EXTRACT}}, // 提取扫码电机
    //电机板3
    {"0400", {IS_GlobalLevel, LM_GLOBAL}}, // 中位机CAN通信
    {"0401", {IS_ModuleLevel, LM_PCR_CATCHER}}, // 抓手 —— 根据您的指示映射到LM_PCR_CATCHER
    {"0402", {IS_ModuleLevel, LM_TRANS_AND_MIX}}, // PCR管传输电机 —— LM_PCR 更正为 LM_TRANS_AND_MIX
    {"0403", {IS_ModuleLevel, LM_PCR_CATCHER}}, // 抓手X轴电机
    {"0404", {IS_ModuleLevel, LM_PCR_CATCHER}}, // 抓手Y轴电机
    {"0405", {IS_ModuleLevel, LM_PCR_CATCHER}}, // 抓手Z轴电机
    {"0406", {IS_ModuleLevel, LM_TRANS_AND_MIX}}, // 离心电机 —— 同样更正为 LM_TRANS_AND_MIX

};

// 创建一个Map来关联故障类型枚举与故障代码
QMap<ErrorID, QString> kErrorCodeIDMap = {
    //-------------------电机错误----------------
    {ErrorID::FT_ElectricShortCircuit_APhase, "0001"}, // A相供电短路告警
    {ErrorID::FT_ElectricShortCircuit_BPhase, "0002"}, // B相供电短路告警
    {ErrorID::FT_MotorOverTemperatureAlarm, "0003"}, // 电机过温异常
    {ErrorID::FT_MotorOverTemperatureWarning, "0004"}, // 电机过温预警
    {ErrorID::FT_MotorCmdTimeout, "0005"}, // 电机指令执行超时
    {ErrorID::FT_MotorStepLoss, "0006"}, // 电机运动丢步
    {ErrorID::FT_MotorStall, "0007"}, // 电机堵转
    {ErrorID::FT_DriveStatusError, "0008"}, // 驱动状态错误
    {ErrorID::FT_ResetOptocouplerError, "0009"}, // 复位光耦状态异常
    {ErrorID::FT_ResetOptocouplerNotFound, "0010"}, // 找不到复位光耦
    {ErrorID::FT_SPICommError, "0011"}, // SPI通信异常
    {ErrorID::FT_MotorMotionConflict, "0012"}, // 电机运动冲突
    {ErrorID::FT_EncoderTypeError, "0013"}, // 编码器类型配置错误
    {ErrorID::FT_EncoderPhaseError, "0014"}, // 编码器AB相序接反
    {ErrorID::FT_EncoderConfigError, "0015"}, // 编码器配置错误
    {ErrorID::FT_EncoderDeviationExceeded, "0016"}, // 编码器偏差超出设定阈值
    {ErrorID::FT_MotorParamLoadFailed, "0017"}, // 电机配置参数加载失败
    {ErrorID::FT_MotorMotionParamLoadFailed, "0018"}, // 电机运动参数加载失败
    {ErrorID::FT_MotorCompensationParamLoadFailed, "0019"}, // 电机补偿参数加载失败
    {ErrorID::FT_MotorParamSaveFailed, "0020"}, // 电机配置参数保存失败
    {ErrorID::FT_MotorMotionParamSaveFailed, "0021"}, // 电机运动参数保存失败
    {ErrorID::FT_MotorCompensationParamSaveFailed, "0022"}, // 电机补偿参数保存失败
    {ErrorID::FT_FPGACommError, "0023"}, // FPGA通信异常
    //----------------------通信类故障---------------
    {ErrorID::FT_Comm_OpenFail, "0101"}, // 通信打开失败
    {ErrorID::FT_Comm_PacketLengthAbnormal, "0102"}, // 通信包长度异常
    {ErrorID::FT_Comm_PacketCRCError, "0103"}, // 通信包CRC校验异常
    {ErrorID::FT_Comm_Interrupt, "0104"}, // 通信中断
    {ErrorID::FT_Comm_ConnectionTimeout, "0105"}, // 通信连接超时
    {ErrorID::FT_Comm_CacheFull, "0106"}, // 通信缓存满
    {ErrorID::FT_Comm_IDError, "0107"}, // 通信对象ID重复
    {ErrorID::FT_Comm_Resend, "0108"}, // 通信重发
    //----------------------耗材类错误--------------------
    {ErrorID::FT_Material_NotLoaded, "0201"}, // 未装载
    {ErrorID::FT_Material_Expired, "0202"}, // 已过期
    {ErrorID::FT_Material_LowStockAlert, "0203"}, // 余量低于报警线
    {ErrorID::FT_Material_StockOverflow, "0204"}, // 余量超过容量，请确认
    {ErrorID::FT_Material_NotInPosition, "0205"}, // 装载未到位
    {ErrorID::FT_Material_ReadError, "0206"}, // 耗材信息读取错误
    {ErrorID::FT_Material_ParseError, "0207"}, // 耗材信息解析错误
    {ErrorID::FT_Material_UpdateError, "0208"}, // 耗材信息更新错误
    {ErrorID::FT_Material_Empty, "0209"}, // 已用完
    {ErrorID::FT_Material_Loaded, "0210"}, // 装载
    {ErrorID::FT_Material_Unloaded, "0211"}, // 卸载
    //---------------------------RFID-------------------------------
    {ErrorID::FT_AntennaIDConversionError, "0601"}, // 转换天线ID异常
    {ErrorID::FT_RFIDDataParseError_TypeMismatch, "0602"}, // 解析RFID数据异常，传入耗材类型不符合
    {ErrorID::FT_RFIDDataParseError_GroupCountMismatch, "0603"}, // 解析RFID数据异常，组份数与组份项目名称个数不相等
    {ErrorID::FT_RFIDDataWriteError_TypeMismatch, "0604"}, // 写入RFID数据异常，传入耗材类型不符合
    {ErrorID::FT_RFIDDataReadTimeout, "0605"}, // 读取RFID数据超时
    {ErrorID::FT_RFIDDataReadError, "0606"}, // 读取RFID数据失败
    {ErrorID::FT_RFIDDataWriteTimeout, "0607"}, // 写入RFID数据超时
    {ErrorID::FT_RFIDDataWriteError, "0608"}, // 写入RFID数据失败
    {ErrorID::FT_RFIDInitializationError, "0609"}, // RFID初始化异常
    {ErrorID::FT_RFIDTagNotExist, "0610"}, // RFID标签不存在
    //------------------------------扫码---------------------------
    {ErrorID::FT_BarcodeScanner_TriggerModeError, "0701"}, // 扫码器启动指令触发模式错误
    {ErrorID::FT_BarcodeScanner_AutoSenseModeError, "0702"}, // 扫码器启动自动感应模式错误
    {ErrorID::FT_BarcodeScanner_InitFailed, "0703"}, // 扫码器未成功初始化，请联系客服
    {ErrorID::FT_BarcodeScanner_Disconnected, "0704"}, // 扫码器未连接，请联系客服
    {ErrorID::FT_BarcodeScanner_ScanFailed_FastInsertion, "0705"}, // 未扫到样本码，插入速度过快或与扫码器兼容的码制不符
    {ErrorID::FT_BarcodeScanner_RecognitionError, "0706"}, // 条码识别错误，请重新插入
    {ErrorID::FT_BarcodeScanner_CodeLengthMismatch, "0707"}, // 条形码位数与规定的不符
    {ErrorID::FT_BarcodeScanner_FormatMismatch, "0708"}, // 条形码格式与规定的不符
    {ErrorID::FT_BarcodeScanner_RackPositionError, "0709"}, // 样本架位置码或有无码出错
    //----------------------------数据库------------------------------
    {ErrorID::FT_Database_TableCreateError, "0801"}, // 数据表创建失败
    {ErrorID::FT_Database_TableDeleteError, "0802"}, // 删除表失败
    {ErrorID::FT_Database_InsertError, "0803"}, // 数据插入失败
    {ErrorID::FT_Database_QueryError, "0804"}, // 数据查询失败
    {ErrorID::FT_Database_DeleteError, "0805"}, // 数据删除失败
    {ErrorID::FT_Database_DataCorruption, "0806"}, // 数据损坏
    //---------------------------时序-------------------------------
    {ErrorID::FT_SystemSelfCheckFailed, "1101"}, // 整机自检失败
    {ErrorID::FT_SequenceCmdParamError, "1102"}, // 时序指令参数错误
    {ErrorID::FT_SequenceActionTimeout, "1103"}, // 时序动作超时
    {ErrorID::FT_SequenceActionFailed, "1104"}, // 时序动作失败
    {ErrorID::FT_InsufficientMaterial, "1105"}, // 耗材余量不足以满足本次检测
    {ErrorID::FT_MissingReagentKit, "1106"}, // 无配套项目试剂
    {ErrorID::FT_SequenceStartFailed, "1107"}, // 启动时序失败
    {ErrorID::FT_SequencePauseFailed, "1108"}, // 暂停时序失败
    {ErrorID::FT_SequenceStopFailed, "1109"}, // 停止时序失败
    {ErrorID::FT_SequenceResumeFailed, "1110"}, // 恢复时序失败
    //--------------------------------系统--------------------------
    {ErrorID::FT_WatchdogReset, "1301"}, // 看门狗重启
    {ErrorID::FT_OtherErrorReset, "1302"}, // 其他错误重启
    {ErrorID::FT_ClockNotSynchronized, "1303"}, // 时钟不同步
    {ErrorID::FT_DiskSpaceLow_WriteFailed, "1304"}, // 硬盘空间不足，写入失败
    //----------------------------电源------------------------------
    {ErrorID::FT_PowerLossDuringTest, "1401"}, // 测试过程中异常掉电
    //-----------------------------内存-----------------------------
    {ErrorID::FT_MemoryAllocFailed, "1501"}, // 内存不足，分配失败
    {ErrorID::FT_MemoryWriteFailed, "1502"}, // 内存空间不足，写入失败
    {ErrorID::FT_CommandQueueFull, "1503"}, // 指令容器满
    {ErrorID::FT_FLASHInitFailed, "1504"}, // FLASH初始化失败
    //------------------------------耗材回收----------------------------
    {ErrorID::FT_WasteBinFull, "1601"}, // 废物箱已满
    {ErrorID::FT_WasteLevelHighAlert, "1602"}, // 废物量高于报警线
    //------------------------------升级----------------------------
    {ErrorID::FT_UpgradeFileChecksumError, "2001"}, // 升级文件校验失败
    {ErrorID::FT_UpgradeFileNotExist, "2002"}, // 升级文件不存在
    {ErrorID::FT_UpgradeFileOpenFailed, "2003"}, // 升级文件无法打开
    {ErrorID::FT_VersionQueryFailed, "2004"}, // 版本查询失败
    {ErrorID::FT_VersionQueryTimeout, "2005"}, // 版本查询超时
    {ErrorID::FT_UpgradePackageIDError, "2006"}, // 升级包ID有误
    {ErrorID::FT_UpgradeFailed, "2007"}, // 升级失败
    //-------------------------------参数---------------------------
    {ErrorID::FT_ParameterRangeError, "2101"}, // 参数范围异常
    //-------------------------------测试流程---------------------------
    {ErrorID::FT_TestQueueFull, "3001"}, // 测试队列已满
    {ErrorID::FT_StartProcessFailed, "3002"}, // 启动测试流程失败
    {ErrorID::FT_AddBatchFailed, "3003"}, // 添加批次失败
    {ErrorID::FT_ParseBatchInfoFailed, "3004"}, // 解析批次信息失败
    {ErrorID::FT_CurBatchExist, "3005"}, // 当前批次已存在
    {ErrorID::FT_SampleSizeOut, "3006"}, // 样本量超出范围
    {ErrorID::FT_NoSample, "3007"}, // 未检测到样本
};


// 查询与strSubModuleID完全匹配的项
bool findExactSubModuleItem(const QString &strSubModuleID,DevSubModuleItem& devSubModuleItem)
{
    bool bExist = false;
    auto itor = kDevSubModuleItemMap.find(strSubModuleID);
    if(itor != kDevSubModuleItemMap.end())
    {
        devSubModuleItem = itor.value();
        bExist = true;
    }
    return bExist;
}

bool findSubModule(const quint8 &subModuleID,QString& strSubModule)
{
    bool bExist = false;
    auto itor = kDevSubModuleStrMap.find(subModuleID);
    if(itor != kDevSubModuleStrMap.end())
    {
        strSubModule = itor.value();
        bExist = true;
    }
    return bExist;
}

bool findErrorCodeID(const ErrorID &errorID,QString& strErrorID)
{
    bool bExist = false;
    auto itor = kErrorCodeIDMap.find(errorID);
    if(itor != kErrorCodeIDMap.end())
    {
        strErrorID = itor.value();
        bExist = true;
    }
    return bExist;
}

QString getErrorCode(MidMachineSubmodule subModule, ErrorID errorID)
{
    QString strErrorCode = "";
    QString strSubModuleStr = "";
    QString strErrorID = "";
    findSubModule(subModule, strSubModuleStr);
    findErrorCodeID(errorID, strErrorID);
    return strSubModuleStr+strErrorID;
}

