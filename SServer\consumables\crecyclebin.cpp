#include "crecyclebin.h"
#include<QDebug>
#include "error/cerrornotify.h"
#include "cglobalconfig.h"
#include "control/coperationunit.h"
#include "datacontrol/CSystemDB.h"

//可用的回收位置
const int kRecycleAvrPos = 3;
// 最大容量
const quint16 MAX_RECYCLE_TUBE_CAPACITY = 300;// tube最大回收量
const quint16 MAX_RECYCLE_TIP_CAPACITY = 200; // tip最大回收量

CRecycleBin* CRecycleBin::m_pFrontInstance = nullptr;
CRecycleBin* CRecycleBin::m_pBackInstance = nullptr;

static const QString strTubeCapKey = "Recycle_TubeCap";
static const QString strTipKey     = "Recycle_Tip";

CRecycleBin::CRecycleBin(int iID,QString strKey,quint16 uiMaxCapacity)
{
    m_iCapacity = uiMaxCapacity;
    QVariant vValue = CSystemDB::getInstance().getValueFromKey(strKey);
    if (!vValue.isNull())
    {
        quint16 uiCapacity = CSystemDB::getInstance().getIntValueFromKey(strKey);
        m_iCapacity = uiCapacity;
    }
  
    m_iRemaining = m_iCapacity;
    m_iNextOperationIndex = 0;
    m_iID = iID;
    m_bAvr = true;
    _updateConsumeInfo();
}

CRecycleBin::~CRecycleBin()
{
}

CRecycleBin &CRecycleBin::frontBin()
{
    if (!m_pFrontInstance)
    {
        m_pFrontInstance = new CRecycleBin(RBT_FRONT_BIN,strTubeCapKey,MAX_RECYCLE_TUBE_CAPACITY);
    }
    return *m_pFrontInstance;
}

CRecycleBin &CRecycleBin::backBin()
{
    if (!m_pBackInstance)
    {
        m_pBackInstance = new CRecycleBin(RBT_BACK_BIN,strTipKey,MAX_RECYCLE_TIP_CAPACITY);
    }
    return *m_pBackInstance;
}

int CRecycleBin::getNextOperationIndex() const
{
    return m_iNextOperationIndex;
}

void CRecycleBin::useNextOperationIndex()
{
    m_iNextOperationIndex = (m_iNextOperationIndex + 1) % kRecycleAvrPos; // 循环回0, 1, 2
    qDebug()<<"RecycleBin nextOpertionIndex:"<<m_iNextOperationIndex;
}

void CRecycleBin::reload()
{
    m_bAvr = true;
    m_iRemaining = MAX_RECYCLE_TIP_CAPACITY;
    if (RBT_BACK_BIN == m_iID)
    {
        m_iRemaining = MAX_RECYCLE_TUBE_CAPACITY;
    }
    _updateConsumeInfo();// 更新回收信息到上位机
    qDebug()<<"CRecycleBin::reload"<<m_iID<<m_iRemaining;
}

void CRecycleBin::setCapacity(int capacity)
{
    if (capacity > 0)
    {
        m_iCapacity = capacity;
        if (m_iRemaining > capacity)
        {
            m_iRemaining = capacity; // 确保余量不超过容量
        }
    }
}

int CRecycleBin::getRemaining() const
{
    return m_iRemaining;
}

bool CRecycleBin::use(int amount)
{
    qDebug()<<"CRecycleBin"<<m_iID<<" remain:"<<m_iRemaining<<" use:"<<amount;
    if(CGlobalConfig::getInstance().getAgingMode() && m_iRemaining<amount)
    {
        reload();
    }
    if (m_iRemaining >= amount)
    {
        m_iRemaining -= amount;
        useNextOperationIndex(); // 使用后更新操作位置
        _updateConsumeInfo();// 更新回收信息到上位机
        return true;
    }
    CErrorNotify::getInstance().addErrorInfoItem(static_cast<MidMachineSubmodule>( Mid_Sub_Cons_Recycling1+m_iID),
                                                 FT_WasteBinFull, QString("RecycleBin %1 is full.").arg(m_iID));
    return false; // 空间不足
}

void CRecycleBin::unload()
{
    m_bAvr = false;
    qDebug()<<"CRecycleBin::unload"<<m_iID;
}

bool CRecycleBin::getAvr()
{
    return  m_bAvr;
}

bool CRecycleBin::isResourceEnough(quint8 uiTotalUseSize)
{
    bool bRet = false;
    if (uiTotalUseSize < m_iRemaining)
    {
        bRet = true;
    }
    qDebug()<<"CRecycleBin::isResourceEnough "<<m_iID<<m_iRemaining<<uiTotalUseSize<<bRet;
    return bRet;
}

void CRecycleBin::_updateConsumeInfo()
{
    // 更新回收信息到上位机
    QString strUpdateParam = "";
    QString strParam = QString::number(Recycle_TubeCap);
    QString strCapacity = QString::number(MAX_RECYCLE_TUBE_CAPACITY);
    QString strKey = strTubeCapKey;
    if (RBT_FRONT_BIN == m_iID)
    {
        strParam = QString::number(Recycle_Tip);
        strCapacity = QString::number(MAX_RECYCLE_TIP_CAPACITY);
        strKey = strTipKey;
    }
    strUpdateParam = QString("%1,%2,%3,%4").arg("").arg(strParam).arg(m_iRemaining).arg(strCapacity);
    COperationUnit::getInstance().sendStringResult(Method_update_consumable, strUpdateParam, Machine_UpperHost);  
    //更新到数据库
    CSystemDB::getInstance().addKeyValue(strKey,m_iRemaining);
    qDebug()<<"CRecycleBin::_updateConsumeInfo"<<m_iID<<strKey<<strUpdateParam;
}