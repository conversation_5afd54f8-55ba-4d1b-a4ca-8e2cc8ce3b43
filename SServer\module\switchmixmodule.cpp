#include "switchmixmodule.h"

SwitchMixModule::SwitchMixModule(bool bUseThread, quint8 uiCatchType)
    : DeviceModule("SwitchMixModule", bUseThread), m_uiCatchType(uiCatchType)
{

} // 在构造函数中进行初始化

void SwitchMixModule::SetCatchType(quint8 uiCatchType)
{
    m_uiCatchType = uiCatchType; // 新增设置 m_uiCatchType 变量的函数
}


void SwitchMixModule::SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr)
{
    // 可以在这里添加子类特有的逻辑
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
}

void SwitchMixModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "SwitchMixModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
        switch (task.uiSubTaskID)
        {
        case SMTI_SWITCH_AND_MIX:
        {
            _AddSubTask(task.strParamStr, Action_CentrifugeTube);
            break;
        }
        default:
            break;
        }
        m_qWaitProcessSubTask.pop_front();
    }
}

void SwitchMixModule::SlotAddTask(const CmdTask& task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "SwitchMixModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}

void SwitchMixModule::_AddSwitchAndMixTask()
{
    CmdTask sTask;
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = "";
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = "";
    }
    DeviceModule::SlotAddTask(sTask);
}
