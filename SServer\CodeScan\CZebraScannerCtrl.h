#ifndef CZEBRASCANNERCTRL_H
#define CZEBRASCANNERCTRL_H

#include <QMutex>
#include"cextractscannerthread.h"
#include "error/errorconfig.h"

#define ZebraScannerCMD_SingleScanTriger   "04E40400FF14"
#define ZebraScannerCMD_CloseScan                "04E50400FF13"
#define ZebraScannerCMD_OpenAck                  "07C60400FF9F01FD90"
#define ZebraScannerCMD_FactorySet               "04C80400FF30"
#define ZebraScannerCMD_CloseWake               "07C60400FF8000FDB0"
#define ZebraScannerCMD_CloseAck                  "07c60400FF9F00FD91"
#define ZebraScannerCMD_DataPacket             "07c60400FFEE01FD41"
#define ZebraScannerCMD_FocusMode             "08c60400fff09202fcab"
#define ZebraScannerCMD_CloseBeef               "07C60400FF3800FDF8 "
#define ZebraScannerCMD_OpenBeef               "07c60400FF3801FDF7 "

enum ScanWorkMode
{
    Mode_ScanCode,
    Mode_SetParam,
    Mode_CloseScan,
};

enum ScanCodeStatus    //扫码信息打包发送（出现过分开多帧接收情况），包含头的
{
    Mode_ScanCode_Ready,  //扫码准备
    Mode_ScanCode_DecodeDataOK,  //全部接收完成
};


class CZebraScannerCtrl:public QObject
{
    Q_OBJECT
public:
    explicit CZebraScannerCtrl(QString strSerialName,QString m_strBandRate);
    ~CZebraScannerCtrl();
    void DoSingleScanCmd();
    int CloseSingleScanCmd();   //执行扫描指令  1：成功   0 超时   -1 fail
    int TranslateScanRs(); // 1：成功   0 超时   -1 fail
    QString GetLastScanRs();
signals:
    void sigWriteData(QByteArray qByteArray);
    void sigZebraScannerRs(QString dataRs,int iStatus);
    void sigWaitStatusFeedBack(int iNeedACKReply ,int _mode);
    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    void slotReceSerialData(QByteArray data);
    int slotWaitStatusFeedBack(int iNeedACKReply ,int _mode);

    /**
     * @brief InitScanCodeSetting
     * 初始扫码器的操作，因为这个扫码器，每次断电会回复默认状态。但是无法覆盖如果运作过程中，人为拨去扫码器供电，再接上而中位机没断电情况
     */
    void InitScanCodeSetting();

private:
    int _doSendCmd(QString strMsg,int iNeedACKReply =0,int iMaxTime =10000);//执行扫描指令  1：成功   0 超时   -1 fail    关闭ACK后，之后发送的指令将不再回复

private:
    CExtractScannerThread  *pExtractScannerThread;
    QThread *m_pThreadForInit;
    QByteArray m_readBuf;
    QMutex recvMutex;
    ScanWorkMode m_ScanWorkMode;
    ScanCodeStatus m_ScanCodeStatus;
    bool m_bInitOk;
    QString strLastScanRs;   //最后的扫码结果
};
#endif // CZEBRASCANNERCTRL_H
