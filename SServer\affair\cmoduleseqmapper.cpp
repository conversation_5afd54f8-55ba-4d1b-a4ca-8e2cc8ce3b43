#include "cmoduleseqmapper.h"


void CModuleSeqMapper::_init()
{
    m_seqMap[LM_GLOBAL] = QVector<quint8>({
                                                 SEST_WAIT_SAMPLE_CATCH ,
                                                 SEST_WAIT_OPEN_SAMPLE_CAP,
                                                 SEST_WAIT_CLOSE_SAMPLE_CAP,
                                                 SEST_WAIT_SAMPLE_BACK_HOME,
                                                 SEST_WAIT_SAMPLING,
                                                 SEST_WAIT_SPIT_SAMPLING,
                                                 SEST_WAIT_TRANS_CLEVAGE,
                                                 SEST_WAIT_EXTRACT,
                                                 SEST_WAIT_SUB_PACK_REAGENT,
                                                 SEST_WAIT_TRANS_PURIFY,
                                                 SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE,
                                                 SEST_WAIT_PCR_MIX,
                                                 SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA,
                                                 SEST_WAIT_CLOSE_PCR,
                                                 SEST_WAIT_PCR_AMPLIFY,
                                                 SEST_WAIT_ABANDON});

    m_seqMap[LM_SAMPLE] = QVector<quint8>({
                                                 SEST_WAIT_SAMPLE_CATCH,
                                                 SEST_WAIT_OPEN_SAMPLE_CAP,
                                                 SEST_WAIT_CLOSE_SAMPLE_CAP,
                                                 SEST_WAIT_SAMPLE_BACK_HOME});

    m_seqMap[LM_EXTRACT] = QVector<quint8>({SEST_WAIT_EXTRACT});

    m_seqMap[LM_GANTRY] = QVector<quint8>({
                                                 SEST_WAIT_SAMPLING,
                                                 SEST_WAIT_SPIT_SAMPLING,
                                                 SEST_WAIT_TRANS_CLEVAGE,
                                                 SEST_WAIT_SUB_PACK_REAGENT,
                                                 SEST_WAIT_TRANS_PURIFY,
                                                 SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE});

    m_seqMap[LM_TRANS_AND_MIX] = QVector<quint8>({SEST_WAIT_PCR_MIX});

    m_seqMap[LM_PCR_CATCHER] = QVector<quint8>({
                                                      SEST_WAIT_PCR_MIX,
                                                      SEST_WAIT_TRANS_TO_PCR_AMPLIFY_AREA,
                                                      SEST_WAIT_CLOSE_PCR,
                                                      SEST_WAIT_ABANDON});
    m_seqMap[LM_PCR] = QVector<quint8>({SEST_WAIT_PCR_AMPLIFY});

}

CModuleSeqMapper::CModuleSeqMapper() {
    // 初始化逻辑模块和时序状态的映射
    _init();
}

CModuleSeqMapper& CModuleSeqMapper::getInstance() {
    static CModuleSeqMapper instance; // 单例实例
    return instance;
}

QVector<quint8> CModuleSeqMapper::getSequences(const quint8 uiLogicalModule) const {
    auto it = m_seqMap.find(uiLogicalModule);
    if (it != m_seqMap.end()) {
        return it.value();
    } else {
        return QVector<quint8>(); // 如果没有找到，返回空向量
    }
}
