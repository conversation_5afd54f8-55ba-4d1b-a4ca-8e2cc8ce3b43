#ifndef CEXTRACTSCANNERTHREAD_H
#define CEXTRACTSCANNERTHREAD_H

#include <QObject>
#include <QThread>
#include <QSerialPort>
class CExtractScannerThread : public QObject
{
    Q_OBJECT
public:
    explicit CExtractScannerThread(QString strSerialName,
                                   QString m_strBandRate, QObject *parent = nullptr,QString strScannerName="");
        ~CExtractScannerThread();
     bool GetIsOpenSerialPort();
signals:
    void sigReciveSeirlaData(QByteArray qByteArray);

public slots:
    void sendFrameData(const QByteArray &qSendMsgAarry);

    void slotSendMessageNoList(QByteArray qSendMsgAarry);//
private slots:
    void _slotReadyRead();
private:
    void _InitPort(QString strComName,QString strBandRate);

private:
    QThread *m_pThread;
    // 串口
    QString m_strSerialName;
    QSerialPort *m_pSerialPort;
    bool m_bOpenSerialPort;
    QString m_strBandRate;
    QByteArray m_qReciveMessageByteArray;
    // 解析
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    char *m_pFramePos;
    int m_iReadSeqNumber;
    QByteArray m_qPayloadByteArray;
    QString m_qPayloadString;
    QString m_strPayloadString;
    quint16 m_iReadPayloadLength;
    quint32 m_iReciveMachineID;
    QString m_strScannerName;
};

#endif // CEXTRACTSCANNERTHREAD_H
