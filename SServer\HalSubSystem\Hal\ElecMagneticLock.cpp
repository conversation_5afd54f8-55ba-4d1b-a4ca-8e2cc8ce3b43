#include<qdebug.h>
#include<QTime>
#include <QThread>
#include <QTimer> 
#include"ElecMagneticLock.h"
#include "control/coperationunit.h"

ElecMagneticLock::ElecMagneticLock(QObject *parent) : QObject(parent)
{

}

void ElecMagneticLock::SetRFIDElecMagneticLock(ElecMagneticLock::EnumLockState state, quint16 uiLockType, quint16 uiIndex,EnumMachineID machineID)
{
    int iMethod = EnumMethod_FeatMngBoard::Method_FeatMngBoard_Lock;
    switch (state)
    {
    case Lock:
        uiIndex = ConvertRFIDConsumableType(uiIndex); 
        iMethod = EnumMethod_FeatMngBoard::Method_FeatMngBoard_Lock;
        break;
    case Unlock:
        uiIndex = ConvertRFIDConsumableType(uiIndex); 
        iMethod = EnumMethod_FeatMngBoard::Method_FeatMngBoard_Unlock;
        break; 
    case MotorLock:
        iMethod = EnumMethod_Motor01::Method_MotorBoard_Lock;
        break;
    case MotorUnlock:
        iMethod = EnumMethod_Motor01::Method_MotorBoard_Unlock;
        break;            
    default:
        break;
    }
    QString strInputParam = QString("%1,%2").arg(uiLockType).arg(uiIndex);
    COperationUnit::getInstance().sendStringData(iMethod,strInputParam,machineID);// 电磁锁都属于功能管理板
    qDebug() << "ElecMagneticLock::SetElecMagneticLock" 
             << "state" << state << "iMethod" << iMethod << "strInputParam" << strInputParam << "machineID" << machineID;
}

quint16 ElecMagneticLock::ConvertRFIDConsumableType(quint16 uiRFIDConsumableType)
{
    quint16 uiRetValue = 0;
    switch (uiRFIDConsumableType)
    {
    case RFID_ConsumableType_Reagent1:
        uiRetValue = Strip1;
        break;
    case RFID_ConsumableType_Reagent2:
        uiRetValue = Strip2;
        break;  
    case RFID_ConsumableType_Reagent3:
        uiRetValue = Strip3;    
        break;
    case RFID_ConsumableType_Reagent4:
        uiRetValue = Strip4;    
        break;
    case RFID_ConsumableType_Tip1:
        uiRetValue = Tip1;  
        break;
    case RFID_ConsumableType_Tip2:
        uiRetValue = Tip2;  
        break;  
    case RFID_ConsumableType_TubeCap1:
        uiRetValue = Tube1;  
        break;
    case RFID_ConsumableType_TubeCap2:
        uiRetValue = Tube2;  
        break;                    
    default:
        break;
    }
    return uiRetValue;
}

void ElecMagneticLock::SetAllElecMagneticUnlock()
{  
    SetRFIDElecMagneticLock(ElecMagneticLock::Unlock,EnumLockType::All,EnumLockType::All,Machine_Function_manager_Ctrl);
    SetRFIDElecMagneticLock(ElecMagneticLock::MotorUnlock,EnumMotorLockType::MotorAll,EnumMotorLockType::MotorAll,Machine_Motor_1);
    SetRFIDElecMagneticLock(ElecMagneticLock::MotorUnlock,EnumMotorLockType::MotorAll,EnumMotorLockType::MotorAll,Machine_Motor_4);
    qDebug()<<"SetAllElecMagneticUnlock";    
}

void ElecMagneticLock::SetAllElecMagneticLock()
{
    SetRFIDElecMagneticLock(ElecMagneticLock::Lock,EnumLockType::All,EnumLockType::All,Machine_Function_manager_Ctrl);
    SetRFIDElecMagneticLock(ElecMagneticLock::MotorLock,EnumMotorLockType::MotorAll,EnumMotorLockType::MotorAll,Machine_Motor_1);
    SetRFIDElecMagneticLock(ElecMagneticLock::MotorLock,EnumMotorLockType::MotorAll,EnumMotorLockType::MotorAll,Machine_Motor_4);
    qDebug()<<"SetAllElecMagneticLock";   
}
