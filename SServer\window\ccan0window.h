/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description:  CAN0业务类
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CCAN0WINDOW_H
#define CCAN0WINDOW_H

#include "cwindowobject.h"
class CCan0Window : public CWindowObject
{
    Q_OBJECT
public:
    explicit CCan0Window(QObject *parent = nullptr);
    ~CCan0Window();
signals:

public slots:

protected:
    void _HandleReceiveList() override ;
};

#endif // CCAN0WINDOW_H
