#include "extractmodule.h"
#include "control/coperationunit.h"

ExtractModule::ExtractModule(bool bUseThread)
    : DeviceModule("ExtractModule", bUseThread)
{
    ScanCodeInfoList.clear();
} // 在构造函数中进行初始化


void ExtractModule::SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr)
{
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
    // 可以在这里添加子类特有的逻辑
//    qDebug() << "ExtractModule adding task with specific logic (uiSubTaskID: " << uiSubTaskID << ", strCommandStr: " << strCommandStr << ", strParamStr: " << strParamStr << ")";

}

void ExtractModule::AddScanCodeStripIdx(QList<int> StripIdxList)
{
    ScanCodeInfoList.clear();
    ExtractScanCodeInfo tempInfo;
    tempInfo.iStatus =  Status_NoScan;
    tempInfo.strCodeMsg="";
    for(int i=0;i<StripIdxList.size();i++)
    {
        if(StripIdxList[i]==1)   //需要扫码的
        {
            tempInfo.iIdx=i;
            ScanCodeInfoList.push_back(tempInfo);
        }
    }
}

void ExtractModule::ResetScanCodeStrip()
{
    ScanCodeInfoList.clear();
}

void ExtractModule::SetExtractScanStatus(int iStripIdxList,ExtractScanCodeStatus iStatus) //设置扫描状态
{
    for(int i=0;i<ScanCodeInfoList.size();i++)
    {
        if(ScanCodeInfoList[i].iIdx ==iStripIdxList)
        {
            ScanCodeInfoList[i].iStatus = iStatus;
            break;
        }
    }
}

 //格式（同一个试剂条“，”，不同的“|”）：试剂条序号，扫码状态（2：Status_ScanFinish   3：Status_ScanFail），条码信息|
void ExtractModule::PackageScanStripScanRsInfo(QString &strInfo)//打包扫描条码信息
{
    strInfo.clear();
    for(int i=0;i<ScanCodeInfoList.size();i++)
    {
        strInfo =strInfo+QString::number(ScanCodeInfoList[i].iIdx)+","
                +QString::number(ScanCodeInfoList[i].iStatus)+","
                +ScanCodeInfoList[i].strCodeMsg;
        if(i!=ScanCodeInfoList.size()-1)
        {
            strInfo=strInfo+"|";
        }
    }

}

int ExtractModule::JudgeScanRs(QList<QString> listMsg,QVector<long> &vecDiffCnt)
{
    int iNotSameCnt =0;
    for(int i=0;i<listMsg.size();i++)
    {
        if(listMsg[i]!=ScanCodeInfoList[i].strCodeMsg)
        {
            iNotSameCnt++;
            if(i<vecDiffCnt.size())
            {
                vecDiffCnt[i]++;
            }
        }
    }
    return iNotSameCnt;
}

void ExtractModule::FindNextScanStripIdxList(int &iStripIdxList)   //iStripIdxList =-1 已经全部完成
{
    iStripIdxList = -1;
    for(int i=0;i<ScanCodeInfoList.size();i++)
    {
        if(ScanCodeInfoList[i].iStatus == Status_NoScan)
        {
            iStripIdxList = ScanCodeInfoList[i].iIdx;
            break;
        }
    }
}

void ExtractModule::UpdataScanCodeInfo(QString strCodeData,ExtractScanCodeStatus iStatus) //查找第一个status = Scanning
{
    for(int i=0;i<ScanCodeInfoList.size();i++)
    {
        if(ScanCodeInfoList[i].iStatus == Status_Scanning)
        {
            ScanCodeInfoList[i].strCodeMsg = strCodeData;
            ScanCodeInfoList[i].iStatus = iStatus;
            break;
        }
    }
}

void ExtractModule::SlotAddTask(const CmdTask& task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "ExtractModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}
void ExtractModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "ExtractModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
        switch (task.uiSubTaskID)
         {
         case ETI_PUNCH:
         {
             _AddSubTask("", Action_Punch);
             break;
         }     
         case ETI_EXTRACT:
         {
            _AddSubTask("", Action_Extract);
             break;
         }
         case ETI_EXTRACT_START:
         {
            _AddSubTask("", Action_Extract_Start);
             break;
         }
         case ETI_EXTRACT_RUN:
         {
            _AddSubTask(task.strParamStr, Action_Extract_Run);
             break;
         }
         case ETI_EXTRACT_END:
         {
            _AddSubTask(task.strParamStr, Action_Extract_End);
             break;
         }
         case ETI_EJECT_MAG_TUBE:
         {
             _AddSubTask(task.strParamStr, Action_Board1EjectMagTube);
             break;
         }
         default:
             break;
         }
        m_qWaitProcessSubTask.pop_front();
    }
}


void ExtractModule::SlotInitialize()
{
    DeviceModule::SlotInitialize();
//    connect(this, &ExtractModule::SignalAddSendExtractSeqTask, this, &ExtractModule::SlotAddSendExtractSeqTask);
}

void ExtractModule::SetDebugStatus(bool bRun)
{
    m_bDebug = bRun;
    qDebug() << "SetDebugStatus:" << bRun;
}
     
void ExtractModule::DebugActionCmdReply(quint16 uiComplexID, quint16 uiResult)
{
    if(!m_bDebug)
    {
        return;
    }

    switch (uiComplexID)
    {
    case Action_Extract_Start:
    case Action_Extract_Run:
        SetState(false);
        break;
    case Action_Extract_End:
        SetState(false);
        m_bDebug = false;
        break;
    default:
        break;
    }
    qDebug() << "DebugActionCmdReply:" << uiComplexID << uiResult;    
}

void ExtractModule::SetExtractClevageHoleStatus(quint16 uiHoleStatus)
{
    const QString strHeatHole = "1";
    const QString strHeatNumber = "60";
    switch (uiHoleStatus)
    {
    case Method_extract_heater_start:
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_StartHeating, strHeatHole+","+strHeatNumber, Machine_Function_manager_Ctrl);
        break;
    case Method_extract_heater_stop:
        COperationUnit::getInstance().sendStringData(Method_FeatMngBoard_StopHeating, strHeatHole, Machine_Function_manager_Ctrl);
        break;    
    default:
        break;
    }
    qDebug() << "SetExtractClevageHoleStatus:" << uiHoleStatus;
}