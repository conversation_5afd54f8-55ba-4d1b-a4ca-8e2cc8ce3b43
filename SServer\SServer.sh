#!/bin/bash
SCRIPT_DIR=$(cd $(dirname $0); pwd)
VERSION="V0.5.1.1"
OUTPUT_FILE="${SCRIPT_DIR}/SServer.pri"


# 获取短提交ID（后6位）
GIT_COMMIT=$(git rev-parse HEAD | tail -c 5)  # 或使用 ${GIT_COMMIT: -6}
# 获取提交日期（格式：YYYYMMDD）
GIT_DATE=$(git show -s --format=%cd --date=short HEAD | sed 's/-//g')

{
echo "DEFINES += APP_VERSION=\\\\\\\"${VERSION}\\\\\\\""
echo "DEFINES += APP_COMMIT_ID=\\\\\\\"${GIT_DATE}_${GIT_COMMIT}\\\\\\\""
echo "DEFINES += APP_BRANCH_NAME=\\\\\\\"$(git symbolic-ref --short -q HEAD)\\\\\\\""
echo "DEFINES += APP_FULL_VERSION=\\\\\\\"${VERSION}.${GIT_COMMIT}\\\\(${GIT_DATE}\\\\).$(git symbolic-ref --short -q HEAD)\\\\\\\""
} > SServer.pri

if [ ! -f "$OUTPUT_FILE" ]; then
    echo "error invalid directory $OUTPUT_FILE" >&2
    exit 1
else
    echo "success output $OUTPUT_FILE"
fi

# 執行構建流程
#qmake SServer.pro && make
