/*****************************************************
  * Copyright: 万孚生物
  * Author: mflin
  * Date: 2024-2-26
  * Description:  樣本管理
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef SAMPLECONTROL_H
#define SAMPLECONTROL_H
#include <QtGlobal>
#include <QString>
#include <QVector>
#include <QMap>
#include <QQueue>
#include <QMutex>
#include <QSet>
#include "affair/cmoduleseqmapper.h"
#include "SampleAmplificate/SampleAmplificate.h"

#define SAMPLE_MAX_SIZE 16
#define BATCH_MAX_SIZE 3
#define SAMPLE_ROW_SIZE 8
#define SAMPLE_COLUMN_SIZE 2

struct ProjectInfo
{
    QString strName;//项目名称
    QString strProjID;//项目ID
    QString strCompName;//組分名稱
    quint8 uiCompNum;//組分數量
    QString strTecName;//TEC时序

    QString printInfo() const
    {
        QString info;
        info += "strName:" + strName + " ";
        info += "strProjID:" + strProjID + " ";
        info += "strCompName:" + strCompName + " ";
        info += "uiCompNum:" + QString::number(uiCompNum) + " ";
        info += "strTecName:" + strTecName;
        return info;
    }
};

struct PosInfo
{
    quint8 uiRowIndex;
    quint8 uiColumnIndex;
    quint8 uiAreaIndex;
};

struct TestProcessData
{
    QVector<PosInfo> qTubePosVect;//測試所使用的PCR管位置
};

struct SampleInfo
{
    quint16 uiRowIndex;//样本所在行信息
    quint16 uiColumnIndex;//样本所在列信息
    bool bExist;
    bool bIsCaped;//是否带盖
    quint8 uiStripIndex;//后续配套的提取卡条位置索引
    QString strBatchNo;//批次号
    QString strSampleNo;//样本号
    QVector<ProjectInfo> qProjInfoVect;//该样本测试项目信息
    TestProcessData tpData;//測試過程數據
    bool bIsStandard;//是否为内标
    QString strStandardProject;//内标项目
    bool bIsStandardAdd;//是否已添加内标

    QString printInfo() const
    {
        QString info;
        info += "uiRowIndex:" + QString::number(uiRowIndex) + " ";
        info += "uiColumnIndex:" + QString::number(uiColumnIndex) + " ";
        info += "bExist:" + QString::number(bExist) + " ";
        info += "bIsCaped:" + QString::number(bIsCaped) + " ";
        info += "uiStripIndex:" + QString::number(uiStripIndex) + " ";
        info += "strBatchNo:" + strBatchNo + " ";
        info += "strSampleNo:" + strSampleNo + " ";
        // info += "bIsStandard:" + QString::number(bIsStandard) + " ";
        // info += "strStandardProject:" + strStandardProject + " ";
        // info += "bIsStandardAdd:" + QString::number(bIsStandardAdd);

        // 输出 qProjInfoVect 信息
        info += "qProjInfoVect:[";
        for (const auto& projInfo : qProjInfoVect) {
            info += projInfo.printInfo() + ";";
        }
        info.chop(1); // 移除最后一个多余的分号
        info += "]";

        return info;
    }
};

struct BatchInfo
{
    QString strBatchNo;
    SampleInfo qSampleInfos[SAMPLE_ROW_SIZE][SAMPLE_COLUMN_SIZE];
    QString strExtract;//提取时序名称
    qint8 iCurOpRowIndex;//当前操作样本行索引
    qint8 iCurOpColumnIndex;//当前操作样本列索引
    bool bExist;
    qint8 iSampleSize;//当前批次样本数量
    qint8 iStandardSampleSize;//当前批次样本数量
};

struct SystemBuildInfo
{
    quint8 uiStripIndex;//提取卡條位置
    quint8 uiBatchIndex;//批次索引
    quint8 uiSampleIndex;//樣本索引
    quint8 uiProjIndex;//所屬樣本的項目索引
    quint8 uiAmplifyCompIndex;//擴增項目的組分索引
    PosInfo reagentPos;//試劑所在位置
    PosInfo tubeCapPos;//過程中所用PCR管帽位置
    PosInfo tubePos;//PCR管所在PCR管耗材區位置
    PosInfo pcrPos;//PCR管所在PCR區域擴增位置
    PosInfo tipPos;//用于转移提纯液，试剂，石蜡油的Tip位置
    PosInfo pcrAreaPos;//所在PCR區域位置
    QString strProjID;//項目ID信息
    QString strSampleID;//项目ID
    QString strResult;//結果數據
    QString strTecName;//TEC 时序
    bool bNeedOpenCap;//是否需要打开PCR区域盖子
    bool bNeedCloseCap;//是否需要关闭PCR区域盖子
    bool bLastOneInCurTecBatch;//是否为该批次特定TEC时序的最后一个操作元素,是的话会需要关闭pcr区域盖子
    QString strBatchNo;//批次号
    quint8 uiHoleIndex;//孔位索引(0-63)

    QString printInfo() const
    {
        QString info;
        info += "strBatchNo:" + strBatchNo + " ";
        info += "strProjID:" + strProjID + " ";
        info += "strSampleID:" + strSampleID + " ";
        info += "uiHoleIndex:" + QString::number(uiHoleIndex)+ " ";
        info += "uiAmplifyCompIndex:" + QString::number(uiAmplifyCompIndex) + " ";
        info += "uiStripIndex:" + QString::number(uiStripIndex) + " ";
        info += "uiBatchIndex:" + QString::number(uiBatchIndex) + " ";
        info += "uiSampleIndex:" + QString::number(uiSampleIndex) + "," + QString::number(uiSampleIndex / SAMPLE_COLUMN_SIZE) + "," + QString::number(uiSampleIndex % SAMPLE_COLUMN_SIZE) + " ";
        info += "uiProjIndex:" + QString::number(uiProjIndex) + " ";
        info += "reagentPos:" + QString::number(reagentPos.uiRowIndex) + "," + QString::number(reagentPos.uiColumnIndex) + "," + QString::number(reagentPos.uiAreaIndex) + " ";
        info += "tubeCapPos:" + QString::number(tubeCapPos.uiRowIndex) + "," + QString::number(tubeCapPos.uiColumnIndex) + "," + QString::number(tubeCapPos.uiAreaIndex) + " ";
        info += "tubePos:" + QString::number(tubePos.uiRowIndex) + "," + QString::number(tubePos.uiColumnIndex) + "," + QString::number(tubePos.uiAreaIndex) + " ";
        info += "pcrPos:" + QString::number(pcrPos.uiRowIndex) + "," + QString::number(pcrPos.uiColumnIndex) + "," + QString::number(pcrPos.uiAreaIndex) + " ";
        info += "tipPos:" + QString::number(tipPos.uiRowIndex) + "," + QString::number(tipPos.uiColumnIndex) + "," + QString::number(tipPos.uiAreaIndex) + " ";
        info += "pcrAreaPos:" + QString::number(pcrAreaPos.uiRowIndex) + "," + QString::number(pcrAreaPos.uiColumnIndex) + "," + QString::number(pcrAreaPos.uiAreaIndex) + " ";
        info += "strResult:" + strResult + " ";
        info += "strTecName:" + strTecName + " ";
        info += "bNeedOpenCap:" + QString::number(bNeedOpenCap) + " ";
        info += "bNeedCloseCap:" + QString::number(bNeedCloseCap) + " ";
        info += "bLastOneInCurTecBatch:" + QString::number(bLastOneInCurTecBatch);
        return info;
    }
};


class SampleControl
{
public:
    static SampleControl &getInstance();

    void SetCatchType(quint8 uiCatchType);

    void SetCurBatchSamples(QString strCurBatch);

    bool AddCurBatch();

    bool AddBatch(QString strBatchInfo);

    bool ParseBatchInfo(QString &strBatchInfo, BatchInfo& sBatchInfo);

    bool HasNextSampleCatchSize();   //获取下次抓取样本数量

    quint8 GetNextSampleCatchAndMixSize();//获取下次等待樣本抓取及混勻的样本数量

    quint8 GetNextInternalStandardCatchAndMixSize();//获取下次等待内标樣本抓取及混勻的样本数量

    QVector<SampleInfo> GetNextWaitCatchAndMixSampleInfos();//获取下一次等待樣本抓取及混勻的样本信息

    void AddTstSamples(BatchInfo &sBatchInfo);//添加测试样本函数，临时版

    bool GetCurBatchInfo(BatchInfo &sBatchInfo);//获取当前执行批次样本信息

    quint8 GetBatchSampleSize(QString strBatchNo);

    quint8 GetCurBatchSampleSize();

    /**
     * @brief GetCurBatchNo 获取当前执行批号
     * @return 批号
     */
    QString GetCurBatchNo();

    /**
     * @brief IsCurBatchSampleSpecificActionDone 当前批次的样本是否已经都完成了特定动作
     * @param uiCurExecST 特定动作状态，在该状态结束时查询
     * @return
     */
    bool IsCurBatchSampleSpecificActionDone(quint8 uiCurExecST);

    /**
     * @brief IsCurBatchSampleStandardActionDone 当前批次的内标是否已经都完成了特定动作
     * @return 内标是否添加完成
     */
    bool IsCurBatchSampleStandardActionDone();

    /**
     * @brief AddSampleStandardActionCount 当前批次的内标添加计数
     * @return
     */
    void AddSampleStandardActionCount();

    /**
     * @brief IsCurBatchSampleStandardAvailable 当前批次的内标是否可用(不可用不进入内标添加)
     * @return 判断当前批次内标和项目是否有对应项目
     */
    bool IsCurBatchSampleStandardAvailable();

    /**
     * @brief GetSampleSize 获取指定批次样本的样本数量
     * @param batchInfo
     * @return
     */
    qint8 GetSampleSize(BatchInfo &batchInfo);

    /**
     * @brief UpdateSampleExecSTToNext 将当前执行样本的状态更新到下一个状态，在当前动作执行完会进行状态更新
     * @param uiCurExecST 当前状态
     * @param uiNextExecST 下个状态
     * @return
     */
    bool UpdateSampleExecSTToNext(quint8 uiCurExecST, quint8 uiNextExecST);

    /**
     * @brief UpdateSampleExecSTToNext 将当前执行样本的状态更新到下个状态集合，在当前动作执行完会进行状态更新
     * @param uiCurExecST
     * @param qNextExecSTVect 下一步需要执行的动作集合
     * @return
     */
    bool UpdateSampleExecSTToNext(quint8 uiCurExecST, QVector<quint8> qNextExecSTVect);

    bool UpdateExecSTToError(QVector<quint8> qImpactExecSTVect);
    /**
     * @brief AddSampleToNextState 新增指定样本到下一个状态
     * @param qVect 样本集合
     * @param uiNextExecST 下一个状态
     * @param bResult 结果信息
     */
    void AddSampleToNextState(QVector<SampleInfo> &qVect, quint8 uiNextExecST, bool &bResult, bool bNeedLocker);

    /**
     * @brief GetCurExecSampleInfo 获取当前执行某一个状态的样本信息
     * @param uiCurExecST
     * @param qVect
     * @return
     */
    bool GetCurExecSampleInfo(quint8 uiCurExecST, QVector<SampleInfo> &qVect);

    /**
     * @brief GetCurExecSampleCapacity 获取当前执行某一个状态的样本容量
     * @return 样本容量
     */
    qint32 GetCurExecSampleCapacity();

    /**
     * @brief UpdateCurExecSampleInfo 更新當前執行特定狀態的樣本信息
     * @param uiCurExecST
     * @param qVect
     * @return
     */
    bool UpdateCurExecSampleInfo(quint8 uiCurExecST, QVector<SampleInfo> &qVect);

    /**
      * @brief UpdateCurExecStripAndSamplingSTIndex 更新當前待加樣样本所关联的提取卡条位置,返回卡条及样本吸样起始位置信息
      * @param strParam 返回卡条及吸样位置信息字符串,以“,stripIndex1,sampingIndex”形式返回
      * @param uiSize 返回本次操作卡条数量
      * @return
      */
    bool UpdateCurExecStripAndSamplingSTIndex(QString& strParam, quint8& uiSize);

    /**
      * @brief UpdateCurExecStripAndSamplingSTIndex 更新當前待加樣样本所关联的提取卡条位置(主要更新内标样本位置，方便使用移液泵1、2)
      * @return
      */
    void UpdateCurExecStripAndSamplingSTIndex(quint8& uiSize);

    /**
      * @brief GetStripIndexParamStr 獲取當前特定狀態樣本關聯的卡條位置信息
      * @param uiState
      * @param strParam
      * @param uiSize 卡条数量
      * @return
      */
    bool GetStripIndexParamStr(quint8 uiState, QString& strParam, quint8& uiSize);

    /**
      * @brief GetBatchSampleInfo
      * @param uiState
      * @return
      */
    QQueue<QVector<SampleInfo>> GetBatchSampleInfo(quint8 uiState);

    /**
      * @brief UpdateBacthSampleInfo 更新特定批次特定狀態的樣本信息
      * @param uiState
      * @param qQueue
      */
    void UpdateBacthSampleInfo(quint8 uiState, QQueue<QVector<SampleInfo>> qQueue);


    /**
     * @brief RearraySampleInfoToSystemBuildInfo
     * 執行完批次樣本提取後，將數據結構轉爲體系構建結構，以擴增管爲基本操作單位，不再以樣本作爲基本操作單元
     *
     */
    void RearraySampleInfoToSystemBuildInfo();

    /**
      * @brief RearraySystemBuildRegentInfo 重排系统构建試劑信息，根据项目信息进行重新整理各個擴增項目所需數量
      */
    void RearraySystemBuildRegentInfo();

    QMap<QString, quint8> GetSystemBuildReagentInfo();

    //打印各個狀態的樣本信息及系統構建信息
    void ShowSTMapInfo();
    void ShowSampleSTMap();
    void ShowSystemBuildSTMap();
    /**
        * @brief UpdateSystemBuildExecSTToNext 将当前执行系统构建扩增的状态更新到下一个状态，在当前动作执行完会进行状态更新
        * @param uiCurExecST 当前状态
        * @param uiNextExecST 下个状态
        * @return
        */
    bool UpdateSystemBuildExecSTToNext(quint8 uiCurExecST, quint8 uiNextExecST);

    /**
        * @brief UpdateSystemBuildExecSTToNext 将当前执行系统构建扩增的状态更新到下个状态集合，在当前动作执行完会进行状态更新
        * @param uiCurExecST
        * @param qNextExecSTVect 下一步需要执行的动作集合
        * @return
        */
    bool UpdateSystemBuildExecSTToNext(quint8 uiCurExecST, QVector<quint8> qNextExecSTVect);

    /**
        * @brief AddSystemBuildToNextState 新增指定系统构建扩增到下一个状态
        * @param qVect 系统构建扩增集合
        * @param uiNextExecST 下一个状态
        * @param bResult 结果信息
        */
    void AddSystemBuildToNextState(QVector<SystemBuildInfo> &qVect, quint8 uiNextExecST, bool &bResult, bool bNeedLocker);

    /**
        * @brief GetCurExecSystemBuildInfo 获取当前执行某一个状态的系统构建扩增信息
        * @param uiCurExecST
        * @param qVect
        * @return
        */
    bool GetCurExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo> &qVect);

    /**
    * @brief GetLatestExecSystemBuildInfo 获取最新执行某一个状态的系统构建扩增信息
    * @param uiCurExecST
    * @param qVect
    * @return
    */
    bool GetLatestExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo> &qVect);
    
    /**
    * @brief GetWaitAbandonExecSystemBuildInfo 获取丢弃PCR管的系统构建扩增信息
    * @param qVect                             系统构建扩增集合
    * @return
    */
    bool GetWaitAbandonExecSystemBuildInfo(QVector<SystemBuildInfo> &qVect);

    /**
        * @brief UpdateCurExecSystemBuildInfo 更新當前執行特定狀態的系统构建扩增信息
        * @param uiCurExecST
        * @param qVect
        * @return
        */
    bool UpdateCurExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo> &qVect);
    
    /**
        * @brief IsCurBatchSystemBuildSpecificActionDone 当前批次的系統構建是否已经都完成了特定动作
        * @param uiCurExecST 特定动作状态，在该状态结束时查询
        * @param strBatchNo 批次号
        * @return
        */
    bool IsCurBatchSystemBuildSpecificActionDone(quint8 uiCurExecST,const QString strBatchNo);

    /**
        * @brief IsAllSystemBuildSpecificActionDone 当前所有批次是否已经都完成了特定动作
        * @param uiCurExecST 特定动作状态，在该状态结束时查询
        * @return
        */
    bool IsAllSystemBuildSpecificActionDone(quint8 uiCurExecST);

    /**
    * @brief IsCurBatchSystemBuildSpecificActionDone 当前批次的系統構建是否已经都完成了特定动作
    * @param strBatchNo 批次号
    * @return
    */
    bool IsCurBatchSystemBuildSpecificActionDone(const QString strBatchNo);

    /**
    * @brief IsCurBatchSystemBuildSpecificActionDone 当前批次的系統構建是否已经都完成了特定动作
    * @param strBatchNo 批次号
    * @return
    */
   bool IsAllBatchSystemBuildSpecificActionDone(const QString strBatchNo);
    
    /**
     * @brief RearraySystemBuildInfoBeforePCRAbandon pcr完成时回复
     * @param uiPCRIndex                             pcr索引
     */  
    void RearraySystemBuildInfoBeforePCRAbandon(quint16 uiPCRIndex);

    /**
     * @brief RearraySystemBuildInfoByTecType
     * @param qDstQueue  匹配后的样本信息
     * 根据不同的TEC时序进行构建信息重组，基本原则：不同的TEC时序不可一起操作
     */
    void RearraySystemBuildInfoByTecType(QQueue<QVector<SystemBuildInfo>>& qQueue);

    /**
        * @brief IsExistSingleOperateTestInRemainQueue 剩余队伍中是否还有独立操作的对象
        * @param uiCurExecST 特定状态
        * @return
        */
    bool IsExistSingleOperateTestInRemainQueue(quint8 uiCurExecST);

    /**
        * @brief IsCurTestTheLastOne 当前测试的样本是否是特定状态队伍里的最后一个
        * @param uiCurExecST
        * @return
        */
    bool IsCurTestTheLastOneInSpecST(quint8 uiCurExecST, quint8& uiRemainOpSize);
    /**
        * @brief IsCurTestThelastOpOneInBatch 当前测试是否是当前批次在特定状态及之前状态的最后一个操作测试（一个操作测试可能有一个样本，也可能涉及两个样本)
        * @param uiCurExecST 当前特定状态
        * @return
        */
    bool IsCurTestThelastOpOneInBatch(quint8 uiCurExecST);

    /**
    * @brief GetCurBatchPCRAreaUsingList 获取使用的PCR区域信息
    * @param strBatchNo 批次号
    * @return
    */
    QMap<quint8, QString> GetCurBatchPCRAreaUsingList(const QString strBatchNo);

    /**
     * @brief GetAllPCRInfoString 获取当前所有正在进行PCR扩增过程的数据信息，
     *          每个扩增信息以以下格式进行组织：孔id1,样本id,项目货号,项目组分id;....;
     * @param strBatchNo 批次号
     * @return
     */
    QString GetAllPCRInfoString(const QString strBatchNo);

    void ClearCurBatchData();

    /**
     * @brief DequeueNextSystemBuildReagentInfo 获取并删除第一个系统构建试剂信息
     * @param strProj     项目名称
     * @param uiProjSize  项目数量
     * @return
     */
    bool DequeueNextSystemBuildReagentInfo(QString &strProj, quint8& uiProjSize);

    /**
     * @brief getCurBatchExtractProgram 获取当前批次的提取时序名称
     * @return
     */
    QString getCurBatchExtractProgram();
    void ClearSampleSTMap();

    bool isCurOpSamplesSingleRight();
    
    /**
     * @brief CheckSampleIsStandard 获取并删除第一个系统构建试剂信息
     * @param strSampleNo           样本号
     * @param strStandardProject    内标项目
     * @return true为内标
     */
    bool CheckSampleIsStandard(const QString& strSampleNo,QString& strStandardProject);

    /**
     * @brief GetProjectStripIndex 获取项目对应的卡条信息
     * @param strProjID 项目名称 
     * @param vStripIndex 卡条位置
     * @return 
     */
    void GetProjectStripIndex(const QString& strProjID,QList<quint8>& qStripIndex);

    /**
     * @brief UpdateProjectStripIndex 更新项目对应的卡条信息
     * @param strProjID 项目名称 
     * @param uiStripIndex 卡条位置
     * @return 是否添加完成
     */
    bool UpdateProjectStripIndex(const QString& strProjID,const quint8& uiStripIndex);

    /**
     * @brief ResetCurBatchStatus 重置当前批次状态
     * @return 
     */
    void ResetCurBatchStatus();      

    /**
     * @brief GetNextWaitProjectStripIndex 重置当前批次状态
     * @return 返回可用的卡条位置
     */
    quint8 GetNextWaitProjectStripIndex();       

    /**
     * @brief GetNextWaitProjectStripIndex 重置当前批次状态
     * @param strProjectName 项目名称 
     * @param uiStripIndex 卡条位置
     * @param uiColumnIndex 样本位置
     * @return 是否存在
     */
    bool GetNextWaitSampleInfo(QString& strProjectName,quint8& uiStripIndex,quint8& uiColumnIndex);  

    /**
     * @brief SetCurBatchSystemBuildSize 设置当前批次系统构建扩增数量
     * @param uiSize 扩增数量
     * @return 
     */
    void SetCurBatchSystemBuildSize(quint8 uiSize);  
    
    /**
     * @brief GetCurBatchSystemBuildSize 获取当前批次系统构建扩增数量
     * @return 使用数量
     */
    quint16 GetCurBatchSystemBuildSize(); 

    /**
     * @brief SetCalcSystemBuildInfo 设置当前批次预分配孔位信息
     * @param qCalcSystemBuildInfo 样本信息(修改孔位信息)
     * @return 
     */
    void SetCalcSystemBuildInfo(QQueue<QVector<SystemBuildInfo>>& qCalcSystemBuildInfo);  

    /**
     * @brief ClearFinishSystemBuildInfo 清除当前批次系统构建扩增结束信息
     * @param strBatchNo 批次信息
     * @return 
     */
    void ClearFinishSystemBuildInfo(const QString strBatchNo);  
    
public:// 扩增例程样本信息(减少SampleControl类代码量)
    SampleAmplificate m_sampleAmplificate;
private:
    SampleControl();
    ~SampleControl();
    quint8 _GetBatchSampleSize(BatchInfo& batchInfo);
    bool _GetNextWaitExecBatch(bool bNeedLocker);//获取下一批执行的该批次样本项目信息
    void _AddSampleToNextStateNoLocker(QVector<SampleInfo> &qVect, quint8 uiNextExecST, bool &bResult);
    void _AddSystemBuildToNextStateNoLock(QVector<SystemBuildInfo> &qVect, quint8 uiNextExecST, bool &bResult);
    /**
     * @brief _GroupByTecName 根据TEC时序进行分组
     * @param qSrcQueue
     * @param qTecSystemBuildMap
     */
    void _GroupByTecName(QQueue<QVector<SystemBuildInfo> > &qSrcQueue,
                        QMap<QString, QVector<SystemBuildInfo> > &qTecSystemBuildMap);

    /**
     * @brief _ReorganizeWithPriorityAdjacentPairing
     * 同一个TEC分组里，优先相邻元素配对，不相邻元素再两两配对，直至无或者未配对的单一元素
     * @param qTecSystemBuildMap
     * @param qDstQueue
     */
    void _ReorganizeWithPriorityAdjacentPairing(
            QMap<QString, QVector<SystemBuildInfo> > &qTecSystemBuildMap,
            QQueue<QVector<SystemBuildInfo> > &qDstQueue);

    /**
     * @brief UpdateProjectStripIndex 更新项目对应的卡条信息
     * @param vProjectInfo 项目名称 
     * @param uiStripIndex 卡条位置
     * @return 是否添加完成
     */
    bool _UpdateProjectStripIndex(const QVector<ProjectInfo>& vProjectInfo,const quint8& uiStripIndex);
    
    /**
     * @brief _CheckTecTimeSeqValid 检查tec时序是否有效
     * @param strName tec时序名称
     * @return 数据库是否存在
     */    
    bool _CheckTecTimeSeqValid(const QString& strName); 

    /**
     * @brief _CheckTecTimeSeqValid 检查提取时序是否有效
     * @param strName 提取时序名称
     * @return 数据库是否存在
     */    
    bool _CheckExtractTimeSeqValid(const QString& strName);     

    /**
     * @brief _CheckProjIDValid 检查提取时序是否有效
     * @param strName 项目名称
     * @return 数据库是否存在
     */    
    bool _CheckProjIDValid(const QString& strName);   

    /**
     * @brief _DeleteStandardProjID 删除不需要添加到普通样本的内标
     * @param qStandardProjID 内标项目名称
     * @param qProjID         样本项目名称
     * @param sBatchInfo      批次信息
     * @return 
     */    
    void _DeleteStandardProjID(QSet<QString>& qStandardProjID,QSet<QString>& qProjID,BatchInfo &sBatchInfo);           

    /**
     * @brief _CalcStandardSampleInfo 计算内标相关参数
     * @param qProjID         所有样本项目id
     * @param qStandardProjID 所有内标项目id
     * @param qSampleInfo     所有样本信息
     * @return 
     */
    void _CalcStandardSampleInfo(QSet<QString> &qProjID,QSet<QString> &qStandardProjID,QVector<SampleInfo> &qSampleInfo); 

    /**
     * @brief _HandlePairingBaseSample 处理基数样本
     * @param qOddVect      基数样本信息
     * @param qDstQueue     样本信息
     * @return 
     */
    void _HandlePairingBaseSample(QVector<SystemBuildInfo>& qOddVect,QQueue<QVector<SystemBuildInfo>>& qDstQueue); 

private:
    BatchInfo m_sBatchInfoList[BATCH_MAX_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex{0};
    int m_iCurrentWriteIndex{0};
    BatchInfo m_sCurBatch;
    BatchInfo m_sNextBatch;
    quint8 m_uiCatchType;
    QMutex m_qBatchMutex;

    QVector<SampleInfo> m_qNextExecSampleInfoVect;
    QMap<quint8, QQueue<QVector<SampleInfo>>> m_qSampleSTMap;//負責體系構建前面階段的數據結構
    QMap<quint8, QQueue<QVector<SystemBuildInfo>>> m_qSystemBuildSTMap;//負責體系構建之後階段的數據結構
    QMutex m_qSampleSTMutex;
    QMutex m_qSytemBuildSTMutex;
    QMutex m_qSystemBuildReagentMutex;
    QMap<QString, quint8> m_qSytemBuildReagentMap;//key 是项目id, value是关联的项目数量,系统构建体系项目关联的數量信息
    quint8 m_uiCurBatchSystemBuildSize;//當前批次PCR擴增管數
    QString m_strCurBatchSamples;

    bool m_bCurOpSingleRightSample;//当前操作的样本是否是右侧单个样本
    QMap<QString, QVector<quint8>> m_qProjectStripIndexMap; // 项目对应的样本卡条位置信息
    quint8 m_iStandardSampleUsedCount = 0;  // 内标使用数量计数
    bool m_bStandardAndSampleCountMatch = false;  // 内标项目数量与样本项目数量是否匹配 
    QQueue<QVector<SystemBuildInfo>> m_qSystemBuildInfoBeforePCRAbandon; // 丢弃PCR管前的系统构建扩增信息 
    QSet<QString> m_qBatchNo;  // 批次号
};

#endif // SampleControl_H
