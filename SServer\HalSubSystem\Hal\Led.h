#ifndef LED_H
#define LED_H


#include<QObject>
#include<QVector>
#include "publicconfig.h"

class Led : public QObject
{
    Q_OBJECT
public:
    enum EnumTriggerState //0-灭，1-亮
    {
        OFF,
        ON,
    }; 
    
    enum EnumMoudle //1-样本架1（右边起），2-样本架2，3-提取模块
    {
        Sample1_Right = 1,
        Sample2_Left,
        Strip_Box,
    };    
public:
    explicit Led(QObject *parent = nullptr);

    /**
     * @brief Trigger 触发灯状态
     * @param state   状态
     * @param moudle  模块
     * @param machineID  板卡id
     * @return 
     */    
    void Trigger(EnumTriggerState state, EnumMoudle moudle,EnumMachineID machineID);

    /**
     * @brief GetMoudleStatus 获取灯状态
     * @param moudle  模块
     * @return 状态
     */    
    EnumTriggerState GetMoudleStatus(EnumMoudle moudle);    
signals:

public slots:

private:

private:
    QHash<EnumMoudle,EnumTriggerState> m_ledMap;//灯状态
};


#endif // LED_H
