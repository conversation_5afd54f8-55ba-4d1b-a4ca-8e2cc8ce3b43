﻿#ifndef ErrorCodeDB_H
#define ErrorCodeDB_H

#include <QObject>
#include "cdbobject.h"
#if Q_OS_QML
#include <QQmlEngine>
#endif

class ErrorCodeDB : public CDBObject
{
    Q_OBJECT

public:
#if Q_OS_QML
    static QObject* qmlSingletonInstance( QQmlEngine* engine, QJSEngine* scriptEngine )
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static ErrorCodeDB& getInstance();
    Q_INVOKABLE void initDataBase();
    
protected:
    QString getDatabasePath() const override {
        return CGlobalConfig::getInstance().GetErrorCodeDBDir();
    }
    QString getConnectionName() const override {
        return gk_strErrorCodeDBConnect;
    }
    WriteConnectionPool *getWriteConnectionPool() override {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }
public:
    bool addErrorCodeInfo(QStringList strDataList);
    QString getFaultLevelFromErrorCode(QString strErrorCode);
    QStringList getErrorInfoFromErrorCode(QString strErrorCode);
#if Q_OS_QML
    Q_INVOKABLE void readFromXlsxAndSave(QString strFilePath = "");
#endif

private:
    explicit ErrorCodeDB(QObject *parent = nullptr);
private:

    struct errorCodeTableFields {
        static inline const QString error_code = "error_code";
        static inline const QString board_name = "board_name";
        static inline const QString board_id = "board_id";
        static inline const QString business_name = "business_name";
        static inline const QString business_id = "business_id";
        static inline const QString fault_code = "fault_code";
        static inline const QString fault_level_description = "fault_level_description";
        static inline const QString fault_level = "fault_level";
        static inline const QString user_fault_description = "user_fault_description";
        static inline const QString factory_fault_description = "factory_fault_description";
        static inline const QString fault_handle_plan = "fault_handle_plan";
    };

private:
    errorCodeTableFields errorCodeFields;
    QString m_strErrorCodeFieldsTabelName;
    QStringList m_strErrorCodeFieldsFieldsList;
};

#endif // ErrorCodeDB_H
