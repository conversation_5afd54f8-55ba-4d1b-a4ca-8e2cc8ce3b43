#include "samplemodule.h"

SampleModule::SampleModule(bool bUseThread, quint8 quCatchType)
    : DeviceModule("SampleModule",bUseThread), m_uiCatchType(quCatchType)
{
    m_uiSampleCatchType = SCT_RIGHT;
} // 在构造函数中进行初始化

void SampleModule::SetCatchType(quint8 quCatchType)
{
    m_uiCatchType = quCatchType; // 新增设置 m_uiCatchType 变量的函数
}

void SampleModule::SetSampleCatchType(quint8 uiSampleCatchType)
{
    m_uiSampleCatchType = uiSampleCatchType;
}


void SampleModule::SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr)
{
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
    // 可以在这里添加子类特有的逻辑
//    qDebug() << "SampleModule adding task with specific logic (uiSubTaskID: " << uiSubTaskID << ", strCommandStr: " << strCommandStr << ", strParamStr: " << strParamStr << ")";

}

void SampleModule::SlotAddTask(const CmdTask& task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "SampleModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}

void SampleModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "SampleModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
        switch (task.uiSubTaskID)
        {
        case STI_CATCH_SAMPLE:
        {
            _AddSubTask(task.strParamStr, Action_SampleToOpenCap+m_uiSampleCatchType);
            break;
        }
        case STI_CATCH_CLOSE_CAP:
        {
            _AddSubTask(task.strParamStr, Action_CloseCap+m_uiSampleCatchType);
            break;
        }
        case STI_CATCH_OPEN_CAP:
        {
            _AddSubTask(task.strParamStr, Action_OpenCap+m_uiSampleCatchType);
            break;
        }
        case STI_CATCH_BACK_HOME:
        {
            _AddSubTask(task.strParamStr, Action_SampleBackHome+m_uiSampleCatchType);
            break;
        }
        case STI_CATCH_INIT:
        {
            _AddSubTask("", Action_Board1Gripper_Init);
            break;
        }
        case STI_CATCH_CHECK_EXIST:
        {
            _AddSubTask("", Action_SampleCodeScanTubeCheck);
            break;
        }
        case STI_CATCH_CHECK_EXIST_LEFT:
        {
            _AddSubTask("", Action_SampleCodeScanTubeCheckLeft);
            break;
        }         
        default:
            break;
        }
        m_qWaitProcessSubTask.pop_front();
    }
}


