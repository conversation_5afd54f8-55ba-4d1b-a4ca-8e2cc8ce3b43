#include "cserialwindow.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
CSerialWindow::CSerialWindow(QObject *parent) : CWindowObject(parent)
{

}

CSerialWindow::~CSerialWindow()
{
    m_bThreadExit = true;
}

void CSerialWindow::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            CGlobalConfig::getInstance().printMessageInfo(qMessage,
                                                          "[serial->server] ");
            CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_UpperHost);
            // 转发--用哪种通信方式，获取DestinationID，不修改SourceID
            switch (m_iMethodID)
            {
            default:
                break;
            }
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}
