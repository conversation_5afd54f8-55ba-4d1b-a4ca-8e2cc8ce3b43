﻿/*****************************************************
 * Copyright: 万孚生物
 * Author: qliu
 * Date: 2024-03-11
 * Description: 数据库操作基类，旨在编写通用的数据库创建，增删改查函数
 * 为了防止死锁的出现，使用读写连接池分离策略
 * -----------------------------------------------------------------
 * History:
 *
 *
 *
 * -----------------------------------------------------------------
 ****************************************************/
#ifndef CDBOBJECT_H
#define CDBOBJECT_H
#include <QObject>
#include <QSqlError>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QMutex>
#include <QMap>
#include <QString>
#include <QVariant>
#if Q_OS_QML
#include <QQmlEngine>
#endif
#include "cglobalconfig.h"
#include <QThreadStorage>
#include <QQueue>
#include <QtConcurrent/QtConcurrent>
#include <exception>

class CDBObject : public QObject
{
    Q_OBJECT
public:
    explicit CDBObject(QObject *parent = nullptr);
    using QueryIntCallback = std::function<void(int)>;
    using QueryStringListCallback = std::function<void(QStringList)>;
    using QueryStringListListCallback = std::function<void(QList<QStringList>)>;
    using QueryBoolCallback = std::function<void(bool)>;

protected:
    // 读连接管理
    class ThreadLocalConnection
    {
    private:
        struct DBConnection
        {
            QSqlDatabase db;
            QString connPrefix;
            bool inUse{false};
            QString connectionName; // 存储实际的连接名
        };

        static const int MAX_CONNECTIONS_PER_THREAD = 5; // 每个线程最大连接数

    public:
        static QThreadStorage<QMap<QString, DBConnection>> &getThreadConnections()
        {
            // 直接在头文件中实现
            static QThreadStorage<QMap<QString, DBConnection>> s_threadConnections;
            return s_threadConnections;
        }

        static QString generateConnectionName(const QString &prefix)
        {
            return QString("%1_%2").arg(prefix).arg((quintptr)QThread::currentThreadId());
        }

        // RAII方式管理连接
        class ReadGuard
        {
        public:
            ReadGuard(const QString &dbPath, const QString &connPrefix)
            {
                m_bIsValid = initConnection(dbPath, connPrefix);
            }

            ~ReadGuard()
            {
                if (m_pConnection)
                {
                    // 检查连接是否还有效
                    if (!m_pConnection->db.isValid() || !m_pConnection->db.isOpen())
                    {
                        // 移除无效连接
                        QString connName = m_pConnection->connectionName;
                        QSqlDatabase::removeDatabase(connName);
                        auto &connections = getThreadConnections();
                        if (connections.hasLocalData())
                        {
                            connections.localData().remove(connName);
                        }
                        qWarning() << "移除无效的数据库连接:" << connName;
                    }
                    else
                    {
                        m_pConnection->inUse = false;
                    }
                }
            }

            QSqlDatabase &get()
            {
                if (!isValid())
                {
                    qWarning() << "获取到无效的数据库连接";
                    static QSqlDatabase invalidDb; // 返回一个静态的无效连接
                    return invalidDb;
                }
                return m_pConnection->db;
            }

            bool isValid() const
            {
                return m_bIsValid && m_pConnection &&
                       m_pConnection->db.isValid() &&
                       m_pConnection->db.isOpen();
            }

            QString getLastError() const
            {
                return m_strLastError;
            }

        private:
            bool initConnection(const QString &dbPath, const QString &connPrefix)
            {
                auto &connections = getThreadConnections();
                if (!connections.hasLocalData())
                {
                    connections.setLocalData(QMap<QString, DBConnection>());
                }

                auto &localConnections = connections.localData();

                // 尝试查找空闲的现有连接
                QString baseConnName = generateConnectionName(connPrefix);
                for (auto &conn : localConnections)
                {
                    if (conn.connPrefix == connPrefix && !conn.inUse)
                    {
                        if (conn.db.isValid() && conn.db.isOpen())
                        {
                            conn.inUse = true;
                            m_pConnection = &conn;
                            return true;
                        }
                    }
                }

                // 检查连接数量限制
                int iCurrentConnCount = 0;
                for (const auto &conn : localConnections)
                {
                    if (conn.connPrefix == connPrefix)
                    {
                        iCurrentConnCount++;
                    }
                }

                if (iCurrentConnCount >= MAX_CONNECTIONS_PER_THREAD)
                {
                    m_strLastError = QString("线程 %1 的连接数已达到上限 %2")
                                         .arg((quintptr)QThread::currentThreadId())
                                         .arg(MAX_CONNECTIONS_PER_THREAD);
                    qWarning() << m_strLastError;
                    return false;
                }

                // 创建新连接
                QString newConnName = QString("%1_%2").arg(baseConnName).arg(iCurrentConnCount + 1);

                DBConnection dbConn;
                dbConn.db = QSqlDatabase::addDatabase("QSQLITE", newConnName);
                dbConn.db.setDatabaseName(dbPath);
                dbConn.connPrefix = connPrefix;
                dbConn.connectionName = newConnName;

                if (!dbConn.db.open())
                {
                    m_strLastError = QString("打开数据库失败: %1").arg(dbConn.db.lastError().text());
                    qWarning() << m_strLastError;
                    QSqlDatabase::removeDatabase(newConnName);
                    return false;
                }

                dbConn.inUse = true;
                localConnections[newConnName] = dbConn;
                m_pConnection = &localConnections[newConnName];

                qDebug() << "创建新的数据库连接:" << newConnName
                         << "当前线程连接数:" << (iCurrentConnCount + 1);
                return true;
            }

        private:
            DBConnection *m_pConnection{nullptr};
            bool m_bIsValid{false};
            QString m_strLastError;
        };

        // 清理线程的所有连接
        static void closeAllConnections()
        {
            auto &connections = getThreadConnections();
            if (connections.hasLocalData())
            {
                auto &localConnections = connections.localData();
                for (auto &conn : localConnections)
                {
                    if (conn.db.isValid())
                    {
                        if (conn.inUse)
                        {
                            qWarning() << "关闭使用中的连接:" << conn.connectionName;
                        }
                        conn.db.close();
                        QSqlDatabase::removeDatabase(conn.connectionName);
                    }
                }
                localConnections.clear();
            }
        }
    };

    // 写连接池
    class WriteConnectionPool
    {
    public:
        WriteConnectionPool(const QString &dbPath, const QString &connPrefix)
            : m_strDatabasePath(dbPath), m_strConnectionPrefix(connPrefix)
        {
            initWriteConnection();
        }

        QSqlDatabase acquireWriteConnection()
        {
            QMutexLocker locker(&m_mutexForConnection);
            if (!m_qWriteConnection.isValid() || !m_qWriteConnection.isOpen())
            {
                initWriteConnection();
            }
            return m_qWriteConnection;
        }

        template <typename Func>
        auto executeWrite(Func operation) -> decltype(operation())
        {
            QMutexLocker locker(&m_mutexForWrite); // 锁住整个写操作
            QSqlDatabase db = acquireWriteConnection();
            try
            {
                auto result = operation();
                return result;
            }
            catch (...)
            {
                throw;
            }
        }

        QMutex &getWriteMutex()
        {
            return m_mutexForWrite;
        }

        void closeConnection()
        {
            QMutexLocker locker(&m_mutexForConnection);
            if (m_qWriteConnection.isValid())
            {
                // 关闭连接前清理所有查询
                {
                    QSqlQuery query(m_qWriteConnection);
                    query.clear();
                }
                m_qWriteConnection.close();
            }
        }

    private:
        void initWriteConnection()
        {
            QString connectionName = QString("%1_write").arg(m_strConnectionPrefix);
            if (QSqlDatabase::contains(connectionName))
            {
                m_qWriteConnection = QSqlDatabase::database(connectionName);
            }
            else
            {
                m_qWriteConnection = QSqlDatabase::addDatabase("QSQLITE", connectionName);
                m_qWriteConnection.setDatabaseName(m_strDatabasePath);
            }

            if (!m_qWriteConnection.isOpen() && !m_qWriteConnection.open())
            {
                qCritical() << "Failed to open write database:" << m_qWriteConnection.lastError().text();
            }
        }

        QString m_strDatabasePath;
        QString m_strConnectionPrefix;
        QSqlDatabase m_qWriteConnection;
        QMutex m_mutexForWrite;      // 保护写操作
        QMutex m_mutexForConnection; // 保护连接的获取和释放
    };

    // RAII 连接管理器
    class ConnectionGuard
    {
    public:
        ConnectionGuard(CDBObject *db) : m_pDB(db),
                                         m_guard(db->getDatabasePath(), db->getConnectionName())
        {
            m_db = &m_guard.get();
        }

        ~ConnectionGuard() {}

        QSqlDatabase &get() { return *m_db; }

        QString getLastError() const
        {
            return m_guard.getLastError();
        }

        bool isValid() const
        {
            return m_guard.isValid();
        }

    private:
        CDBObject *m_pDB;
        ThreadLocalConnection::ReadGuard m_guard;
        QSqlDatabase *m_db;
    };

    // 连接池管理虚函数
    virtual QString getDatabasePath() const = 0;
    virtual QString getConnectionName() const = 0;

    virtual WriteConnectionPool *getWriteConnectionPool()
    {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }

public:
    // 连接获取和释放接口
    QSqlDatabase acquireReadConnection()
    {
        ThreadLocalConnection::ReadGuard guard(getDatabasePath(), getConnectionName());
        return guard.get();
    }

    void releaseReadConnection(QSqlDatabase &)
    {
        // 不需要实现，连接由Guard管理
    }

    QSqlDatabase acquireWriteConnection()
    {
        return getWriteConnectionPool()->acquireWriteConnection();
    }

    // 关闭所有数据库连接
    void closeAllConnections()
    {
        // 关闭写连接
        getWriteConnectionPool()->closeConnection();

        // 关闭读连接
        ThreadLocalConnection::closeAllConnections();
    }

protected:
    // 错误处理和重试机制
    bool executeWithRetry(const std::function<bool()> &operation, int maxRetries = 3)
    {
        for (int iTry = 0; iTry < maxRetries; ++iTry)
        {
            try
            {
                if (operation())
                {
                    return true;
                }
            }
            catch (const std::exception &e)
            {
                qWarning() << "数据库操作失败，重试次数:" << iTry << "错误:" << e.what();
                if (iTry == maxRetries - 1)
                {
                    return false;
                }
                QThread::msleep(100 * (iTry + 1));
            }
        }
        return false;
    }

    // 基础数据库操作
    // 创建数据库表, strOtherCondition:附加条件
    bool createDBTable(const QString &kstrTableName,
                       const QVector<QPair<QString, QString>> &fieldsAndTypes,
                       const QString &strOtherCondition = "");

    // 增加字段表结构, 表名称不变，如果bIsDeleteOldTable为true，则删除临时表（原旧表）
    bool addFieldToTable(const QString &kstrTableName, 
                     const QStringList &strNewFieldList,                     
                     const bool &bIsDeleteOldTable = true);

    // 新增，线程安全，无需额外加锁，只作为内部接口使用，不推荐用于业务层
    bool addOneDBRecord(const QString &kstrTableName,
                        const QStringList &strFieldNameList,  // 字段
                        const QStringList &strFieldValueList, // 值
                        const QString &strSameKeyValue = "");      // 是否需要比较唯一值，当存在，只更新，不添加新条目
    bool addOneDBRecordOfMoreSame(const QString &kstrTableName,
                        const QStringList &strFieldNameList,  // 字段
                        const QStringList &strFieldValueList, // 值
                        const QStringList &strSameKeyValue);  // 是否需要比较唯一值，当存在，只更新，不添加新条目
    bool addMoreDBRecord(const QString &kstrTableName,
                         const QStringList &strFieldNameList,         // 字段
                         const QList<QStringList> &strFieldValueList, // 值
                         const QStringList &strSameKeyValue);         // 是否需要比较唯一值，当存在，只更新，不添加新条目
    bool appendOneDBRecord(const QString &kstrTableName,              // 字段追加，不删除原本的内容
                           const QString &strFieldName,
                           const QString &strFieldValue,
                           const QString &kstrConditioFieldName,
                           const QString &kstrConditioFieldValue); // 判断查询条件的更新
    //////////////////////////////////////////////////////////////////////////////////////////////
    // 一般通用查询，线程安全，无需额外加锁，只作为内部接口使用，不推荐用于业务层
    // 返回满足查询条件数据的条数
    uint32_t getDBRecordCount(const QString &kstrTableName,
                              const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>()); // 默认没有选择条件
    uint32_t getDBRecordCountFromData(const QString &kstrTableName, const QString &strDateFieldName,
                              const QDate &qBeginDate, const QDate &qEndDate); // 日期内数量
    uint32_t getDBRecordCountFromLastDays(const QString &kstrTableName, const QString &strDateFieldName,
                              const QString &strLastDays); // 最近日期内数量
    uint32_t getDBRecordCountByKeyword(const QString &kstrTableName,
                                       const QStringList &kstrFindDataList, const QString &strKeyValue); // 关键字搜索总条目

    // 查找一个字段一条记录，返回值只有查找的字段值，没有名称
    QVariant getOneRecordOneField(const QString &kstrTableName,
                                  const QString &kstrFindData,
                                  const QString &kstrConditioFieldName,
                                  const QString &kstrConditioFieldValue); // 单一条件
    QVariant getOneRecordOneField(const QString &kstrTableName,
                                  const QString &kstrFindData,                                                // 1
                                  const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>()); // 默认没有选择条件

    QStringList getMultipleRecordsFieldWithCondition(const QString &kstrTableName,
                                                     const QString &kstrFindData,
                                                     const QString &kstrConditionField,
                                                     const QString &kstrConditionValue); // 获取某个字段包含的多个返回

    // 查询多个字段一条记录，返回值只有查找的字段值，没有名称
    QStringList getOneRecordMoreFields(const QString &kstrTableName,
                                       const QStringList &kstrFindDataList,                                        // 1-N
                                       const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>()); // 默认按照给定的字段
    QStringList getOneRecordMoreFields(const QString &kstrTableName,
                                       const QStringList &kstrFindDataList, // 1-N
                                       const QString &kstrConditioFieldName,
                                       const QString &kstrConditioFieldValue); // 默认按照给定的字段
    QList<QStringList> getMultipleRecordsMoreFieldsWithCondition(const QString &kstrTableName,
                                                                 const QStringList &kstrFindDataList,
                                                                 const QString &kstrConditionField,
                                                                 const QString &kstrConditionValue); // 获取一条记录，条件是包含，而非相等

    // 查询一个字段多条记录，返回值只有查找的字段值，没有名称
    QStringList getMoreRecordsOneField(const QString &kstrTableName,
                                       const QString &kstrFindData,                                                 // 1
                                       const QString &kstrConditioFieldName, const QString &kstrConditioFieldValue, // 单一条件
                                       const QString &strOrderBy = "");                                             // 默认按照给定的字段
    QStringList getMoreRecordsOneField(const QString &kstrTableName,
                                       const QString &kstrFindData,                                               // 1
                                       const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>(), // 默认没有选择条件
                                       const QString &strOrderBy = "");                                           // 默认按照给定的字段
    // 批量查询一个字段的多条记录，使用 IN 子句优化
    QStringList getMoreRecordsOneFieldBatch(const QString &kstrTableName,
                                          const QString &kstrFindData,           // 要查询的字段
                                          const QString &kstrConditionField,     // 条件字段
                                          const QStringList &kstrConditionValues, // 条件值列表
                                          const QString &strOrderBy = "");        // 排序方式    
    // 批量查询多个字段的多条记录，使用 IN 子句优化
    QList<QStringList> getMoreRecordsMoreFieldsBatch(const QString &kstrTableName,
                                          const QStringList &kstrFindDataList,           // 要查询的字段
                                          const QString &kstrConditionField,     // 条件字段
                                          const QStringList &kstrConditionValues, // 条件值列表
                                          const QString &strOrderBy = "");        // 排序方式

    // 查询某个字段的所有不重复的值
    QStringList getDistinctFieldValues(const QString &kstrTableName, const QString &strFieldName);

    // 查询多个字段多条记录，返回值只有查找的字段值，没有名称，
    QList<QStringList> getMoreRecordsMoreFields(const QString &kstrTableName,
                                                const QStringList &kstrFindDataList,
                                                const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>(), // 默认没有选择条件
                                                const QString &strOrderBy = "");                                           // 默认按照给定的字段
    //  查询多个字段多条记录，返回值只有查找的字段值，没有名称，如果是在QML使用，可使用【, ;】分隔符返回QString类型
    QString getMoreRecordsMoreFieldsString(const QString &kstrTableName,
                                           const QStringList &kstrFindDataList,
                                           const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>(), // 默认没有选择条件
                                           const QString &strOrderBy = "");                                           // 默认按照给定的字段
    // 返回值使用map，QMap<字段名称，字段值>
    QList<QMap<QString, QString>> getMoreRecordsMoreFieldsMap(const QString &kstrTableName,
                                                              const QStringList &kstrFindDataList,
                                                              const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>(), // 默认没有选择条件
                                                              const QString &strOrderBy = "");                                           // 默认按照给定的字段
    // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    // 偏移量查询
    // 返回查找到所有字段的一页数据，返回值只有查找的字段值，没有名称
    // 查询某些字段的一页数据
    // 根据某一字段orderByField倒叙查询某些字段的一页数据
    QList<QStringList> getSomeDBRecordOnePageOrderBy(const QString &kstrTableName,
                                                     const QStringList &kstrFindDataList,
                                                     const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                     const QString &orderByField = "id");
    QList<QStringList> getSomeDBRecordOnePageOrderByDate(const QString &kstrTableName,
                                                         const QStringList &kstrFindDataList,
                                                         const QString &strDateFieldName,
                                                         const QDate &qBeginDate, const QDate &qEndDate,
                                                         const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                         const QString &orderByField = "id");
    QList<QStringList> getSomeDBRecordOnePageOrderByDate(const QString &kstrTableName,
                                                         const QStringList &kstrFindDataList,
                                                         const QString &strDateFieldName,
                                                         const QString &strLastDays,
                                                         const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                         const QString &orderByField = "id");
    // 查询字段包含内容的完整字段多条记录
    QList<QStringList> searchMultipleRecordsByKey(const QString &kstrTableName,
                                                  const QStringList &kstrFindDataList,
                                                  const QString &strKeyValue,
                                                  const bool isDescending = true);

    QList<QStringList> searchMultipleRecordsByDate(const QString &kstrTableName,
                                                   const QStringList &kstrFindDataList,
                                                   const QString &strDateFieldName,
                                                   const QDate &qBeginDate, const QDate &qEndDate,
                                                   const QString &orderByField = "id");

    QList<QStringList> searchMultipleRecordsByDate(const QString &kstrTableName,
                                                   const QStringList &kstrFindDataList,
                                                   const QString &strDateFieldName,
                                                   const QString &strLastDays);

    // 查询多条记录，并分页显示
    QList<QStringList> searchMultipleRecordsByKeyOnePage(const QString &kstrTableName,
                                                         const QStringList &kstrFindDataList,
                                                         const QString &strKeyValue,
                                                         const uint32_t &iBeginIndex, const uint32_t &iPageLength,
                                                         const bool isDescending = true);
    // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   

    //////////////////////////////////////////////////////////////////////////////////////////////
    // 通用更新，线程安全，无需额外加锁，只作为内部接口使用，不推荐用于业务层
    bool updateValueFromConditionFieldName(const QString &kstrTableName, // 通过查找某个字段来更新另一个字段
                                           const QString &kstrUpdateFieldName, const QString &kstrUpdateieldValue,
                                           const QString &kstrConditioFieldName, const QString &kstrConditioFieldValue);
    bool updateDBRecord(const QString &kstrTableName, // 条件更新数据
                        const QMap<QString, QString> &kstrUpdateMap,
                        const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>());

    // 通用删除，线程安全，无需额外加锁
    bool deleteDBRecord(const QString &kstrTableName, // 没有条件，或者条件字段有多个
                        const QMap<QString, QString> &kstrConditionMap = QMap<QString, QString>());
    bool deleteDBRecord(const QString &kstrTableName, // 删除条件字段只有一个
                        const QString &strFiledName, const QString &strFiledValue);

    // 通用删除数据库整个表, 使用时，请谨慎，只作为内部接口使用，不推荐用于业务层
    bool deleteDBTable(const QString &kstrTableName);

    // 基于id偏移的分页查询函数 
                                                   
    // 查询指定字段的一页数据(基于id偏移)
   QList<QStringList> getSomeDBRecordByIdRange(const QString &kstrTableName,
                                                const QStringList &kstrFindDataList,
                                                const uint32_t &lastId,
                                                const uint32_t &iPageLength,
                                                bool isForward,  // true表示向后翻页，false表示向前翻页
                                                int skipCount = 0,// 跳页时跳过多少条（用于jumpTo）
                                                bool bIsDescending = true
                                                );

    QList<QStringList> searchSomeRecordsByKeyOnePageIdRange(const QString &kstrTableName,
                                                                const QStringList &kstrFindDataList,
                                                                const QString &strKeyValue,
                                                                const uint32_t &lastId,
                                                                const uint32_t &iPageLength,
                                                                bool isForward, // true表示向后翻页，false表示向前翻页                                                                
                                                                int skipCount = 0, // 跳页时跳过多少条（用于jumpTo）
                                                                bool bIsDescending = true
                                                                );


    // 按日期范围和指定字段排序的分页查询(基于id偏移) getSomeDBRecordOnePageOrderByDate
    QList<QStringList> getSomeDBRecordOrderByDateIdRange(const QString &kstrTableName,
                                                        const QStringList &kstrFindDataList,
                                                                const QString &strDateFieldName,
                                                                const QDate &qBeginDate,
                                                                const QDate &qEndDate,
                                                        const uint32_t &lastId,
                                                        const uint32_t &iPageLength,
                                                        bool isForward,  // true表示向后翻页，false表示向前翻页
                                                        int skipCount = 0,// 跳页时跳过多少条（用于jumpTo）
                                                        bool bIsDescending = true);

    // 按最近天数查询的分页查询(基于id偏移) getSomeDBRecordOnePageOrderByDate
    QList<QStringList> getSomeDBRecordOrderByDateIdRange(const QString &kstrTableName,
                                                const QStringList &kstrFindDataList,
                                                         const QString &strDateFieldName,
                                                         const QString &strLastDays,
                                                const uint32_t &lastId,
                                                const uint32_t &iPageLength,
                                                bool isForward,  // true表示向后翻页，false表示向前翻页
                                                int skipCount = 0,// 跳页时跳过多少条（用于jumpTo）
                                                bool bIsDescending = true);

private:
    bool _addOneDBRecord(const QString &kstrTableName,
                        const QVector<QPair<QString, QString>> &kstrAddOnePair, // <字段,值>
                        const QString &strSameKeyValue);                        // 是否需要比较唯一值，当存在，只更新，不添加新条目
    bool _addOneDBRecordOfMoreSame(const QString &kstrTableName,
                        const QVector<QPair<QString, QString>> &kstrAddOnePair, // <字段,值>
                        const QStringList &strSameKeyValueList);                // 是否需要比较唯一值，当存在，只更新，不添加新条目

protected:
    const QString m_strIDFieldName = "id";
};

#endif // CDBOBJECT_H
