#include<qdebug.h>
#include<QTime>
#include <QFile>
#include <QVariantMap>
#include <QDir>
#include"CpuMonitor.h"

QVector<CpuUsage> CpuMonitor::calculateCpuUsage()
{
    QVector<CpuUsage> results;
    for (int i = 0; i < currentCpuData.size(); ++i) 
    {
        const QVector<long long> &current = currentCpuData[i];
        const QVector<long long> &previous = previousCpuData[i];

        long long totalDiff = 0;
        long long idleDiff = current[3] - previous[3];
        
        for (int j = 0; j < current.size(); ++j) 
        {
            totalDiff += current[j] - previous[j];
        }
        
        double usagePercent = 0.0f;
        if (totalDiff != 0)
        {
            usagePercent = 100.0 * (totalDiff - idleDiff) / totalDiff;
        }
        
        results.append(CpuUsage(i - 1, usagePercent)); // i - 1 because the first CPU (sum of all) gets id -1
    }
    return results;
}

double  CpuMonitor::getCpuUsage()
{
    if (previousCpuData.isEmpty() || currentCpuData.isEmpty()) {
        return 0.0; // 若无数据，则返回0表示无法计算使用率
    }
    QVector<CpuUsage> usages = calculateCpuUsage();
    double totalUsage = 0.0;
    // 累加所有单核CPU的使用率（跳过总体的CPU使用率，即cpuId == -1）
    for (const CpuUsage &usage : usages) {
        if (usage.cpuId != -1) {  // cpuId == -1 is the total CPU usage
            totalUsage += usage.usagePercentage;
        }
    }
    return totalUsage; // 返回所有单核CPU的累加使用率
}

void CpuMonitor::fetchCpuStats() 
{
    QProcess *process = new QProcess(this);
    connect(process, SIGNAL(finished(int, QProcess::ExitStatus)), this, SLOT(onProcessFinished(int, QProcess::ExitStatus)));
    process->start("cat", QStringList() << "/proc/stat");
}

void CpuMonitor::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus) 
{
    if (exitCode == 0 && exitStatus == QProcess::NormalExit) 
    {
        QProcess *process = qobject_cast<QProcess *>(sender());
        QString data = process->readAllStandardOutput();
        process->deleteLater();
        currentCpuData.clear();
        QStringList lines = data.trimmed().split("\n");
        for (const QString &line : lines) 
        {
            if (line.startsWith("cpu")) 
            {
                QStringList parts = line.split(QRegExp("\\s+"), QString::SkipEmptyParts);
                QVector<long long> values;
                std::transform(parts.begin() + 1, parts.end(), std::back_inserter(values),
                               [](const QString &str) { return str.toLongLong(); });
                currentCpuData.append(values);
            }
        }
        if (!previousCpuData.isEmpty())
         {
#if 0            
            // 计算并打印每个CPU的使用率
            QVector<CpuUsage> usage = calculateCpuUsage();
            for (const CpuUsage &u : usage) 
            {
                qDebug() << "CPU" << u.cpuId << "Usage:" << u.usagePercentage << "%";
            }
#else
            double totalusagePercentage = getCpuUsage();
            // CPU占用率超过80%时，打印日志
            if (totalusagePercentage > 80.0)
            {
                qDebug() << "CPU Usage is too high:" << totalusagePercentage << "%";
            }

            if (totalusagePercentage > 90.0)
            {
                // 超过90%时，打印日志，打印前三个CPU使用最高线程
                QProcess *process = new QProcess(this);
                // 打印全系统CPU占用率最高的10个线程（不含表头）
                process->start("sh", QStringList() << "-c" << "top -H -b -n 1 -o %CPU");
                connect(process, SIGNAL(finished(int, QProcess::ExitStatus)), this, SLOT(onProcessCalcFinished(int, QProcess::ExitStatus)));
            }

#endif
        }
        previousCpuData = currentCpuData;
    }
}

void CpuMonitor::onProcessCalcFinished(int, QProcess::ExitStatus)
{
    QProcess *process = qobject_cast<QProcess *>(sender());
    QString data = process->readAllStandardOutput();
    process->deleteLater();
    QStringList lines = data.split('\n', QString::SkipEmptyParts);
    // 跳过表头（前6行），只输出后面前10行
    int count = 0;
    for (int i = 6; i < lines.size() && count < 10; ++i) {
        qDebug() << lines[i];
        count++;
    }
}

CpuMonitor::CpuMonitor()
{
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &CpuMonitor::fetchCpuStats);
}

CpuMonitor::~CpuMonitor()
{

}

CpuMonitor &CpuMonitor::getInstance()
{
    static CpuMonitor obj;
    return obj;
}

void CpuMonitor::Start(qint32 msec)
{
    if (timer != NULL)
    {
        timer->start(msec); 
    }
}