#include "cpcrwindow.h"

#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "ccommunicationobject.h"
#include"control/coperationunit.h"
CPcrWindow::CPcrWindow(QObject *parent) : CWindowObject(parent)
{

}

CPcrWindow::~CPcrWindow()
{
    m_bThreadExit = true;
}

void CPcrWindow::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
            m_iMethodID = GetByte2Int(m_pFramePos);
            m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
            m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
            m_pFramePos = qMessage.data() + gk_iSeqPos;
            m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
            m_iReadPayloadLength = GetByte2Int(qMessage.data() + gk_iLengthPos);
            m_qPayloadByteArray = qMessage.mid(gk_iFrameDataPos, m_iReadPayloadLength);
            m_qPayloadString = QString::fromLocal8Bit(m_qPayloadByteArray);
            m_qPayloadString = m_qPayloadString.replace("[", "");
            m_qPayloadString = m_qPayloadString.replace("]", "");
            // CGlobalConfig::getInstance().printMessageInfo(qMessage,
            //                                               "[pcr->server] ");
            if(m_iMethodID !=Method_upgrade_data)
            {
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_UpperHost);
            }
            switch (m_iMethodID)
            {
            case Method_TEC_PCR_StartOrStop:
            case Method_TEC_PCR_SignalReport:
            case Method_TEC_RequestTransmitTimingTable:
            case Method_TEC_TransmitTimingData:
            case Method_TEC_TransmitTimingEnd:
            case Method_FLCYEND:
            case Method_FLGAIN:
            case Method_FLMDT:
            case Method_pause:
            case Method_stop:
            case Method_resume:
            {
                qDebug()<<"CPCRWindow Deal with PCR cmd:"<<m_iMethodID<<m_qPayloadString;
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_error_info:
            {
                //错误处理
                qDebug()<<"cpcrwindow Deal with Method_error_info :"<<m_iMethodID<<m_qPayloadString;
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_TEC_PCR_RunInfo:
            {              
                QStringList strList = m_qPayloadString.split(",");
                if(strList.size()!=4)
                {
                    qDebug()<<__FUNCTION__<<"PCR Temp Error,m_qPayloadString="<<m_qPayloadString;
                }        
                break;
            }
            case Method_upgrade_data:
            {
                qDebug()<<"enter Method_upgrade_data-pcr";
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_board_info:
            {
                qDebug()<<"enter Method_board_info-pcr";
                CCommunicationObject::getInstance().sendMessageToMachine(qMessage, Machine_Middle_Host);
                break;
            }
            case Method_upgrade_end:
            {
                qDebug()<<"m_iSourceID="<<m_iSourceID<<",m_iResult"<<m_iResult;
                emit sigUpgradeEndMsg(EnumMachineID(m_iSourceID),m_iResult);
                //QString strStatus = QString("%1,%2").arg("0").arg("");//状态IDLE目前暂用0替代,information为空
                // COperationUnit::getInstance().sendStringData(Method_unit_status, strStatus, m_iSourceID);
                break;
            }
            }
        }
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
    }
}
