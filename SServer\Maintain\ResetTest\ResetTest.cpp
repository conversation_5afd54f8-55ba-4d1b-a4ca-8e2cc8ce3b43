#include<qdebug.h>
#include<QTime>
#include<algorithm>
#include "ResetTest.h"
#include "publicconfig.h"
#include "consumables/consumables.h"
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include "cglobalconfig.h"
#include "error/cerrorhandler.h"
#include "error/cerrornotify.h"
#include "affair/caffair.h"
#define MAGIC_ENUM_RANGE_MAX 2550
#include "magic_enum/magic_enum.hpp"

CResetTest::CResetTest()
{
    _init();
}

CResetTest::~CResetTest()
{

}

void CResetTest::_init()
{
    const QList<quint16> listBoardZInit{ Action_Board1ZInit,Action_Board2ZInit,Action_Board3ZInit,Action_Board4ZInit };              // 第二阶段(板1Z初始化 + 板2Z初始化 + 板3z初始化)  
    const QList<quint16> listBoardXYInit{ Action_Board1RemainInit,Action_Board2RemainInit,Action_Board3RemainInit,Action_Board4RemainInit };                                    // 第三阶段(板1其他电机初始化 + 板4其他电机初始化)

    // 时序完成结果检查函数映射表
    m_stepValidators[Action_Board1ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board2ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board3ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board4ZInit]           = [this](quint16 uiComplexID) { return _checkAllBoardZInit(uiComplexID); };
    m_stepValidators[Action_Board1RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit(uiComplexID); };
    m_stepValidators[Action_Board2RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit(uiComplexID); };
    m_stepValidators[Action_Board3RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit(uiComplexID); };
    m_stepValidators[Action_Board4RemainInit]      = [this](quint16 uiComplexID) { return _checkAllBoardXYInit(uiComplexID); };

    //时序执行函数映射表
    m_stepExecutors[Action_Board1ZInit]            = [this]() { _onBoard1ZInit(); };
    m_stepExecutors[Action_Board2ZInit]            = [this]() { _onBoard2ZInit(); };
    m_stepExecutors[Action_Board3ZInit]            = [this]() { _onBoard3ZInit(); };
    m_stepExecutors[Action_Board4ZInit]            = [this]() { _onBoard4ZInit(); };
    m_stepExecutors[Action_Board1RemainInit]       = [this]() { _onBoard1RemainInit(); };
    m_stepExecutors[Action_Board2RemainInit]       = [this]() { _onBoard2RemainInit(); };
    m_stepExecutors[Action_Board3RemainInit]       = [this]() { _onBoard3RemainInit(); };
    m_stepExecutors[Action_Board4RemainInit]       = [this]() { _onBoard4RemainInit(); };

    
    _initHash("listBoardZInit",m_hashZInit,listBoardZInit);
    _initHash("listBoardXYInit",m_hashXYInit,listBoardXYInit);

    //复位流程节点(支持并行)
    // 1、第二阶段(板1Z初始化 + 板2Z初始化 + 板3z初始化 + 板4z初始化    )  
    m_listRunningStep.append(listBoardZInit);  
    // 2、第三阶段(板1其他电机初始化 + 板2其他电机初始化 + 板3其他电机初始化 + 板4其他电机初始化)
    m_listRunningStep.append(listBoardXYInit);                
}

void CResetTest::_initHash(QString strName,QHash<quint16, bool>& hash,QList<quint16> list)
{
    hash.clear();
    for (auto id:list)
    {
        hash[id] = false;
    }
    qDebug()<<"_initHash"<<strName<<list.size();
}

void CResetTest::_resetInitHash(QString strName, QHash<quint16, bool>& hash)
{
    for (auto& value : hash) {
        value = false;
    }
    qDebug()<<"_resetInitHash"<<strName<<hash.keys();
}

bool CResetTest::_checkBoard(QString str, QHash<quint16, bool> &hash, quint16 uiComplexID)
{
    if (!hash.contains(uiComplexID))
    {
        qDebug()<<str<<" not contains"<<uiComplexID;
        return false;
    }
    
    hash[uiComplexID] = true;
    auto values = hash.values();
    bool allTrue = std::all_of(values.begin(), values.end(), 
                               [](const bool& value) { return value; });
    
    qDebug()<<"_checkBoard:"<<uiComplexID<<str<<allTrue<<hash.keys()<<hash.values();
    return allTrue;    
}

bool CResetTest::_checkAllBoardZInit(quint16 uiComplexID)
{
    return _checkBoard("_checkAllBoardZInit",m_hashZInit,uiComplexID);
}

bool CResetTest::_checkAllBoardXYInit(quint16 uiComplexID)
{
    return _checkBoard("_checkAllBoardXYInit",m_hashXYInit,uiComplexID);
}

void CResetTest::_clearRemainingSteps()
{
    m_uiCurrentStageIndex = 0;  
    m_allStepIds.clear();
    _onUpdateMiddleHostStatus();
    COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(ST_RESET), Machine_UpperHost, 1); 
    if (ST_RESET != static_cast<SeqType>(m_uiSeqType))
    {
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_Middle_Host, 1);
    }
    qDebug()<<"_clearRemainingSteps"<<m_allStepIds.size();
}

void CResetTest::_resetStep()
{
    m_uiCurrentStageIndex = 0; 
    m_allStepIds.clear();
    for (const auto& stage : m_listRunningStep) {
        m_allStepIds.unite(QSet<quint16>::fromList(stage));
    }
    _resetInitHash("hashZInit",m_hashZInit);
    _resetInitHash("hashXYInit",m_hashXYInit);
    qDebug()<<"_resetStep"<<m_allStepIds.size();      
}

void CResetTest::_executeCurrentStage()
{
    if (m_allStepIds.isEmpty()) {
        qDebug() << "ResetTest completed: All stages finished";
        return;
    }

    for (size_t i = m_uiCurrentStageIndex; i < m_listRunningStep.size(); i++)
    {
        // 执行新的阶段
        for (auto step:m_listRunningStep[i])
        {
            m_stepExecutors[step]();
        }
        m_uiCurrentStageIndex++;
        break;        
    }
    qDebug()<<"_executeCurrentStage:"<<m_uiCurrentStageIndex;
}

void CResetTest::_onBoardAction(const QString strName,quint16 uiComplexID, QString strParam)
{
    QString strCommandStr = QString::number(uiComplexID);
    int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(strCommandStr);
    COperationUnit::getInstance().sendStringData(Method_comp_cmd, strCommandStr+strParam, iMachineID);     
    qDebug()<<"_onBoardAction "<<strName<<uiComplexID<<strParam<<iMachineID;
}

void CResetTest::_onBoard1ZInit()
{
    _onBoardAction("_onBoard1ZInit",Action_Board1ZInit,"");
}

void CResetTest::_onBoard2ZInit()
{
    _onBoardAction("_onBoard2ZInit",Action_Board2ZInit,"");
}

void CResetTest::_onBoard3ZInit()
{
    _onBoardAction("_onBoard3ZInit",Action_Board3ZInit,"");
}

void CResetTest::_onBoard4ZInit()
{
    _onBoardAction("_onBoard4ZInit",Action_Board4ZInit,"");
}

void CResetTest::_onBoard1RemainInit()
{
    _onBoardAction("_onBoard1RemainInit",Action_Board1RemainInit,"");
}

void CResetTest::_onBoard2RemainInit()
{
    _onBoardAction("_onBoard2RemainInit",Action_Board2RemainInit,"");
}

void CResetTest::_onBoard3RemainInit()
{
    _onBoardAction("_onBoard3RemainInit",Action_Board3RemainInit,"");
}

void CResetTest::_onBoard4RemainInit()
{
    _onBoardAction("_onBoard4RemainInit",Action_Board4RemainInit,"");
}

void CResetTest::_sendSelfResetResult(quint16 uiResult)
{
    if (ST_RESET == static_cast<SeqType>(m_uiSeqType))
    {
        _onUpdateMiddleHostStatus();
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_UpperHost, uiResult); 
    }
    else
    {
        COperationUnit::getInstance().sendStringResult(Method_start, QString("%1:").arg(m_uiSeqType), Machine_Middle_Host, uiResult);
    }
    qDebug()<<"_sendSelfResetResult:";
}

void CResetTest::_onUpdateMiddleHostStatus()
{
    QString strAction = "";
    auto action = magic_enum::enum_cast<RunStat>(RST_IDLE);
    if (action.has_value()) {
        auto action_name = magic_enum::enum_name(action.value());
        strAction = action_name.data();
    }    
    COperationUnit::getInstance().sendStringData(Method_status, strAction, Machine_Middle_Host);
    qDebug()<<"_onUpdateMiddleHostStatus:";
}

void CResetTest::StartResetTest(quint16 uiSeqType)
{
    m_uiSeqType = uiSeqType;
    m_isAborted = false;     // 重置异常标志 
    _resetStep();            // 重置步骤
    SendGainOptoStatusCommand();
    SendElecMagneticLockCommand();
    _executeCurrentStage();//开始执行自检第一阶段
    qDebug()<<"StartResetTest:";
}

void CResetTest::SendGainOptoStatusCommand()
{
    // 获取光耦状态
    COperationUnit::getInstance().sendStringData(Method_AllOptoStatus, "", Machine_Motor_1); // 获取板卡1全部光耦状态    
    COperationUnit::getInstance().sendStringData(Method_AllOptoStatus, "", Machine_Motor_3); // 获取板卡3全部光耦状态
    qDebug() << "ResetTest SendGainOptoStatusCommand";
}

void CResetTest::SendElecMagneticLockCommand()
{
    Consumables::getInstance().CheckConsumableBoxStatus();//检查试剂，准备上锁和灯
    qDebug() << "ResetTest SendElecMagneticLockCommand";       
}

void CResetTest::HandleTimeseqReply(quint16 uiComplexID, quint16 uiResult)
{
    qDebug() << "ResetTest HandleTimeseqReply: ID=" << uiComplexID 
             << "result=" << uiResult
             << "aborted=" << m_isAborted 
             << "m_allStepIds" << m_allStepIds
             << "m_stepValidators" << m_stepValidators.keys();
    // 异常已触发，直接返回
    if (m_isAborted) return;

    // 步骤不在运行列表中，忽略
    if (!m_allStepIds.contains(uiComplexID)) return;

    // _reportCurrentStage(uiComplexID,uiResult);// 上报结果
    
    // 1. 检查步骤执行结果（uiResult非0表示失败）
    if (uiResult != 0) {
        qWarning() << "ResetTest self step result failed" << uiComplexID;
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SystemSelfCheckFailed, QString("ComplexID %1 failed.").arg(uiComplexID));
        if (!m_isAborted)//只发送一次
        {
            _clearRemainingSteps();
        }
        m_isAborted = true;
        return;
    }

    bool isValidAll = false;
    // 2. 检查是否需要特殊验证（从映射表获取验证函数）
    if (m_stepValidators.contains(uiComplexID)) {
        isValidAll = m_stepValidators[uiComplexID](uiComplexID);  // 执行验证函数
    }

    // 3. 正常完成：从运行列表移除步骤
    m_allStepIds.remove(uiComplexID);

    qDebug() << "ResetTest _handleSelfTimeseqReply step" << uiComplexID << "m_allStepIds.size:" << m_allStepIds.size()<<"m_uiCurrentStageIndex:"<<m_uiCurrentStageIndex<<"isValidAll:"<<isValidAll;    

    //执行下一阶段
    if (isValidAll)
    {
        _executeCurrentStage();
    }

    // 如果所有步骤都执行完成，则发送结果
    if (m_allStepIds.isEmpty())
    {
        auto valuesZ = m_hashZInit.values();
        bool allTrueZ = std::all_of(valuesZ.begin(), valuesZ.end(), 
                                [](const bool& value) { return value; });

        auto valuesXY = m_hashXYInit.values();
        bool allTrueXY = std::all_of(valuesXY.begin(), valuesXY.end(), 
                               [](const bool& value) { return value; });
                               
        if (allTrueZ && allTrueXY)
        {
            _sendSelfResetResult(uiResult);
        }
        qDebug()<<"ResetTest completed:"<<allTrueZ<<allTrueXY<<valuesZ<<valuesXY;
    }
    qDebug() << "ResetTest _handleSelfTimeseqReply finished";   
}