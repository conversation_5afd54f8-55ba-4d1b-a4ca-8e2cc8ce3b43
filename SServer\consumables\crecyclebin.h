#ifndef CRECYCLEBIN_H
#define CRECYCLEBIN_H
#include <qglobal.h>

enum RecycleBinType
{
    RBT_FRONT_BIN = 0, //前面的Tip、tube废弃仓
    RBT_BACK_BIN, //后面的pcr tube废弃仓
};

class CRecycleBin
{

public:
    explicit CRecycleBin(int iID,QString strKey,quint16 uiMaxCapacity);
    ~CRecycleBin();

    static CRecycleBin& frontBin();
    static CRecycleBin& backBin();

    // 获取下一个操作的位置
    int getNextOperationIndex() const;

    // 使用下一个操作位置并更新
    void useNextOperationIndex();

    // 装载耗材，将余量设置为最大容量
    void reload();

    // 设置容量，一般外部不直接设置，这里仅为演示添加
    void setCapacity(int capacity);

    // 获取余量
    int getRemaining() const;

    // 使用耗材，假设每次使用减少固定数量，这里简化处理，实际应用中可能需要更复杂的逻辑
    bool use(int amount);

    void unload();

    bool getAvr();

     /**
     * @brief isResourceEnough 计算当前批次pcr耗材回收是否充足
     * @param uiTotalUseSize   需要使用的回收空间数量
     * @return
     */
    bool isResourceEnough(quint8 uiTotalUseSize);
private:
     /**
     * @brief _updateConsumeInfo 更新回收仓使用信息
     * @param 
     * @return
     */
    void _updateConsumeInfo();    
private:
    int m_iID;//回收仓ID
    int m_iRemaining; // 当前余量
    int m_iCapacity;  // 最大容量
    int m_iNextOperationIndex; // 下一次操作位置
    bool m_bAvr;//是否可用
    static CRecycleBin* m_pFrontInstance;
    static CRecycleBin* m_pBackInstance;
};

#endif // CRECYCLEBIN_H
