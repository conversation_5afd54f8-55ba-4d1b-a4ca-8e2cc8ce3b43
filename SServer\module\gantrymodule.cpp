#include "gantrymodule.h"

#include <QCanBusDevice>
#include <QJsonArray>
#include <algorithm>
#include "consumables/reagent.h"
#include "consumables/consumables.h"
#include "publicconfig.h"
#include "cglobalconfig.h"
#include "consumables/crecyclebin.h"
#include "datacontrol/cprojectdb.h"
#include "datacontrol/csystemdb.h"

GantryModule::GantryModule(bool bUseThread, quint8 uiCatchType)
    :DeviceModule("GantryModule",bUseThread), m_uiCatchType(uiCatchType)
{

}

void GantryModule::SetCatchType(quint8 uiCatchType)
{
    m_uiCatchType = uiCatchType; // 新增设置 m_uiCatchType 变量的函数
}

SubPackData GantryModule::_GetSubPackDataMaxRow(QVector<SubPackData>& subPackVect)
{
    if (subPackVect.isEmpty())
    {
        qDebug()<<"_GetSubPackDataMaxRow subPackVect is empty ";
        return SubPackData();
    }
    // 查找最大位置信息
    auto maxElement = std::max_element(subPackVect.begin(), subPackVect.end(),
    [](const SubPackData& a, const SubPackData& b) {
        return a.uiRowIndex < b.uiRowIndex;
    });
    SubPackData data = *maxElement;    
    qDebug()<<"_GetSubPackDataMaxRow"<<subPackVect.size()<<data.uiRowIndex<<data.strProjID;
    return data;
}

void GantryModule::_PunchAndSubPackReagent()
{
    //获取所有需要分装的试剂信息，进行刺破
    QVector<SubPackData> subPackVect = Reagent::getInstance().GetReagentBallSubPackDatasVect();
    qDebug()<<"_PunchAndSubPackReagent GetReagentBallSubPackDatasVect"<<subPackVect.size();
    for(int i =0;i<subPackVect.size();i++)
    {
        SubPackData data = subPackVect.at(i);
        data.bNeedOpenCap = data.bNeedCloseCap = false;
        if(i ==0)
        {
            data.bNeedOpenCap = true;
        }
        if(i == (subPackVect.size()-1))
        {
            data.bNeedCloseCap = true;
        }
        _AddSubPackPunchTask(data);
    }
    
    if (!subPackVect.isEmpty())
    {
        SubPackData data = _GetSubPackDataMaxRow(subPackVect);
        Reagent::getInstance().UpdateReagentPunchInfo(data);// 更新试剂刺破最新信息        
    }

    //获取所有分装试剂分组信息，按组别进行分装，同一类别使用同一个Tip
    QMap<QString, QMap<quint8, QVector<SubPackData>>> subPackMap = Reagent::getInstance().GetReagentBallSubPackDatas();
    qDebug()<<"_PunchAndSubPackReagent GetReagentBallSubPackDatas"<<subPackMap.size()<<subPackMap.keys();
    foreach (const QString &key, subPackMap.keys())
    {
        QMap<quint8, QVector<SubPackData>> &innerMap = subPackMap[key];
        QVector<SubPackData> qDatas;

        foreach(const quint8 & innerKey, innerMap.keys())
        {
            _AddGetTipTask(Action_GetTip);//获取TIP
            foreach(auto data, innerMap[innerKey])//同类分装
            {
                _AddSubPackReagentTask(data);
                qDatas.append(data);
            }
            _AddAbandonTipTask(Action_AbandonTip);//丢弃TIP  
            qDebug()<<"_PunchAndSubPackReagent innerKey"<<innerKey;
        }
        _AddSubTask("", Action_SubpackFinish);
        SubPackData data = _GetSubPackDataMaxRow(qDatas);
        Reagent::getInstance().UpdateReagentStatus(data);// 更新试剂有效期(更新复溶信息) 
        qDebug()<<"_PunchAndSubPackReagent innerMap keys"<<innerMap.keys();
    }
}

void GantryModule::SlotAddSubTask(quint8 uiSubTaskID, const QString &strCommandStr, const QString &strParamStr)
{
    DeviceModule::SlotAddSubTask(uiSubTaskID, strCommandStr, strParamStr);
    // 可以在这里添加子类特有的逻辑
    qDebug() << "SampleModule adding task with specific logic (uiSubTaskID: " << uiSubTaskID << ", strCommandStr: " << strCommandStr << ", strParamStr: " << strParamStr << ")";

}

void GantryModule::SlotAddTask(const CmdTask &task)
{
    // 可以在这里添加子类特有的逻辑
    qDebug() << "GantryModule adding task with specific logic (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
    // 调用基类的添加任务函数
    DeviceModule::SlotAddTask(task);
}

void GantryModule::_ProcessSubTask()
{
    while (m_qWaitProcessSubTask.size()>0)
    {
        CmdTask task = m_qWaitProcessSubTask.front();
        qDebug() << "GantryModule adding task with specific logic (uiSubTaskID: "
                 << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ")";
        switch (task.uiSubTaskID)
        {
        case GTI_GET_TIP1000:
        {
            _AddGetTip1000Task(task.strParamStr);
            break;
        }            
        case GTI_SAMPLING:
        {
            _AddSamplingTask(task.strParamStr);
            break;
        }
        case GTI_SPIT_SAMPLE:
        {
            _AddSpitSampleTask(task.strParamStr);
            break;
        }
        case GTI_TRANS_CLEVAGE_REAGENT:
        {
            _AddTransClevageReagentTask(task.strParamStr);
            break;
        }
        case GTI_PUNCH_AND_SUB_PACK_REAGENT:
        {
            _PunchAndSubPackReagent();
            break;
        }
        case GTI_TRANS_REAGENT:
        {
            _AddTransReagentTask(task.strParamStr);
            break;
        }
        case GTI_TRANS_PURIFY:
        {
            _AddTransPurifyTask(task.strParamStr);
            break;
        }
        case GTI_CAP_AND_TRANS_TUBE:
        {
            _AddCapAndTransTubeTask(task.strParamStr);
            break;
        }
        case GTI_CAP_AND_ABANDON:
        {
            _AddSubTask(task.strParamStr, Action_Board2PumpCapTubeAndAbandon);
            break;
        }
        case GTI_ABANDON_AND_INIT:
        {
            _AddSubTask("", Action_Board2PumpAbandonAndInit);
            break;
        }
        case GTI_GET_STANDARD_TIP200:
        {
            _AddGetTipTask(Action_GetStandardTip200,task.strParamStr);//获取TIP
            break;
        }        
        case GTI_ABANDON_STANDARD_TIP200:
        {
            _AddAbandonTipTask(Action_AbandonStandardTip200);//丢弃TIP
            break;
        }
        case GTI_TRANS_TUBE:
        {
            _AddTransTubeTask(task.strParamStr);
            break;
        }
        case GTI_GET_TIP200:
        {
            _AddSubTask(task.strParamStr, Action_GetTip200);
            break;
        }        
        case GTI_ABANDON_TIP200:
        {
            CRecycleBin::frontBin().use(m_uiCatchType+1);
            _AddSubTask(task.strParamStr, Action_AbandonTip200);
            break;
        }
        case GTI_GET_CROSS_TIP200:
        {
            _AddSubTask(task.strParamStr, Action_GetCrossTip200);
            break;
        }
        case GTI_SUCK_REAGENT:
        {
            _AddSubTask(task.strParamStr, Action_SwReagentSuck);
            break;
        }
        case GTI_SPIT_REAGENT:
        {
            _AddSubTask(task.strParamStr, Action_SwReagentSpit);
            break;
        }
        case GTI_SUCK_PURIFY_OIL:
        {
            _AddSubTask(task.strParamStr, Action_SwPurifySuck);
            break;
        }
        case GTI_SPIT_PURIFY:
        {
            _AddSubTask(task.strParamStr, Action_SwPurifySpit);
            break;
        }
        case GTI_SPIT_OIL:
        {
            _AddSubTask(task.strParamStr, Action_SwOilSpit);
            break;
        }
        case GTI_GET_PCR_CAP:
        {
            _AddSubTask(task.strParamStr, Action_GetPcrCap);
            break;
        }
        case GTI_CAP_TO_TUBE:
        {
            _AddSubTask(task.strParamStr, Action_CapToTube);
            break;
        }
        case GTI_TRANS_TUBE_CAP:
        {
            _AddSubTask(task.strParamStr, Action_TransTubeCap);
            break;
        }
        default:
            break;
        }
        m_qWaitProcessSubTask.pop_front();
    }
}

void GantryModule::_AddGetTip1000Task(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_SingleSample1000Tip);
        sTask.strParamStr = strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_DoubleSample1000Tip);
        sTask.strParamStr = strParam;
    }
    qDebug()<<"GantryModule::_AddGetTip1000Task"<<sTask.strCommandStr<<strParam;
    DeviceModule::SlotAddTask(sTask);
}

void GantryModule::_AddSamplingTask(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_Sampling);
        sTask.strParamStr = strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_SamplingDouble);
        sTask.strParamStr = strParam;
    }
    qDebug()<<"GantryModule::AddSamplingTask"<<sTask.strCommandStr<<strParam;
    DeviceModule::SlotAddTask(sTask);
}

void GantryModule::_AddSpitSampleTask(QString strParam)
{
    qDebug()<<"GantryModule::AddSpitSampleTask"<<strParam;
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_SpitSample);
        sTask.strParamStr= strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_SpitSampleDouble);
        sTask.strParamStr= strParam;
    }
    DeviceModule::SlotAddTask(sTask);
}

void GantryModule::_AddTransClevageReagentTask(QString strParam)
{
    qDebug()<<"GantryModule::AddTransClevageReagentTask"<<strParam;
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_TransClevage);
        sTask.strParamStr = strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_TransClevageDouble);
        sTask.strParamStr = strParam;
    }
    DeviceModule::SlotAddTask(sTask);
}

void GantryModule::_AddSubPackReagentTask(SubPackData data)
{
#if 0
    CmdTask sTask = {};
    sTask.bSync = false;
    //5 params:
    Consumables* pConsumables = &Consumables::getInstance();
    quint8 uiTipRowIndex =0;
    quint8 uiTipColumnIndex = 0;
    pConsumables->GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
    if(data.bNeedPunchDuilent)//帶刺破復溶液的試劑分裝
    {
        if(pConsumables->IsConsumableCurBoxIndexNoOne(CT_TIP))
        {
            sTask.strCommandStr = QString::number(Action_SubPackReagentPunchDiluent1);//第一個TIP盒取TIP加分裝試劑
            sTask.strParamStr = QString("%1,%2,%3,%4,%5").arg(data.uiColumnIndex).
                    arg(data.uiDuilentRowIndex).arg(data.uiRowIndex).
                    arg(uiTipRowIndex).arg(uiTipColumnIndex);
        }
        else
        {
            sTask.strCommandStr = QString::number(Action_SubPackReagentPunchDiluent2);//第二個Tip盒取TIP加分裝試劑
            sTask.strParamStr = QString("%1,%2,%3,%4,%5").arg(data.uiColumnIndex).
                    arg(data.uiDuilentRowIndex).arg(data.uiRowIndex).
                    arg(uiTipRowIndex).arg(uiTipColumnIndex);
        }
    }
    else//不帶刺破復溶液的試劑分裝
    {
        if(pConsumables->IsConsumableCurBoxIndexNoOne(CT_TIP))
        {
            sTask.strCommandStr = QString::number(Action_SubPackReagentNoPunchDiluent1);//第一個TIP盒取TIP加分裝試劑
            sTask.strParamStr = QString("%1,%2,%3,%4").arg(data.uiColumnIndex).
                    arg(data.uiRowIndex).arg(uiTipRowIndex).arg(uiTipColumnIndex);
        }
        else
        {
            sTask.strCommandStr = QString::number(Action_SubPackReagentNoPunchDiluent2);//第二個Tip盒取TIP加分裝試劑
            sTask.strParamStr = QString("%1,%2,%3,%4").arg(data.uiColumnIndex).
                    arg(data.uiRowIndex).arg(uiTipRowIndex).arg(uiTipColumnIndex);
        }
    }
    DeviceModule::SlotAddTask(sTask);
#endif

    const quint8 kCapacity = 100;//容量系数
    qint32 iPackReagentCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(data.strProjID,CProjectDBImpl::FieldName_ProjectInfo::complexSolutionCalibrationCapacity)*kCapacity;
    if (iPackReagentCapacity == 0)
    {
        iPackReagentCapacity = 320*kCapacity;
    }

    CmdTask sTask = {};
    sTask.bSync = false;
    //M1 3 params: 试剂columnIndex, 复溶液RowIndex,冻干球rowIndex
    sTask.strCommandStr = QString::number(Action_SubpackReagent);//分裝試劑
    sTask.strParamStr = QString(",%1,%2,%3,%4").arg(data.uiColumnIndex)
                                               .arg(data.uiDuilentRowIndex)
                                               .arg(data.uiRowIndex)
                                               .arg(CSystemDB::getInstance().getIntValueFromKey("ResolutionReagentPosition"));
    sTask.strParamStr+= QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
    sTask.strParamStr += QString (",%1").arg(CGlobalConfig::getInstance().getBlockedNeedleDetectFlag());
    sTask.strParamStr += QString (",%1").arg(iPackReagentCapacity/2);

    DeviceModule::SlotAddTask(sTask);
    qDebug()<<"GantryModule::AddSubPackReagentTask"<<Action_SubpackReagent<<sTask.strParamStr;
}

void GantryModule::_AddTransReagentTask(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_SSwReagent);
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_DSwReagent);
    }

    sTask.strParamStr = strParam;
    Consumables::getInstance().Consume(CT_TIP);
    Consumables::getInstance().Consume(CT_TUBE);
    Consumables::getInstance().Consume(CT_CAP);
    DeviceModule::SlotAddTask(sTask);
    qDebug()<<"GantryModule::AddTransReagentTask"<<sTask.strCommandStr<<sTask.strParamStr;
}

void GantryModule::_AddTransPurifyTask(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_SSwPurifyOraffin);
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_DSwPurifyOraffin);
    }

    sTask.strParamStr = strParam;
    DeviceModule::SlotAddTask(sTask);
    CRecycleBin::frontBin().use(m_uiCatchType+1);
    qDebug()<<"GantryModule::AddTransPurifyTask"<<sTask.strCommandStr<<sTask.strParamStr;
}

void GantryModule::_AddCapAndTransTubeTask(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_CapAndTransTube);
        sTask.strParamStr = strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_CapAndTransTubeDouble);
        sTask.strParamStr = strParam;
    }
    DeviceModule::SlotAddTask(sTask);
}

void GantryModule::_AddSubPackPunchTask(SubPackData data)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(data.bNeedPunchDuilent)
    {
        sTask.strCommandStr = QString::number(Action_SubPackPunch1);//需刺破复溶液
        sTask.strParamStr = QString(",%1,%2,%3").arg(data.uiColumnIndex).arg(data.uiRowIndex).arg(data.uiDuilentRowIndex);
        sTask.strParamStr += QString(",%1,%2").arg(data.bNeedOpenCap).arg(data.bNeedCloseCap);
        qDebug()<<"AddSubPackPunchTask"<<"columnIndex"<<data.uiColumnIndex
               <<"rowIndex"<<data.uiRowIndex<<"duilentIndex"<<data.uiDuilentRowIndex
              <<"openCap: "<<data.bNeedOpenCap<< " closeCap: "<<data.bNeedCloseCap;
    }
    else
    {
        sTask.strCommandStr = QString ::number(Action_SubPackPunch2);//无需刺破复溶液
        sTask.strParamStr = QString(",%1,%2").arg(data.uiColumnIndex).arg(data.uiRowIndex);
        sTask.strParamStr += QString(",%1,%2").arg(data.bNeedOpenCap).arg(data.bNeedCloseCap);
        qDebug()<<"AddSubPackPunchTask"<<"columnIndex"<<data.uiColumnIndex<<"rowIndex"<<data.uiRowIndex
                  <<"openCap: "<<data.bNeedOpenCap<< " closeCap: "<<data.bNeedCloseCap;;
    }
    DeviceModule::SlotAddTask(sTask);
    qDebug()<<"GantryModule::AddSubPackPunchTask"<<sTask.strCommandStr<<sTask.strParamStr;
}

void GantryModule::_AddGetTipTask(quint16 uiType,QString strSingleRightParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    quint8 uiTipRowIndex =0;
    quint8 uiTipColumnIndex = 0;
    Consumables::getInstance().SetCatchType(CT_TIP, CT_SINGLE);
    Consumables::getInstance().GetNextAvrConsumable(CT_TIP, uiTipRowIndex, uiTipColumnIndex);
    quint8 uiBoxIndex = Consumables::getInstance().GetConsumableBoxIndex(CT_TIP);
#ifdef M1
    QString strParam = QString(",%1,%2").arg(uiTipColumnIndex).arg(uiTipRowIndex);
    sTask.strParamStr = strParam;
#else
    QString strParam = QString(",%1,%2,%3").arg(uiBoxIndex).arg(uiTipColumnIndex).arg(uiTipRowIndex);
    strParam += QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
    strParam += strSingleRightParam;// 是否是右边移液泵
    sTask.strParamStr = strParam;
#endif
    sTask.strCommandStr = QString::number(uiType);//第一個TIP盒取TIP加分裝試劑
    Consumables::getInstance().Consume(CT_TIP);
    DeviceModule::SlotAddTask(sTask);
    qDebug()<<"GantryModule::AddGetTipTask"<<sTask.strCommandStr<<sTask.strParamStr;
}

void GantryModule::_AddAbandonTipTask(quint16 uiType)
{
    QString strParam = QString (",%1").arg(CGlobalConfig::getInstance().getPumpDetectFlag());
    strParam += QString(",%1").arg(CRecycleBin::frontBin().getNextOperationIndex());//添加耗材丢弃位参数
    _AddSubTask(strParam, uiType);
    CRecycleBin::frontBin().use(m_uiCatchType+1);
}

void GantryModule::_AddTransTubeTask(QString strParam)
{
    CmdTask sTask = {};
    sTask.bSync = false;
    if(m_uiCatchType == CT_SINGLE)
    {
        sTask.strCommandStr = QString::number(Action_AmplTransTube);
        sTask.strParamStr = strParam;
    }
    else if (m_uiCatchType == CT_DOUBLE)
    {
        sTask.strCommandStr = QString::number(Action_AmplTransTubeDouble);
        sTask.strParamStr = strParam;
    }
    DeviceModule::SlotAddTask(sTask);    
}
