#ifndef SAMPLEAMPLIFICATE_H
#define SAMPLEAMPLIFICATE_H


#include<QObject>
#include<QVector>
#include<QQueue>
#include<QMutex>
#include<QList>
#include<QMap>

class SampleAmplificate : public QObject
{
    Q_OBJECT

public:
    struct AmplificateSampleInfo
    {
        quint8 uiTubeRowIndex;         // PCR管位置行索引
        quint8 uiTubeColumnIndex;      // PCR管位置本列索引
        quint8 uiTubeAreaIndex;        // PCR管位置区域索引
        quint8 uiPcrAreaPosRowIndex;   // 所有扩增区域位置行索引
        quint8 uiPcrAreaPosColumnIndex;// 所有扩增区域位置本列索引 
        quint8 uiAmplifyAreaIndex;     // 所有扩增区域位置区域索引
        quint8 uiAmplifyRowIndex;      // 界面数据解析后行索引
        quint8 uiAmplifyColumnIndex;   // 界面数据解析后本列索引 
        quint8 uiPcrPosRowIndex;       // 单个扩增区域位置行索引
        quint8 uiPcrPosColumnIndex;    // 单个扩增区域位置本列索引 
        quint8 iSampleSize;            // 当前批次样本数量
        quint8 iStandardSampleSize;    // 当前批次样本数量
        QString strSampleID;           // 样本ID
        QString strProjectID;          // 项目ID
        bool bNeedOpenCap;             // 是否需要打开PCR区域盖子
        bool bNeedCloseCap;            // 是否需要关闭PCR区域盖子
        bool bLastOneInCurTecBatch;    // 是否为该批次特定TEC时序的最后一个操作元素,是的话会需要关闭pcr区域盖子
        quint8 uiCompNum;              //组分数量
    };

public:
    explicit SampleAmplificate(QObject *parent = nullptr);

public:
    /**
    * @brief ParseBatchInfo 解析扩增例程样本信息
    * @param strBatchInfo 样本信息字符串 
    * @return
    */
    bool ParseSampleInfo(QString &strBatchInfo);

    /**
    * @brief GetAmplificateSampleInfo 获取扩增例程样本信息
    * @param info 获取的样本信息
    * @return 是否获取成功
    */
    bool GetNextAmplificateSampleInfo(AmplificateSampleInfo &info);

    /**
    * @brief UpdateCurAmplificateSampleInfo 更新扩增例程样本信息
    * @param info 更新样本信息
    * @return 是否更新成功
    */
    bool UpdateCurAmplificateSampleInfo(AmplificateSampleInfo &info);

    /**
    * @brief GetAmplificateSampleInfoSize 获取还没测试的样本信息数量
    * @param 
    * @return 样本信息数量
    */
    quint8 GetAmplificateSampleInfoSize(); 

    /**
    * @brief GetAmplificateSampleInfoUsedSize 获取已使用的样本信息数量
    * @param 
    * @return 样本信息数量
    */
    quint8 GetAmplificateSampleInfoUsedSize(); 

    /**
    * @brief GetTecName 获取扩增时序名称
    * @param 
    * @return 扩增时序名称
    */
    QString GetTecName(){ return strTecName;} 

    /**
    * @brief GetRunStatus 获取单例程执行状态
    * @param 
    * @return 是否正在运行
    */
    bool GetRunStatus(); 

    /**
    * @brief ResetRunStatus 重置单例程执行状态
    * @return 
    */
    void ResetRunStatus(); 

    /**
    * @brief GetBatchNo 获取批号
    * @return 
    */
    QString GetBatchNo(); 
private:    
    /**
    * @brief _CalcPCRTubeAreaRowIndexToArea 计算PCR管区域索引
    * @param uiRowIndex 行索引
    * @return 区域索引
    */
    qint8 _CalcPCRTubeAreaRowColumnIndexToArea(quint8& uiRowIndex);

    /**
    * @brief _CalcAmplifyAreaToArea 计算扩增区域行列
    * @param info 样本信息
    * @return 
    */
    void _CalcAmplifyArea(AmplificateSampleInfo& info);

    /**
    * @brief _CalcAmplifyAreaSampleDistribution 计算样本在扩增区域分布
    * @param qAmplifyAreas 区域信息
    * @return 
    */
    void _CalcAmplifyAreaSampleDistribution(QSet<quint8>& qAmplifyAreas);    
signals:

public slots:

private:
    QList<AmplificateSampleInfo> m_batchInfoList;                // 样本信息列表
    QMap<quint8, QList<AmplificateSampleInfo>> m_qBatchInfosMap; // 样本信息映射表(区域对应的样本信息列表)
    QString strTecName;                                          // 扩增时序名称
    QQueue<AmplificateSampleInfo> m_qBatchInfos;                 // 样本信息列队列
    QQueue<AmplificateSampleInfo> m_qBatchInfosUsed;             // 使用的样本信息队列
    QMutex m_qMutex;            
    bool m_bRunning;                                             // 是否正在运行
    QString m_strBatchNo;                                        // 批次号
};


#endif // SAMPLEAMPLIFICATE_H
