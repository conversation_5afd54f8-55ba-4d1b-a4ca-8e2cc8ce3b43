#include "ctcpserverthread.h"
#include <QDebug>
#include <QTime>
#include <QDataStream>
#include <QNetworkProxy>
#include "publicconfig.h"
#include "publicfunction.h"
#include "cglobalconfig.h"
CTcpServerThread::CTcpServerThread(QObject *parent) : QObject(parent)
{
    m_pConnectTcpSocket = nullptr;
}

CTcpServerThread::CTcpServerThread(int iPort, QHostAddress strHostIP, QString strHostMac, QObject *parent)
    :  QObject(parent)
    , m_iPort(iPort)
    , m_qHostAddress(strHostIP)
    , m_strHostMacAddress(strHostMac)
{
    // 设置不使用代理
    QNetworkProxy::setApplicationProxy(QNetworkProxy::NoProxy);
    memset(m_bWaitAck, 0, BUFFER_SIZE);
    m_iSeqNumber = -1;
    m_strClientName = (m_iPort == 30080 ? "client" : "pcr");

    m_iConnected = 0;
    m_iDisConnectedCount = 0;
    m_pReadFramesTimer = new QTimer();
    m_pReadFramesTimer->setSingleShot(false);
    m_pResendTimer = new QTimer();
    m_pResendTimer->setSingleShot(false);

    m_pThread = new QThread();
    m_pTCPServer = new QTcpServer();
    m_pConnectTcpSocket = nullptr;
    connect(this, &CTcpServerThread::sigInitServer,
            this, &CTcpServerThread::_slotInitServer, Qt::QueuedConnection);

    m_pTCPServer->moveToThread(m_pThread);
    m_pReadFramesTimer->moveToThread(m_pThread);
    m_pResendTimer->moveToThread(m_pThread);
    //    m_pConnectTcpSocket->moveToThread(m_pThread);
    this->moveToThread(m_pThread);
    connect(m_pReadFramesTimer,SIGNAL(timeout()),this,SLOT(_slotReadFramesTimer()));
    connect(m_pResendTimer,SIGNAL(timeout()),this,SLOT(_slotReSendTimer()));
    connect(this, &CTcpServerThread::signalReadTimer,
            this, &CTcpServerThread::_slotStartTimer, Qt::QueuedConnection);

    m_pThread->start();
    emit sigInitServer();
    emit signalReadTimer();
}

CTcpServerThread::~CTcpServerThread()
{
    if(m_pTCPServer && m_pTCPServer->isListening())
    {
        m_pTCPServer->close();
    }
    if(m_pConnectTcpSocket)
    {
        // 如果连接还存在，先关闭它
        if (m_pConnectTcpSocket->state() == QAbstractSocket::ConnectedState)
        {
            m_pConnectTcpSocket->disconnectFromHost();
        }
        delete m_pConnectTcpSocket;
        m_pConnectTcpSocket = nullptr;
    }

    if(m_pTCPServer)
    {
        //        m_pTCPServer->deleteLater();
        delete m_pTCPServer;
        m_pTCPServer = nullptr;
    }

    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }
}

void CTcpServerThread::slotWaitACK(quint16 iFrameNumber)
{
    m_bWaitAck[iFrameNumber] = true;
}

void CTcpServerThread::slotSendAckBack(QByteArray qSendMsgAarry)
{
    _sendFrameData(qSendMsgAarry, true);
}

void CTcpServerThread::slotSendMessage(QByteArray qMsgBtyeArray)
{
    // 如果连接不存在,不转发数据(第一次启动除外)
    if (m_strClientName == "client" && (m_pConnectTcpSocket == nullptr) && m_iDisConnectedCount >= 1)
    {
        qWarning() << "CTcpServerThread::slotSendMessage: client is not connected";
        return;
    }    
    
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qWarning() << "CTcpServerThread^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        emit sigError(FT_Comm_CacheFull, "");
        return;
    }
    m_qSendMessageList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
}

void CTcpServerThread::slotReInitServer(QHostAddress qHostAddress, QString strHostMac)
{
    m_qHostAddress = qHostAddress;
    m_strHostMacAddress = strHostMac;
    _toListen();
}

void CTcpServerThread::slot_recvmessage()
{
    QTcpSocket* pConnectSocket = (QTcpSocket*)this->sender();
    if(pConnectSocket != NULL)
    {
        // 原子标志位检查，避免与定时器轮询竞争
        if (m_isReading.testAndSetOrdered(0, 1))
        {
            QByteArray m_qReadFrameByteArray = pConnectSocket->readAll();    //接收消息
            m_isReading.store(0); // 重置标志位
            
            if (!m_qReadFrameByteArray.isEmpty()) // 过滤空数据
            {
                #ifndef ShortOutPutLog
                // qDebug() << m_iPort << "tcp read" << m_qReadFrameByteArray.toHex(':').toUpper();
                CGlobalConfig::getInstance().printMessageInfo(m_qReadFrameByteArray,
                                                              "[" + m_strClientName + "->server]");

                #endif
                emit sigReciveMessage(m_qReadFrameByteArray);
            }
        }
    }
}

void CTcpServerThread::_slotReadFramesTimer()
{    
    if (m_pConnectTcpSocket == nullptr)
    {
        return;
    }
    
    // 主动轮询检查是否有数据可读（方案1：定时器轮询检查）
    if (m_pConnectTcpSocket->bytesAvailable() > 0)
    {
        // 原子标志位检查，避免与信号槽竞争
        if (m_isReading.testAndSetOrdered(0, 1))
        {
            QByteArray m_qReadFrameByteArray = m_pConnectTcpSocket->readAll();
            m_isReading.store(0); // 重置标志位
            
            if (!m_qReadFrameByteArray.isEmpty()) // 过滤空数据
            {
                #ifndef ShortOutPutLog
                qDebug() << m_iPort << "++++++++++++++  server read from client [timer poll]  "
                        << m_qReadFrameByteArray << m_qReadFrameByteArray.toHex(':');
                #endif
                emit sigReciveMessage(m_qReadFrameByteArray);
            }
        }
    }
    
    // 写
    if(m_iReadIndex.load() != m_iWriteIndex.load())
    {
        m_sCurrentSendMessage = m_qSendMessageList[m_iReadIndex.load()];
        _reSetFrameNumber(m_sCurrentSendMessage);
        _sendFrameData(m_sCurrentSendMessage);
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        // 重发
        if(m_sCurrentSendMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 0x05不做重发
            m_iCmdID  = *((quint8*)m_sCurrentSendMessage.data() + gk_iCmdIDPos);
            if(m_iCmdID != 0x05)
            {
                // qDebug() << "m_iCmdID" << m_iCmdID << m_sCurrentSendMessage.toHex(':').toUpper() << m_sCurrentSendMessage.count() << gk_iFrameLengthNotData;
                MessageInfo messageInfo;
                messageInfo.qSendMessageDataByteArray = m_sCurrentSendMessage;
                messageInfo.timestamp = QDateTime::currentMSecsSinceEpoch();
                m_pFramePos = m_sCurrentSendMessage.data() + gk_iSeqPos;
                messageInfo.iSeqNumber =  GetByte2Int(m_pFramePos);
                //
                m_iRingCurrentWriteIndex = m_iRingWriteIndex.load();
                m_iRingNextWriteIndex = (m_iRingCurrentWriteIndex + 1) % BUFFER_SIZE;
                //
                if (m_iRingNextWriteIndex == m_iRingReadIndex.load()) {
                    // 原则上不可能有65535个重发存在，故而不做考虑
                    qWarning() << "CSerialDeviceThread^^^^^^^^^^ERROR-resnd tcp^^^^^^^^^^^^^";
                    emit sigError(FT_Comm_CacheFull, "");
                }
                m_bWaitAck[messageInfo.iSeqNumber] = false; // 这里帧号才是正确，因为有的帧可能不会有重发
                m_sRingMessageInfoList[m_iRingCurrentWriteIndex] = messageInfo;
                m_iRingWriteIndex.store(m_iRingNextWriteIndex);
                // qDebug() << "m_iRingWriteIndex" << m_iRingWriteIndex;
            }
        }
    }
    if(m_iReSendReadIndex.load() != m_iReSendWriteIndex.load())
    {// 重发队列
        // qInfo() << m_iPort << "---------tcp server resend -----------" << m_iReSendReadIndex.load() << m_iReSendWriteIndex.load();
        QByteArray& qMessage = m_qReSendMessageList[m_iReSendReadIndex.load()];
        if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
        {// 只做MethodID初步解析
            qDebug()  << "resend Message_" << m_iPort << qMessage.toHex(':').toUpper();
            _sendFrameData(qMessage);
            emit sigError(FT_Comm_Resend, QString("resend message method id %1").arg(GetByte2Int(qMessage.data() + gk_iMethodIDPos)));
        }
        // 环形队列
        m_iReSendReadIndex.store((m_iReSendReadIndex.load() + 1) % BUFFER_SIZE);
    }
}

void CTcpServerThread::_sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend)
{
    if(m_pConnectTcpSocket != nullptr)
    {
        m_pConnectTcpSocket->write(qSendMsgAarry);   //发送消息到客户端
        QString strType = bACKSend ? "ack" : "data";
        CGlobalConfig::getInstance().printMessageInfo(qSendMsgAarry,
                                                      "[server->" + m_strClientName.leftJustified(7, ' ') + " " + strType + "]");
    }
}

void CTcpServerThread::_toListen()
{
    if(!m_qHostAddress.isNull() && m_pTCPServer)
    {
        if(m_pTCPServer->listen(m_qHostAddress, m_iPort))
        {
            qDebug() << "Successed to start listening:" << m_qHostAddress.toString();
            connect(m_pTCPServer, &QTcpServer::newConnection, this, &CTcpServerThread::slot_newconnect, Qt::QueuedConnection);
        }
        else
        {
            emit sigError(FT_Comm_OpenFail, "");
            qDebug() << "Failed to start listening:" << m_pTCPServer->errorString();
        }
    }
}

void CTcpServerThread::_reSetFrameNumber(QByteArray &qByteArray)
{// 帧号
    m_iSeqNumber++;
    if(m_iSeqNumber > 0xFFFF)
    {
        m_iSeqNumber = 0;
    }
    // 将 quint16 值转换为字节序列，你可以选择使用大端或小端
    if(qByteArray.count() >= gk_iFrameLengthNotData)// 帧长
    {
        QByteArray qBlockByteArray;
        QDataStream qOutDataStream
                (&qBlockByteArray,QIODevice::ReadWrite);
        qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
        qOutDataStream << quint16(m_iSeqNumber);
        qByteArray = qByteArray.replace(gk_iSeqPos, 2,  qBlockByteArray);
        quint16 iCrc16 = GetCRC16(qByteArray.data(), qByteArray.count()-2, 0);
        QByteArray qBlockByteArrayCrc;
        QDataStream qOutDataStreamCrc
                (&qBlockByteArrayCrc,QIODevice::ReadWrite);
        qOutDataStreamCrc.setByteOrder(QDataStream::BigEndian);  // 设置xiao端格式
        qOutDataStreamCrc << quint16(iCrc16);
        qByteArray = qByteArray.replace(qByteArray.count()-2, 2,  qBlockByteArrayCrc);
    }
}

void CTcpServerThread::slot_newconnect()
{
    if(m_pTCPServer && m_pTCPServer->hasPendingConnections())  //查询是否有新连接
    {
        QTcpSocket* pConnectSocket = m_pTCPServer->nextPendingConnection();
        // 先断开旧的连接及其信号
        if (m_pConnectTcpSocket != nullptr)
        {
            // 断开与旧socket的所有连接
            m_pConnectTcpSocket->disconnect(this);
            m_pConnectTcpSocket->abort();
            m_pConnectTcpSocket->deleteLater();
            m_pConnectTcpSocket = nullptr;
        }
        // 设置新的socket
        m_iConnected = 1;
        m_pConnectTcpSocket = pConnectSocket;
        qDebug() << "login " << m_pConnectTcpSocket->peerAddress().toString() << m_pConnectTcpSocket->localPort();
        emit sigNewNetworkConnect(m_pConnectTcpSocket->peerAddress().toString(), m_iPort, true);
        m_pConnectTcpSocket->setReadBufferSize(1024*1000 * 100);
        connect(m_pConnectTcpSocket, &QTcpSocket::readyRead, this, &CTcpServerThread::slot_recvmessage);
        connect(m_pConnectTcpSocket, &QTcpSocket::disconnected, this, &CTcpServerThread::slot_disconnect);
    }
}

void CTcpServerThread::slot_disconnect()
{
    QTcpSocket* pConnectSocket = qobject_cast<QTcpSocket*>(this->sender());
    if(pConnectSocket)
    {
        emit sigNewNetworkConnect(pConnectSocket->peerAddress().toString(), m_iPort, false);
        m_iConnected = 0;
        m_iDisConnectedCount++;
        if(pConnectSocket == m_pConnectTcpSocket)
        {
            m_pConnectTcpSocket->deleteLater();
            m_pConnectTcpSocket = nullptr;
        }          
        qDebug() << "slot_disconnect"<<m_strClientName<<m_iPort;
    }
}

void CTcpServerThread::_slotReSendTimer()
{
    m_iRingCurrentReadIndex = m_iRingReadIndex.load();
    if (m_iRingCurrentReadIndex != m_iRingWriteIndex.load())
    {
        const MessageInfo& messageInfo = m_sRingMessageInfoList[m_iRingCurrentReadIndex];
        if(!m_bWaitAck[messageInfo.iSeqNumber])
        {// 重发
            const qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
            if ((currentTime - messageInfo.timestamp) >= gk_iWaitMsecs)
            { // If the message is old, resend it
                m_iReSendCurrentWriteIndex = m_iReSendWriteIndex.load();
                m_iReSendNextWriteIndex = (m_iReSendCurrentWriteIndex + 1) % BUFFER_SIZE;

                if (m_iReSendNextWriteIndex == m_iReSendReadIndex.load())
                { // 原则上不可能有65535个重发存在，故而不做考虑
                    qWarning() << "m_qReSendMessageInfoList^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
                    emit sigError(FT_Comm_CacheFull, "");
                    return;
                }
                m_qReSendMessageList[m_iReSendCurrentWriteIndex] = messageInfo.qSendMessageDataByteArray;
                m_iReSendWriteIndex.store(m_iReSendNextWriteIndex);

                qDebug() <<  "CTcpServerThread_slotReSendTimer"  << __LINE__ << m_iRingCurrentReadIndex << m_iRingWriteIndex.load()
                         << m_iReSendReadIndex.load()  <<  m_iReSendCurrentWriteIndex << m_iReSendNextWriteIndex;
                m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
            }
        }
        else
        {
            m_iRingReadIndex.store((m_iRingReadIndex.load() + 1) % BUFFER_SIZE);
        }
    }
}
void CTcpServerThread::_slotInitServer()
{ // 启动UDP广播
    _toListen();
}

void CTcpServerThread::_slotStartTimer()
{
    m_pReadFramesTimer->start(10);//
    m_pResendTimer->start(100);
}


