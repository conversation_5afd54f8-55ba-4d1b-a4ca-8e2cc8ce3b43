﻿/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description: 全局配置信息
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef PUBLICCONFIG_H
#define PUBLICCONFIG_H

#include <QDir>
#include <QObject>
#include <QDate>
static constexpr int BUFFER_SIZE = 65536;
static constexpr int MIDDLE_BUFFER_SIZE = 256;
static constexpr int SMALL_BUFFER_SIZE = 16;
static constexpr int CONDITION_SIZE = 256;
static constexpr int ACTION_SIZE = 128*3;
static const int gk_iReSendTimes = 2;// 重发次数
static const int gk_iWaitMsecs = 1000;// 重发等待时间间隔
static const int gk_iMaxCycleCount = 45;
static const int gk_iHoleNumber = 2;
static const int gk_iBGRYNumber = 4;

// 帧数据配置
static const QString gk_strScannerHeadBytes = "\x02";  // 扫码器帧头标记
static const QByteArray gk_strScannerTailBytes = "\r\n";  // 扫码器帧尾标记
static const QString gk_strHeadBytes = "@M1*";// 帧头标记
static const quint8 gk_iMachineIDPos = 5;
static const quint8 gk_iCmdIDPos = 6;
static const quint8 gk_iDestinationIDPos = 7;
static const quint8 gk_iSourceIDPos = 8;
static const quint8 gk_iSeqPos = 10;// 帧号位置
static const quint8 gk_iMethodIDPos = 12;
static const quint8 gk_iResultPos = 14;
static const quint8 gk_iSyncPos = 15;
static const quint8 gk_iLengthPos = 17; // 帧长度位置
static const quint8 gk_iFrameDataPos = 19;// FrameData位置
static const quint8 gk_iFrameLengthNotData = 21;// 除去FrameData的长度

const QString gk_strinternalStandard = "IS"; // 定量的内标
const QString gk_strinternalControl = "IC"; // 定性的内控
const QString gk_WFQC = "WFQC+"; //
const QString gk_WFIS = "WFIS+"; //
const QString gk_SampleErrorID = "WF+Error";
//U盘升级路径和文件名
static const int gk_iUpdateTimeOut = 10;  //10s
const QString gk_strUDiskUpdateDir = "WonNova1600/";
const QString gk_strAppName        = "SClient";
const QString gk_strAlgorithmName       = "libCT.so";
const QString gk_strAutoName       = "auto.tar.gz";
const QString gk_strMyShName       = "network.sh";
const QString gk_strMiddleFileName       = "SServer";
const QString gk_strFirmName       = "mExtractor.xbin";
const QString gk_strSlaveName      = "mOptTool2_7C.xbin";

const  QString gk_strUDiskUpdateTempDir ="UpdateTemp";
const QString gk_strAtuoUpgradeZipName ="AutoUpgrade.zip";
const QString gk_strPCRMainUpgradeName        = "BoardPCRmain.bin";
const QString gk_strTECUpgradeName         = "BoardTEC.bin";
const QString gk_strFLUpgradeName         = "BoardFluorence.bin";
const QString gk_strRFIDUpgradeName         = "BoardRFID.bin";
const QString gk_strBoardFunctionUpgradeName        = "BoardFunctionCtrl.bin";
const QString gk_strBoardMotorUpgradeName        = "BoardMotor.bin";
const QString gk_strBoardPowerUpgradeName        = "BoardPower.bin";
const QString gk_strBoardVersionCheckName        = "version.json";
//升级一个包的大小
static const quint16  gk_iPackageSize_FunctionManager= 512;
static const quint16  gk_iPackageSize_Motor= 512;
static const quint16  gk_iPackageSize_PCRMain = 256;
static const quint16  gk_iPackageSize_TEC = 256;
static const quint16  gk_iPackageSize_RFID = 128;
static const quint16  gk_iPackageSize_Fluorence = 128;
static const quint16  gk_iPackageSize_PowerCtrl= 128;

const QString gk_strFaultCodeName  = "faultcode.xlsx";

// 数据库
const QString gk_strMotorDBName = "MotorInfo.db";
const QString gk_strTimingInfoDBConnect = "timinginfo_connect";
const QString gk_strTimingDBConnect = "timing_connect";
const QString gk_strMotorInfoDBConnect = "motorinfo_connect";
const QString gk_strHistoryDBConnect = "history_connect";
const QString gk_strSystemDBConnect = "system_connect";
const QString gk_strGraphDBConnect = "graph_connect";
const QString gk_strClientDBConnect = "client_connect";
const QString gk_strProjectDBConnect = "project_connect";
const QString gk_strErrorCodeDBConnect = "errorcode_connect";
const QString gk_strProjectDBReadConnect = "project_read_connect";

const QString gk_strFaultCodeXlsxName = "s1_fault_code.xlsx";
const QString gk_strCMDNameXlsxName = "ProtocolSpecification_M1.xlsx";
const QString gk_strDataTimeFormat = "yyyy-MM-dd hh:mm:ss";

//耗材及试剂结构体容量长度
static const int  gk_iConsumableStructSize_Tip = 9;
static const int  gk_iConsumableStructSize_TubeCap= 16;
static const int  gk_iReagentStructSize = 17;
static const int gk_iMaxMethodIDSize = 10000;



#define SPLIT_And "&"
#define SPLIT_BETWEEN_CMD ";"   //指令与指令之间分隔符
#define SPLIT_IN_CMD      ","   //指令内部分隔符
#define Connect_Unit_ID   "_"   //条件单元与条件ID连接符
#define QDFUN_LINE qDebug() << __FUNCTION__ <<__LINE__ // 用于输出函数名+行
#define DEBUG_LOG qDebug()  << "<" << __LINE__ << __FUNCTION__ << ">" // 用于输出函数名+行
// ftp file: filename+$+filesize-$-filedata
const QByteArray gk_strSplitFtpFileName = "++$++";
const QByteArray gk_strSplitFtpFileSize = "--$--";


#define PCR_COLUMN_SIZE 2
#define PCR_ROW_SIZE  2
#define PCR_SUB_AREA_SIZE1 8 //32孔PCR每个区域PCR孔大小
#define PCR_SUB_AREA_SIZE2 16 //64孔PCR每个区域PCR孔大小
#define PCR_SUB_AREA_COLUMN_SIZE 8
#define PCR_MODULE_SIZE 4

// 重发参数
struct MessageInfo
{
    quint16 iSeqNumber;
    QByteArray qSendMessageDataByteArray;
    qint64 timestamp;
};

enum EnumCommandType
{
    CmdType_Command = 0,
    CmdType_Data,
    CmdType_Ack,
    CmdType_Reply,
    CmdType_Notification,
    CmdType_Bulletin,
};
enum PortTeyp
{
    Port_Cmd = 10000,
    Port_Log = 10001,
    Port_Update = 10002,
};

enum EConnectType
{
    Connect_Network_0 = 0,// 上位机
    Connect_Network_1,// PCR
    Connect_Serial,
    Connect_CanBus_0 ,
    Connect_CanBus_1,
    Connect_Serial_Scan,// 扫码模块-样本
    Connect_Extract_Scan, // 扫码-提取
    Connect_Middle_Host,// 中位机自身处理
};

enum RunLogLevel
{
    LEVEL_DEBUG=0,
    LEVEL_TRACE=1,// 普通用户提示信息等同INFO
    LEVEL_WARN=2,
    LEVEL_ERROR=3,
    LEVEL_CRIT=4,
    LEVEL_ALERT=5,// 普通用户错误提示
    LEVEL_EMERG=6,
    LEVEL_OFF=7,
};

enum EUIContentOneDataEnum
{
    UI_ONE_A = 1,
    UI_ONE_B,
    UI_ONE_C,
    UI_ONE_D,
    UI_ONE_E,
    UI_ONE_F,
    UI_ONE_G,
    UI_ONE_H,
};

enum ExtractReagentType
{
    ERT_ELUENT,//洗脱液
    ERT_CLEAN_PART3,//清洗液3
    ERT_CLEAN_PART2,//清洗液2
    ERT_CLEAN_PART1,//清洗液1
    ERT_PROTEASE_K,//蛋白酶K
    ERT_PARAFFIN_OIL,//石蜡油
    ERT_LYSATE//裂解液
};

enum CatchType
{
    CT_SINGLE,//单个
    CT_DOUBLE,//两个
    CT_TRIPLE,//三个
    CT_QUADRUPLE,//四个
};
#define DEVICE_CATCH_TYPE CT_DOUBLE
enum PeroidExecState //时序周期执行状态
{
    PEST_SEQ_START,//时序开始
    PEST_SEQ_END,//时序结束
    PEST_SAMPLE_PROCESS_START,//样本处理开始
    PEST_SAMPLE_PROCESS_END,
    PEST_EXTRACT_START,//提取开始
    PEST_EXTRACT_END,
    PEST_SYSTEM_BUILD_START,//体系构建开始
    PEST_SYSTEM_BUILD_END,
    PEST_AMPLIFY_START,//扩增开始
    PEST_AMPLIFY_END,
    PEST_MELT_START,//熔解开始
    PEST_MELT_END,
    PEST_SEQ_FAILED,//时序失败
    PEST_SAMPLE_SCAN_START,//旋转扫码开始
    PEST_SAMPLE_SCAN_END, //旋转扫码结束
    PEST_GAIN_START=81, //采光开启
};

enum EnumMachineID
{
    Machine_Middle_Host = 0,
    Machine_Function_manager_Ctrl,//功能管理板
    Machine_Motor_1,
    Machine_Motor_2,
    Machine_Motor_3,
    Machine_Temp_Ctrl,   //已经没用
    Machine_Power_Ctrl, 
    Machine_PCR_MainCtrl = 7,
    Machine_PCR_Ctrl,
    Machine_PCR_Ctrl_1,  //温控
    Machine_PCR_Ctrl_2,
    Machine_PCR_Ctrl_3,
    Machine_PCR_Ctrl_4,
    Machine_Motor_4 = 0x10,

    Machine_RFID_1 = 0x11,
    Machine_RFID_2,
    Machine_RFID_3,

    Machine_Fluorence = 0x19,
    Machine_GarbageStateCtrl,
    Machine_Scan_Board_1,
    Machine_Scan_Board_2,
    Machine_UpperHost,
};

enum EnumUnitCondition
{
    Unit_Condition_Sample = 0,

};

enum EnumTimingAction
{
    TimingAction_HeatBegin= 0,
    TimingAction_Move,
    TimingAction_Wait,
    TimingAction_Mix,
    TimingAction_Magnetic,
    TimingAction_HeatEnd,
};

//电机光耦bit位置
enum GXIO_MOTOR
{
    GXIO_0=0,  // 复位光耦（最高位）
    GXIO_1,  //磁棒底部光耦
    GXIO_2,  //复位光耦(对准3和4号板）
    GXIO_3,  //水平光耦2（对准2和5号板）
    GXIO_4,  //水平光耦3(对准1号和6号板）加热板孔
    GXIO_5,  //等待位光耦(最高位置）
    GXIO_6,  //复位光耦（插拔磁套位置）
    GXIO_7,  //磁套底部光耦
    GXIO_8,  //复位光耦（非遮挡）
    GXIO_9,  //复位光耦（对准4，5， 6号板）
    GXIO_10, //深孔板光耦2（对准1，2，3号板）
    GXIO_11, //磁套就绪与否对射光耦
    GXIO_12, //
    GXIO_13, //
    GXIO_14, //
    GXIO_15,//
};

enum EnumAffairAction
{
    //适用于整机时序流程
    Action_Board1Init=1,
    Action_SampleToOpenCap,
    Action_SampleToOpenCapLeft,
    Action_SampleToOpenCapDouble,
    Action_OpenCap,
    Action_OpenCapLeft,
    Action_OpenCapDouble,
    Action_CloseCap,
    Action_CloseCapLeft,
    Action_CloseCapDouble,
    Action_SampleBackHome,
    Action_SampleBackHomeLeft,
    Action_SampleBackHomeDouble,
    Action_SampleCodeScanStepStart = 20,//样本扫码-->>每一个试管位置扫码开始
    Action_SampleCodeScanRotate,        //样本扫码-->>旋转爪子
    Action_SampleCodeScanStepEnd,       //样本扫码-->>每一个试管位置扫码结束
    Action_SampleCodeScanInit,          //样本扫码-->>样本扫码复位    
    Action_SampleCodeScanTubeCheck,     //样本扫码-->>检测样本管有无(右)
    Action_SampleCodeScanTubeCheckLeft, //样本扫码-->>检测样本管有无(左)  
    Action_SampleScanTubeExist,         //样本管有无扫码 
    
    // Action_ExtractScanMotorMove=32,//add lxj
    // Action_Punch = 33,
    // Action_Extract,
    // Action_StripCarMoveToScanPos, //add lxj
    // Action_StripPutDownPos,// 卡盒架移动到卡条放置位
    // Action_ExtractScanMotorInit,//扫码电机复位
    // Action_MotorPunchReset,      // 刺破电机(刺破+复位)
    // Action_PunchStripPos,        // 卡盒架电机移动到特定试剂位(刺破)   
    // Action_SwitchPosTip1000,     // 加样区卡盒架电机移动到1000Tip扎取位  

    Action_Board2Init =129,
    Action_Sampling,
    Action_SamplingDouble,
    Action_SpitSample,
    Action_SpitSampleDouble,
    Action_TransClevage,
    Action_TransClevageDouble,
    Action_SubPackPunch1,//复溶刺破冻干球及分装试剂
    Action_SubPackPunch2,//复溶刺破冻干球
    Action_SubpackReagent ,
    Action_GetTip,
    Action_AbandonTip,
    Action_TransPurify,
    Action_TransPurifyDouble,
    Action_CapAndTransTube,
    Action_CapAndTransTubeDouble,
    Action_SSwPurifyOraffin,
    Action_DSwPurifyOraffin,
    Action_SSwReagent,
    Action_DSwReagent,
    Action_AmplTransTube =150, // 扩增例程PCR管转移
    Action_AmplTransTubeDouble,
    Action_GetStandardTip200,// 获取内标tip200
    Action_AbandonStandardTip200,// 丢弃内标tip200
    Action_SingleSample1000Tip,// 获取单个tip1000
    Action_DoubleSample1000Tip,// 获取多个tip1000    
    Action_GetTip200,// 获取tip200
    Action_AbandonTip200,// 丢弃tip200
    Action_GetCrossTip200,// 获取跨区tip200
    Action_SwReagentSuck,// 吸扩增试剂
    Action_SwReagentSpit = 160,// 吐扩增试剂
    Action_SwPurifySuck ,// 吸提纯液
    Action_SwPurifySpit,// 吐提纯液
    Action_SwOilSuck,// 吸石蜡油
    Action_SwOilSpit,// 吐石蜡油
    Action_GetPcrCap,// 获取PCR帽
    Action_CapToTube,// 转移PCR帽到PCR管
    Action_TransTubeCap,// 转移PCR管帽
    Action_SubpackFinish,// 分装完成

    Action_Board3Init = 193,
    Action_OpenPCRCap,
    Action_ClosePCRCap,//转移关盖
    Action_TransPCRTube,
    Action_AbandonPCR,
    Action_SwitchTube,
    Action_CentrifugeTube,
    Action_AbandonOpenPCRCap,
    Action_AbandonClosePCRCap,//丢弃pcr管关盖    

    //适用于整机复位，整机自检
    Action_Board1RemainPartInit = 122,
    Action_Board1ZInit_Condition,
    Action_Board1EjectMagTube,
    Action_Board1Gripper_Init ,
    Action_Board1ExceptZMotorInit ,
    Action_Board1RemainInit,
    Action_Board1ZInit,

    Action_Board2PumpCapTubeAndAbandon = 187,
    Action_Board2PumpAbandonAndInit,
    Action_Board2PumpInit,
    Action_Board2ExceptZMotorInit ,
    Action_Board2RemainInit,
    Action_Board2ZInit = 192,

    Action_Board3RemainPartInit = 248,
    Action_CleanCentrifuge,
    Action_Board3ZInit_Condition,
    Action_Board3GripperAbandonAndInit,
    Action_Board3GripperInit,
    Action_Board3ExceptZMotorInit ,
    Action_Board3RemainInit,
    Action_Board3ZInit = 255,

    // 板卡4时序
    Action_Board4Init = 300,
    Action_Punch,
    Action_Extract,
    Action_ExtractScanMotorMove,
    Action_StripCarMoveToScanPos,
    Action_StripPutDownPos,// 卡盒架移动到卡条放置位
    Action_ExtractScanMotorInit,//扫码电机复位
    Action_Board4RemainInit,
    Action_Board4ZInit = 308, 
    Action_Board4EjectMagTube,
    Action_Extract_Start, // 提取开始
    Action_Extract_Run, // 提取运行
    Action_Extract_End, // 提取结束
};

enum EnumMethod_MotherBoard
{
    Method_start = 1,
    Method_stop,
    Method_pause,
    Method_resume,
    Method_status,
    Method_systemdb_add = 6,
    Method_sys_info = 8,
    Method_env_temp,
    Method_ht_info = 10,
    Method_read_motor_cmd,
    Method_set_motor_cmd,
    Method_read_all_cmds,
    Method_reset_all_cmds,
    Method_delete_motor_cmd,
    Method_as_debug,
    Method_mlog,
    Method_mlog_req,
    Method_mlog_data,
    Method_mlog_end = 20,
    Method_mlog_info,
    Method_opt_byte,
    Method_timing_file,
    Method_timing_step,
    Method_notify_flag,
    Method_rtc,
    Method_upgrade_req,
    Method_upgrade_data,
    Method_upgrade_end,
    Method_machine_reset = 30,
    Method_no_reset,
    Method_unit_status,
    Method_notify,
    Method_power_off,
    Method_heart_beat,
    Method_beep_flag,
    Method_beep_cfg,
    Method_erase_flash,
    Method_dev_id,
    Method_config_extract = 40,
    Method_extract_start,
    Method_extract_stop,
    Method_extract_read,
    Method_extract_write,
    Method_extract_timing_step,
    Method_connect_flag,
    Method_start_test,
    Method_custom_timing_ID,
    Method_comp_cmd,
    Method_read_comp_cmd = 50,
    Method_set_comp_cmd,
    Method_read_all_comp_cmds,
    Method_reset_all_comp_cmds,
    Method_delete_comp_cmd,
    Method_comp_cmd_st,
    Method_set_process_cmd,
    Method_start_process_cmd,
    Method_pause_process_cmd,
    Method_stop_process_cmd,
    Method_sample_process_cmd = 60,
    Method_comp_cmd_exec_cond,
    Method_dev_comp_status,
    Method_stop_comp_cmd,
    Method_machine_clean,
    Method_complex_compose_run = 65,//  执行业务组合指令
    Method_complex_compose_stop,
    Method_tecdb_read = 67,// tec
    Method_tecdb_write,
    Method_tecdb_delete,
    Method_projectdb_read = 70,// 项目信息数据库获取
    Method_projectdb_write,
    Method_projectdb_delete,
    Method_test_info,//测试项目信息
    Method_test_result,//测试结果
    Method_melt_result,//熔解结果
    Method_daily_monitor=76,
    Method_board_info=77,
    Method_daily_monitor_warn,
    Method_strip_codescan_test=79,
    Method_upgrade_progressBar = 80,
    Method_extract_qrcode=86,//提取条扫码信息
    Method_uv_open=87,
    Method_ftp_file_info = 88,
    Method_ftp_file_sync,
    Method_pcr_size_type = 90,
    Method_set_consumable,
    Method_set_reagent,
    Method_load_consumable,
    Method_unload_consumable,
    Method_enough_consumable,//获取耗材是否充足
    Method_update_consumable,//耗材更新
    Method_pos_debug,//位置调试
    Method_get_pcr_time = 98,    //获取pcr时间
    Method_aging_test=99,//老化测试
    Method_error_info = 100,
    Method_Start_pos_debug=101, //开始位置调试
    Method_Stop_pos_debug=102, //结束位置调试
    Method_Stop_Upgrade=103,//停止升级
    Method_Stop_strip_codescan_test=104,//停止扫码老化测试
    Method_pcr_program_version = 239,
    Method_extract_heater_start = 250,
    Method_extract_heater_stop,
    Method_DELAY=252,
    Method_loop_st,
    Method_loop,
    Method_jump
};
enum EnumMethod_Motor01
{
    Method_MDB1_MCHK=257,
    Method_MDB1_SRST,
    Method_SCYReset=260,
    Method_SCYMTSamplePos,
    Method_SCYMTUncapDownPos,
    Method_SCYMTUncapOrCapPos,
    Method_SCYMTCapCatchPos,
    Method_SClampYReset=270,
    Method_SClampPartically,
    Method_SClampFully,
    Method_UC1ZReset=280,
    Method_UC1ZMTUncapPos,
    Method_UC1ZUncap,
    Method_UC1ZCap,
    Method_UC1ZMTCapPos,
    Method_UC1ZMTCatchPos,
    Method_UC2ZReset=290,
    Method_UC2ZMTUncapPos,
    Method_UC2ZUncap,
    Method_UC2ZCap,
    Method_UC2ZMTCapPos,
    Method_UC2ZMTCatchPos,

    // Method_CReset=300,
    // Method_CMTSwitchPos,
    // Method_CMTMagnetShieldPos,
    Method_SACMTReagentPosForPunch=303,
    // Method_SACMTReagentPosForExtract,
    Method_SACMTStripPutDownPos=305,
    // Method_SACMTStripHoldDownPos=308,
    Method_PZReset=310,
    Method_PZMTPunchPos,
    // Method_CAMRReset=320,
    // Method_CAMRMTWaitPos,
    Method_CAMRMTWaitPosByOC=321,
    Method_CAMRMTBottomPos,
    Method_CAMRUpAbsorbMag,
    Method_CAMRDownAbsorbMag,
    Method_CAMRMove,
    // Method_CAMRMTTakeoffPos,
    // Method_CAMSReset=330,
    Method_CAMSMTWaitPos=331,
    Method_CAMSMTBottomPos,
    Method_CAMSUpAbsorbMag,
    Method_CAMSDownAbsorbMag,
    // Method_CAMSMove=335,
    // Method_CAMSMix=336,
    // Method_CAMSMTCatchPos=337,
    // Method_CAMSMTTakeOffPos=338,
    // Method_CAVTOMW=340,
    // Method_CAVTMV=341,
    // Method_CAMADO=342,
    // Method_CAMAUP=343,
    Method_CAVTOMD=344,
    // Method_CAMSMTHoldDownPos,
    // Method_OptocouplerCheck=354,//光耦目标状态判定
    Method_MotorBoard_Lock = 1063,// 电磁锁上锁
    Method_MotorBoard_Unlock = 1064,// 电磁锁解锁
};

// 电机板1通知相关枚举
enum EnumMethod_Motor01_Notification
{
    Method_Notification_SampleExist=264,// 样本管有无通知
};

enum EnumMethod_Motor02
{
    Method_MDB2_MCHK=513,
    Method_MDB2_SRST,
    Method_PXReset=520,
    Method_PXMTPunchPos,
    Method_PXMTReagentZ1Pos,
    Method_PXMTTipAreaPos,
    Method_PX1MTTubeAreaPos=525,
    Method_PX1MTTubeCapArea1Pos,
    Method_PXMTPCRSwitchPos=529,
    Method_PXMTTipEjectPos,
    Method_PXMTReagentStripPos,
    Method_PXMTSampleAbsorbPos,
    Method_PXMTReagentZ2Pos,
    Method_PX2MTTubeAreaPos,
    Method_PX2MTTubeCapArea1Pos,
    Method_PYReset=540,
    Method_PYMTPunchPos,
    Method_PYMTReagentPos,
    Method_PYMTTipAreaPos,
    Method_PYMTTubeAreaPos=545,
    Method_PYMTTubeCapAreaPos,
    Method_PYMTPCRSwitchPos=549,
    Method_PYMTTipEjectPos,
    Method_PYMTReagentStripPos,
    Method_PYMTSampleAbsorbPos,
    Method_PXMTStripTipPos,
    Method_PZ1Reset=560,
    Method_PZ1MTPunchPos,
    Method_PZ1TipLoad,
    Method_PZ1TipEject,
    Method_PZ1MTAbsorbRedissolvePos,
    Method_PZ1MTSpitRedissolvePos,
    Method_PZ1MTAbsorbMixPos,
    Method_PZ1MTSpitMixPos,
    Method_PZ1MTAbsorbElutionPos,
    Method_PZ1MTSpitElutionPos,
    Method_PZ1MTAbsorbParaffinOilPos,
    Method_PZ1MTSpitParaffinOilPos,
    Method_PZ1LiquidDetect,
    Method_PZ1Move,
    Method_PZ1MTCapLoad,
    Method_PZ1MTCapTube,
    Method_PZ1MTPCRSwitchPos,
    Method_PZ1MTSampleTipLoadPos,
    Method_PZ1MTSampleAbsorbPos,
    Method_PZ1MTSampleSpitPos,
    Method_PZ1MTCleavageReagentAbsorbPos,
    Method_PZ1MTCleavageReagentSpitPos,
    Method_PZ1MTSampleTipEjectPos,
    Method_PZ1Mix,
    Method_PZ1MTWaitPos,
    Method_PZ2Reset=590,
    Method_PZ2MTPunchPos,
    Method_PZ2TipLoad,
    Method_PZ2TipEject,
    Method_PZ2MTAbsorbRedissolvePos,
    Method_PZ2MTSpitRedissolvePos,
    Method_PZ2MTAbsorbMixPos,
    Method_PZ2MTSpitMixPos,
    Method_PZ2MTAbsorbElutionPos,
    Method_PZ2MTSpitElutionPos,
    Method_PZ2MTAbsorbParaffinOilPos,
    Method_PZ2MTSpitParaffinOilPos,
    Method_PZ2LiquidDetect,
    Method_PZ2Move,
    Method_PZ2MTCapLoad,
    Method_PZ2MTCapTube,
    Method_PZ2MTPCRSwitchPos,
    Method_PZ2MTSampleTipLoadPos,
    Method_PZ2MTSampleAbsorbPos,
    Method_PZ2MTSampleSpitPos,
    Method_PZ2MTCleavageReagentAbsorbPos,
    Method_PZ2MTCleavageReagentSpitPos,
    Method_PZ2MTSampleTipEjectPos,
    Method_PZ2Mix,
    Method_PZ2MTWaitPos,
    Method_PStrutReset=620,
    Method_PumpStrutForConsume,
    Method_ReagentReset=630,
    Method_ReagentCloseCap,
    Method_ReagentOpenCap
};
enum EnumMethod_Motor03
{
    Method_MDB3_MCHK=769,
    Method_MDB3_SRST,
    Method_PCRSReset=790,
    Method_PCRSMTSwitch,
    Method_PCRXReset=800,
    Method_PCRXMTPCRSwitchPos,
    Method_PCRXMTCentrifugePos,
    Method_PCRXMTSubAreaPos,
    Method_PCRXMTPCRAbandonPos,
    Method_PCRXMTOpenCapCatchPos,
    Method_PCRXMTCloseCapCatchPos,
    Method_PCROpenCap,
    Method_PCRCloseCap,
    Method_PCRYReset=820,
    Method_PCRYMTPCRSwitchPos,
    Method_PCRYMTCentrifugePos,
    Method_PCRYMTSubAreaPos,
    Method_PCRYMTPCRAbandonPos,
    Method_PCRYMTOpenCapCatchPos,
    Method_PCRYMTCloseCapCatchPos,
    Method_PCRZReset=840,
    Method_PCRZSwitchPos,
    Method_PCRZCentrifugePos,
    Method_PCRZCatchOpPos,
    Method_PCRZAbandonPos,
    Method_PCRZOpenCapCatchPos,
    Method_PCRZCloseCapCatchPos,
    Method_PCRZSavePos,
    Method_CRST=900,
    Method_TAP,
    Method_TRP,
    Method_TV,
    Method_VF,
    Method_TT,
    Method_TF,
    Method_MS,
    Method_ACC,
    Method_FF,
    Method_ENorDS,
    Method_SP,
    Method_PCP,
    Method_PCI,
    Method_VCP,
    Method_VCI,
    Method_CP,
    Method_RCVS,
    Method_RCT,
    Method_RHRS,
    Method_QHRStatusTimeout,
    Method_Centrifuge,
    Method_OptoCoupleStatus=931,
    Method_AllOptoStatus=1048,
};

enum EnumMethod_CommonCmd //电机板公共命令 0x04
{
    Method_CommonCmd_MotorReset=1027,//电机复位
    Method_CommonCmd_GAMP =1034,//询问某个电机在中位机的值
    Method_CommonCmd_SCMP =1036,// 设置电机业务位置
    Method_CommonCmd_MOVE =1043,//电机移动
    Method_CommonCmd_MRTP =1046 ,//电机运行到指定位置
    Method_CommonCmd_LiquidDetect =1061 ,//液面探测取出高度，无极限位置的
};

enum EnumMethod_LiquidTestCmd
{
    Method_LiquidTest_Go=0,
    Method_LiquidTest_StartDetect,
    Method_LiquidTest_Save,
    Method_Liquidest_Reset,
};
enum EnumMethod_Motor04
{
    Method_RIGHT=1025,
    Method_LEFT,
    Method_ROTATE,
    Method_STOP,
    Method_MVTO,
    Method_MVBY,
    Method_GAP,
    Method_SAP,
    Method_SPEED,
    Method_RREG,
    Method_WREG,
    Method_SCMP,
    Method_GCMP,
    Method_RLCMP,
    Method_SDIR,
    Method_GDIR,
    Method_SPAM,
    Method_GPAM,
    Method_MOVE,
    Method_GMSL,
    Method_SMSL,
    Method_RLPAM,
    Method_STAT,
    Method_GXIO,
    Method_SREGA,
    Method_RREGA,
    Method_CREGA,
    Method_SRCHOP,
    Method_RRCHOP,
    Method_CRCHOP,
    Method_CLEARPOS,
    Method_ACTUALPOS,
    Method_ENN,
    Method_MSWST,
    Method_MSWFLAG,
    Method_GMCLK,
    Method_SMCLK,
    Method_RLCFG,
    Method_MOTOR_COMPOSE,

    Method_CReset = 2570,
    Method_CMTSwitchPos = 2571,
    Method_CMTMagnetShieldPos = 2572, 
    Method_SACMTReagentPosForExtract = 2574,   
    Method_SACMTStripHoldDownPos = 2578,
    Method_CAMRReset = 2590,
    Method_CAMRMTWaitPos = 2591,
    Method_CAMRMTTakeoffPos = 2597,
    Method_CAMSReset = 2600,
    Method_CAMSMove = 2605, 
    Method_CAMSMix = 2606,
    Method_CAMSMTCatchPos = 2607,
    Method_CAMSMTTakeOffPos = 2608,
    Method_CAVTOMW = 2610,
    Method_CAVTMV = 2611,
    Method_CAMADO = 2612,
    Method_CAMAUP = 2613,
    Method_CAMSMTHoldDownPos = 2615,
    Method_OptocouplerCheck = 2650,
};

enum EnumMethod_RFID
{
    Method_rfid_read = 1536,
    Method_rfid_write = 1537,
};

enum EnumMethod_PCR
{
    Method_pcr_start = 513,
    Method_pcr_stop = 514,
    Method_pcr_tec_table_req = 515,
    Method_pcr_tec_table_data = 516,
    Method_pcr_tec_table_end = 517,
    Method_pcr_temp_info = 518,
    Method_set_info_interval = 519,
    Method_pcr_signal = 520,
    Method_pcr_version = 553,
    Method_pcr_reboot = 554,
    Method_pcr_upgrade_req = 555,
    Method_pcr_upgrade_data = 556,
    Method_pcr_upgrade_end = 557,
    Method_pcr_cali = 590,
    Method_wait_signal = 591,
};

enum EnumMethod_Light
{
    Method_FLLED=4100,
    Method_FLADC,
    Method_FLCST,
    Method_FLCDT,
    Method_FLFREQ,
    Method_FLMST,
    Method_FLMDT,
    Method_FLGSET,
    Method_FLGREAD,
    Method_FLCYEND,
    Method_FLGAIN=4114,
};

enum EnumMethod_Heater
{
    Method_HTST=1025,  //启动加热
    Method_HTSP,  //停止加热
    Method_HTSET, //设置PID参数
    Method_HTGET, //读取PID参数
    Method_HTCALC, //温度校准
    Method_HTPARAMS, //获取频率参数
};

//超声单指令命令ID
enum EnumMethod_US
{
    Method_US_USST=1281,         //启动超声
    Method_US_USSP=1282,         //停止超声
    Method_US_USPSET=1283,       //设置超声功率PID参数
    Method_US_USMSET=1284,       //设置超声电机PID参数
    Method_US_AMP=1285,          //设置超声振幅
    Method_US_GPWR=1286,         //获取运行状态
    Method_US_USREBOOT=1287,     //重启超声模块/时间校正
    Method_US_USPARAM=1288,      //超声参数
    Method_US_USFTY=1289,        // 模块厂家0：佳源达，1：东方金荣
    Method_US_USVERSION=1290,    //版本信息
    Method_US_USINFO=1291        //获取超声状态，频率，振幅 和 功率
};

enum EnumMethod_Claw
{
    Method_claw_InitClaw = 2049,
    Method_claw_InitRotate,
    Method_claw_EmerStop,
    Method_claw_ClawTorque,
    Method_claw_ClawRunSpeed,
    Method_claw_RunClawToPos,
    Method_claw_SetRotateTorque,
    Method_claw_RotateRunSpeed,
    Method_claw_RunRotateToPos,
    Method_claw_GetClawInitState,
    Method_claw_GetRotateInitState,
    Method_claw_GetClawRunState,
    Method_claw_GetRotateRunState,
    Method_claw_GetClawCurPos,
    Method_claw_GetRotateCurPos,
    Method_claw_ClawInitDir,
    Method_claw_RotateInitDir,
    Method_claw_SaveData,
    Method_claw_SetDevId,
    Method_claw_ReleaseOrEnableClaw,
    Method_claw_ReleaseOrEnableRotate,


};

enum EnumMethod_Pump
{
    Method_pump_init = 2305,
    Method_pump_absorb,
    Method_pump_drain,
    Method_pump_ejectTip,
    Method_pump_LiquidDetect,
    Method_pump_moveAbsPos,
    Method_pump_relPosMoveUp,
    Method_pump_relPosMoveDown,
    Method_pump_writeReg,
    Method_pump_readReg,
    Method_pump_queryState,
    Method_pump_loopStart,
    Method_pump_loopEnd,
    Method_pump_delay,
    Method_pump_holdOn,
    Method_pump_stop,
    Method_pump_run,
    Method_pump_unset,
    Method_pump_restoreFac,
    Method_pump_saveParams,
    Method_pump_queryLiquidDetectResult,
};

enum EnumMethod_Scanner
{
    Method_Disable_PCR = 7425,//PCR区域设置(临时)
    Method_Disable_PCR_HOLE = 7426,//PCR孔位设置(临时)
    Method_ExScanner_set = 7641,
    Method_ExScanner_close_light,
    Method_ExScanner_open_light,
    Method_ExScanner_lighting,
    Method_ExScanner_close_green,
    Method_ExScanner_open_green,
    Method_ExScanner_greening,
    Method_ExScanner_close_led,
    Method_ExScanner_info,
    Method_ExScanner_barcodeData,
    Method_Scanner_version= 7651,
    Method_Scanner_singleTrig,
    Method_Scanner_closeSingleTrig,
    Method_Scanner_continueTrig,
    Method_Scanner_closeContinueTrig,
    Method_Scanner_reset,
    Method_Scanner_restoreToFac,
    Method_Scanner_openAutoTrig,
    Method_Scanner_closeAutoTrig,
    Method_Scanner_openNoDuplicate,
    Method_Scanner_closeNoDuplicate,
    Method_Scanner_closeCodeITF,
    Method_Scanner_openCodeITF,
    Method_Scanner_closeCode39,
    Method_Scanner_openCode39,
    Method_Scanner_closeCode128,
    Method_Scanner_openCode128,
    Method_Scanner_closeCodebar,
    Method_Scanner_openCodebar,
    Method_Scanner_fixDataNumITF,
    Method_Scanner_minDataNumITF,
    Method_Scanner_maxDataNumITF,
    Method_Scanner_minDataNum39,
    Method_Scanner_maxDataNum39,
    Method_Scanner_minDataNum128,
    Method_Scanner_maxDataNum128,
    Method_Scanner_minDataNumCodebar,
    Method_Scanner_maxDataNumCodebar,
    Method_Scanner_barcodeData,
    Method_Scanner_setCodeITFDataInfo,
    Method_Scanner_setCode39DataInfo,
    Method_Scanner_setCode128DataInfo,
    Method_Scanner_setCodebarDataInfo,
};

enum EnumMethod_FeatMngBoard
{
    Method_FeatMngBoard_GetCurrentTemp=1824,
    Method_FeatMngBoard_StartHeating,
    Method_FeatMngBoard_StopHeating,
    Method_FeatMngBoard_SetTempPIDParam,
    Method_FeatMngBoard_GetTempPIDParam,
    Method_FeatMngBoard_SetTempReportFreq,
    Method_FeatMngBoard_ReportCurrentTemp,
    Method_FeatMngBoard_SetAlarmTemp,
    Method_FeatMngBoard_SetTargetTemp,
    Method_FeatMngBoard_StartNegPressureFan=1840,
    Method_FeatMngBoard_StopNegPressureFan,
    Method_FeatMngBoard_SetFanParam,
    Method_FeatMngBoard_StartFullMachineFan,
    Method_FeatMngBoard_StopFullMachineFan,
    Method_FeatMngBoard_NotifyLockStateChange=1856,
    Method_FeatMngBoard_NotifyLocking,
    Method_FeatMngBoard_NotifyUnlocking,
    Method_FeatMngBoard_QueryLockState,
    Method_FeatMngBoard_Lock,
    Method_FeatMngBoard_Unlock,
    Method_FeatMngBoard_SampleTrayLockSensorStatus=1872,
    Method_FeatMngBoard_TipTrayLockSensorStatus,
    Method_FeatMngBoard_PCRTrayLockSensorStatus,
    Method_FeatMngBoard_ReagentBarLockSensorStatus,
    Method_FeatMngBoard_ExtractModuleLockSensorStatus,
    Method_FeatMngBoard_ExtractBarPositionStatusCheck,
    Method_FeatMngBoard_MagneticRodSleevePositionStatusCheck,
    Method_FeatMngBoard_MagneticRodSleevePositionStatusCheckCommand,

    Method_FeatMngBoard_StartPump=1888,
    Method_FeatMngBoard_StopPump,
    Method_FeatMngBoard_SetPumpParam,
    Method_FeatMngBoard_CloseValve,
    Method_FeatMngBoard_OpenValve,
    Method_FeatMngBoard_PowerOnUltrasoundModule=1904,
    Method_FeatMngBoard_PowerOffUltrasoundModule,
    Method_FeatMngBoard_TurnOnWhiteLight=1920,
    Method_FeatMngBoard_TurnOffWhiteLight,
    Method_FeatMngBoard_TurnOnDisinfectionUV=1936,
    Method_FeatMngBoard_TurnOffDisinfectionUV,
    Method_FeatMngBoard_ResetRestart,
    Method_FeatMngBoard_ShutDown,
    Method_FeatMngBoard_SetCanID,
    Method_FeatMngBoard_SetCanBaudRate,
    Method_FeatMngBoard_GetBoardInfo,
};

enum EnumMethod_PCRMainBoard
{
    Method_PCRMainBoard_IPConfig=3590,
    Method_PCRMainBoard_MotorMove,
    Method_PCRMainBoard_MotorResetParams,
    Method_PCRMainBoard_MotorSystemParams,
    Method_PCRMainBoard_MotorEnable,
    Method_PCRMainBoard_EncoderEnable,
    Method_PCRMainBoard_MotorStop,
    Method_PCRMainBoard_FanStart,
    Method_PCRMainBoard_FanStop,
    Method_PCRMainBoard_OpticalSensorStatus,
    Method_PCRMainBoard_MotorReset,
};

enum EnumMethod_PoweCtrlBoard
{
    Method_PoweCtrl_DisplayFanParam=9004,
    Method_PoweCtrl_UV=9008,
    Method_PoweCtrl_DoorSensor=9010,//5路门传感器显示
    Method_PoweCtrl_FanMonitorSet=9015,
};

enum EnumMethod_TEC {
    Method_TEC_PCR_StartOrStop=3850,
    Method_TEC_RequestTransmitTimingTable,
    Method_TEC_TransmitTimingData,
    Method_TEC_TransmitTimingEnd,
    Method_TEC_PCR_RunInfo,
    Method_TEC_SetStatusUploadInterval,
    Method_TEC_PCR_SignalReport,
    Method_TEC_SetTempPIDParam,
    Method_TEC_GetTempPIDParam,
    Method_TEC_SetCurrentPIDParam,
    Method_TEC_GetCurrentPIDParam,
    Method_TEC_SetVoltagePIDParam,
    Method_TEC_GetVoltagePIDParam,
    Method_TEC_SetTempThreshold,
    Method_TEC_SetSensorTempCalibration
};

enum EnumTECSignalType
{
    TECST_SEQ_END, //TEC时序运行结束
    TECST_PCR_LIGHT_DETECT,//PCR光学采样触发
    TECST_PCR_CYCLE_END,//PCR光学采样循环结束
    TECST_HRM_LIGHT_DETECT, //HRM光学采样触发
    TECST_HRM_CYCLE_END,//HRM采样循环结束
    TECST_PCR_LIGHT_DETECT_START,//PCR 采样循环开始
    TECST_MELT_START,//熔解开始
};

enum EnumPopWindowType
{
    ePopInfoWindow = 0,
    ePopWarnWindow = 1,
    ePopErrorWindow = 2,
    ePopSuccessWindow = 3,
    ePopQuestionWindow = 4
};


//上位机的保存日志类型
enum LogType
{
    LOG_TYPE_DEBUG = 0,
    LOG_TYPE_INFO = 1,
    LOG_TYPE_WARNING = 2,
    LOG_TYPE_ERROR = 3,
    LOG_TYPE_FATAL = 4,
    LOG_TYPE_SLAVE = 5,
    LOG_TYPE_MAX = 6,
};

typedef struct SLogSave
{
    LogType logType;
    QString strLog;
}SLogSaveStruct;

enum FaultCode
{
    FaultCode_Order=0,
    FaultCode_ID=1,
    FaultCode_Level=2,
    FaultCode_Unit=3,
    FaultCode_UserDesc=4,
    FaultCode_FacDesc=5,
    FaultCode_Handle=6
};
enum EMachineStatus
{
    Machine_Idle = 0,
    Machine_Ready_To_Running,// 在下发PCR和时序过程中，start指令前期
    Machine_Running_Timing,// 全流程
    Machine_Running_PCR,// 只PCR状态
    Machine_Abort,
    Machind_Error,
    Machine_Fault,
    Machine_Detectting ,
};

enum EUpdateType
{
    EUpdate_App = 0,
    EUpdate_Algo = 1,
    EUpdate_Auto = 2,
    EUpdate_Slave = 10,
    EUpdate_PCR = 11,
};

//enum ModuleNamePosDebug
//{
////    DebugType_MotorPos = 0,// 电机位置调试
////    DebugType_SampleScan,  // 样本扫码
////    DebugType_StripScan,   // 提取条扫码
////    DebugType_RFID,        // rfid调试
//    ModuleNamePosDebug_Sample= 0,  //样本模块
//    ModuleNamePosDebug_Extract= 1,  //提取模块
//    ModuleNamePosDebug_ADP= 2,  //移液模块
//    ModuleNamePosDebug_PCR=  3,  //PCR模块
//    ModuleNamePosDebug_SampleScan= 4,  //样本扫码模块
//   ModuleNamePosDebug_StripsScan= 5,  //提取条扫码模
//};


enum SubMethodPosDebug
{
    Method_DebugPos_AskActionList =4361,  ////查询调试模块的分解动作集合
    Method_DebugPos_AskDeviceList,  // 查询调试模块某个分解动作的相关调试电机集合
    Method_DebugPos_DoMotion,   //执行某个电机的某个动作
    Method_DebugPos_SaveParam,   //保存参数
    Method_DebugPos_Prepare,  //准备动作
    Method_DebugPos_Verify=4366, //验证子动作
    Method_DebugPos_LiquidTest=4367, //液面探测极限位置
    Method_DebugPos_LiquidCalSuck=4368, //移液校准
};

enum SubMethodAgingTest
{
    Method_AgingTest_AskActionList =4610,  ////查询老化的集合
    //执行动作自行查询数据库,并发送65
};

struct SCanBusDataStruct
{
    quint8 quVersion = 0x01;
    quint8 quMachineID = 0x00;
    quint8 quCmdID = 0x00;
    quint8 quDestinationID = 0x00;
    quint8 quSourceID = 0x00;
    quint8 quFormat = 0x01;
    quint16 quFrameSeq = 0x00;// 帧号
    quint16 quMethonID = 0x0000;
    quint8 quResult = 0x00;
    quint8 quSync = 0x00;
    quint8 quReserve = 0x00;
    quint16 quLength = 0x0000;
    QByteArray qbPayload = "";
};

struct SCanBusDeviceStruct
{
    QString strPlugin;// 接口类型
    QString strInterfaceName;// 接口名称
    qint32 iFrameId;// CAN总线标识符
    qint32 iBitRate;// 波特率
};
// 封装包
struct SCanBusFrameDataStruct
{
    quint8 quFrameID;
    QByteArray qbSendData;
};
// 运行状态信息
struct SRunningInfoStruct
{
    QString strPCRName = "";
    QString strTimingName = "";
    QString strCardID = "";
    QString streBeginTime = "";
    int iStatus = 0;// 运行状态
};

enum EColumName
{
    Colum_Index = 0,
    Colum_Name,
    Colum_Unit,
    Colum_Hole,
    Colum_WaitTime,
    Colum_MixTime,
    Colum_MixSpeed,
    Colum_MagenticTime,
    Colum_Volume,
    Colum_HeatHole,
    Colum_HeatTemp,
};

enum EnumDailyMonitorUploadType {
    DailyMonitor_FreezeArea,
    DailyMonitor_SplitArea,
    DailyMonitor_ElutionArea,
    DailyMonitor_FanArea
};

enum EnumDailyMonitorWarnSetType {
    DailyMonitorWarnSetType_FreezeArea,
    DailyMonitorWarnSetType_SplitArea,
    DailyMonitorWarnSetType_ElutionArea,
    DailyMonitorWarnSetType_FanArea

};

enum ConsumableType
{
    CT_TIP = 0,//Tip
    CT_TUBE,//扩增管
    CT_CAP,//扩增管帽
    CT_MAX,
    CT_REAGENT = 3,
    CT_RECYCLE_BIN, //回收仓
    CT_PCR_HOLE, //pcr孔位
};

enum AreaType
{
    AT_SAMPLE = 0,
    AT_TIP,
    AT_TUBECAP,
    AT_REAGENT,
};

enum RFIDConsumableType  //这个是根据植工那边定义的Idx，不要修改
{
    RFID_ConsumableType_Reagent1=1,
    RFID_ConsumableType_Reagent2,
    RFID_ConsumableType_Reagent3,
    RFID_ConsumableType_Reagent4,
    RFID_ConsumableType_Tip1,  //
    RFID_ConsumableType_Tip2,
    RFID_ConsumableType_TubeCap1,  //管帽
    RFID_ConsumableType_TubeCap2,  //管帽  
};

enum RecycleType  
{
    Recycle_TubeCap=9,  //回收管帽
    Recycle_Tip,        //回收tip
};

struct  ConsumableBox
{
    QString  strWireID;  //天线ID，舍弃没用，根据卡盒类型（唯一）去读取写入的，所以就算这个错的也没关系
    quint8 uiState;//盒的状态
    quint8 uiType;//卡盒类型
    qint8 iRemain;//剩余次数
    quint8 uiNextSingleAvrPos;//下一个抓取单个耗材的位置
    quint8 uiNextDoubleAvrPos;//下一个抓取两个耗材的位置
    QString strSeqNo;//序列号
    QString strBatchNo;//批号
    QDate qExpDate;//有效期
    quint8 uiCapacity;//总份数
    quint8 uiCRC;//校验
    quint8 uiColumnIndex;//排序使用
};


struct  ReagentBox
{
    QString  strWireID;; //天线ID，舍弃没用 ，根据卡盒类型（唯一）去读取写入的，所以就算这个错的也没关系
    quint8 uiColumnIndex;//当前所在列信息
    quint8 uiState;//试剂盒的状态
    quint8 uiType;//卡盒类型  注意转换   //植工那边需要这个区分
    QString strProjID;//项目ID
    QString strBatchNo;//批号(货号+生产日期+流水号）
    QDate qExpDate;//有效期
    quint8 uiCapacity;//总份数
    quint8 uiCompNum;//組分數
    QString strCompInfo;//組分信息
    quint8 uiSingleHoleCapacity;//單孔人份數
    quint8 uiRemain;//剩余次数
    quint8 uiNextAvrRowPos;//下一个位置
    QDate qFirstUsedTime;//開封日期
    quint8 uiExpDays;//開封有效天數
    quint8 uiHandleResultType;//處理結果類型
    float uiThreshold;//閾值
    float fInnerDensity;// 内标浓度
    //参考曲线个数
    //结果处理规则
    quint8 uiPunchHole=0;//已经刺破的孔位
    QDate  qPunchTime; //刺破日期
    quint8 uiPackHole=0; //已经复溶的孔位
    QDate  qPackTime;  //分装日期 qFirstUsedTime就是分装的时间};
};

struct WasterBin
{
    qint8 iRemain;//剩余次数
    quint8 uiCapacity;//总份数
    quint8 uiNextActionPos;//下一次丢弃位置
};


struct CRFIDTask
{
    int iMethod;  //区分读写
    RFIDConsumableType iType; //用于反馈及区分是试剂还是耗材
    ReagentBox rBox;     //是试剂的，写入用这个值
    ConsumableBox cBox;   //是耗材的，写入用这个值
    int isTubeTask;         //1管子 0不是管子   ，只有在RFID_ConsumableType_TubeCube1/2 起效
    // 因为管帽同用一个RFID，所以如果写入的数据为帽数据，管的数据要保持不变（在内部Map获取）
    //isTubeTask ==1  cBox 为Tube数据，需要取出cap数据整合
    quint16 ui16Delay = 0;//延时时间(管帽任务使用)
};
#if Q_OS_QML
#include <QQmlEngine>
#include <QJsonObject>
#include <QObject>
class CPublicConfig:public QObject
{
    Q_OBJECT
    Q_ENUMS(MEnumRunLogLevel)
    Q_ENUMS(MEColumName)
    Q_ENUMS(MEnumPageID)
    Q_ENUMS(MEnumMachineID)
    Q_ENUMS(MEnumTestState)
    // Q_ENUMS(MEnumMethod_MotherBoard)
    Q_ENUMS(MEnumMethod_PCR)
    Q_ENUMS(MEnumMethod_RFID)
    Q_ENUMS(MEnumMethod_Light)
    Q_ENUMS(MEnumMethod_Heater)
    Q_ENUMS(MEnumMethod_US)
    Q_ENUMS(MEnumMethod_Claw)
    Q_ENUMS(MEnumMethod_Pump)
    Q_ENUMS(MEMachineStatus)
    Q_ENUMS(MEnumMethod_US)
    Q_ENUMS(MEnumUpdateType)
    Q_ENUMS(MEnumMethod_Timing)
    Q_ENUMS(MEnumMethod_Scanner)
    Q_ENUMS(MEnumMethod_Motor01)
    Q_ENUMS(MEnumMethod_Motor02)
    Q_ENUMS(MEnumMethod_Motor03)
    Q_ENUMS(MEnumMethod_Motor04)
    Q_ENUMS(MEnumMethod_FeatMngBoard)
    Q_ENUMS(MEnumMethod_PCRMainBoard)
    Q_ENUMS(MEnumMethod_TEC)
    Q_ENUMS(MEnumPCRStartOrStop)
public:
    explicit CPublicConfig();
    ~CPublicConfig();

public:
    enum MEnumRunLogLevel
    {
        LEVEL_DEBUG=0,
        LEVEL_TRACE=1,// 普通用户提示信息等同ＩＮＦＯ
        LEVEL_WARN=2,
        LEVEL_ERROR=3,
        LEVEL_CRIT=4,
        LEVEL_ALERT=5,// 普通用户错误提示
        LEVEL_EMERG=6,
        LEVEL_OFF=7,
    };

    enum MEColumName
    {
        Colum_Index = 0,
        Colum_Name,
        Colum_Unit,
        Colum_Hole,
        Colum_WaitTime,
        Colum_MixTime,
        Colum_MixSpeed,
        Colum_MagenticTime,
        Colum_Volume,
        Colum_HeatHole,
        Colum_HeatTemp,
    };

    enum MEnumPageID
    {
        Page_Config = 0,
        Page_Normal,
        Page_Motor_Debug,
        Page_Motor_Compose,
        Page_Timing_Compose,
        Page_FLData,
        Page_MeltingCurve,
        Page_HRM,
        Page_Light_Debug,
    };

    enum MEnumTestState
    {
        PEST_SEQ_START,//时序开始
        PEST_SEQ_END,//时序结束
        PEST_SAMPLE_PROCESS_START,//样本处理开始
        PEST_SAMPLE_PROCESS_END,
        PEST_EXTRACT_START,//提取开始
        PEST_EXTRACT_END,
        PEST_SYSTEM_BUILD_START,//体系构建开始
        PEST_SYSTEM_BUILD_END,
        PEST_AMPLIFY_START,//扩增开始
        PEST_AMPLIFY_END,
        PEST_MELT_START,//熔解开始
        PEST_MELT_END,
    };

    enum MEnumMachineID
    {
        Machine_Middle_Host = 0,
        Machine_Function_manager_Ctrl,
        Machine_Motor_1,
        Machine_Motor_2,
        Machine_Motor_3,
        Machine_Temp_Ctrl,
        Machine_RFID,
        Machine_PCR_MainCtrl,
        Machine_PCR_Ctrl,
        Machine_PCR_Ctrl_1,
        Machine_PCR_Ctrl_2,
        Machine_PCR_Ctrl_3,
        Machine_PCR_Ctrl_4,
        Machine_PCR_Ctrl_5,
        Machine_PCR_Ctrl_6,
        Machine_PCR_Ctrl_7,
        Machine_PCR_Ctrl_8,
        Machine_PCR_Ctrl_9,
        Machine_PCR_Ctrl_10,
        Machine_PCR_Ctrl_11,
        Machine_PCR_Ctrl_12,
        Machine_PCR_Ctrl_13,
        Machine_PCR_Ctrl_14,
        Machine_PCR_Ctrl_15,
        Machine_PCR_Ctrl_16,
        Machine_Fluorence,
        Machine_GarbageStateCtrl,
        Machine_Scan_Board_1,
        Machine_Scan_Board_2,
        Machine_UpperHost,
    };

    enum MEnumMethod_Motor01
    {
        Method_MDB1_MCHK=257,
        Method_MDB1_SRST,
        Method_SCYReset=260,
        Method_SCYMTSamplePos,
        Method_SCYMTUncapDownPos,
        Method_SCYMTUncapOrCapPos,
        Method_SCYMTCapCatchPos,
        Method_SClampYReset=270,
        Method_SClampPartically,
        Method_SClampFully,
        Method_UC1ZReset=280,
        Method_UC1ZMTUncapPos,
        Method_UC1ZUncap,
        Method_UC1ZCap,
        Method_UC1ZMTCapPos,
        Method_UC1ZMTCatchPos,
        Method_UC2ZReset=290,
        Method_UC2ZMTUncapPos,
        Method_UC2ZUncap,
        Method_UC2ZCap,
        Method_UC2ZMTCapPos,
        Method_UC2ZMTCatchPos,
        Method_CReset=300,
        Method_CMTSwitchPos,
        Method_CMTMagnetShieldPos,
        Method_SACMTReagentPosForPunch,
        Method_SACMTReagentPosForExtract,
        Method_SACMTStripPutDownPos,
        Method_PZReset=310,
        Method_PZMTPunchPos,
        Method_CAMRReset=320,
        Method_CAMRMTWaitPos,
        Method_CAMRMTWaitPosByOC,
        Method_CAMRMTBottomPos,
        Method_CAMRUpAbsorbMag,
        Method_CAMRDownAbsorbMag,
        Method_CAMRMove,
        Method_CAMRMTTakeoffPos,
        Method_CAMSReset=330,
        Method_CAMSMTWaitPos,
        Method_CAMSMTBottomPos,
        Method_CAMSUpAbsorbMag,
        Method_CAMSDownAbsorbMag,
        Method_CAMSMove,
        Method_CAMSMix,
        Method_CAMSMTCatchPos,
        Method_CAMSMTTakeOffPos,
        Method_CAVTOMW=340,
        Method_CAVTMV,
        Method_CAMADO,
        Method_CAMAUP,
        Method_CAVTOMD
    };
    enum MEnumMethod_Motor02
    {
        Method_MDB2_MCHK=513,
        Method_MDB2_SRST,
        Method_PXReset=520,
        Method_PXMTPunchPos,
        Method_PXMTReagentZ1Pos,
        Method_PXMTTipAreaPos,
        Method_PX1MTTubeAreaPos=525,
        Method_PX1MTTubeCapArea1Pos,
        Method_PXMTPCRSwitchPos=529,
        Method_PXMTTipEjectPos,
        Method_PXMTReagentStripPos,
        Method_PXMTSampleAbsorbPos,
        Method_PXMTReagentZ2Pos,
        Method_PX2MTTubeAreaPos,
        Method_PX2MTTubeCapArea1Pos,
        Method_PYReset=540,
        Method_PYMTPunchPos,
        Method_PYMTReagentPos,
        Method_PYMTTipAreaPos,
        Method_PYMTTubeAreaPos=545,
        Method_PYMTTubeCapAreaPos,
        Method_PYMTPCRSwitchPos=549,
        Method_PYMTTipEjectPos,
        Method_PYMTReagentStripPos,
        Method_PYMTSampleAbsorbPos,
        Method_PXMTStripTipPos,
        Method_PZ1Reset=560,
        Method_PZ1MTPunchPos,
        Method_PZ1TipLoad,
        Method_PZ1TipEject,
        Method_PZ1MTAbsorbRedissolvePos,
        Method_PZ1MTSpitRedissolvePos,
        Method_PZ1MTAbsorbMixPos,
        Method_PZ1MTSpitMixPos,
        Method_PZ1MTAbsorbElutionPos,
        Method_PZ1MTSpitElutionPos,
        Method_PZ1MTAbsorbParaffinOilPos,
        Method_PZ1MTSpitParaffinOilPos,
        Method_PZ1LiquidDetect,
        Method_PZ1Move,
        Method_PZ1MTCapLoad,
        Method_PZ1MTCapTube,
        Method_PZ1MTPCRSwitchPos,
        Method_PZ1MTSampleTipLoadPos,
        Method_PZ1MTSampleAbsorbPos,
        Method_PZ1MTSampleSpitPos,
        Method_PZ1MTCleavageReagentAbsorbPos,
        Method_PZ1MTCleavageReagentSpitPos,
        Method_PZ1MTSampleTipEjectPos,
        Method_PZ1Mix,
        Method_PZ1MTWaitPos,
        Method_PZ2Reset=590,
        Method_PZ2MTPunchPos,
        Method_PZ2TipLoad,
        Method_PZ2TipEject,
        Method_PZ2MTAbsorbRedissolvePos,
        Method_PZ2MTSpitRedissolvePos,
        Method_PZ2MTAbsorbMixPos,
        Method_PZ2MTSpitMixPos,
        Method_PZ2MTAbsorbElutionPos,
        Method_PZ2MTSpitElutionPos,
        Method_PZ2MTAbsorbParaffinOilPos,
        Method_PZ2MTSpitParaffinOilPos,
        Method_PZ2LiquidDetect,
        Method_PZ2Move,
        Method_PZ2MTCapLoad,
        Method_PZ2MTCapTube,
        Method_PZ2MTPCRSwitchPos,
        Method_PZ2MTSampleTipLoadPos,
        Method_PZ2MTSampleAbsorbPos,
        Method_PZ2MTSampleSpitPos,
        Method_PZ2MTCleavageReagentAbsorbPos,
        Method_PZ2MTCleavageReagentSpitPos,
        Method_PZ2MTSampleTipEjectPos,
        Method_PZ2Mix,
        Method_PZ2MTWaitPos,
        Method_PStrutReset=620,
        Method_PumpStrutForConsume,
        Method_ReagentReset=630,
        Method_ReagentCloseCap,
        Method_ReagentOpenCap
    };
    enum MEnumMethod_Motor03
    {
        Method_MDB3_MCHK=769,
        Method_MDB3_SRST,
        Method_PCRSReset=790,
        Method_PCRSMTSwitch,
        Method_PCRXReset=800,
        Method_PCRXMTPCRSwitchPos,
        Method_PCRXMTCentrifugePos,
        Method_PCRXMTSubAreaPos,
        Method_PCRXMTPCRAbandonPos,
        Method_PCRXMTOpenCapCatchPos,
        Method_PCRXMTCloseCapCatchPos,
        Method_PCROpenCap,
        Method_PCRCloseCap,
        Method_PCRYReset=820,
        Method_PCRYMTPCRSwitchPos,
        Method_PCRYMTCentrifugePos,
        Method_PCRYMTSubAreaPos,
        Method_PCRYMTPCRAbandonPos,
        Method_PCRYMTOpenCapCatchPos,
        Method_PCRYMTCloseCapCatchPos,
        Method_PCRZReset=840,
        Method_PCRZSwitchPos,
        Method_PCRZCentrifugePos,
        Method_PCRZCatchOpPos,
        Method_PCRZAbandonPos,
        Method_PCRZOpenCapCatchPos,
        Method_PCRZCloseCapCatchPos,
        Method_PCRZSavePos,
        Method_CRST=900,
        Method_TAP,
        Method_TRP,
        Method_TV,
        Method_VF,
        Method_TT,
        Method_TF,
        Method_MS,
        Method_ACC,
        Method_FF,
        Method_ENorDS,
        Method_SP,
        Method_PCP,
        Method_PCI,
        Method_VCP,
        Method_VCI,
        Method_CP,
        Method_RCVS,
        Method_RCT,
        Method_RHRS,
        Method_QHRStatusTimeout,
        Method_Centrifuge,
    };
    enum MEnumMethod_Motor04
    {
        Method_RIGHT=1025,
        Method_LEFT,
        Method_ROTATE,
        Method_STOP,
        Method_MVTO,
        Method_MVBY,
        Method_GAP,
        Method_SAP,
        Method_SPEED,
        Method_RREG,
        Method_WREG,
        Method_SCMP,
        Method_GCMP,
        Method_RLCMP,
        Method_SDIR,
        Method_GDIR,
        Method_SPAM,
        Method_GPAM,
        Method_MOVE,
        Method_GMSL,
        Method_SMSL,
        Method_RLPAM,
        Method_STAT,
        Method_GXIO,
        Method_SREGA,
        Method_RREGA,
        Method_CREGA,
        Method_SRCHOP,
        Method_RRCHOP,
        Method_CRCHOP,
        Method_CLEARPOS,
        Method_ACTUALPOS,
        Method_ENN,
        Method_MSWST,
        Method_MSWFLAG,
        Method_GMCLK,
        Method_SMCLK,
        Method_RLCFG,
        Method_MOTOR_COMPOSE
    };
    enum MEnumMethod_PCR
    {
        Method_pcr_start = 513,
        Method_pcr_stop = 514,
        Method_pcr_tec_table_req = 515,
        Method_pcr_tec_table_data = 516,
        Method_pcr_tec_table_end = 517,
        Method_pcr_temp_info = 518,
        Method_set_info_interval,
        Method_pcr_signal,
        Method_pcr_version = 553,
        Method_pcr_reboot = 554,
        Method_pcr_cali = 590,
        Method_wait_signal = 591,
    };

    enum MEnumMethod_RFID
    {
        Method_rfid_read = 1536,
        Method_rfid_write = 1537,
    };

    enum MEnumMethod_Light
    {
        Method_FLLED=4100,
        Method_FLADC,
        Method_FLCST,
        Method_FLCDT,
        Method_FLFREQ,
        Method_FLMST,
        Method_FLMDT,
        Method_FLGSET,
        Method_FLGREAD,
        Method_FLCYEND,
    };


    enum MEnumMethod_Heater
    {
        Method_HTST=1025,  //启动加热
        Method_HTSP,  //停止加热
        Method_HTSET, //设置PID参数
        Method_HTGET, //读取PID参数
        Method_HTCALC, //温度校准
        Method_ht_param, //设置/读取参数设置
        Method_HTCHK,
    };

    enum MEnumMethod_US
    {
        Method_US_USST=1281,         //启动超声
        Method_US_USSP=1282,         //停止超声
        Method_US_USPSET=1283,       //设置超声功率PID参数
        Method_US_USMSET=1284,       //设置超声电机PID参数
        Method_US_AMP=1285,          //设置超声振幅
        Method_US_GPWR=1286,         //获取运行状态
        Method_US_USREBOOT=1287,     //重启超声模块/时间校正
        Method_US_USPARAM=1288,      //超声参数
        Method_US_USFTY=1289,        // 模块厂家0：佳源达，1：东方金荣
        Method_US_USVERSION=1290,    //版本信息
        Method_US_USINFO=1291        //获取超声状态，频率，振幅 和 功率
    };

    enum MEnumMethod_Claw
    {
        Method_claw_InitClaw = 2049,
        Method_claw_InitRotate,
        Method_claw_EmerStop,
        Method_claw_ClawTorque,
        Method_claw_ClawRunSpeed,
        Method_claw_RunClawToPos,
        Method_claw_SetRotateTorque,
        Method_claw_RotateRunSpeed,
        Method_claw_RunRotateToPos,
        Method_claw_GetClawInitState,
        Method_claw_GetRotateInitState,
        Method_claw_GetClawRunState,
        Method_claw_GetRotateRunState,
        Method_claw_GetClawCurPos,
        Method_claw_GetRotateCurPos,
        Method_claw_ClawInitDir,
        Method_claw_RotateInitDir,
        Method_claw_SaveData,
        Method_claw_SetDevId,
        Method_claw_ReleaseOrEnableClaw,
        Method_claw_ReleaseOrEnableRotate,
    };

    enum MEnumMethod_Pump
    {
        Method_pump_init = 2305,
        Method_pump_absorb,
        Method_pump_drain,
        Method_pump_ejectTip,
        Method_pump_LiquidDetect,
        Method_pump_moveAbsPos,
        Method_pump_relPosMoveUp,
        Method_pump_relPosMoveDown,
        Method_pump_writeReg,
        Method_pump_readReg,
        Method_pump_queryState,
        Method_pump_loopStart,
        Method_pump_loopEnd,
        Method_pump_delay,
        Method_pump_holdOn,
        Method_pump_stop,
        Method_pump_run,
        Method_pump_unset,
        Method_pump_restoreFac,
        Method_pump_saveParams,
        Method_pump_queryLiquidDetectResult,
    };

    enum MEnumMethod_Scanner
    {
        Method_ExScanner_set = 7641,
        Method_ExScanner_close_light,
        Method_ExScanner_open_light,
        Method_ExScanner_lighting,
        Method_ExScanner_close_green,
        Method_ExScanner_open_green,
        Method_ExScanner_greening,
        Method_ExScanner_close_led,
        Method_ExScanner_info,
        Method_ExScanner_barcodeData,
        Method_Scanner_version= 7651,
        Method_Scanner_singleTrig,
        Method_Scanner_closeSingleTrig,
        Method_Scanner_continueTrig,
        Method_Scanner_closeContinueTrig,
        Method_Scanner_reset,
        Method_Scanner_restoreToFac,
        Method_Scanner_openAutoTrig,
        Method_Scanner_closeAutoTrig,
        Method_Scanner_openNoDuplicate,
        Method_Scanner_closeNoDuplicate,
        Method_Scanner_closeCodeITF,
        Method_Scanner_openCodeITF,
        Method_Scanner_closeCode39,
        Method_Scanner_openCode39,
        Method_Scanner_closeCode128,
        Method_Scanner_openCode128,
        Method_Scanner_closeCodebar,
        Method_Scanner_openCodebar,
        Method_Scanner_fixDataNumITF,
        Method_Scanner_minDataNumITF,
        Method_Scanner_maxDataNumITF,
        Method_Scanner_minDataNum39,
        Method_Scanner_maxDataNum39,
        Method_Scanner_minDataNum128,
        Method_Scanner_maxDataNum128,
        Method_Scanner_minDataNumCodebar,
        Method_Scanner_maxDataNumCodebar,
        Method_Scanner_barcodeData,
        Method_Scanner_setCodeITFDataInfo,
        Method_Scanner_setCode39DataInfo,
        Method_Scanner_setCode128DataInfo,
        Method_Scanner_setCodebarDataInfo,
    };

    enum MEnumMethod_FeatMngBoard
    {
        Method_FeatMngBoard_GetCurrentTemp=1824,
        Method_FeatMngBoard_StartHeating,
        Method_FeatMngBoard_StopHeating,
        Method_FeatMngBoard_SetTempPIDParam,
        Method_FeatMngBoard_GetTempPIDParam,
        Method_FeatMngBoard_SetTempReportFreq,
        Method_FeatMngBoard_ReportCurrentTemp,
        Method_FeatMngBoard_SetAlarmTemp,
        Method_FeatMngBoard_StartNegPressureFan=1840,
        Method_FeatMngBoard_StopNegPressureFan,
        Method_FeatMngBoard_SetFanParam,
        Method_FeatMngBoard_StartFullMachineFan,
        Method_FeatMngBoard_StopFullMachineFan,
        Method_FeatMngBoard_NotifyLockStateChange=1856,
        Method_FeatMngBoard_NotifyLocking,
        Method_FeatMngBoard_NotifyUnlocking,
        Method_FeatMngBoard_QueryLockState,
        Method_FeatMngBoard_Lock,
        Method_FeatMngBoard_Unlock,
        Method_FeatMngBoard_GetCoverSensorStatus=1872,
        Method_FeatMngBoard_GetTraySensorStatus,
        Method_FeatMngBoard_GetExtractSensorStatus,
        Method_FeatMngBoard_StartPump=1888,
        Method_FeatMngBoard_StopPump,
        Method_FeatMngBoard_SetPumpParam,
        Method_FeatMngBoard_CloseValve,
        Method_FeatMngBoard_OpenValve,
        Method_FeatMngBoard_PowerOnUltrasoundModule=1904,
        Method_FeatMngBoard_PowerOffUltrasoundModule,
        Method_FeatMngBoard_TurnOnWhiteLight=1920,
        Method_FeatMngBoard_TurnOffWhiteLight,
        Method_FeatMngBoard_TurnOnDisinfectionUV=1936,
        Method_FeatMngBoard_TurnOffDisinfectionUV,
        Method_FeatMngBoard_ResetRestart,
        Method_FeatMngBoard_ShutDown,
        Method_FeatMngBoard_SetCanID,
        Method_FeatMngBoard_SetCanBaudRate,
        Method_FeatMngBoard_GetBoardInfo,
    };

    enum MEnumMethod_PCRMainBoard
    {
        Method_PCRMainBoard_IPConfig=3590,
        Method_PCRMainBoard_MotorMove,
        Method_PCRMainBoard_MotorResetParams,
        Method_PCRMainBoard_MotorSystemParams,
        Method_PCRMainBoard_MotorEnable,
        Method_PCRMainBoard_EncoderEnable,
        Method_PCRMainBoard_MotorStop,
        Method_PCRMainBoard_FanStart,
        Method_PCRMainBoard_FanStop,
        Method_PCRMainBoard_OpticalSensorStatus,
        Method_PCRMainBoard_MotorReset,
    };

    enum MEnumMethod_TEC {
        Method_TEC_PCR_StartOrStop=3850,
        Method_TEC_RequestTransmitTimingTable,
        Method_TEC_TransmitTimingData,
        Method_TEC_TransmitTimingEnd,
        Method_TEC_PCR_RunInfo,
        Method_TEC_SetStatusUploadInterval,
        Method_TEC_PCR_SignalReport,
        Method_TEC_SetTempPIDParam,
        Method_TEC_GetTempPIDParam,
        Method_TEC_SetCurrentPIDParam,
        Method_TEC_GetCurrentPIDParam,
        Method_TEC_SetVoltagePIDParam,
        Method_TEC_GetVoltagePIDParam,
        Method_TEC_SetTempThreshold,
        Method_TEC_SetSensorTempCalibration
    };

    enum MEMachineStatus
    {
        Machine_Idle = 0,
        Machine_Running_Timing,// 全流程
        Machine_Running_PCR,// 只PCR状态
        Machine_Abort,
        Machind_Error,
        Machine_Fault,
        Machine_Detectting ,
    };

    enum MEnumUpdateType
    {
        EUpdate_App = 0,
        EUpdate_Algo = 1,
        EUpdate_Auto = 2,
        EUpdate_Slave = 10,
        EUpdate_PCR = 11,
    };

    enum MEnumPCRStartOrStop
    {
        Method_TEC_PCR_Start=1,
        Method_TEC_PCR_Stop=2,
    };

public:
    static QObject* qmlSingletonInstance( QQmlEngine* engine, QJSEngine* scriptEngine )
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
    static CPublicConfig &getInstance();
};
#endif
#endif // PUBLICCONFIG_H
