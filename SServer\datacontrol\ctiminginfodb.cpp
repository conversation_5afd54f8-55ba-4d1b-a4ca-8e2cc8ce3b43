﻿#include "ctiminginfodb.h"
#include <QDebug>
#include <QSqlError>
#include <QString>

#include "publicconfig.h"
#include "publicfunction.h"
#include "cglobalconfig.h"
#include <QThread>




CTimingDB::CTimingDB(QObject *parent) : CDBObject(parent)
{
    m_strExtractTabelName = "extract";
    m_strTimingExtractFieldsList << timingExtractFields.timing_name << timingExtractFields.ui_content
                                 << timingExtractFields.motor_content << timingExtractFields.revise_time
                                 << timingExtractFields.standard << timingExtractFields.remark;

    m_strTecTabelName = "tecEx"; // 0.4，新增tec运行时间
    m_strTecFieldsList << TecFields.name << TecFields.content_name << TecFields.content << TecFields.programs
                         << TecFields.steps << TecFields.standard << TecFields.time << TecFields.remarks;
}

CTimingDB &CTimingDB::getInstance()
{
    static CTimingDB cCTimingDB;
    return cCTimingDB;
}

void CTimingDB::initDataBase()
{
    QList<QStringList> strFieldsLists;
    strFieldsLists << m_strTimingExtractFieldsList << m_strTecFieldsList;

    QStringList strTableNamesList;
    strTableNamesList << m_strExtractTabelName << m_strTecTabelName;

    QStringList strFieldNameList;
    for (int i = 0; i != strFieldsLists.length(); ++i)
    {
        if (i >= strTableNamesList.size())
        {
            qWarning() << "Index out of range for strTableNameList";
            break;
        }
        strFieldNameList = strFieldsLists.at(i);

        if (this->addFieldToTable(strTableNamesList.at(i), strFieldNameList, false))
        {
            QDFUN_LINE << strTableNamesList.at(i) << "Table created successfully";
        }
        else
        {
            QDFUN_LINE << strTableNamesList.at(i) << "Failed to create table";
        }
    }
}

void CTimingDB::updateDBFile(const QString &strNewFilePath)
{
    // 获取旧数据库文件路径
    QString oldDbPath = CGlobalConfig::getInstance().getTimingDBDir();
    try
    {
        // 首先关闭已有的数据库连接
        closeAllConnections();

        // 把新的数据库文件复制到旧的位置
        if (QFile::exists(strNewFilePath))
        {// 如果旧的数据库文件存在，删除它
            if (QFile::exists(oldDbPath))
            {
                QFile::remove(oldDbPath);
            }
            QFile::copy(strNewFilePath, oldDbPath);
        }
        //建立新连接
        initDataBase();
    }
    catch (const std::exception &e)
    {
        qWarning() << "Exception during database reload:" << e.what();
    }
}

int CTimingDB::getTecFieldCount()
{
    return m_strTecFieldsList.size();
}

/////////////////////////////////////////////////////////////////////////////
// 提取
int CTimingDB::getExtractTimingDBCount()
{
    return this->getDBRecordCount(m_strExtractTabelName);
}

bool CTimingDB::addExtractTiming(QString strTimingName,
                                 QString strUIContenet, QString strMotorContent,
                                 QString strStandard, QString strRemark)
{
    if (getExtractTimingStandardFromName(strTimingName) == 1)
    {
        QDFUN_LINE << strTimingName << "为标准程序，无法修改";
        return false;
    }
    QString strReviseTime = CGlobalConfig::getInstance().getCurrentDateTime();
    QStringList strDataList = {strTimingName, strUIContenet, strMotorContent, strReviseTime,
                               strStandard, strRemark};
    bool bResult = this->addOneDBRecord(m_strExtractTabelName,
                                        m_strTimingExtractFieldsList,
                                        strDataList, timingExtractFields.timing_name);
    return bResult;
}

QString CTimingDB::copyExtractTimingFile(QString strTimingName)
{ //  不可枷锁
    QString strContent = this->getExtractTimingUIContentFromName(strTimingName);
    QString strMotorContent = this->getExtractTimingMotorContentFromName(strTimingName);
    QString strNewTimingName = strTimingName + "(1)";
    while (this->getExtractTimingUIContentFromName(strNewTimingName) != "")
    {
        strNewTimingName += "(1)";
    }
    this->addExtractTiming(strNewTimingName, strContent, strMotorContent);
    return strNewTimingName;
}

bool CTimingDB::deleteExtractTimingFromName(QString strTimingName)
{
    if (getExtractTimingStandardFromName(strTimingName) == 1)
    {
        QDFUN_LINE << strTimingName << "为标准程序，无法删除";
        return false;
    }
    bool bResult = this->deleteDBRecord(m_strExtractTabelName,
                                        timingExtractFields.timing_name, strTimingName);
    return bResult;
}

bool CTimingDB::deleteExtractTimingNotStandard()
{
    bool bResult = this->deleteDBRecord(m_strExtractTabelName,
                                        timingExtractFields.standard, "0");
    return bResult;
}

QStringList CTimingDB::getAllExtractTimingNames()
{
    QStringList strResult;
    QMap<QString, QString> strConditionMap;
    strResult = this->getMoreRecordsOneField(m_strExtractTabelName,
                                             timingExtractFields.timing_name,
                                             strConditionMap, timingExtractFields.revise_time);
    return strResult;
}

QStringList CTimingDB::getExtractTimingShowDataFromName(QString strName)
{
    QStringList strResult;
    if (strName == "")
    {
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
        {{timingExtractFields.timing_name, strName}};
    QStringList strGetRecordFields = {timingExtractFields.timing_name,
                                      timingExtractFields.revise_time};
    strResult = this->getOneRecordMoreFields(m_strExtractTabelName,
                                             strGetRecordFields, strConditionMap);
    return strResult;
}

QList<QStringList> CTimingDB::getAllExtractTimingShowData()
{
    QList<QStringList> strQueryResult;
    QStringList strGetFileds = {timingExtractFields.timing_name, timingExtractFields.revise_time};
    QMap<QString, QString> strConditionMap = {};
    strQueryResult = this->getMoreRecordsMoreFields(m_strExtractTabelName,
                                                    strGetFileds, strConditionMap,
                                                    timingExtractFields.revise_time);
    return strQueryResult;
}

QString CTimingDB::getAllExtractTimingData()
{
    QList<QStringList> strQueryResult;
    QString strResultData = "";
    QStringList strGetFileds = {timingExtractFields.timing_name, timingExtractFields.ui_content};
    QMap<QString, QString> strConditionMap = {};
    strQueryResult = this->getMoreRecordsMoreFields(m_strExtractTabelName,
                                                    strGetFileds, strConditionMap,
                                                    timingExtractFields.revise_time);
    for (auto &resultList : strQueryResult)
    {
        if (resultList.size() < 2)
        {
            qWarning() << "Unexpected result list size: " << resultList.size();
            break;
        }
        strResultData += resultList[0] + ":" + resultList[1] + "+";
    }
    strResultData = strResultData.remove(strResultData.length() - 1, 1);
    return strResultData;
}

QString CTimingDB::getExtractTimingUIContentFromName(QString strName)
{
    QString strResult = "";
    if (strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
        {{timingExtractFields.timing_name, strName}};

    strResult = this->getOneRecordOneField(m_strExtractTabelName,
                                           timingExtractFields.ui_content, strConditionMap)
                    .toString();
    return strResult;
}

QString CTimingDB::getExtractTimingMotorContentFromName(QString strName)
{
    QString strResult = "";
    if (strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
        {{timingExtractFields.timing_name, strName}};

    strResult = this->getOneRecordOneField(m_strExtractTabelName,
                                           timingExtractFields.motor_content, strConditionMap)
                    .toString();
    return strResult;
}

int CTimingDB::getExtractTimingStandardFromName(QString strName)
{
    int iResult = 0;
    if (strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return iResult;
    }
    QMap<QString, QString> strConditionMap =
        {{timingExtractFields.timing_name, strName}};

    iResult = this->getOneRecordOneField(m_strExtractTabelName,
                                         timingExtractFields.standard, strConditionMap)
                  .toInt();
    return iResult;
}

bool CTimingDB::addTecTiming(QString strTimingName, QString strContentName, QString strContent,
                             int strPrograms, int strSteps, int iTotalTime,
                             QString strRemarks)
{
    if (getTecStandardFromName(strTimingName) == 1)
    {
        QDFUN_LINE << strTimingName << "为标准程序，无法修改";
        return false;
    }
    QStringList strDataList = {strTimingName, strContentName, strContent, QString::number(strPrograms),
                               QString::number(strSteps), "0", QString::number(iTotalTime), strRemarks};
    bool bResult = this->addOneDBRecord(m_strTecTabelName,
                                        m_strTecFieldsList,
                                        strDataList, TecFields.name);
    return bResult;
}

Q_INVOKABLE bool CTimingDB::addTecTimingFromList(QStringList strTecTimingList)
{
    if(strTecTimingList.length() >= this->getTecFieldCount())
    {
        bool bResult = this->addOneDBRecord(m_strTecTabelName,
                                        m_strTecFieldsList,
                                        strTecTimingList, TecFields.name);
        return bResult;
    }
    return false;
}

QString CTimingDB::getTecContentFromName(QString strTimingName)
{
    QString strResult = "";
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
        {{TecFields.name, strTimingName}};

    strResult = this->getOneRecordOneField(m_strTecTabelName,
                                           TecFields.content, strConditionMap)
                    .toString();
    return strResult;
}

QString CTimingDB::getTecContentNameFromName(QString strTimingName)
{
    QString strResult = "";
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return strResult;
    }
    strResult = this->getOneRecordOneField(m_strTecTabelName,
                                           TecFields.content_name, TecFields.name, strTimingName)
                    .toString();
    return strResult;
}

int CTimingDB::getTecProgramsFromName(QString strTimingName)
{
    int iResult = 0;
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return iResult;
    }
    QMap<QString, QString> strConditionMap =
        {{TecFields.name, strTimingName}};

    iResult = this->getOneRecordOneField(m_strTecTabelName,
                                         TecFields.programs, strConditionMap)
                  .toInt();
    return iResult;
}

int CTimingDB::getTecStepsFromName(QString strTimingName)
{
    int iResult = 0;
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return iResult;
    }
    QMap<QString, QString> strConditionMap =
        {{TecFields.name, strTimingName}};

    iResult = this->getOneRecordOneField(m_strTecTabelName,
                                         TecFields.steps, strConditionMap)
                  .toInt();
    return iResult;
}

int CTimingDB::getTecStandardFromName(QString strTimingName)
{
    int iResult = 0;
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return iResult;
    }
    iResult = this->getOneRecordOneField(m_strTecTabelName,
                                         TecFields.standard, TecFields.name, strTimingName)
                  .toInt();
    return iResult;
}

int CTimingDB::getTotalTimeFromName(QString strTimingName)
{
    int iResult = 0;
    if (strTimingName.isEmpty())
    {
        QDFUN_LINE << "strTimingName is Empty";
        return iResult;
    }
    iResult = this->getOneRecordOneField(m_strTecTabelName,
                                         TecFields.time, TecFields.name, strTimingName)
                  .toInt();
    return iResult;
}

QStringList CTimingDB::getAllTecTimingName()
{
    QStringList strResult;
    strResult = this->getMoreRecordsOneField(m_strTecTabelName,
                                             TecFields.name);
    return strResult;
}

std::tuple<int, int, QString> CTimingDB::getTecInfoFromName(QString strTimingName)
{
    QStringList strResult;
    int iPrograms, iSteps = 0;
    QString strContext = "";
    if (strTimingName == "")
    {
        return {iPrograms, iSteps, strContext};
    }
    QMap<QString, QString> strConditionMap =
        {{TecFields.name, strTimingName}};
    QStringList strGetRecordFields = {TecFields.programs,
                                      TecFields.steps,
                                      TecFields.content};
    strResult = this->getOneRecordMoreFields(m_strTecTabelName,
                                             strGetRecordFields, strConditionMap);
    if (strResult.size() < 3)
    {
        qWarning() << "Unexpected result list size: " << strResult.size();
        return {};
    }
    iPrograms = strResult[0].toInt();
    iSteps = strResult[1].toInt();
    strContext = strResult[2];
    return {iPrograms, iSteps, strContext};
}

bool CTimingDB::deleteTecContentFromName(QString strName)
{
    if (getTecStandardFromName(strName) == 1)
    {
        QDFUN_LINE << strName << "为标准程序，无法删除";
        return false;
    }
    bool bResult = this->deleteDBRecord(m_strTecTabelName,
                                        TecFields.name, strName);
    return bResult;
}

quint32 CTimingDB::getTecRunTimeFromName(QString strTimingName)
{
    
    quint32 ui32RunTime = 0;
    if(strTimingName == "")
    {
        return ui32RunTime;
    }
    QMap<QString, QString> strConditionMap = {{TecFields.name, strTimingName}};
    QStringList strGetRecordFields = {TecFields.time};
    QStringList strResult = this->getOneRecordMoreFields(m_strTecTabelName,strGetRecordFields, strConditionMap);
    if (strResult.size() < 1) {
        qWarning() << "Unexpected result list size: " << strResult.size()<<strTimingName;
        return ui32RunTime;
    }
    ui32RunTime = strResult[0].toInt();
    return ui32RunTime;
}

CTimingInfoDB::CTimingInfoDB(QObject *parent) : CDBObject(parent)
{
    m_strTimingComposeTabelName = "timing_compose_table";
    m_strTimingComposeFieldsList << timingComposeFields.cmd_name << timingComposeFields.content;
    m_strTimingComplexTabelName = "timing_complex_table";
    m_strTimingComplexFieldsList << timingComplexFields.motor_board_index << timingComplexFields.unit_name
                                 << timingComplexFields.cmd_name << timingComplexFields.cmd_id
                                 << timingComplexFields.content;

    m_strTimingComplexComposeTabelName = "timing_complex_compose_table";
    m_strTimingComplexComposeFieldsList << timingComplexComposeFields.name
                                        << timingComplexComposeFields.content
                                        << timingComplexComposeFields.constant
                                        << timingComplexComposeFields.remark1;

    m_strTimingProcessTabelName = "timing_process_table";
    m_strTimingProcessFieldsList << timingProcessFields.cmd_name << timingProcessFields.content;

    m_strTimingConditionTabelName = "timing_condition_table";
    m_strTimingConditionFieldsList << timingConditionFields.cmd_name << timingConditionFields.content;

    m_strTimingExeTabelName = "timing_exe_table";
    m_strTimingExeFieldsList << timingExeFields.cmd_name << timingExeFields.content;


    m_strTimingComplexInfoName = "timing_complex_info";
    m_strTimingComplexInfoFieldsList << TimingComplexInfoFields.cmd_id << TimingComplexInfoFields.cmd_name;
}


CTimingInfoDB &CTimingInfoDB::getInstance()
{
    static CTimingInfoDB cCTimingInfoDB;
    return cCTimingInfoDB;
}

void CTimingInfoDB::initDataBase()
{
    QList<QStringList> strFieldsLists;
    strFieldsLists << m_strTimingComposeFieldsList << m_strTimingComplexFieldsList
                   << m_strTimingComplexComposeFieldsList
                   << m_strTimingProcessFieldsList << m_strTimingConditionFieldsList
                   << m_strTimingExeFieldsList <<m_strTimingComplexInfoFieldsList;

    QStringList strTableNamesList;
    strTableNamesList << m_strTimingComposeTabelName << m_strTimingComplexTabelName
                      << m_strTimingComplexComposeTabelName 
                      << m_strTimingProcessTabelName << m_strTimingConditionTabelName
                      << m_strTimingExeTabelName <<m_strTimingComplexInfoName;

    QVector<QPair<QString, QString>> fieldsAndTypes;
    QStringList strFieldNameList;
    for (int i = 0; i != strFieldsLists.length(); ++i)
    {
        if(i >= strTableNamesList.size()){
            qWarning() << "Index out of range for strTableNameList";
            break;
        }
        fieldsAndTypes.clear();
        strFieldNameList = strFieldsLists.at(i);
        for (auto strField : strFieldNameList)
        {
            fieldsAndTypes.push_back({strField, "VARCHAR"});
        }

        if (this->createDBTable(strTableNamesList.at(i), fieldsAndTypes))
        {
            QDFUN_LINE << strTableNamesList.at(i) << "Table created successfully";
        }
        else
        {
            QDFUN_LINE << strTableNamesList.at(i) << "Failed to create table";
        }
    }
}

bool CTimingInfoDB::addTimingComposeTiming(QString strName, QString strContent)
{
    QStringList strDataList = { strName, strContent};
    bool bResult =  this->addOneDBRecord(m_strTimingComposeTabelName,
                                         m_strTimingComposeFieldsList,
                                         strDataList, timingComposeFields.cmd_name);
    return bResult;
}

bool CTimingInfoDB::copyCMDFromName(QString strNewName, QString strDBName)
{
    // Get the content of the original command
    QString strContent = getTimingComposeContentFromName(strDBName);
    // Add a new command with the new name and the original content
    bool bResult = addTimingComposeTiming(strNewName, strContent);

    return bResult;
}

bool CTimingInfoDB::deleteTimingComposeTimingFromName(QString strName)
{
    bool bResult = this->deleteDBRecord(m_strTimingComposeTabelName,
                                        timingComposeFields.cmd_name, strName);
    return bResult;
}

QStringList CTimingInfoDB::getAllTimingComposeTimingNames()
{
    QStringList strList;
    QMap<QString, QString> strConditionMap = {};
    strList = this->getMoreRecordsOneField(m_strTimingComposeTabelName,
                                           timingComposeFields.cmd_name);
    return strList;
}

QString CTimingInfoDB::getTimingComposeContentFromName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComposeFields.cmd_name, strName}};

    strResult = this->getOneRecordOneField(m_strTimingComposeTabelName,
                                           timingComposeFields.content, strConditionMap).toString();
    return strResult;
}

bool CTimingInfoDB::deleteAllTimingComposeTiming()
{
    
    bool bResult = this->deleteDBRecord(m_strTimingComposeTabelName);
    return bResult;
}


bool CTimingInfoDB::addComplexTiming(QString strMotorBoardIndex, QString strUnitName, QString strName, QString strID,  QString strContent)
{
    QStringList strDataList = {strMotorBoardIndex, strUnitName, strName, strID, strContent};

    

    bool bResult = this->addOneDBRecord(m_strTimingComplexTabelName,
                                         m_strTimingComplexFieldsList,
                                        strDataList, timingComplexFields.cmd_id);
    return bResult;
}

bool CTimingInfoDB::copyComplexCMDFromID(QString strNewCMDID, QString strNewCMDName, QString strDBID)
{
    QString strContent = getComplexTimingContentFromID(strDBID);
    QString strName = getComplexNameContentFromID(strDBID);
    QString strUnitName = getUnitNameContentFromID(strDBID);
    int iMotorBoardIndex = getComplexMotorBoardIndexFromID(strDBID);
    bool isSuccess = addComplexTiming(QString::number(iMotorBoardIndex), strUnitName,
                                      strNewCMDName, strNewCMDID, strContent);
    return isSuccess;
}

bool CTimingInfoDB::deleteComplexTimingFromID(QString strID)
{
    

    bool bResult = this->deleteDBRecord(m_strTimingComplexTabelName, timingComplexFields.cmd_id, strID);

    return bResult;
}

QStringList CTimingInfoDB::getAllComplexTimingNames()
{
    QList<QStringList> strQueryResult;
    QStringList strResult;
    QStringList strGetFileds = {timingComplexFields.cmd_id, timingComplexFields.cmd_name,
                                timingComplexFields.unit_name};
    QMap<QString, QString> strConditionMap = {};
    
    strQueryResult = this->getMoreRecordsMoreFields(
                m_strTimingComplexTabelName, strGetFileds, strConditionMap,
                "CAST("
                + timingComplexFields.cmd_id
                + " AS INTEGER)");
    for(auto& resultList : strQueryResult)
    {
        if (resultList.size() < 3) {
            qWarning() << "Unexpected result list size: " << resultList.size();
            break;
        }
        strResult.push_back(resultList[0] + "_" + resultList[1] + "_" + resultList[2]);
    }
    return strResult;
}

QStringList CTimingInfoDB::getAllComplexTimingIdName()
{
    QList<QStringList> strQueryResult;
    QStringList strResult;
    QStringList strGetFileds = {timingComplexFields.cmd_id, timingComplexFields.cmd_name};
    QMap<QString, QString> strConditionMap = {};
    
    strQueryResult = this->getMoreRecordsMoreFields(
                m_strTimingComplexTabelName, strGetFileds, strConditionMap,
                "CAST("
                + timingComplexFields.cmd_id
                + " AS INTEGER)");
    for(auto& resultList : strQueryResult)
    {
        if (resultList.size() < 2) {
            qWarning() << "Unexpected result list size: " << resultList.size();
            break;
        }
        strResult.push_back(resultList[0] + "_" + resultList[1]);
    }
    return strResult;
}

QStringList CTimingInfoDB::getAllComplexTimingId()
{
    QList<QStringList> strQueryResult;
    QStringList strResult;
    QStringList strGetFileds = {timingComplexFields.cmd_id};
    QMap<QString, QString> strConditionMap = {};
    
    strQueryResult = this->getMoreRecordsMoreFields(
                m_strTimingComplexTabelName, strGetFileds, strConditionMap,
                "CAST("
                + timingComplexFields.cmd_id
                + " AS INTEGER)");
    for(auto& resultList : strQueryResult)
    {
        if (resultList.size() < 1) {
            qWarning() << "Unexpected result list size: " << resultList.size();
            break;
        }
        strResult.push_back(resultList[0]);
    }
    return strResult;
}
QString CTimingInfoDB::getComplexTimingNamesFormID(QString strID)
{
    QStringList strQueryResult ;
    QString strResult;
    if(strID == "")
    {
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexFields.cmd_id, strID}};
    QStringList strGetRecordFields = {timingComplexFields.cmd_id, timingComplexFields.cmd_name,
                                      timingComplexFields.unit_name};
    
    strQueryResult = this->getOneRecordMoreFields(m_strTimingComplexTabelName,
                                                  strGetRecordFields, strConditionMap);
    if (strQueryResult.size() < 3) {
        qWarning() << "Unexpected result list size: " << strQueryResult.size();
        return "";
    }
    strResult = strQueryResult[0]+ "_" + strQueryResult[1] + "_" + strQueryResult[2];
    return strResult;
}

QString CTimingInfoDB::getComplexTimingContentFromID(QString strID)
{
    QString strResult = "";
    if(strID.isEmpty())
    {
        QDFUN_LINE << "strID is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexFields.cmd_id, strID}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexTabelName,
                                           timingComplexFields.content, strConditionMap).toString();
    return strResult;
}

QString CTimingInfoDB::getComplexNameContentFromID(QString strID)
{
    QString strResult = "";
    if(strID.isEmpty())
    {
        QDFUN_LINE << "strID is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexFields.cmd_id, strID}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexTabelName,
                                           timingComplexFields.cmd_name, strConditionMap).toString();
    return strResult;
}

QString CTimingInfoDB::getUnitNameContentFromID(QString strID)
{
    QString strResult = "";
    if(strID.isEmpty())
    {
        QDFUN_LINE << "strID is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexFields.cmd_id, strID}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexTabelName,
                                           timingComplexFields.unit_name, strConditionMap).toString();
    return strResult;
}

int CTimingInfoDB::getComplexMotorBoardIndexFromID(QString strID)
{
    int iResult = 2;
    if(strID.isEmpty())
    {
        QDFUN_LINE << "strID is Empty";
        return iResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexFields.cmd_id, strID}};

    
    iResult = this->getOneRecordOneField(m_strTimingComplexTabelName,
                                         timingComplexFields.motor_board_index, strConditionMap).toInt();
    return iResult;
}

bool CTimingInfoDB::deleteAllComplexTiming()
{
    
    bool bResult = this->deleteDBRecord(m_strTimingComplexTabelName);
    return bResult;
}

bool CTimingInfoDB::addComplexComposeTiming(QString strName, QString strContent)
{
    QStringList strDataList = {strName, strContent};
    QStringList strFiledsList = {timingComplexComposeFields.name, timingComplexComposeFields.content};
    
    bool bResult =  this->addOneDBRecord(m_strTimingComplexComposeTabelName,
                                         strFiledsList,
                                         strDataList, timingComplexComposeFields.name);
    return bResult;
}

bool CTimingInfoDB::addComplexComposeConstant(QString strName, QString strContent)
{
    QStringList strDataList = { strName, strContent};
    QStringList strFiledsList = { timingComplexComposeFields.name, timingComplexComposeFields.constant};
    
    bool bResult =  this->addOneDBRecord(m_strTimingComplexComposeTabelName,
                                         strFiledsList,
                                         strDataList, timingComplexComposeFields.name);
    return bResult;
}

QString CTimingInfoDB::getComplexComposeContentFromeName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexComposeFields.name, strName}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexComposeTabelName,
                                           timingComplexComposeFields.content, strConditionMap).toString();
    return strResult;
}

QString CTimingInfoDB::getComplexComposeConstantFromeName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingComplexComposeFields.name, strName}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexComposeTabelName,
                                           timingComplexComposeFields.constant, strConditionMap).toString();
    return strResult;
}

QStringList CTimingInfoDB::getAllComplexComposeTimingNames()
{
    QStringList strResult;
    
    strResult = this->getMoreRecordsOneField(m_strTimingComplexComposeTabelName,
                                             timingComplexComposeFields.name);
    return strResult;
}

bool CTimingInfoDB::deleteComplexComposeFromName(QString strName)
{
    

    bool bResult = this->deleteDBRecord(m_strTimingComplexComposeTabelName,
                                        timingComplexComposeFields.name, strName);

    return bResult;
}

bool CTimingInfoDB::addProcessTiming(QString strName, QString strContent)
{
    QStringList strDataList = { strName, strContent};
    
    bool bResult =  this->addOneDBRecord(m_strTimingProcessTabelName,
                                         m_strTimingProcessFieldsList,
                                         strDataList, timingProcessFields.cmd_name);
    return bResult;
}

bool CTimingInfoDB::copyProcessCMDFromName(QString strNewName, QString strDBName)
{
    QString strContent = getProcessContentFromName(strDBName);
    qDebug() << strContent;
    bool bResult = addProcessTiming(strNewName, strContent);
    return bResult;
}

bool CTimingInfoDB::modifyProcessTimingName(QString strOldName, QString strNewName)
{
    bool bResult = false;
    QMap<QString, QString> strUpdataMap;
    QMap<QString, QString> strConditionMap;
    strUpdataMap.insert(timingProcessFields.cmd_name, strNewName);
    strConditionMap.insert(timingProcessFields.cmd_name, strOldName);
    
    bResult = this->updateDBRecord(m_strTimingProcessTabelName, strUpdataMap, strConditionMap);
    return bResult;
}

bool CTimingInfoDB::deleteProcessTimingFromName(QString strName)
{
    

    bool bResult = this->deleteDBRecord(m_strTimingProcessTabelName,
                                        timingProcessFields.cmd_name, strName);

    return bResult;
}

QStringList CTimingInfoDB::getAllProcessTimingNames()
{
    QStringList strResult;
    
    strResult = this->getMoreRecordsOneField(m_strTimingProcessTabelName,
                                             timingProcessFields.cmd_name);
    return strResult;
}

QString CTimingInfoDB::getProcessContentFromName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingProcessFields.cmd_name, strName}};

    
    strResult = this->getOneRecordOneField(m_strTimingProcessTabelName,
                                           timingProcessFields.content, strConditionMap).toString();
    return strResult;
}

bool CTimingInfoDB::deleteAllProcessTiming()
{
    
    bool bResult = this->deleteDBRecord(m_strTimingProcessTabelName);
    return bResult;
}

bool CTimingInfoDB::addProcessCondition(QString strName, QString strContent)
{
    QStringList strDataList = { strName, strContent};
    
    bool bResult =  this->addOneDBRecord(m_strTimingConditionTabelName,
                                         m_strTimingConditionFieldsList,
                                         strDataList, timingConditionFields.cmd_name);
    return bResult;
}

bool CTimingInfoDB::deleteProcessConditionFromName(QString strName)
{
    

    bool bResult = this->deleteDBRecord(m_strTimingConditionTabelName,
                                        timingConditionFields.cmd_name, strName);

    return bResult;
}

QStringList CTimingInfoDB::getAllProcessConditionNames()
{
    QStringList strResult;
    QMap<QString, QString> strConditionMap;
    
    strResult = this->getMoreRecordsOneField(m_strTimingConditionTabelName,
                                             timingConditionFields.cmd_name,
                                             strConditionMap,
                                             "CAST("
                                             + timingConditionFields.id
                                             + " AS INTEGER)");
    return strResult;
}

QString CTimingInfoDB::getProcessConditionContentFromName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingConditionFields.cmd_name, strName}};

    
    strResult = this->getOneRecordOneField(m_strTimingConditionTabelName,
                                           timingConditionFields.content, strConditionMap).toString();
    return strResult;
}

bool CTimingInfoDB::deleteAllProcessCondition()
{
    
    bool bResult = this->deleteDBRecord(m_strTimingConditionTabelName);
    return bResult;
}

bool CTimingInfoDB::addExeCondition(QString strName, QString strContent)
{
    QStringList strDataList = { strName, strContent};
    
    bool bResult =  this->addOneDBRecord(m_strTimingExeTabelName,
                                         m_strTimingExeFieldsList,
                                         strDataList, timingExeFields.cmd_name);
    return bResult;
}

bool CTimingInfoDB::deleteExeConditionFromName(QString strName)
{
    

    bool bResult = this->deleteDBRecord(m_strTimingExeTabelName,
                                        timingExeFields.cmd_name, strName);

    return bResult;
}

QStringList CTimingInfoDB::getAllExeConditionNames()
{
    QStringList strResult;
    QMap<QString, QString> strConditionMap;
    
    strResult = this->getMoreRecordsOneField(m_strTimingExeTabelName,
                                             timingExeFields.cmd_name,
                                             strConditionMap,
                                             "CAST("
                                             + timingExeFields.id
                                             + " AS INTEGER)");
    return strResult;
}

QString CTimingInfoDB::getExeConditionContentFromName(QString strName)
{
    QString strResult = "";
    if(strName.isEmpty())
    {
        QDFUN_LINE << "strName is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{timingExeFields.cmd_name, strName}};

    
    strResult = this->getOneRecordOneField(m_strTimingExeTabelName,
                                           timingExeFields.content, strConditionMap).toString();
    return strResult;
}

bool CTimingInfoDB::deleteAllExeCondition()
{
    
    bool bResult = this->deleteDBRecord(m_strTimingExeTabelName);
    return bResult;
}


bool CTimingInfoDB::addTimingComplexInfo(QString strCMDID, QString strCMDName)
{
    QStringList strDataList = { strCMDID, strCMDName};
    
    bool bResult =  this->addOneDBRecord(m_strTimingComplexInfoName,
                                         m_strTimingComplexInfoFieldsList,
                                         strDataList, TimingComplexInfoFields.cmd_id);
    return bResult;
}

QString CTimingInfoDB::getCMDNameFromID(QString strCMDID)
{
    QString strResult = "";
    if(strCMDID.isEmpty())
    {
        QDFUN_LINE << "strCMDID is Empty";
        return strResult;
    }
    QMap<QString, QString> strConditionMap =
    {{TimingComplexInfoFields.cmd_id, strCMDID}};

    
    strResult = this->getOneRecordOneField(m_strTimingComplexInfoName,
                                           TimingComplexInfoFields.cmd_name, strConditionMap).toString();
    return strResult;
}
