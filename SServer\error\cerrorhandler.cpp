#include "cerrorhandler.h"
#include <QtDebug>
#include <affair/cmoduleseqmapper.h>
#include "error/errorconfig.h"
#include "SampleControl/samplecontrol.h"
#include "datacontrol/errorcodedb.h"
#include "control/coperationunit.h"

CErrorHandler &CErrorHandler::getInstance()
{
    static CErrorHandler cErrorHandler;
    return cErrorHandler;
}

void CErrorHandler::_HandleReceiveList()
{
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QByteArray& qMessage = m_qSendMessageInfoList[m_iReadIndex.load()];
        // 环形队列
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        //解析
        qDebug()<<"CErrorHandler handle errorInfo"<<qMessage;
        if(qMessage.size()>=ERROR_CODE_LEN)
        {
            QString strPayloadString = QString::fromLocal8Bit(qMessage);
            QStringList strParams = strPayloadString.split(",");
            if(strParams.length() >= 1)
            {
                // QString strErrorCode = "02000101";//strParams.at(0);
                static QString strLastErrorCode;
                QString strErrorCode = strParams.at(0);//

                //step1. 获取錯誤等級信息
                quint8 iErrorLevel = ErrorCodeDB::getInstance().getFaultLevelFromErrorCode(strErrorCode).toUInt();
                if(iErrorLevel<ERR_ERROR)
                {
                    continue;//低于错误等级的中位机不进行处理，只在上位机提示
                }
                qDebug() << "CErrorHandler iErrorLevel"<<iErrorLevel;
                //FIXME 屏蔽故障处理
                continue;// 目前屏蔽故障处理
               //step2. 按等級處理消息
               if(strErrorCode.length()>4)//板ID（2）+子模塊ID（2）
               {
                   //
                   QString strSubModuleID = strErrorCode.left(4);
                   DevSubModuleItem subModuleItem;
                   findExactSubModuleItem(strSubModuleID, subModuleItem);
                   if(subModuleItem.uiImpactScope == IS_GlobalLevel)
                   {
                       //整机停止
                       QVector<quint8> qImpactExecSTVect = CModuleSeqMapper::getInstance().getSequences(LM_GLOBAL);
                        //清理关联的任务
                       SampleControl::getInstance().UpdateExecSTToError(qImpactExecSTVect);
                       emit sigStopProcess(false);
                   }
                   else if(subModuleItem.uiImpactScope == IS_ModuleLevel)
                   {
                       //模块部分影响
                       QVector<quint8> qImpactExecSTVect = CModuleSeqMapper::getInstance().getSequences(subModuleItem.uiModOrCompID);
                        SampleControl::getInstance().UpdateExecSTToError(qImpactExecSTVect);
                   }
                   else if(subModuleItem.uiImpactScope == IS_ComponentLevel)
                   {
                       //组件部分影响，设置关联组件状态
                        if(subModuleItem.uiModOrCompID>=CU_SAMPLE_RIGHT_CATCH
                                &&subModuleItem.uiModOrCompID <= CU_SAMPLE_LEFT_CATCH )
                        {
                            quint8 uiLogicalModule = LM_SAMPLE;
                            QVector<quint8> qImpactExecSTVect = CModuleSeqMapper::getInstance().getSequences(uiLogicalModule);
                             SampleControl::getInstance().UpdateExecSTToError(qImpactExecSTVect);
                        }
                        else if(subModuleItem.uiModOrCompID>=CU_GANTRY_RIGHT_PUMP
                                &&subModuleItem.uiModOrCompID <= CU_GANTRY_LEFT_PUMP )
                        {
                            quint8 uiLogicalModule = LM_GANTRY;
                            QVector<quint8> qImpactExecSTVect = CModuleSeqMapper::getInstance().getSequences(uiLogicalModule);
                             SampleControl::getInstance().UpdateExecSTToError(qImpactExecSTVect);
                        }
                       //停止关联部分，并设置状态
                   }
               }
            }
            continue;
        }

    }
}

CErrorHandler::CErrorHandler(QObject *parent)
    :CAffairBase(parent)
{

}

CErrorHandler::~CErrorHandler()
{

}


