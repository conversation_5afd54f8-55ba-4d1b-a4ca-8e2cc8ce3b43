#include "ccananalyzethread.h"
#include <QDebug>
#include <unistd.h>
#include "publicfunction.h"
#include "cglobalconfig.h"
#include "error/cerrornotify.h"

#define CAN_ID_OFFSET  0x20 //中位机和其他CAN 下位机通信的id偏移量, 下位机发送给中位机的CANID = 下位机CANID + CAN_ID_OFFSET
CCanAnalyzeThread::CCanAnalyzeThread(QObject *parent) : QObject(parent)
{
    m_bThreadExit = false;
    m_iFrameNumber = 0;
    // 参数初始化
    m_iMaxCanID = 0x1F;//由板的最大ID决定
    m_qbReadBuffArray.resize(m_iMaxCanID);
    m_iRedaBuffArrayLength.resize(m_iMaxCanID);
    m_iReadPayloadLength.resize(m_iMaxCanID);
    m_iReadSeqNumber.resize(m_iMaxCanID);
    // m_bReCalculation.resize(m_iMaxCanID);

    for(quint16 i = 0; i != m_iMaxCanID; ++i)
    {
        m_qbReadBuffArray[i] = "";
        m_qbReadBuffArray[i].reserve(1024 * 1024 * 4);
        // m_bReCalculation[i] = true;
    }

    m_pCleanupTimer = new QTimer();
    connect(m_pCleanupTimer, &QTimer::timeout, this, &CCanAnalyzeThread::slotCleanupReceivedPackets);
    m_pCleanupTimer->start(1000); // 每秒清理一次

    m_qReciveDataByteArrayList.reserve(40960);
    m_iPayloadMaxLength = 512;
    pthread_t tid;
    pthread_create(&tid, NULL, _createThreadHandleList,this);
}

CCanAnalyzeThread::~CCanAnalyzeThread()
{
    m_bThreadExit = true;
}

void CCanAnalyzeThread::slotReciveOriginalMessageData(QVector<QCanBusFrame> qCanFramsVector)
{
    this->_addReadCanFrame(qCanFramsVector);
}

void CCanAnalyzeThread::slotCleanupReceivedPackets()
{
    QDateTime current_time = QDateTime::currentDateTime();
    QMutexLocker locker(&m_qSameFrameMutex);
    auto it = m_qReceivedPackets.begin();
    while (it != m_qReceivedPackets.end())
    {
        if (it.value().secsTo(current_time) > 5)
        { // 清理5秒以上未被访问的数据包
            it = m_qReceivedPackets.erase(it);
        }
        else
        {
            ++it;
        }
    }
}


void CCanAnalyzeThread::_addReadCanFrame(const QVector<QCanBusFrame> &qCanFramsVector)
{
    for(int i = 0; i < qCanFramsVector.length(); ++i)
    {
        m_iCurrentWriteIndex = m_iWriteIndex.load();
        m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

        if (m_iNextWriteIndex == m_iReadIndex.load())
        { // 原则上不可能有65535个重发存在，故而不做考虑
            qWarning() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
            emit sigError(FT_Comm_CacheFull, "");
            return;
        }
        m_qSendMessageInfoList[m_iCurrentWriteIndex] = qCanFramsVector[i];
        m_iWriteIndex.store(m_iNextWriteIndex);
        m_conditionVariable.notify_one();// 唤醒
#ifndef ShortOutPutLog
        // qDebug() << "can read" << (qCanFramsVector[i].frameId() - CAN_ID_OFFSET) << qCanFramsVector[i].payload().toHex(':').toUpper();
#endif
    }
}

uint64_t CCanAnalyzeThread::_combineIDs(uint16_t can_id, uint16_t cmd_type,
                                        uint16_t frame_id, uint16_t command_id)
{
    return (static_cast<uint64_t>(can_id) << 48) | (static_cast<uint64_t>(cmd_type) << 32)
            | (static_cast<uint64_t>(frame_id) << 16) | command_id;
}

void CCanAnalyzeThread::_processPacket(const quint16 &iCanID, const quint16 &iCmdType,
                                       const quint16 &iSeqNumber, const quint16 &iMethodID)
{
    uint64_t combined_id = _combineIDs(iCanID, iCmdType, iSeqNumber, iMethodID);
    QDateTime current_time = QDateTime::currentDateTime();
    {
        QMutexLocker locker(&m_qSameFrameMutex);
        if (!m_qReceivedPackets.contains(combined_id))
        {
            // 数据包是新的，转发到业务层
            emit sigReciveMessage(m_qCurrentReciveDataByteArray);
            // 存储数据包的时间戳
            m_qReceivedPackets.insert(combined_id, current_time);
        }
        else
        {
            // 重复数据包，忽略
            qInfo() << "Warning Can_processSamePacket" << hex << "iCanID" << iCanID
                    << "iCmdType" << iCmdType << "iSeqNumber"<< iSeqNumber << "iMethodID" << iMethodID
                    << "m_qReceivedPackets time" << m_qReceivedPackets[combined_id];
        }
    }
}

void *CCanAnalyzeThread::_createThreadHandleList(void *arg)
{
    CCanAnalyzeThread* pCCanAnalyzeThread = (CCanAnalyzeThread*)arg;
    std::unique_lock<std::mutex> uniqueLock(pCCanAnalyzeThread->m_mutex);
    while(!pCCanAnalyzeThread->m_bThreadExit)
    {
        pCCanAnalyzeThread->m_conditionVariable.wait(uniqueLock, [pCCanAnalyzeThread]
        { return pCCanAnalyzeThread->m_iReadIndex.load() != pCCanAnalyzeThread->m_iWriteIndex.load()
                    || pCCanAnalyzeThread->m_bThreadExit;
        });
        if (pCCanAnalyzeThread->m_bThreadExit)
        {
            break;
        }
        pCCanAnalyzeThread->_handleReceiveList();
    }
    return NULL;
}
void CCanAnalyzeThread::_handleReceiveList()
{  // 接收
    while (m_iReadIndex.load() != m_iWriteIndex.load())
    {
        QCanBusFrame canBusFrame = m_qSendMessageInfoList[m_iReadIndex.load()];
        // 环形队列 自动加1避免陷入死循环
        m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
        m_iCanID = canBusFrame.frameId();
        m_iCanID -= CAN_ID_OFFSET;  //中位机和其他CAN 下位机通信的id偏移量, 下位机发送给中位机的CANID = 下位机CANID + CAN_ID_OFFSET
        if(m_iCanID >= 0 && m_iCanID < m_iMaxCanID)
        {
            m_qbReadBuffArray[m_iCanID] += canBusFrame.payload();
        }
        else
        {
            emit sigError(FT_Comm_IDError, QString("Bad canid %1").arg(m_iCanID));
            qWarning()<<"#####################CanId ERR:"<<m_iCanID;
            m_iCanID = 0;
            continue;
        }
        //
        m_iRedaBuffArrayLength[m_iCanID]= m_qbReadBuffArray[m_iCanID].length();
        while(m_iRedaBuffArrayLength[m_iCanID] >= gk_iFrameLengthNotData)// 一帧至少长度为21
        {
            if (m_qbReadBuffArray[m_iCanID][0] == static_cast<char>(0x40)
                    && m_qbReadBuffArray[m_iCanID][1] == static_cast<char>(0x4D)
                    && m_qbReadBuffArray[m_iCanID][2] == static_cast<char>(0x31)
                    && m_qbReadBuffArray[m_iCanID][3] == static_cast<char>(0x2A))
            {//
                // if(m_bReCalculation[m_iCanID])
                {
                    pLength = m_qbReadBuffArray[m_iCanID].data() + gk_iSeqPos;
                    m_iReadSeqNumber[m_iCanID] = GetByte2Int(pLength);
                    pLength = m_qbReadBuffArray[m_iCanID].data() + gk_iLengthPos;
                    m_iReadPayloadLength[m_iCanID] = GetByte2Int(pLength);
                    // m_bReCalculation[m_iCanID] = false;
                }
                // 判定帧长度与payload长度正确性
                if(m_iRedaBuffArrayLength[m_iCanID] >= m_iReadPayloadLength[m_iCanID] + gk_iFrameLengthNotData)//m_iFrameLengthNotData为除去payload的剩余帧长度
                {
                    // m_bReCalculation[m_iCanID] = true;
                    m_qFindHeaderByteArray = m_qbReadBuffArray[m_iCanID].mid(4, m_iReadPayloadLength[m_iCanID]+gk_iFrameLengthNotData-4);
                    m_iTitleIndex = m_qFindHeaderByteArray.indexOf(gk_strHeadBytes);// 在一个标准帧中查找是否存在header
                    if(m_iTitleIndex >= 0)
                    {// payload包含下一帧数据// payload包含@M1*
                        qInfo() << m_iCanID  << "before remove 0" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                        m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, 4 + m_iTitleIndex);// 删除bad帧
                        m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                        qDebug() << m_iCanID << "after remove 0" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                        continue;
                    }
                    // 判断CRC
                    m_qCRCByteArray = m_qbReadBuffArray[m_iCanID].mid(m_iReadPayloadLength[m_iCanID]+gk_iFrameDataPos, 2);
                    m_iReadDataCRC = m_qCRCByteArray.toHex().toInt(&m_bOk, 16);
                    m_iGetCRC = GetCRC16(m_qbReadBuffArray[m_iCanID].data(), m_iReadPayloadLength[m_iCanID] + gk_iFrameDataPos, 0);
                    if(m_iGetCRC != m_iReadDataCRC)
                    {
                        qDebug() << m_iCanID  << "Length" << m_iReadPayloadLength[m_iCanID] + gk_iFrameDataPos
                                              << "read crc" << m_iReadDataCRC << m_qCRCByteArray.toHex(':').toUpper() 
                                              << "calc crc" << m_iGetCRC << hex << m_iGetCRC;
                        emit sigError(FT_Comm_PacketCRCError, QString("calc crc:%1,read crc:%2").arg(m_iGetCRC).arg(m_iReadDataCRC));

                        // 去掉@M1*开始查找
                        m_qFindHeaderByteArray = m_qbReadBuffArray[m_iCanID].mid(4);
                        // qDebug() << "m_qFindHeaderByteArray" << m_qFindHeaderByteArray.toHex(':').toUpper();
                        // 存在下一帧数据，判断CRC中是否存在@,payload上面已经判断，不再判定
                        m_iTitleIndex = m_qFindHeaderByteArray.indexOf(gk_strHeadBytes);
                        if(m_iTitleIndex >= 0)
                        {
                            qDebug() << m_iCanID << "before remove 1" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                            m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, 4 + m_iTitleIndex);// 删除bad帧
                            m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                            qDebug() << m_iCanID << "after remove 1" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                            continue;
                        }
                        QString strHeadBytes = gk_strHeadBytes;
                        int iMidPos = 4;
                        bool bFind = false;
                        while(m_iTitleIndex < 0 && iMidPos > 1)
                        {
                            iMidPos--;
                            strHeadBytes = gk_strHeadBytes.mid(0, iMidPos);
                            m_iTitleIndex = m_qFindHeaderByteArray.indexOf(strHeadBytes);
                            if(m_iTitleIndex >= 0)
                            {
                                qDebug() << m_iCanID << "before remove 2" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                                m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, 4 + m_iTitleIndex);// 删除bad帧
                                m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                                qDebug() << m_iCanID << "after remove 2" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                                bFind = true;
                                break;
                            }
                        }
                        if(!bFind)
                        {  // 不存在@M1*或者@，丢帧，crc错误，直接丢弃
                            qDebug() << m_iCanID << "before remove 3" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                            m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, m_iReadPayloadLength[m_iCanID]+gk_iFrameLengthNotData);// 删除一帧
                            m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                            qDebug() << m_iCanID << "after remove 3" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                        }
                        continue;
                    }
                    //////////////////////////////////////////////////////////////////////

                    QString strRec = "";
                    m_qCurrentReciveDataByteArray = m_qbReadBuffArray[m_iCanID].mid(0, m_iReadPayloadLength[m_iCanID]+gk_iFrameLengthNotData);
                    if(m_qbReadBuffArray[m_iCanID][6] == static_cast<char>(CmdType_Ack))
                    {// ACK应答包
                        emit sigWaitACK(m_iReadSeqNumber[m_iCanID]);
                        #ifndef ShortOutPutLog
                            strRec = "ack";
                        #endif
                    }
                    else
                    {
                        if(m_qbReadBuffArray[m_iCanID][6] != static_cast<char>(CmdType_Bulletin))
                        {
                            // send ack,立刻返回ACK
                            SCanBusDataStruct sSCanBusDataStruct;
                            sSCanBusDataStruct.quMachineID =  m_qCurrentReciveDataByteArray[gk_iMachineIDPos];
                            sSCanBusDataStruct.quCmdID = CmdType_Ack;
                            sSCanBusDataStruct.quDestinationID = m_qCurrentReciveDataByteArray[gk_iSourceIDPos];
                            sSCanBusDataStruct.quSourceID = m_qCurrentReciveDataByteArray[gk_iDestinationIDPos];
                            sSCanBusDataStruct.quFrameSeq = m_iReadSeqNumber[m_iCanID];
                            m_iMethodACK = GetByte2Int(m_qCurrentReciveDataByteArray.data() + gk_iMethodIDPos);
                            sSCanBusDataStruct.quMethonID = m_iMethodACK;

                            QByteArray qSendAckBackArra = GetSendData(sSCanBusDataStruct);
                            emit sigSendACKBack(qSendAckBackArra);
                        }
                        _processPacket(m_iCanID, m_qCurrentReciveDataByteArray[gk_iCmdIDPos] , m_iReadSeqNumber[m_iCanID], m_iMethodACK);// 判定是否有重复帧包
                        #ifndef ShortOutPutLog
                            strRec = "data";
                        #endif
                    }
                    //////////////////////////////////////////////////////////////////////
                    m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, m_iReadPayloadLength[m_iCanID]+gk_iFrameLengthNotData);// 删除一帧
                    m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
#ifndef ShortOutPutLog
                    // qDebug() << "can finish" << strRec << m_iCanID << m_qCurrentReciveDataByteArray.toHex(':').toUpper();
                    // CGlobalConfig::getInstance().printMessageInfo(m_qCurrentReciveDataByteArray,
                    //                                               "[can ->server:" + QString("%1").arg(m_iCanID, 2, 10, QChar('0')) + " " + strRec + "]");
#endif
                }
                else
                {//等待足够的帧数据解析完整帧
                    
                    // 查找是不是丢帧，错误帧中包含头，不满足完整帧，但是中间包含头，丢弃前面部分
                    // 去掉@M1*开始查找
                    QByteArray qFindHead = m_qbReadBuffArray[m_iCanID].mid(4, m_iRedaBuffArrayLength[m_iCanID]-4);
                    // 存在下一帧数据，判断CRC中是否存在@,payload上面已经判断，不再判定
                    m_iTitleIndex = qFindHead.indexOf(gk_strHeadBytes);
                    // qDebug() << m_iCanID  << "Warning m_qbReadBuffArray: " << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() 
                    //                       << "Length" << m_iRedaBuffArrayLength[m_iCanID] << m_iReadPayloadLength[m_iCanID]
                    //                       << "m_iTitleIndex" << m_iTitleIndex;
                    if(m_iTitleIndex >= 0)
                    {
                        qInfo() << m_iCanID  << "before remove 4" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                        m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, 4 + m_iTitleIndex);// 删除bad帧
                        m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                        // m_bReCalculation[m_iCanID] = true;
                        qDebug() << m_iCanID << "after remove 4" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                        continue;// 帧中间有header，丢弃再次尝试
                    }
                    break;// 等待后继帧数据
                }
            }
            else
            {
                #ifndef ShortOutPutLog
                // qDebug() << m_iCanID  << "can bad, not @M1* " << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper();
                #endif
                // m_bReCalculation[m_iCanID] = true;
                m_iTitleIndex = m_qbReadBuffArray[m_iCanID].indexOf("@");
                if(m_iTitleIndex <= 0)
                {// 稳定后，整帧丢弃
                    qInfo() << m_iCanID  << "before remove 5" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                    m_qbReadBuffArray[m_iCanID] = "";
                    m_iRedaBuffArrayLength[m_iCanID] = 0;
                    qDebug() << m_iCanID << "after remove 5" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                }
                else
                {
                    qInfo() << m_iCanID  << "before remove 6" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                    m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, m_iTitleIndex);// 首字节不是@M1*，摒弃
                    m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                    qDebug() << m_iCanID << "after remove 6" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
                }
            }
        }

        if(m_iRedaBuffArrayLength[m_iCanID] < gk_iFrameLengthNotData)
        {
            m_iTitleIndex = m_qbReadBuffArray[m_iCanID].indexOf(gk_strHeadBytes);
            if(m_iTitleIndex > 0)
            {
                qInfo() << m_iCanID  << "before remove 7" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                m_qbReadBuffArray[m_iCanID] = m_qbReadBuffArray[m_iCanID].remove(0, m_iTitleIndex);
                m_iRedaBuffArrayLength[m_iCanID] = m_qbReadBuffArray[m_iCanID].length();
                qDebug() << m_iCanID << "after remove 7" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
            }
            else if(m_iTitleIndex < 0 && m_iRedaBuffArrayLength[m_iCanID] > 0)
            {
                if (!(m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() == "00" 
                    || m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() == "00:00"))
                    {
                        qInfo() << m_iCanID  << "before remove 8" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iTitleIndex;
                    }
                m_qbReadBuffArray[m_iCanID] = "";
                m_iRedaBuffArrayLength[m_iCanID] = 0;
                // qDebug() << m_iCanID << "after remove 8" << m_qbReadBuffArray[m_iCanID].toHex(':').toUpper() << m_iRedaBuffArrayLength[m_iCanID];
            }
        }
    }
}
