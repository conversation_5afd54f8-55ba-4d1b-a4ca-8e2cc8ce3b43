#include "cstrip.h"
#include "QDebug"


CStrip &CStrip::getInstance()
{
    static CStrip instance;
    return instance;
}

CStrip::CStrip()
{
    InitStrip();
}

bool CStrip::IsStripEnough(quint16 size)
{
    QMutexLocker locker(&m_qMutex);
    bool bResult = false;
    if(m_uiSize>=size)
    {
        bResult = true;
    }
    m_uiLastIndex = STRIP_MAX_SIZE;
    return bResult;
}

void CStrip::SetStripSize(quint16 uiSize)
{
    QMutexLocker locker(&m_qMutex);
    m_uiPreIndex = -1;//-1;//��ʱΪ1,����2������������ʱ����
    m_uiSize = uiSize;
}

QVector<quint8> CStrip::Consume(quint16 uiConsumeSize)
{
    QMutexLocker locker(&m_qMutex);
    QVector<quint8> qVect;
    if(m_uiSize>=uiConsumeSize)
        m_uiSize -= uiConsumeSize;
    else
        qDebug()<<"CStrip isn't enough to consume," << "TotalSize:"<<m_uiSize<<" ConsumeSize:"<<uiConsumeSize;
    for(qint8 i= 0;i<uiConsumeSize;i++)
    {
        qVect.push_back(++m_uiPreIndex);
    }
    m_vCurConsume.clear();
    m_vCurConsume.append(qVect);
    qDebug()<<"CStrip Consume:"<<qVect.size()<<qVect;
    return qVect;
}

void CStrip::InitStrip()
{
    QMutexLocker locker(&m_qMutex);
    m_uiSize = STRIP_MAX_SIZE;
    m_uiMagneticPos = 0;
}

int CStrip::GetMagneticRodSleeveQuantity()
{
    QMutexLocker locker(&m_qMutex);
    quint16 num = m_uiMagneticPos;
    unsigned short count = 0;
    while (num) {
        count += num & 1;
        num >>= 1;
    }
    return count;
}

quint16 CStrip::GetMagneticRodSleevePositionAll()
{
    return m_uiMagneticPos;
}

void CStrip::GetMagneticRodSleevePositionAll(QList<int> &list)
{
    list.clear();
    for (int i = 0; i < STRIP_MAX_SIZE; i++) 
    {
        list.append(GetMagneticRodSleevePositionBit(i));
    }
    qDebug() << "CStrip::GetMagneticRodSleevePositionAll"<<list.size()<<list;
}

int CStrip::GetMagneticRodSleevePositionBit(int bitPos)
{
    return (m_uiMagneticPos >> bitPos) & 1;
}

void CStrip::SetMagneticRodSleevePositionAll(quint16 uiValue)
{
    QMutexLocker locker(&m_qMutex);
    m_uiMagneticPos = uiValue;
    // ��Ҫ������ȡ���Ŀ�ʼ�ͽ���λ��
    QList<int> list;
    GetMagneticRodSleevePositionAll(list);
    // ���ҿ�ʼ����
    auto startIt = std::find(list.begin(), list.end(), 1);
    int startIndex = 0;
    if (startIt != list.end())
    {
        startIndex = std::distance(list.begin(), startIt);
    }
    // ���ҽ�������
    auto endIt = std::find_if(list.rbegin(), list.rend(), [](int val) { return val == 1; });
    int endIndex = STRIP_MAX_SIZE;
    if (endIt != list.rend())
    {
        endIndex = list.size() - std::distance(list.rbegin(), endIt) - 1;
    }

    int onesCount = std::count(list.begin(), list.end(), 1);// ͳ�ƹ�����Ч״̬ 1-��Ч״̬

    m_uiPreIndex = startIndex-1;//������ڵ�һ��λ�÷�ֹ��ȡ��(��Ҫ���ݹ���״̬��ȡ)����1����Ϊ�����Ǵ�0��ʼ��Consume������++m_uiPreIndex
    m_uiSize = onesCount;
    m_uiLastIndex = endIndex;

    qDebug()<<"SetMagneticRodSleevePositionAll"<<startIndex<<endIndex<<m_uiPreIndex<<m_uiSize<<m_uiMagneticPos<<m_uiLastIndex;
}

void CStrip::SetMagneticRodSleevePositionBit(int bitPos, int bitValue)
{   
    QMutexLocker locker(&m_qMutex);
    if (bitValue == 1) {
        m_uiMagneticPos |= 1 << bitPos;
    } else {
        m_uiMagneticPos &= ~(1 << bitPos);
    }    
}

bool CStrip::GetMagneticRodSleevePositionBit16()
{
    int iRet = GetMagneticRodSleevePositionBit(15);
    qDebug()<<"GetMagneticRodSleevePositionBit16: "<<iRet;
    // return iRet == 1;
    return true;
}

QVector<quint8> CStrip::GetCurConsume()
{
    qDebug()<<"GetCurConsume: "<<m_vCurConsume.size();
    return m_vCurConsume;
}

QVector<quint8> CStrip::GetNextConsume(quint16 uiConsumeSize)
{
    QMutexLocker locker(&m_qMutex);
    quint16 uiSize = m_uiSize;
    quint16 uiPreIndex = m_uiPreIndex;
    QVector<quint8> qVect;
    if(uiSize>=uiConsumeSize)
    {
        uiSize -= uiConsumeSize;
    }
    else
    {
        qDebug()<<"CStrip isn't enough to next consume," << "TotalSize:"<<m_uiSize<<" ConsumeSize:"<<uiConsumeSize;
    }
    qDebug()<<"GetNextConsume uiPreIndex"<<uiPreIndex;

    for(qint8 i= 0;i<uiConsumeSize;i++)
    {
        qVect.push_back(++uiPreIndex);
    }
    return qVect;
}

void CStrip::ResetStripSize()
{
    quint16 uiValue = GetMagneticRodSleevePositionAll();
    SetMagneticRodSleevePositionAll(uiValue);
}

bool CStrip::CheckLastStripInLeftNext()
{
    bool bRet = false;
    const quint8 uiPos = 15;//��15����ȡ��λ��
    if (GetMagneticRodSleevePositionBit(uiPos))
    {
        if (m_uiLastIndex <= (m_uiPreIndex+2))// ���жϵ�ǰʹ����ȡ���Ƿ��Ѿ��ӽ�����ߣ�����+2�ǿ��ܳ���������ģ��������ȡ��λ��
        {
            bRet = true;
        }
    }
    qDebug()<<"CheckLastStripInLeftNext"<<m_uiSize<<m_uiLastIndex<<m_uiPreIndex<<bRet;
    return bRet;
}

bool CStrip::CheckLastStripInLeft()
{
    bool bRet = false;
    const quint8 uiPos = 15;//��15����ȡ��λ��
    if (GetMagneticRodSleevePositionBit(uiPos))
    {
        if (m_uiLastIndex <= m_uiPreIndex)// ���жϵ�ǰʹ����ȡ���Ƿ��Ѿ��ӽ�����ߣ�����+2�ǿ��ܳ���������ģ��������ȡ��λ��
        {
            bRet = true;
        }
    }
    qDebug()<<"CheckLastStripInLeft"<<m_uiSize<<m_uiLastIndex<<m_uiPreIndex<<bRet;
    return bRet;
}

bool CStrip::CheckStripInOrder()
{
    QList<int> list;
    GetMagneticRodSleevePositionAll(list);  // ��ȡ������ȡ��״̬��0/1��
    
    // �ҵ�һ�� 1 ������
    int first = list.indexOf(1);
    if (first == -1) return true;  // ����ȡ������Ϊ����

    // �����һ�� 1 ������
    int last = list.lastIndexOf(1);

    // ��� first �� last ֮���Ƿ�ȫΪ 1
    for (int i = first; i <= last; ++i) {
        if (list[i] != 1) return false;  // ���ڼ����������
    }

    return true;  // ���� 1 ����
}

quint8 CStrip::GetStripValidSize()
{
    QList<int> list;
    GetMagneticRodSleevePositionAll(list);  // ��ȡ������ȡ��״̬��0/1��
    // ��������ֵΪ1������
    quint8 count = 0;
    for (int i = 0; i < list.size(); ++i) {
        if (list[i] == 1) count++;
    }
    qDebug()<<"GetStripValidSize: "<<count;
    return count;
}