﻿#ifndef CTIMINGINFODB_H
#define CTIMINGINFODB_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QMutex>

#if Q_OS_QML
#include <QQmlEngine>
#endif
#include "cdbobject.h"


class CTimingDB : public CDBObject
{
    Q_OBJECT

public:
#if Q_OS_QML
    static QObject *qmlSingletonInstance(QQmlEngine *engine, QJSEngine *scriptEngine)
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static CTimingDB &getInstance();

protected:
    QString getDatabasePath() const override
    {
        return CGlobalConfig::getInstance().getTimingDBDir(); // CTestDB 的数据库路径
    }

    QString getConnectionName() const override
    {
        return gk_strTimingDBConnect;
    }

    WriteConnectionPool *getWriteConnectionPool() override {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }

public:
    Q_INVOKABLE void initDataBase();
    void updateDBFile(const QString &strNewFilePath);
    int getTecFieldCount();

public:
    // 提取时序
    Q_INVOKABLE int getExtractTimingDBCount();
    Q_INVOKABLE bool addExtractTiming(QString strTimingName,
                                      QString strUIContenet, QString strMotorContent, QString strStandard = "0",
                                      QString strRemark = "");
    Q_INVOKABLE QString copyExtractTimingFile(QString strTimingName);
    Q_INVOKABLE bool deleteExtractTimingFromName(QString strTimingName);
    Q_INVOKABLE bool deleteExtractTimingNotStandard();
    Q_INVOKABLE QStringList getAllExtractTimingNames();
    Q_INVOKABLE QStringList getExtractTimingShowDataFromName(QString strName); // name-time-fast-lock
    Q_INVOKABLE QList<QStringList> getAllExtractTimingShowData();
    Q_INVOKABLE QString getAllExtractTimingData();

    Q_INVOKABLE QString getExtractTimingUIContentFromName(QString strName);
    Q_INVOKABLE QString getExtractTimingMotorContentFromName(QString strName);
    Q_INVOKABLE int getExtractTimingStandardFromName(QString strName);

    // TEC时序
    Q_INVOKABLE bool addTecTiming(QString strTimingName, QString strContentName, QString strContent,
                                  int strPrograms, int strSteps, int iTotalTime,
                                  QString strRemarks);                                  
    Q_INVOKABLE bool addTecTimingFromList(QStringList strTecTimingList);
    Q_INVOKABLE QString getTecContentFromName(QString strTimingName);
    Q_INVOKABLE QString getTecContentNameFromName(QString strTimingName);
    Q_INVOKABLE int getTecProgramsFromName(QString strTimingName);
    Q_INVOKABLE int getTecStepsFromName(QString strTimingName);
    Q_INVOKABLE int getTecStandardFromName(QString strTimingName);
    Q_INVOKABLE int getTotalTimeFromName(QString strTimingName); // 暂无TD段时间计算
    Q_INVOKABLE QStringList getAllTecTimingName();
    std::tuple<int, int, QString> getTecInfoFromName(QString strTimingName); // 返回程序段数，步骤数，内容
    Q_INVOKABLE bool deleteTecContentFromName(QString strName);
    

    /**
     * @brief getTecRunTimeFromName 返回程序运行时间
     * @param strTimingName         时序名称
     * @return
     */
    quint32 getTecRunTimeFromName(QString strTimingName);

private:
    explicit CTimingDB(QObject *parent = nullptr);

private:
    struct TimingExtractTableFields
    {
        static inline const QString timing_name = "timing_name";
        static inline const QString ui_content = "ui_content";
        static inline const QString motor_content = "motor_content";
        static inline const QString revise_time = "revise_time";
        static inline const QString standard = "standard";
        static inline const QString remark = "remark";
    };

    struct TecTableFields
    {
        static inline const QString name = "name";
        static inline const QString content_name = "content_name";
        static inline const QString content = "content";
        static inline const QString programs = "programs";
        static inline const QString steps = "setps";
        static inline const QString standard = "standard";
        static inline const QString time = "time";
        static inline const QString remarks = "remarks";
    };

private:
    TimingExtractTableFields timingExtractFields;
    QString m_strExtractTabelName;
    QStringList m_strTimingExtractFieldsList;

    TecTableFields TecFields;
    QString m_strTecTabelName;
    QStringList m_strTecFieldsList;
};

class CTimingInfoDB : public CDBObject
{
    Q_OBJECT


public:
#if Q_OS_QML
    static QObject* qmlSingletonInstance( QQmlEngine* engine, QJSEngine* scriptEngine )
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
    static CTimingInfoDB& getInstance();
    Q_INVOKABLE void initDataBase();


protected:
    QString getDatabasePath() const override
    {
        return CGlobalConfig::getInstance().getTimingInfoDBDir(); // CTestDB 的数据库路径
    }

    QString getConnectionName() const override
    {
        return gk_strTimingInfoDBConnect;
    }

    WriteConnectionPool *getWriteConnectionPool() override {
        static WriteConnectionPool pool(getDatabasePath(), getConnectionName());
        return &pool;
    }

public:
    // 时序子指令集合
    Q_INVOKABLE bool addTimingComposeTiming(QString strName, QString strContent);
    Q_INVOKABLE bool copyCMDFromName(QString strNewName, QString strDBName);
    Q_INVOKABLE bool deleteTimingComposeTimingFromName(QString strName);
    Q_INVOKABLE QStringList getAllTimingComposeTimingNames();
    Q_INVOKABLE QString getTimingComposeContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllTimingComposeTiming();
    // 复合指令
    Q_INVOKABLE bool addComplexTiming(QString strMotorBoardIndex, QString strUnitName, QString strName, QString strID, QString strContent);
    Q_INVOKABLE bool copyComplexCMDFromID(QString strNewCMDID, QString strNewCMDName, QString strDBID);
    Q_INVOKABLE bool deleteComplexTimingFromID(QString strID);
    Q_INVOKABLE QStringList getAllComplexTimingNames();// id_name_unit
    Q_INVOKABLE QStringList getAllComplexTimingIdName();// id_name
    Q_INVOKABLE QStringList getAllComplexTimingId();// id_name
    Q_INVOKABLE QString getComplexTimingNamesFormID(QString strID);// id_name
    Q_INVOKABLE QString getComplexTimingContentFromID(QString strID);// content
    Q_INVOKABLE QString getComplexNameContentFromID(QString strID);// name
    Q_INVOKABLE QString getUnitNameContentFromID(QString strID);// unit
    Q_INVOKABLE int getComplexMotorBoardIndexFromID(QString strID);// strMotorBoardIndex
    Q_INVOKABLE bool deleteAllComplexTiming();

    // 业务复合
    Q_INVOKABLE bool addComplexComposeTiming(QString strName, QString strContent);
    Q_INVOKABLE bool addComplexComposeConstant(QString strName, QString strContent);
    Q_INVOKABLE QString getComplexComposeContentFromeName(QString strName);
    Q_INVOKABLE QString getComplexComposeConstantFromeName(QString strName);
    Q_INVOKABLE QStringList getAllComplexComposeTimingNames();
    Q_INVOKABLE bool deleteComplexComposeFromName(QString strName);


    // 流程调试
    Q_INVOKABLE bool addProcessTiming(QString strName, QString strContent);
    Q_INVOKABLE bool copyProcessCMDFromName(QString strNewName, QString strDBName);
    Q_INVOKABLE bool modifyProcessTimingName(QString strOldName, QString strNewName);
    Q_INVOKABLE bool deleteProcessTimingFromName(QString strName);
    Q_INVOKABLE QStringList getAllProcessTimingNames();
    Q_INVOKABLE QString getProcessContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllProcessTiming();
    // 状态条件
    Q_INVOKABLE bool addProcessCondition(QString strName, QString strContent);
    Q_INVOKABLE bool deleteProcessConditionFromName(QString strName);
    Q_INVOKABLE QStringList getAllProcessConditionNames();
    Q_INVOKABLE QString getProcessConditionContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllProcessCondition();
    // 执行条件
    Q_INVOKABLE bool addExeCondition(QString strName, QString strContent);
    Q_INVOKABLE bool deleteExeConditionFromName(QString strName);
    Q_INVOKABLE QStringList getAllExeConditionNames();
    Q_INVOKABLE QString getExeConditionContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllExeCondition();


    //指令id名称对照表
    Q_INVOKABLE bool addTimingComplexInfo(QString strCMDID, QString strCMDName);
    Q_INVOKABLE QString getCMDNameFromID(QString strCMDID);

private:
    explicit CTimingInfoDB(QObject *parent = nullptr);

private:
    struct TimingComposeTableFields {
        static inline const QString cmd_name = "cmd_name";
        static inline const QString content = "content";
    };
    struct TimingComplexTableFields {
        static inline const QString motor_board_index = "motor_board_index";
        static inline const QString unit_name = "unit_name";
        static inline const QString cmd_name = "cmd_name";
        static inline const QString cmd_id = "cmd_id";
        static inline const QString content = "content";
    };
    struct TimingComplexComposeTableFields {
        static inline const QString name = "name";
        static inline const QString content = "content";
        static inline const QString constant = "constant";
        static inline const QString remark1 = "remark1";
    };
    struct TimingProcessTableFields {
        static inline const QString cmd_name = "cmd_name";
        static inline const QString content = "content";
    };
    struct TimingConditionTableFields {
        static inline const QString id = "id";
        static inline const QString cmd_name = "cmd_name";
        static inline const QString content = "content";
    };
    struct TimingExeTableFields {
        static inline const QString id = "id";
        static inline const QString cmd_name = "cmd_name";
        static inline const QString content = "content";
    };
    struct TimingComplexInfoFields {
        static inline const QString cmd_id = "cmd_id";
        static inline const QString cmd_name = "cmd_name";
    };


private:
    TimingComposeTableFields timingComposeFields;
    QString m_strTimingComposeTabelName;
    QStringList m_strTimingComposeFieldsList;

    TimingComplexTableFields timingComplexFields;
    QString m_strTimingComplexTabelName;
    QStringList m_strTimingComplexFieldsList;

    TimingComplexComposeTableFields timingComplexComposeFields;
    QString m_strTimingComplexComposeTabelName;
    QStringList m_strTimingComplexComposeFieldsList;

    TimingProcessTableFields timingProcessFields;
    QString m_strTimingProcessTabelName;
    QStringList m_strTimingProcessFieldsList;

    TimingConditionTableFields timingConditionFields;
    QString m_strTimingConditionTabelName;
    QStringList m_strTimingConditionFieldsList;

    TimingExeTableFields timingExeFields;
    QString m_strTimingExeTabelName;
    QStringList m_strTimingExeFieldsList;

    TimingComplexInfoFields TimingComplexInfoFields;
    QString m_strTimingComplexInfoName;
    QStringList m_strTimingComplexInfoFieldsList;

};




#endif // CTIMINGINFODB_H
