#ifndef CPUMONITOR_H
#define CPUMONITOR_H

#include<QObject>
#include<QVector>
#include<QProcess>
#include<QTimer>

class CpuUsage {
public:
    int cpuId;                   // CPU核心ID，-1表示所有核心的总体使用率
    double usagePercentage;      // CPU使用率百分比

    CpuUsage(int id, double usage) : cpuId(id), usagePercentage(usage) {}

    CpuUsage(){}
};

class CpuMonitor : public QObject
{
    Q_OBJECT
public:
    static CpuMonitor &getInstance();

    /**
     * @brief Start 开始监控
     * @param msec 监控频率(毫秒)
     * @return  
     */
    void Start(qint32 msec);

signals:

private slots:

    /**
     * @brief fetchCpuStats 定时获取CPU数据
     * @return
     */
    void fetchCpuStats();

    /**
     * @brief onProcessFinished 进程结束
     * @param exitCode 退出码
     * @return  
     */
    void onProcessFinished(int, QProcess::ExitStatus);  

    /**
     * @brief onProcessCalcFinished 进程结束
     * @param exitCode 退出码
     * @return  
     */
    void onProcessCalcFinished(int, QProcess::ExitStatus);
private:
    /**
     * @brief calculateCpuUsage 计算CPU占用率
     * @param procStatOutput 获取结果
     * @return  
     */
     QVector<CpuUsage> calculateCpuUsage();

    /**
     * @brief getCpuUsage 计算CPU占用率
     * @param  
     * @return  获取结果
     */
    double getCpuUsage();

     CpuMonitor();
     ~CpuMonitor();
private:
    QTimer *timer;                                // 定时器
    QVector<QVector<long long>> currentCpuData;   // 当前CPU数据
    QVector<QVector<long long>> previousCpuData;  // 上一次CPU数据

};
#endif // CPUMONITOR_H
