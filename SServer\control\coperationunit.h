/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description: 指令集操作，与上位机同一份代码
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef COPERATIONUNIT_H
#define COPERATIONUNIT_H


#include <QObject>

#if Q_OS_QML
#include <QQmlEngine>
#endif

class COperationUnit : public QObject
{
    Q_OBJECT
public:
    explicit COperationUnit(QObject *parent = nullptr);
    static COperationUnit &getInstance();
#if Q_OS_QML
    static QObject* qmlSingletonInstance( QQmlEngine* engine, QJSEngine* scriptEngine )
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return &getInstance();
    }
#endif
signals:


public:
    // DestinationID， sync， 当前machineID可以用于DestinationID，后期如果扩增多机器，可以分开两个参数
    // quiMachineID：会根据指令寻找默认的目标ID，但是如果发往非协议区间ID，需要指定，比如返回结果至上位机，需要明确指定
    // 命令帧CmdID：0x00
    Q_INVOKABLE void sendCmd(const int &iMethodID, const quint8 & quiMachineID = 0,
                               const quint8 &quiSync = 0);
    Q_INVOKABLE void sendDataList(const int &iMethodID, const QStringList & strDataList,
                                    const quint8 & quiMachineID = 0,  const quint8 &quiSync = 0, const quint8 & quCmdID =0x00);
    Q_INVOKABLE void sendStringData(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID = 0, const quint8 &quiSync = 0,  const quint8 & quCmdID =0x00);
    Q_INVOKABLE void sendQByteData(const int &iMethodID, const QByteArray & byteData,
                                    const quint8 & quiMachineID = 0, const quint8 &quiSync = 0,  const quint8 & quCmdID =0x00);

    // 数据帧类型CmdID：0x01
    Q_INVOKABLE void sendDigitalList(const int &iMethodID, const QStringList & strDataList,
                                    const quint8 & quiMachineID = 0,  const quint8 & quiResult = 0);
    Q_INVOKABLE void sendStringDigital(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID = 0, const quint8 & quiResult = 0);
    // 返回结果CmdID：0x03
    Q_INVOKABLE void sendResult(const int &iMethodID, const quint8 & quiMachineID = 0,
                               const quint8 & quiResult = 0);
    Q_INVOKABLE void sendResultList(const int &iMethodID, const QStringList & strDataList,
                                    const quint8 & quiMachineID = 0,  const quint8 & quiResult = 0);
    Q_INVOKABLE void sendStringResult(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID = 0, const quint8 & quiResult = 0);
    // 通知帧类型CmdID：0x04
    Q_INVOKABLE void sendNotify(const int &iMethodID, const quint8 & quiMachineID = 0,
                               const quint8 & quiResult = 0);
    Q_INVOKABLE void sendNotifyList(const int &iMethodID, const QStringList & strDataList,
                                    const quint8 & quiMachineID = 0,  const quint8 & quiResult = 0);
    Q_INVOKABLE void sendStringNotify(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID = 0, const quint8 & quiResult = 0);
    // Bulletin消息类型CmdID: 0x05
    Q_INVOKABLE void sendBulletin(const int &iMethodID, const quint8 & quiMachineID = 0,
                               const quint8 & quiResult = 0);
    Q_INVOKABLE void sendBulletinList(const int &iMethodID, const QStringList & strDataList,
                                    const quint8 & quiMachineID = 0,  const quint8 & quiResult = 0);
    Q_INVOKABLE void sendStringBulletin(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID = 0, const quint8 & quiResult = 0);


private:
    // 基础函数
    void _SendData(const quint8 &quiMachineID, const int &iMethodID, const quint8 &quCmdID ,
                   const quint8 &quObjectID, const quint8 &quSync, const QByteArray &qPayload = "", const quint8 &quiResult = 0);
    void  _AddSendCmdData(const quint8 & quiMachineID, const int &iMethodID, const quint8 & quCmdID ,
                            const quint8 & quObjectID, const quint8 & quSync, const quint8 & quiResult = 0);
    void  _AddSendListData(const quint8 & quiMachineID, const int &iMethodID, const QStringList &strDataList,
                             const quint8 & quCmdID , const quint8 & quObjectID, const quint8 & quSync, const quint8 & quiResult = 0);
    void  _AddSendStringData(const quint8 &quiMachineID, const int &iMethodID, const QString &strData,
                             const quint8 &quCmdID , const quint8 &quObjectID, const quint8 &quSync, const quint8 &quiResult = 0);
private:
    static COperationUnit *sm_pInstance;
};

#endif // COPERATIONUNIT_H
