#ifndef CRFIDCTRL_H
#define CRFIDCTRL_H


#include<QObject>
#include"publicconfig.h"
#include "error/errorconfig.h"
#include<QVector>
//（试剂） ReagentBox
//天线ID（add）
//卡盒类型
//卡盒状态
//项目ID(货号,w288)
//批号(货号+生产日期+流水号）
//有效期
//总份数
//组分数,不同的冻干球数量，如ABABABAB，组分数为2（1/2/3/4/8）
//试剂说明 组分A/B/C分别是什么，组分之间以','分隔
//单孔人份数
//剩余次数（份数，如AB类型，最多份数为48）
//下一个位置
//开封日期
//开封效期
//结果处理规则
//阈值
//内标溶度



//(耗材）ConsumableBox
//天线ID（add）
//卡盒类型 Tip(0)/PCR Cube/ PCR Cube cap
//卡盒状态
//批号（货号+生产日期+流水)
//有效期
//总份数
//剩余份数
//下一次抓取单个耗材的位置
//下一个抓取双个耗材的位置



class CRFIDCtrl : public QObject
{
    //线程已经控制发送的时候要等到feedback才进行下一个task
    Q_OBJECT
public:
    explicit CRFIDCtrl(QObject *parent = nullptr);
    static CRFIDCtrl &getInstance();

    /**
     * @brief RefreshDataFromRFID 从RFID刷新耗材/试剂数据 并更新到数组
     * @param cType
     * @return 0 :ok 1:fail
     */
    int RefreshDataFromRFID(RFIDConsumableType cType);

    /**
     * @brief WriteDataToRFIDConsumableBox写Tip数据到RFID
     * @param box
     * @param bSendToRFID  是否发送到RFID
     * @return  0 :ok 1:fail
     */
    int WriteTipDataToRFIDConsumableBox(ConsumableBox box,bool bSendToRFID = true);

    /**     //需要在内部将管帽数据整合
     * @brief WriteCapToRFIDConsumableBox写数据到RFID
     * @param box
     * @param bSendToRFID  是否发送到RFID
     * @return  0 :ok 1:fail
     */
    int WriteTubeDataToRFIDConsumableBox(ConsumableBox Tubebox,bool bSendToRFID = true);

    /**     //需要在内部将管帽数据整合
     * @brief WriteCapToRFIDConsumableBox写数据到RFID
     * @param box
     * @param bSendToRFID  是否发送到RFID
     * @return  0 :ok 1:fail
     */
     int WriteCapDataToRFIDConsumableBox(ConsumableBox Capbox,bool bSendToRFID = true);

    /**
     * @brief WriteDataToRFIDReagentBox 写试剂数据到RFID
     * @param box
     * @param bSendToRFID  是否发送到RFID
     * @return  0 :ok 1:fail
     */
    int WriteDataToRFIDReagentBox(ReagentBox box,bool bSendToRFID = true);
    RFIDConsumableType ConverIdxToConsumableType(quint8 uiType, quint8 uiIndex);

    /**
     * @brief SendMotion                  读写RFID操作
     * @param iMachineID            机器ID
     * @param iMethod                  方法ID
     * @param strInputParam       仅iMethod=Method_rfid_write 作为输入生效
     * @param strOutputRs           仅iMethod=Method_rfid_read 作为结果输出
     * @return
     */
    int SendMotion(int iMachineID,int iMethod,QString strInputParam,int iType);
    
    
    /**
     * @brief ConverRFIDDataAndUpData   解析结果并压入耗材或试剂Map
     * @param strData
     * @param cType
     * @return    0 :ok 1:fail
     */
    int _converRFIDDataAndUpdate(QString strData ,RFIDConsumableType cType);

signals:
    void sigError(QString strExtraInfo, MidMachineSubmodule subModule, ErrorID errorID);
    void SignWaitFeedBack(int iMethodID,int iType);
    void SignResetStatus();

public slots:
    void SlotRFIDReadData(int iType,QString strData);//
private:
        /**
         * @brief ConverWireIDToIdx   将天线ID转换为数组到Idx (iIdx 从0 开始)
         * @param strWireID 天线ID
         * @param iIdx
         * @return  0 :ok 1:fail
         * WireID
         *  冷藏仓   一个RFID 对应一个卡条
         * Tip与PCR管帽 一个天线管 2盒子
         * 1,2 ->0,1 (冷藏仓)
         * 3,4 ->2,3 (冷藏仓)
         * 5,6 -> 0,1  //tip
         * 7,8 ->0,1  //管帽
         */
        int _converBoxTypeToIdx(RFIDConsumableType cType, int &iIdx);
    // 0 成功 ，1 FAIL
    int _getMachineIDFromBoxType(RFIDConsumableType cType,int &iMachineID);


    
    bool _processRFIDReagentData(QStringList strItem, RFIDConsumableType cType);
    bool _processRFIDTipData(QStringList strItem, RFIDConsumableType cType);
    bool _processRFIDTubeCapData(QStringList strItem, RFIDConsumableType cType);


private:
    //key:Reagent1,Reagent2,Reagent3,Reagent4   ,里面到uiType 是植工的1～8
    QMap<QString, ReagentBox> m_ReagentBoxMap;  //试剂的vector 4个 ，通过Refresh得到;
   //key:Tip1,Tip2,Tube1,Tube2,Cap1,Cap2 里面到uitype 是植工的1～8
    QMap<QString, ConsumableBox> m_ConsumableBoxMap;//耗材的vector 6个 ，按找中位机编码，这里也管帽分开管理
};


#endif // CRFIDCTRL_H
