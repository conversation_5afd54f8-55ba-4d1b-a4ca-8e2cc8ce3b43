#include "CCodeScannerAnalyze.h"
#include "CCodeScannerAnalyze.h"
#include <QDebug>
#include <QTime>
#include <QEventLoop>
#include <QTimer>
#include <unistd.h>
#include"publicconfig.h"
#include "publicfunction.h"
#include"./control/coperationunit.h"
//uchar stopScanCmd[] = {0x02,0x50,0x54,0x30,0x30,0x32,0x30,0x33,0x34,0x30,0x30,0x0D,0x0A};
//uchar continueScan[] = {0x02,0x50,0x54,0x30,0x30,0x32,0x30,0x33,0x34, 0x30,0x31,0x0D,0x0A};

uchar stopScanCmd[] = {0x02,0x43,0x2D,0x0D,0x0A};
uchar continueScan[] = {0x02,0x43,0x2B,0x0D,0x0A};

CCodeScannerAnalyze::CCodeScannerAnalyze()
{
    m_iScanTimes = 0;


    m_bScanning =false ;

    connect(this,&CCodeScannerAnalyze::SignalStartScan,this,&CCodeScannerAnalyze::SlotStartScan);
    connect(this,&CCodeScannerAnalyze::SignalStopScan, this, &CCodeScannerAnalyze::SlotStopScan);
    connect(this,&CCodeScannerAnalyze::SignalInitScan, this, &CCodeScannerAnalyze::SlotInitScan);
    connect(this,&CCodeScannerAnalyze::SignalExecCmd, this, &CCodeScannerAnalyze::SlotExecCmd);

    iCurrentCmdID  =0;
    iCurrentMethodID =0;
    m_bThreadExit = false;
    emit SignalInitScan();
    pthread_t tid;
    pthread_create(&tid, NULL, _CreateThreadHandleList,this);
}

void *CCodeScannerAnalyze::_CreateThreadHandleList(void *arg)
{
    CCodeScannerAnalyze* pCodeScannerThread = (CCodeScannerAnalyze*)arg;
    while(!pCodeScannerThread->m_bThreadExit)
    {
        if(pCodeScannerThread->m_iReadIndex.load() != pCodeScannerThread->m_iWriteIndex.load())
        {
            pCodeScannerThread->_HandleReceiveList();
        }
        else
        {
            ::usleep(10*1000);
        }
    }
    return NULL;
}

void CCodeScannerAnalyze::_HandleReceiveList()
{
        QByteArray scan_buffer;
        // scan_buffer = m_pSerialPort->readAll();
        GetData(scan_buffer);
        qDebug() << "scan result :" <<scan_buffer;

        AnalysisBuff(scan_buffer,iCurrentCmdID,strAnalysisListRs);
        if(m_bScanning ==true)
        {
            COperationUnit::getInstance().sendDataList(Method_Scanner_barcodeData,strAnalysisListRs,0X03,Machine_Scan_Board_1);
        }
        else
        {
            if(strAnalysisListRs.size()!=0)
            {
                qDebug()<<__FUNCTION__<<"iCurrentCmdID ="<<iCurrentCmdID;
                 COperationUnit::getInstance().sendDataList(iCurrentMethodID,strAnalysisListRs,0X03,Machine_Scan_Board_1);

            }
        }
         qDebug() << "strListRs :" <<strAnalysisListRs;
        strAnalysisListRs.clear();
}


void CCodeScannerAnalyze::AnalysisBuff(QByteArray qBuff,int iCmdID,QStringList &strListRs)
{
    if(m_bScanning ==false) //非扫描     命令信息
    {
         bool response = false;
         if(bcl.isCmdHaveResponse(iCmdID))
         {
            response = bcl.parse(qBuff);
         }
         if(response)
         {
             QString strData = bcl.getLatestResult();
             qDebug()<<__FUNCTION__<<"strData = "<<strData;
             if(strData.left(2)== "PS" || strData.left(2)== "PT" || strData.left(3)== "BCL" ) //    如果条形码有PS，可能会出错
             {
                 strListRs<<strData;
             }
         }
    }
    else    //条码信息
    {
        QByteArray qBufferRs;
        qBufferRs = qBuff.right(qBuff.length() - qBuff.indexOf(",") -1);
        if(qBufferRs.count() > 0)
        {
             bcl.parse(qBufferRs);
        }
        if(bcl.getBarCodeSize()>0)
        {
            QVector<QString> barcodeVect = bcl.getAllBarcode();
            for(int i=0;i<barcodeVect.size();i++)
            {
                strListRs<<barcodeVect[i];
            }
            bcl.clearAllBarcode();
        }
    }

}


bool CCodeScannerAnalyze::SlotExecCmd(int cmdId, int settingParam)
{
     iCurrentCmdID = cmdId;

    char cmd[20] = {0};
    uint8_t cmdLen;
    bool rc = bcl.getCmdData(cmdId, cmd, cmdLen, settingParam);
    if(rc)
    {
        //write cmd to code scanner module
        QByteArray cmdArray;
        cmdArray = QByteArray(cmd, cmdLen);
        if((cmdId == BCL_CMD_OPEN_CONT_TRIG) || (cmdId == BCL_CMD_OPEN_AUTO_TRIG))
         {
            bcl.setContinueScan(true);
            m_bScanning = true;
            emit  sigSendScanMessageDataToSerial(cmdArray);
            qDebug()<<"In CCodeScanner SlotExecCmd func write cmd >"<<cmdArray.toHex().toUpper();
        }
        if((cmdId == BCL_CMD_CLOSE_CONT_TRIG) || (cmdId == BCL_CMD_CLOSE_AUTO_TRIG))
        {

            bcl.setContinueScan(false);
            m_bScanning = false;
        }

        if(m_bScanning == false)   //不在扫描状态才可以发送其他的命令
        {
            emit  sigSendScanMessageDataToSerial(cmdArray);
            qDebug()<<"In CCodeScanner SlotExecCmd func write cmd >"<<cmdArray.toHex().toUpper();
        }



    }


    return rc;
}

CCodeScannerAnalyze::~CCodeScannerAnalyze()
{
    m_bThreadExit = true;
    emit SignalStopScan();
}

QString CCodeScannerAnalyze::GetLastestResult()
{
    return m_lastestResult;
}

void CCodeScannerAnalyze::SlotLoopScan()
{
    SendContinueScanCmd();
}

void CCodeScannerAnalyze::SlotStopScan()
{
    qDebug() << "stop continue scan  " << QTime::currentTime().toString("hh:mm:ss:zzz");
    if(!m_bScanning)
        return;
    SlotExecCmd(BCL_CMD_CLOSE_CONT_TRIG);
    //m_bScanning = false;
}

void CCodeScannerAnalyze::SlotInitScan()
{
    //配置code支持的类型以及最大最小数据长度
    SlotExecCmd(BCL_CMD_VERSION);
    SlotExecCmd(BCL_CMD_OPEN_Code_128);
    SlotExecCmd(BCL_CMD_MIN_NUM_Code_128, 1);
    SlotExecCmd(BCL_CMD_MAX_NUM_Code_128, 30);
    //配置不输出重复
    SlotExecCmd(BCL_CMD_OPEN_NO_DUPLICATE);
}

void CCodeScannerAnalyze::SendContinueScanCmd()
{
    qDebug() << "start continue scan  " << QTime::currentTime().toString("hh:mm:ss:zzz");

    SlotExecCmd(BCL_CMD_OPEN_CONT_TRIG);
    bcl.setContinueScan(true);
    //m_bScanning = true;
}


 void CCodeScannerAnalyze::GetData(QByteArray &qRsData)
 {
     qRsData = m_qSendMessageInfoList[m_iReadIndex.load()];
      // 环形队列
      m_iReadIndex.store((m_iReadIndex.load() + 1) % BUFFER_SIZE);
 }


 void CCodeScannerAnalyze::slotSendMessageToScanner(QByteArray qSendMsgAarry)
 {
        quint16 m_iMethodID = GetByte2Int(qSendMsgAarry.data() + gk_iMethodIDPos);
        int iPayloadLength = GetByte2Int(qSendMsgAarry.data() + gk_iLengthPos);
        QByteArray qPayloadByteArray = qSendMsgAarry.mid(gk_iFrameDataPos, iPayloadLength);
       qDebug()<<__FUNCTION__<<"m_iMethodID="<<m_iMethodID<<"qPayloadByteArray="<<qPayloadByteArray;
       // 检查输入字符串是否以'['开头和']'结尾
       if (!qPayloadByteArray.startsWith('[') || !qPayloadByteArray.endsWith(']'))
       {
           qWarning() << "Invalid input format!";

       }
       // 去除开头的'['和结尾的']'
       QString strData = qPayloadByteArray.mid(1, qPayloadByteArray.length() - 2);


       qDebug()<<__FUNCTION__<<"strDatatoint="<<strData.toInt();
       ScannerCmd(m_iMethodID,strData.toInt());

 }

void CCodeScannerAnalyze::slotReciveOriginalMessageData(QByteArray qMsgBtyeArray)
{    
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex + 1) % BUFFER_SIZE;

    if (m_iNextWriteIndex == m_iReadIndex.load())
    { // 原则上不可能有65535个重发存在，故而不做考虑
        qDebug() << "CWindowObject^^^^^^^^^^ERROR-^^^^^^^^^^^^^";
        return;
    }
    m_qSendMessageInfoList[m_iCurrentWriteIndex] = qMsgBtyeArray;
    m_iWriteIndex.store(m_iNextWriteIndex);
    qDebug() << "++++++++++++++++ read  "  << qMsgBtyeArray;
}

void CCodeScannerAnalyze::ScanOneTime()
{
    emit SignalStartScan();
}

void CCodeScannerAnalyze::StopScan()
{
    emit SignalStopScan();
}

bool CCodeScannerAnalyze::GetScanFlag()
{
    return m_bScanning;
}

void CCodeScannerAnalyze::execCmd(int cmdId, int settingParam /*=0*/)
{
    emit SignalExecCmd(cmdId, settingParam);
}

void CCodeScannerAnalyze::init()
{
    emit SignalInitScan();
}

void CCodeScannerAnalyze::SlotStartScan()
{
    SendContinueScanCmd();
}

qint64 CCodeScannerAnalyze::getMS()
{
    return  QDateTime::currentDateTime().toMSecsSinceEpoch();
}



void CCodeScannerAnalyze::DelayMilliSecond(uint msec)
{
    QEventLoop loop;
    QTimer::singleShot(msec, &loop, SLOT(quit()));
    loop.exec();
}



void CCodeScannerAnalyze::SetCode128MinLen(int minDataLen)
{
    emit SignalExecCmd(BCL_CMD_MIN_NUM_Code_128, minDataLen);
}

void CCodeScannerAnalyze::SetCode128MaxLen(int maxDataLen)
{
    emit SignalExecCmd(BCL_CMD_MAX_NUM_Code_128, maxDataLen);
}

void CCodeScannerAnalyze::SetCode39MinLen(int minDataLen)
{
    //minDataLen += 0x40;
    emit SignalExecCmd(BCL_CMD_MIN_NUM_Code_39, minDataLen);
}

void CCodeScannerAnalyze::SetCode39MaxLen(int maxDataLen)
{
    emit SignalExecCmd(BCL_CMD_MAX_NUM_Code_39, maxDataLen);
}

void CCodeScannerAnalyze::SetCodeNW7MinLen(int minDataLen)
{
    //minDataLen += 0x40;
    emit SignalExecCmd(BCL_CMD_MIN_NUM_Codebar, minDataLen);
}

void CCodeScannerAnalyze::SetCodeNW7MaxLen(int maxDataLen)
{
    emit SignalExecCmd(BCL_CMD_MAX_NUM_Codebar, maxDataLen);
}

void CCodeScannerAnalyze::SetCodeITFMinLen(int minDataLen)
{
    //minDataLen += 0x40;
    emit SignalExecCmd(BCL_CMD_MIN_NUM_Code_ITF, minDataLen);
}

void CCodeScannerAnalyze::SetCodeITFMaxLen(int maxDataLen)
{
    emit SignalExecCmd(BCL_CMD_MAX_NUM_Code_ITF, maxDataLen);
}

void CCodeScannerAnalyze::SetCodeITFFixLen(int fixDataLen)
{
    emit SignalExecCmd(BCL_CMD_FIX_NUM_Code_ITF, fixDataLen);
}


void  CCodeScannerAnalyze::ScannerCmd(int iMethodID, int iParam)
{
    iCurrentMethodID = iMethodID;
    switch (iMethodID)
    {
    case   Method_Scanner_version:
        execCmd(BCL_CMD_VERSION);
        break;
    case Method_Scanner_singleTrig:             //not achieve
        break;
    case Method_Scanner_closeSingleTrig:  //not achieve
        break;
    case Method_Scanner_continueTrig:
        ScanOneTime();
        break;
    case Method_Scanner_closeContinueTrig:
        StopScan();
        break;
    case Method_Scanner_reset:
        execCmd(BCL_CMD_RESET);
        break;
    case  Method_Scanner_restoreToFac:
        execCmd(BCL_CMD_ROLL_BACK_FACTORY);
        break;
    case  Method_Scanner_openAutoTrig:   //not achieve
        break;
    case  Method_Scanner_closeAutoTrig:   //not achieve
        break;
    case  Method_Scanner_openNoDuplicate:
        execCmd(BCL_CMD_OPEN_NO_DUPLICATE);
        break;
    case  Method_Scanner_closeNoDuplicate:
        execCmd(BCL_CMD_CLOSE_NO_DUPLICATE);
        break;
    case  Method_Scanner_closeCodeITF:
        execCmd(BCL_CMD_CLOSE_ITF);
        break;
    case  Method_Scanner_openCodeITF:
        execCmd(BCL_CMD_OPEN_ITF);
        break;
    case  Method_Scanner_closeCode39:
        execCmd(BCL_CMD_CLOSE_Code_39);
        break;
    case  Method_Scanner_openCode39:
        execCmd(BCL_CMD_OPEN_Code_39);
        break;
    case  Method_Scanner_closeCode128:
        execCmd(BCL_CMD_CLOSE_Code_128);
        break;
    case  Method_Scanner_openCode128:
        execCmd(BCL_CMD_OPEN_Code_128);
        break;
    case  Method_Scanner_closeCodebar:
        execCmd(BCL_CMD_CLOSE_Codebar);
        break;
    case   Method_Scanner_openCodebar:
        execCmd(BCL_CMD_OPEN_Codebar);
        break;
    case   Method_Scanner_fixDataNumITF:   //not achieve

        break;
    case   Method_Scanner_minDataNumITF:
        SetCodeITFMinLen(iParam);
        break;
    case  Method_Scanner_maxDataNumITF:
        SetCodeITFMaxLen(iParam);
        break;
    case  Method_Scanner_minDataNum39:
        SetCode39MinLen(iParam);
        break;
    case  Method_Scanner_maxDataNum39:
        SetCode39MaxLen(iParam);
        break;
    case  Method_Scanner_minDataNum128:
        SetCode128MinLen(iParam);
        break;
    case  Method_Scanner_maxDataNum128:
        SetCode128MaxLen(iParam);
        break;
    case  Method_Scanner_minDataNumCodebar:
        SetCodeNW7MinLen(iParam);
        break;
    case  Method_Scanner_maxDataNumCodebar:
        SetCodeNW7MaxLen(iParam);
        break;
    case  Method_Scanner_barcodeData:
        break;
    case  Method_Scanner_setCodeITFDataInfo:

        execCmd(BCL_CMD_READ_Code_ITF_Data_Info);
        break;
    case  Method_Scanner_setCode39DataInfo:
        execCmd(BCL_CMD_READ_Code_39_Data_Info);
        break;
    case  Method_Scanner_setCode128DataInfo:
        execCmd(BCL_CMD_READ_Code_128_Data_Info);
        break;
    case  Method_Scanner_setCodebarDataInfo:
        execCmd(BCL_CMD_READ_Code_Codebar_Data_Info);
        break;
    default:
        break;
    }
}
