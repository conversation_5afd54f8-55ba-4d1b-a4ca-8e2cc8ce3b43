/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description:  客户端业务类
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CCLIENTWINDOW_H
#define CCLIENTWINDOW_H

#include "cwindowobject.h"
class CClientWindow : public CWindowObject
{
    Q_OBJECT
public:
    explicit CClientWindow(QObject *parent = nullptr);
    ~CClientWindow();
signals:

public slots:

protected:
    void _HandleReceiveList() override ;

    /**
     * @brief _HandleFaultSimulated 处理故障模拟ID
     * @param 
    */  
    void _HandleFaultSimulatedID(const QString& strPayload);
};
#endif // CCLIENTWINDOW_H
