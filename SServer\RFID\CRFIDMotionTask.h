#ifndef CRFIDMOTIONTASK_H
#define CRFIDMOTIONTASK_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include"publicconfig.h"
#include "error/errorconfig.h"

class CRFIDMotionTask : public QThread
{ 
    Q_OBJECT
public:
    explicit CRFIDMotionTask(QObject *parent = nullptr);
    static CRFIDMotionTask &getInstance();
    void AddTask(CRFIDTask m_task);
private:
    bool GetTask(CRFIDTask &task);

signals:
    void sigError(QString strExtraInfo, MidMachineSubmodule subModule, ErrorID errorID);
public slots:
    void SlotFeedBack(int iMethodID,int iStatus,int iType);//   //0 ok ,1  false,  -1:outtime
    void SlotResetStatua();
     void SlotRFIDReadData(int iType,QString strData);//
protected:
    void run() override;

private:
    QList<CRFIDTask> m_qTaskList;
    QMutex m_qMutex;
    bool m_bIsFeedBack;

};

#endif // CRFIDMOTIONTASK_H
