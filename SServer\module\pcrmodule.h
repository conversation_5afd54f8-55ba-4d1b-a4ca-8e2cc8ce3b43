#ifndef PCRMODULE_H
#define PCRMODULE_H

#include <QObject>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include "devicemodule.h"

enum PCRStartOrStop
{
    // 0x01:启动，0x02:停止
    PCR_START = 0x01,
    PCR_STOP
};

struct FLData //荧光数据
{
    int iCycle;//循环数
    int iBVal;//蓝光数据
    int iGVal;//绿光数据
    int iOVal;//橙光、黄光数据
    int iRVal;//红光数据
};

enum PCRTaskID
{
    PTI_TEC_START,//啓動PCR
    PTI_TEC_STOP,//Stop PCR
    PTI_TEC_TIMING_TABLE_TRANS_REQ,//PCR時序傳輸請求
    PTI_TEC_TIMING_PCR_START_REQ,//PCR時序启动請求
    PTI_TEC_TIMING_TABLE_TRANS_REQ_WITH_PCR_START,//PCR時序傳輸請求,请求结束后带PCR启动
    PTI_TEC_TIMING_TABLE_TRANS_DATA,//PCR時序數據
    PTI_TEC_TIMING_TABLE_TRANS_END,//PCR時序傳輸停止,
    //    PTI_TEC_TIMING_TABLE_TRANS_END,//PCR時序傳輸停止
};

class PCRModule : public DeviceModule {
    Q_OBJECT
public:
    PCRModule(bool bUseThread = true, quint8 quCatchType = DEVICE_CATCH_TYPE); // 添加默认值
    void SetCatchType(quint8 quCatchType); // 新增设置 m_quCatchType 变量的函数
    void SetPCRModuleIndex(quint8 uiModuleIndex);//設置當前操作的PCR模塊索引
    void AddFLData(QString strData);
    void SendFLDataResultToUpperHost(const int iMethodID);//将当前所有荧光数据结果发往上位机
    void StopTimingTransmit();//停止时序文件传送

    /**
     * @brief StartPCR 启动PCR(原来逻辑，由中位机启动，现在改为根据PCR返回状态)
     * @param
     * @return 
     */      
    void StartPCR();
 
    /**
     * @brief ReTransTecTimeSeq 重发tec时序
     * @param
     * @return 
     */     
    void ReTransTecTimeSeq();

    /**
     * @brief UpdateRunStatus 更新运行状态
     * @param iParam 运行参数  uiResult返回结果
     * @return 
     */     
    void UpdateRunStatus(int iParam,quint32 uiResult);

    /**
     * @brief SetFLLedStatus 通道设置/灯源设置
     * @param bOpen 打开或者关闭
     * @return 
     */     
    void SetFLLedStatus(bool bOpen); 
    
    /**
     * @brief TransTecTimeSeq 发送tec时序
     * @return 
     */     
    void TransTecTimeSeq();    

    /**
     * @brief CheckReTransTecTimeSeq 检查是否重发tec时序
     * @return true--已重发
     */     
    bool CheckReTransTecTimeSeq();  
    
    /**
     * @brief StartMultiPCR 启动多个PCR
     * @param 多个pcr区域
     * @return 
     */      
    static void StartMultiPCR(PCRModule pcrModule[5]);  
    
    /**
     * @brief ResetStartMultiPCRStatus 重置pcr启动状态
     * @return 
     */      
    static void ResetStartMultiPCRStatus(PCRModule pcrModule[5],QString strCommand);  

    /**
     * @brief GetCommandStr 获取命令
     * @return 
     */     
    QString GetCommandStr();       
public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr) override;
    void SlotAddTask(const CmdTask& task) override;
    void SlotAddFLData(QString strFLData);

signals:
    void SignalAddFLData(QString strFLData);
    void SignalSendFLDataResultToUpperHost(const int iMethodID);
    void SignalStopTimingTransmit();
    void SignalStartTimer();

public slots:
    void SlotSendToPCRDeviceTimer();

protected:
    void SlotProcessTask(const CmdTask& task) override;
    void SlotInitialize() override;
    void SlotGenerateFLDataString(const int iMethodID);//iMethodID - 测试结果 熔解结果
    void SlotSendFLDataResultToUpperHost(const int iMethodID);
    void SlotDelaySendToPCRDeviceTimer();

    void  SlotStopTimingTransmit();
    void SlotStartTimer();
private:
    void _ProcessSubTask() override;
    void _AddPCRStartOrStopTask(QString strParam);

    /**
     * @brief _AddPCRTimingTableTransReqTask 发送tec时序
     * @param strParam  时序
     * @param bNeedStartPCR 是否启动pcr
     * @param bTransTec 是否发送pcr时序
     * @return 
     */        
    void _AddPCRTimingTableTransReqTask(QString strParam,QString strCommand, bool bNeedStartPCR, bool bTransTec);//添加发送PCR时序文件

private:
    quint8 m_uiCatchType; // 添加成员变量
    quint8 m_uiCurModuleIndex;//當前的PCR模塊索引，0代表全部，1-N代表具體的單元
    QString m_strFLDataStr;//单次旋转一周所有孔位按顺序组合上发的荧光数据字符串
    QMap<quint8,FLData> m_qFLDataMap;//key is hole index
    QString m_strContent;//时序文件内容
    QString m_strSendProgramContent;//当前发送程序段内容
    QStringList m_strProgramsList;// 程序段
    QTimer *m_pReciveMsgTimer;
    int m_iSendIndex;// 用于发送程序段计数
    bool m_bNeedStartPCR;//是否需要同时启动PCR
    QQueue<QString> m_queueParam;//准备下发时序参数(用于重发时序)
    QString m_strCommand;//pcr区域发送
};

#endif // PCRMODULE_H
