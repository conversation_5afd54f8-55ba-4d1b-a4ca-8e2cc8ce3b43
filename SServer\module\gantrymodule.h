#ifndef GANTRYMODULE_H
#define GANTRYMODULE_H

#include <QObject>
#include "devicemodule.h"
#include "consumables/reagent.h"
#include "publicconfig.h"

enum GantryTaskID
{
    GTI_GET_TIP1000 = 0,//吸样本取tip1000
    GTI_SAMPLING,   //吸样本
    GTI_SPIT_SAMPLE,//吐样本
    GTI_TRANS_CLEVAGE_REAGENT,//转移裂解液
    GTI_PUNCH_AND_SUB_PACK_REAGENT,//刺破復溶液分装试剂
    GTI_TRANS_REAGENT, // 转移扩增试剂
    GTI_TRANS_PURIFY,//转移提纯液
    GTI_CAP_AND_TRANS_TUBE,//盖PCR管+转移
    GTI_GET_PCR_CAP, // 获取PCR帽
    GTI_CAP_TO_TUBE, // 转移PCR帽到PCR管
    GTI_TRANS_TUBE_CAP, // 转移PCR管帽
    GTI_CAP_AND_ABANDON,
    GTI_ABANDON_AND_INIT,//清理泵扎取的物品+X+Y+泵复位
    GTI_GET_STANDARD_TIP200,//获取内标TIP200
    GTI_ABANDON_STANDARD_TIP200,//去掉内标TIP200
    GTI_TRANS_TUBE,//转移PCR管
    GTI_GET_TIP200,//获取TIP200
    GTI_ABANDON_TIP200,//去掉TIP200
    GTI_GET_CROSS_TIP200,//获取跨区域TIP200
    GTI_SUCK_REAGENT,// 吸试剂
    GTI_SPIT_REAGENT,// 吐试剂
    GTI_SUCK_PURIFY_OIL,// 吸提纯液/石蜡油
    GTI_SPIT_PURIFY,// 吐提纯液
    GTI_SPIT_OIL,// 吐石蜡油
};


class GantryModule : public DeviceModule {
    Q_OBJECT

public:
    GantryModule(bool bUseThread = false, quint8 uiCatchType = DEVICE_CATCH_TYPE ); // 添加默认值
    void SetCatchType(quint8 uiCatchType); // 新增设置 m_quCatchType 变量的函数
public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr) override;
    void SlotAddTask(const CmdTask& task) override;

private:
    void _ProcessSubTask() override;
    void _AddGetTip1000Task(QString strParam);
    void _AddSamplingTask(QString strParam);
    void _AddSpitSampleTask(QString strParam);
    void _AddTransClevageReagentTask(QString strParam);
    void _AddSubPackReagentTask(SubPackData data);
    void _AddTransReagentTask(QString strParam);
    void _AddTransPurifyTask(QString strParam);
    void _AddCapAndTransTubeTask(QString strParam);
    void _AddSubPackPunchTask(SubPackData data);
    void _AddGetTipTask(quint16 uiType,QString strSingleRightParam = "");
    void _AddAbandonTipTask(quint16 uiType);
    void _PunchAndSubPackReagent();
    void _AddTransTubeTask(QString strParam);

    /**
     * @brief _GetSubPackDataMaxRow 获取最大索引
     * @param subPackVect 分装数据
     * @return 需要更新的信息
     */     
    SubPackData _GetSubPackDataMaxRow(QVector<SubPackData>& subPackVect);
private:
    quint8 m_uiCatchType; // 添加成员变量
};

#endif // GANTRYMODULE_H
