#ifndef CCODESCANNERANALYZE_H
#define CCODESCANNERANALYZE_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QSerialPort>
#include "DSBCL95.h"
#include "publicconfig.h"
#include "error/errorconfig.h"
/*
 * @author:mflin
 * @created:2023-9-20
 *
 */

class CCodeScannerAnalyze : public QObject
{
    Q_OBJECT


public:
    CCodeScannerAnalyze();
    ~CCodeScannerAnalyze();

    void SetCode128MinLen(int minDataLen);
    void SetCode128MaxLen(int maxDataLen);

    void SetCode39MinLen(int minDataLen);
    void SetCode39MaxLen(int maxDataLen);

    void SetCodeNW7MinLen(int minDataLen);
    void SetCodeNW7MaxLen(int maxDataLen);

    void SetCodeITFMinLen(int minDataLen);
    void SetCodeITFMaxLen(int maxDataLen);
    void SetCodeITFFixLen(int fixDataLen);

    QString GetLastestResult();
    void GetData(QByteArray &qRsData);
   void  ScannerCmd(int iMethodID,  int iParam);

   void AnalysisBuff(QByteArray qBuff,int iCmdID,QStringList &strListRs);

signals:
    void SignalSendScanResults(QByteArray);
     void sigSendScanMessageDataToSerial(QByteArray qSendData);// 发送数据



    void SignalStartScan();
    void SignalStopScan();
    void SignalInitScan();
    void SignalExecCmd(int cmdId, int settingParam);
    void SignalRecvNewBarcode(QVector<QString> barcodeVect);
    void SignalRecvReadData(int cmdId, QString readResult);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:


     void slotReciveOriginalMessageData(QByteArray qMsgBtyeArray);// 接受原始数据
     void slotSendMessageToScanner(QByteArray qSendMsgAarry);
private slots:



    void SlotStartScan();
    void SlotLoopScan();
    void SlotStopScan();
    void SlotInitScan();
    bool SlotExecCmd(int cmdId, int settingParam = 0);



public:


    void ScanOneTime();
    void StopScan();
    bool GetScanFlag();
    void execCmd(int cmdId, int settingParam=0);//执行特定指令
    void init();


private:
    void SendContinueScanCmd();
    void DelayMilliSecond(uint msec);
    qint64 getMS();

private:
    void _HandleReceiveList();
    static void* _CreateThreadHandleList(void* arg);
    int m_iScanTimes;


   //QThread* m_pThread;
    bool m_bScanning;
    BCL bcl;
    int m_latestCmdID;
    QString m_lastestResult;

    bool m_bThreadExit;
    QByteArray m_qSendMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;

    int iCurrentCmdID;
    int iCurrentMethodID;
   QStringList  strAnalysisListRs;

};



#endif // CCODESCANNERANALYZE_H
