/*****************************************************
  * Copyright: 万孚生物
  * Author: 刘青
  * Date: 2020-10-11
  * Description: can读写 线程
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/
#ifndef CCANBUSDEVICETHREAD_H
#define CCANBUSDEVICETHREAD_H

#include <QObject>
#include <QThread>
#include <QMutex>

#include <QCanBusDevice>
#include <QTimer>
#include <QTime>
#include <atomic>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CCanBusDeviceThread : public QObject
{
    Q_OBJECT
public:
    explicit CCanBusDeviceThread(QString strInterfaceName, QObject *parent = nullptr);
    ~CCanBusDeviceThread();

signals:
    void sigReadTimer();
    void sigReciveMessage(QVector<QCanBusFrame> qCanFramsVector);
    void sigACKOut();
    void sigConnectError(QString strInterfaceName, QString strErrorCode);
    void sigNewConnect(bool bConnect);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    void slotWaitACK(quint16 iFrameNumber);
    void slotSendAckBack(QByteArray qSendMsgAarry);
    void slotSendMessage(QByteArray qMsgBtyeArray);
private slots:
    void _slotReadFramesTimer();
    void _SlotStartTimer();
    void _slotCanBusError(QCanBusDevice::CanBusError error);
    void _slotReSendTimer();

public:
    bool InitCanBusDevice();
private:
    void _sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend = false);
    void _reSetFrameNumber(QByteArray & qByteArray);// 重置发送帧号及CRC
private:
    QString m_strInterfaceName;
    QTimer *m_pReadFramesTimer;
    QThread *m_pThread;
    // can
    QCanBusDevice *m_pCanBusDevice;
    SCanBusDeviceStruct m_sSCanBusDeviceStruct;
    QCanBusFrame m_qFrameForSend;
    QCanBusFrame m_qFrameForRead;
    // 数据
    QByteArray m_sCurrentSendMessage;
    QByteArray m_qSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;
    // 重发队列
    QByteArray m_qReSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iReSendWriteIndex{0};
    std::atomic<int> m_iReSendReadIndex{0};
    int m_iReSendNextWriteIndex;
    int m_iReSendCurrentWriteIndex;
    // 重发机制中的环形队列
    bool m_bWaitAck[BUFFER_SIZE];
    QTimer *m_pResendTimer;
    MessageInfo m_sRingMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iRingWriteIndex{0};
    std::atomic<int> m_iRingReadIndex{0};
    int m_iRingCurrentWriteIndex;
    int m_iRingNextWriteIndex;
    int m_iRingCurrentReadIndex;
    quint16 m_iSeqNumber;// 帧号 0-65535
    char *m_pFramePos;
    //
    int m_iTotalFrameNumbers;
    int m_iLastFrameCount;
    bool m_bSendDataCount;
    int m_iCurrentPOrtDataByteArraySize;
    QString m_strCurrentTime;
    quint32 m_uiCanID;
    //
    quint8 m_iCmdID;
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iSourceID;
    quint8 m_iSync;

};

#endif // CCANBUSDEVICETHREAD_H
