#ifndef AGINGCONFIGMGR_H
#define AGINGCONFIGMGR_H
#include<QObject>
#include <QMetaEnum>

struct AgingActionCfg
{
    double dOrdIdx;   //动作顺序
    QString strCNName;  //动作中文名字
    QString strCmd;  //动作命令
    QMap<QString,double> mapAgingParam;  //第一个是名字，第二个是顺序
};

class AgingConfigMgr : public QObject
{
public:
    enum AgingParamInfoType
    {
        name,    // 参数名称
        subOrdIdx,  //序号
    };
    Q_ENUM(AgingParamInfoType)

    enum AgingCommonSetType
    {
        ordidx,  //浮点
        CNName,
    };
    Q_ENUM( AgingCommonSetType)


    struct structOrdParam
    {   //用于参数的排序
        double dOrder;      // 排序依据字段
        QString strParamName;
    };

    struct AgingOrd
    {
        double dOrdIdx;   //动作顺序
        QString strCNName;  //动作中文名字
        QList<structOrdParam> listAgingParamOrder;  //第一个是名字，第二个是顺序
    };

    Q_OBJECT
public:

public:
    static AgingConfigMgr &getInstance();
   // QString GetStringValue(QString field,QString type);
   QString GetStringValue(QString type);
   QString GetAgingActionList();  // 无须参数，将配置档读取到的全部打包
   QString  GetAgingCmd(QString strAgingName);
   QStringList  GetAgingParamNameList(QString strAgingName);
private:
    bool _Init();
    bool _InitAgingConfigInfoHash();
    void _readDataToAgingParamInfo(AgingActionCfg &agingInfo);
    void _readDataToAgingSet(AgingActionCfg &commonInfo);

private:
    QString m_strFilePath; // 配置文件路径
    bool    m_bLoadStatus; // 配置文件加载状态
   QHash<QString,AgingActionCfg> m_mapAgingProject;  //第一个为老化的动作名称，第二个为参数
private:
    AgingConfigMgr();
    ~AgingConfigMgr();
private:
    Q_DISABLE_COPY(AgingConfigMgr);
};

#endif // AGINGCONFIGMGR_H
