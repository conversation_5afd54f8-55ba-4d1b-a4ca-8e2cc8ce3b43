#include "cextractparse.h"
#include <QtDebug>
#include <QtMath>
#include "datacontrol/CSystemDB.h"
#include "cglobalconfig.h"
#include "consumables/cstrip.h"
#include "SystemConfig/SystemConfig.h"
#define MAGNETIC_ABSORB_WAIT_TIME 8

CExtractParse::CExtractParse()
{
}

CExtractParse &CExtractParse::getInstance()
{
    static CExtractParse cExtractParse;
    return cExtractParse;
}

QString CExtractParse::_getInitStr()
{
    //磁棒复位+磁套复位+卡盒架复位
    return QString("%1;%2;%3;").arg(Method_CAMRReset).arg(Method_CAMSReset).arg(Method_CReset);
}

QString CExtractParse::_getExtractEndStr()
{
    //磁棒复位+磁套复位+卡盒架复位+卡盒到Tip扎取操作位
    return QString("%1;%2;%3;%4;%5;%6;%7;%8;").arg(Method_CAMRReset)              // 磁棒复位
                                              .arg(Method_CAMSReset)              // 磁套复位
                                              .arg(Method_SACMTStripHoldDownPos)  // 卡盒架电机移动到磁套压整位
                                              .arg(Method_CAMSMTHoldDownPos)      // 磁套电机移动到磁套压整位
                                              .arg(Method_CAMRMTWaitPos)          // 等待位
                                              .arg(Method_CAMSReset)              // 磁套复位
                                              .arg(Method_CReset)                 // 卡盒架复位
                                              .arg(Method_CMTSwitchPos);          // 卡盒到Tip扎取操作位
}

QString CExtractParse::_getMagnetTubeStr()
{
    QString strMagnetStr = QString("%1;").arg(Method_CMTMagnetShieldPos);//卡盒移动架移动到磁套扎取位
    strMagnetStr += QString("%1;").arg(Method_CAMSMTCatchPos);//磁套电机扎取
    strMagnetStr += QString("%1;").arg(Method_CAMSReset);//磁套电机复位

    // 检测磁套到位光耦
    const QString strMagneticRodSleevePosition = "2"; // 2-磁套到位光耦
    strMagnetStr += QString ("%1,%2,%3,%4;").arg(Method_OptocouplerCheck).arg(strMagneticRodSleevePosition)
                                             .arg(CGlobalConfig::getInstance().getMagnetTubeDetectFlag())
                                             .arg(CStrip::getInstance().GetMagneticRodSleevePositionAll());//光耦目标状态判定

    qDebug()<<"_getMagnetTubeStr: "<<strMagnetStr;
    return strMagnetStr;
}

QString CExtractParse::_getStartHeaterStr(const float& fHeaterVal, const QString& strHeatHole)
{
    QString strHeatNumber = QString::number((int)fHeaterVal);
    QString strStartHeaterStr = QString("%1,").arg(Method_extract_heater_start)
            + strHeatHole+ "," + strHeatNumber + ";";
    return strStartHeaterStr;
}

QString CExtractParse::_getStopHeaterStr(const QString& strHeatHole)
{
    return QString("%1,%2;").arg(Method_extract_heater_stop).arg(strHeatHole);
}

QString CExtractParse::_getSeqStepStr(int iStep)
{
    return QString("%1,%2;").arg(Method_extract_timing_step).arg(iStep); //"45," + QString::number(i)+";";
}

QString CExtractParse::_getDelayStr(int iDelaySecond)
{
    return QString("%1,%2;").arg(Method_DELAY).arg(iDelaySecond*1000);
}



void CExtractParse::_getLiquidLevel(QString strLiquidVol, QString& strLiquidLevelH,
                                   QString& strLiquidLevelV, float& fLiquidLevelH)
{
    float fStepF = strLiquidVol.toFloat();
    //    float fLiquidLevelH = 0.0;
#if 0
    if(fStepF <= 90)
    {
        fLiquidLevelH = qPow(3 * fStepF / (2 * 3.1415926), 1.0/3.0);
    }
    else if(fStepF > 90 && fStepF <= 476)
    {
        fLiquidLevelH = (fStepF - 90) / (14 * 3.1415926) + 3.5;
    }
    else
    {
        fLiquidLevelH = (fStepF - 476) / 70.0 +12.1;
    }
#endif
    if(fStepF <= 200)
    {
        fLiquidLevelH = 1.0514*qPow(fStepF, 1.0/3.0);
    }
    else if(fStepF > 200 && fStepF <= 500)
    {
        fLiquidLevelH = (fStepF - 200) / 45 + 7;
    }
    else
    {
        fLiquidLevelH = (fStepF - 600) / 65.63+14.8;
    }

    float fSystemConfigH = CSystemDB::getInstance().getFloatValueFromKey("extract_h_value");
    strLiquidLevelH = QString::number(static_cast<int>(fLiquidLevelH * 1000));
    strLiquidLevelV = QString::number(static_cast<int>((fSystemConfigH - fLiquidLevelH) * 1000));

}

QString  CExtractParse::_getMagneticActionStr(const QString& strLiquidLevelV, const QString& strLiquidLevelH, const QString& strHalfE)
{
    QString strMagneticActionStr = "";
    strMagneticActionStr +=QString("%1,%2,%3;").arg(Method_CAVTMV).arg(strLiquidLevelV).arg(1);//磁棒磁套移动到液面位 //"341," + strLiquidLevelV + ",1;";
    strMagneticActionStr += _getDelayStr(MAGNETIC_ABSORB_WAIT_TIME);//"252,8;";液面位静置吸磁
    strMagneticActionStr += QString("%1,%2,%3;").arg(Method_CAMADO).arg(strHalfE).arg(strLiquidLevelH);//"342," + strHalfE + "," + strLiquidLevelH + ";";//向下吸磁
    strMagneticActionStr += _getDelayStr(MAGNETIC_ABSORB_WAIT_TIME);//底部静置吸磁
    strMagneticActionStr += QString("%1,%2,%3;").arg(Method_CAMAUP).arg(strHalfE).arg(strLiquidLevelH);//"343," + strHalfE + "," + strLiquidLevelH + ";";//吸磁往上
    strMagneticActionStr += QString("%1;").arg(Method_CAVTOMW);//"340;"; 磁棒磁套移动到等待位
    return strMagneticActionStr;
}

QString CExtractParse::_getMixActionStr(const float & fLiquidLevelH, const QString& strLiquidLevelV, int iMixTime, QString strMixLevel)
{
    QString strMixMotorContent = "";
    float fMixPercentage = CSystemDB::getInstance().getFloatValueFromKey("extract_mix_percentage") / 100.0f;//获取混匀行程百分比参数
    float fMixLiquidLevelH = fLiquidLevelH * fMixPercentage * 1000;// 混匀H
    strMixMotorContent += QString("%1,%2,%3;").arg(Method_CAVTMV).arg(strLiquidLevelV).arg(1);//"341," + strLiquidLevelV + ",1;";磁棒磁套移动到液面位
    strMixMotorContent += QString("%1;").arg(Method_CAMRMTWaitPos);//"321;";磁棒移动到等待位
    strMixMotorContent += QString("%1,%2,%3,%4,%5;").arg(Method_CAMSMix).
            arg(iMixTime).arg(0).arg(static_cast<int>(fMixLiquidLevelH)).arg(strMixLevel);//混匀
    // "336," + iMixTime + ",0,"+ fMixLiquidLevelH  +"," + strMixLevel + ";";
    strMixMotorContent += QString("%1;%2;").arg(Method_CAMSReset).arg(Method_CAMRMTWaitPos);//"330;321;";磁棒磁套移动到等待位
    return strMixMotorContent;
}

QString CExtractParse::getExtractMotorContentFromUIContent(QString strUIContent)
{
    bool bHeat = false;
    QString strMotorContent = _getInitStr();//提取初始化
    strMotorContent += _getMagnetTubeStr();//"320;330;300;302;337;330;";扎取磁套和判断磁套
    if(strUIContent.isEmpty())
    {
        return strMotorContent;
    }
    // 步骤拆解，解析
    QStringList strUIContentList = strUIContent.split(SPLIT_BETWEEN_CMD);
    qDebug() << "send strUIContentList" << strUIContentList;
    for (int i = 0;  i < strUIContentList.count(); ++i)
    {
        QStringList strOneLine = strUIContentList[i].split(SPLIT_IN_CMD);
        qDebug() << "ui one line content " << strOneLine;
        if(strOneLine.count() > 8)
        {
            strMotorContent += _getSeqStepStr(i);//"45," + QString::number(i)+";";添加提取时序步骤信息
            // 2.1.加热（S_0
            bHeat = false;
            QString strHeatHole = strOneLine[UI_ONE_G];

            if((strHeatHole == "1" || strHeatHole == "2") && strOneLine[UI_ONE_H] != "")
            {
                bHeat = true;
                float fHeatVal = strOneLine[UI_ONE_H].toFloat()*100.0;
                strMotorContent += _getStartHeaterStr(fHeatVal, strHeatHole);
            }
            else
            {
                strMotorContent += _getStopHeaterStr(strHeatHole);
            }
            // 2.2.等待晾干/转移
            QString strLiquidLevelH;
            QString strLiquidLevelV;
            float fLiquidLevelH = 0.0;
            _getLiquidLevel(strOneLine[UI_ONE_F], strLiquidLevelH, strLiquidLevelV, fLiquidLevelH);

            //移动到特定提取操作孔位
            QString strExtractOpPos = strOneLine[UI_ONE_A];
            strMotorContent += QString("%1,%2;").arg(Method_SACMTReagentPosForExtract).arg(strExtractOpPos);//"304," + strOneLine[UI_ONE_A] + ";";

            int iWaitTime = strOneLine[UI_ONE_B].toInt();
            if(iWaitTime > 0)
            {// 等待
                strMotorContent += _getDelayStr(iWaitTime);//"252," + strOneLine[UI_ONE_B] + ";";
            }

            // 混匀
            int iMixTime = strOneLine[UI_ONE_C].toInt();
            if(iMixTime>0)
            {
                QString strMixLevel = strOneLine[UI_ONE_D];
                strMotorContent += _getMixActionStr( fLiquidLevelH, strLiquidLevelV, iMixTime, strMixLevel);
            }

            int iMagneticTime = strOneLine[UI_ONE_E].toInt();
            QString strHalfE = QString::number(iMagneticTime/2);
            // 吸磁
            if(iMagneticTime > 1)
            {
                strMotorContent += _getMagneticActionStr(strLiquidLevelV,  strLiquidLevelH,  strHalfE);
            }
            if(bHeat)
            {
                strMotorContent += _getStopHeaterStr(strHeatHole);//"251," + strOneLine[UI_ONE_G]  + ";";
            }
        }
    }
    
    strMotorContent += _getRinseMagnetTubeStr();// 多加一个磁套清洗磁珠的步骤
    strMotorContent += _getEjectMagnetTubeStr();//
    strMotorContent  += _getExtractEndStr();//"320;330;321;300;301;";//多一个321去到等待位
    return strMotorContent;
}

QString CExtractParse::_getEjectMagnetTubeStr()
{
    //磁棒复位+磁套复位+卡盒架复位
    return QString("%1;%2;%3;").arg(Method_CMTMagnetShieldPos).arg(Method_CAMSMTTakeOffPos).arg(Method_CAMRMTTakeoffPos);//302;338;326,27500,0;
}

QString CExtractParse::_getRinseMagnetTubeStr()
{
    QString strMotorContent = "";
    QString strVol = "700";//容积默认使用700
    QString strPos = "1";//默认使用清洗3
    QString strMixLevel = "7";//混合速度
    QString strMixTime = "10";//混合时间(S)

    auto& config = SystemConfig::getInstance();
    QMetaEnum metaConfigFieldType = QMetaEnum::fromType<SystemConfig::EnumConfigFieldType>();
    QMetaEnum metaStripType = QMetaEnum::fromType<SystemConfig::EnumStripType>();
    QString strStrip = config.GetMetaEnumFiledString(metaConfigFieldType,SystemConfig::strip);

    config.GetStringValue(strVol,     strStrip,config.GetMetaEnumFiledString(metaStripType,SystemConfig::volume));
    config.GetStringValue(strPos,     strStrip,config.GetMetaEnumFiledString(metaStripType,SystemConfig::pos));
    config.GetStringValue(strMixLevel,strStrip,config.GetMetaEnumFiledString(metaStripType,SystemConfig::mix_level));
    config.GetStringValue(strMixTime, strStrip,config.GetMetaEnumFiledString(metaStripType,SystemConfig::mix_time));

    QString strLiquidLevelH;
    QString strLiquidLevelV;
    float fLiquidLevelH = 0.0;
    _getLiquidLevel(strVol, strLiquidLevelH, strLiquidLevelV, fLiquidLevelH);
    qDebug()<<"LiquidLevel:"<<strLiquidLevelH<<strLiquidLevelV<<fLiquidLevelH;

    float fMixPercentage = CSystemDB::getInstance().getFloatValueFromKey("extract_mix_percentage") / 100.0f;//获取混匀行程百分比参数
    float fMixLiquidLevelH = fLiquidLevelH * fMixPercentage * 1000;// 混匀H

    strMotorContent += QString("%1,%2;").arg(Method_SACMTReagentPosForExtract).arg(strPos);
    strMotorContent += QString("%1,%2,%3;").arg(Method_CAMSMove).arg(strLiquidLevelV).arg(1);
    strMotorContent += QString("%1;").arg(Method_CAMRMTWaitPos);//"321;";磁棒移动到等待位
    strMotorContent += QString("%1,%2,%3,%4,%5;").arg(Method_CAMSMix).
            arg(strMixTime).arg(0).arg(static_cast<int>(fMixLiquidLevelH)).arg(strMixLevel);//混匀
    strMotorContent += QString("%1;").arg(Method_CAMSReset);//磁套电机复位
    strMotorContent += QString("%1;").arg(Method_CAMRMTWaitPos);//"321;";磁棒移动到等待位

// 304,1;335,63676;321;336,10,0,16323,7;330;321;
    qDebug()<<"_getRinseMagnetTubeStr"<<strMotorContent;
    return strMotorContent;
}

void CExtractParse::getExtractMotorContentFromUIContent(QString strUIContent,QStringList &strParamList,QString& strLastStepParams)
{
    bool bHeat = false;
    float fHeatVal = 0.0f;
    // 步骤拆解，解析
    QStringList strUIContentList = strUIContent.split(SPLIT_BETWEEN_CMD);
    qDebug() << "send strUIContentList" << strUIContentList;
    for (int i = 0;  i < strUIContentList.count(); ++i)
    {
        QString strParam = "";
        QStringList strOneLine = strUIContentList[i].split(SPLIT_IN_CMD);
        qDebug() << "ui one line content " << strOneLine;
        if(strOneLine.count() > 8)
        {
            // 2.0添加提取时序步骤信息
            strParam += QString(",%1").arg(i);//"45," + QString::number(i)+";";添加提取时序步骤信息

            // 2.1.加热
            bHeat = false;
            QString strHeatHole = strOneLine[UI_ONE_G];

            if((strHeatHole == "1" || strHeatHole == "2") && strOneLine[UI_ONE_H] != "")
            {
                bHeat = true;
                fHeatVal = strOneLine[UI_ONE_H].toFloat()*100.0;
                QString strHeatNumber = QString::number((int)fHeatVal);
                strParam += QString(",%1,%2,%3").arg(true).arg(strHeatHole).arg(strHeatNumber);
            }
            else
            {
                strParam += QString(",%1,%2,%3").arg(false).arg(strHeatHole).arg(0);
            }

            // 2.2.等待晾干/转移
            QString strLiquidLevelH;
            QString strLiquidLevelV;
            float fLiquidLevelH = 0.0;
            _getLiquidLevel(strOneLine[UI_ONE_F], strLiquidLevelH, strLiquidLevelV, fLiquidLevelH);

            //2.3移动到特定提取操作孔位
            QString strExtractOpPos = strOneLine[UI_ONE_A];
            strParam += QString(",%1").arg(strExtractOpPos);//"304," + strOneLine[UI_ONE_A] + ";";

            // 2.4等待
            int iWaitTime = strOneLine[UI_ONE_B].toInt();
            strParam += QString(",%1,%2").arg(iWaitTime > 0).arg(iWaitTime*1000);

            // 2.5混匀
            int iMixTime = strOneLine[UI_ONE_C].toInt();
            if(iMixTime>0)
            {
                QString strMixLevel = strOneLine[UI_ONE_D];
                float fMixPercentage = CSystemDB::getInstance().getFloatValueFromKey("extract_mix_percentage") / 100.0f;//获取混匀行程百分比参数
                float fMixLiquidLevelH = fLiquidLevelH * fMixPercentage * 1000;// 混匀H
                strLastStepParams = QString(",%1,%2").arg(strLiquidLevelV).arg(static_cast<int>(fMixLiquidLevelH));//获取最后一个清洗混匀的值
                strParam += QString(",%1,%2,%3,%4,%5").arg(true).arg(strLiquidLevelV).arg(iMixTime).arg(static_cast<int>(fMixLiquidLevelH)).arg(strMixLevel);
            }
            else
            {
                //填写6个默认值0
                strParam += QString(",%1,%2,%3,%4,%5").arg(false).arg(0).arg(0).arg(0).arg(0);
            }

            // 2.6吸磁
            int iMagneticTime = strOneLine[UI_ONE_E].toInt();
            QString strHalfE = QString::number(iMagneticTime/2);
            if(iMagneticTime > 1)
            {
                strParam += QString(",%1,%2,%3").arg(true).arg(strHalfE).arg(strLiquidLevelH);    
            }
            else
            {
                strParam += QString(",%1,%2,%3").arg(false).arg(0).arg(0);
            }

            // 2.7关闭加热
            strParam += QString(",%1,%2").arg(bHeat).arg(strHeatHole);
            strParamList.append(strParam);
        }
    }
}