#include "devicemodule.h"
#include "control/coperationunit.h"
#include "datacontrol/ctiminginfodb.h"
#include "publicconfig.h"
#include "magic_enum/magic_enum.hpp"

Q_DECLARE_METATYPE(CmdTask)

DeviceModule::DeviceModule(QString strName, bool bUseThread)
    : m_bUseThread(bUseThread)
{
    // 注册CmdTask类型，使它能在信号槽之间传递
    qRegisterMetaType<CmdTask>("CmdTask");
    m_strName = strName;
    m_bIsBusy = false;
    if (m_bUseThread)
    {
        m_pThread = new QThread();
        this->moveToThread(m_pThread);
        connect(m_pThread, &QThread::started, this, &DeviceModule::SlotInitialize);
        m_pThread->start();
    }
}

DeviceModule::~DeviceModule()
{
    if (m_bUseThread) {
        m_pThread->quit();
        m_pThread->wait();
        delete m_pThread;
    }
}

void DeviceModule::SetState(bool bIsBusy)
{
    if (m_bUseThread)
    {
        emit SignalSetState(bIsBusy);
    }
    else
    {
        _SlotSetState(bIsBusy);
    }
}

void DeviceModule::InitData()
{
    if (m_bUseThread)
    {
        emit SignalInitData();
    }
    else
    {
        SlotInitData();
    }
}

void DeviceModule::_SlotSetState(bool bIsBusy)
{
    qDebug()<<m_strName<<"SlotSetState:"<<bIsBusy;
    m_bIsBusy = bIsBusy;
    if(!m_bIsBusy)
        _SlotExecRemainTask();
}

void DeviceModule::_SlotAddNewSubTask(const CmdTask& task)
{
    m_qWaitProcessSubTask.enqueue(task);
    qDebug()<<m_strName<<"m_qWaitProcessSubTask size:"<<m_qWaitProcessSubTask.size();
    _ProcessSubTask();
}

void DeviceModule::_SlotAddNewTaskToQueue(const CmdTask &task)
{
    m_qTaskQueue.enqueue(task);
    qDebug()<<m_strName<<"m_qTaskQueue size:"<<m_qTaskQueue.size();
    _SlotExecRemainTask();
}

void DeviceModule::SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr)
{
    if (m_bUseThread)
    {
        CmdTask task;
        task.uiSubTaskID = uiSubTaskID;
        task.strCommandStr = strCommandStr;
        task.strParamStr = strParamStr;
        emit SignalAddNewSubTask(task);
    }
    else
    {
        CmdTask task;
        task.uiSubTaskID = uiSubTaskID;
        task.strCommandStr = strCommandStr;
        task.strParamStr = strParamStr;
        m_qWaitProcessSubTask.enqueue(task);
        _ProcessSubTask();
    }
    qDebug()<<"DeviceModule::SlotAddSubTask: "<<m_bUseThread<<uiSubTaskID<<strCommandStr<<strParamStr;
}

void DeviceModule::SlotAddTask(const CmdTask& task)
{
    if (m_bUseThread)
    {
        qDebug()<<m_strName<<"DeviceModule::SlotAddTask SignalAddNewTaskToQueue";
        emit SignalAddNewTaskToQueue(task);
    }
    else
    {
        qDebug()<<m_strName<<"DeviceModule::SlotAddTask SlotProcessTask";
        SlotProcessTask(task);
    }
}

void DeviceModule::SlotInitialize()
{
    qDebug() << "DeviceModule initialized in thread: " << QThread::currentThread();
    connect(this, &DeviceModule::SignalAddNewTaskToQueue, this, &DeviceModule::_SlotAddNewTaskToQueue);
    connect(this, &DeviceModule::SignaModuleStateChanged, this, &DeviceModule::_SlotExecRemainTask);
    connect(this, &DeviceModule::SignalSetState, this, &DeviceModule::_SlotSetState);
    connect(this, &DeviceModule::SignalAddNewSubTask, this, &DeviceModule::_SlotAddNewSubTask);
    connect(this, &DeviceModule::SignalInitData, this, &DeviceModule::SlotInitData);
}

void DeviceModule::SlotProcessTask(const CmdTask& task)
{
    // qDebug() <<m_strName<< "DeviceModule processing task (uiSubTaskID: " << task.uiSubTaskID << ", strCommandStr: " << task.strCommandStr << ", strParamStr: " << task.strParamStr << ") in thread: " << QThread::currentThread();
    QString info = "SendString Cmd: " + m_strName.leftJustified(15, ' ') + " " + task.strCommandStr+task.strParamStr;
    if (!m_bIsBusy)
    {
        //发送指令给下位机模块
        int iMachineID = CTimingInfoDB::getInstance().getComplexMotorBoardIndexFromID(task.strCommandStr);
        COperationUnit::getInstance().sendStringData(Method_comp_cmd, task.strCommandStr+task.strParamStr, iMachineID);
        m_bIsBusy = true;
        auto action = magic_enum::enum_cast<EnumAffairAction>(task.strCommandStr.toInt());
        if (action.has_value())
        {
            auto action_name = magic_enum::enum_name(action.value());
            qDebug() << info << action_name.data();
        }
        else
        {
            qDebug() << info;
        }
        m_qTaskQueue.dequeue();
    }
    else
    {
        qDebug() << info << "Device is busy.";
    }
}

void DeviceModule::SlotInitData()
{
    m_qTaskQueue.clear();
    m_qSampleQueue.clear();
    m_qWaitProcessSubTask.clear();
    m_bIsBusy = false;
    qDebug()<<m_strName<<"SlotInitData";
}

void DeviceModule::_AddSubTask(QString strParam, quint16 uiSubTaskID)
{
    CmdTask sTask;
    sTask.bSync = false;
    sTask.strCommandStr = QString::number(uiSubTaskID);
    sTask.strParamStr = strParam;
    DeviceModule::SlotAddTask(sTask);
}

void DeviceModule::_SlotExecRemainTask()
{
//    qDebug()<<m_strName<<"_SlotExecRemainTask m_bUseThread"<<m_bUseThread;
    if (m_bUseThread)
    {
        bool bSync = true;
        qDebug()<<m_strName<<"SlotProcessNextTask size:"<<m_qTaskQueue.size()<<"bBusy"<<m_bIsBusy<<"bSync"<<bSync ;
        while (m_qTaskQueue.size()>0 && bSync && !m_bIsBusy)
        {
            CmdTask task = m_qTaskQueue.front();
            SlotProcessTask(task);
            bSync = task.bSync;
        }
    }
}

void DeviceModule::_SlotModuleState(quint8 quState)
{
    m_bIsBusy = quState;
    if (!m_bIsBusy)
    {
        _SlotExecRemainTask();
    }
}
