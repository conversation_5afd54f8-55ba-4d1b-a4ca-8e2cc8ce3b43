#include "testpreprocessing.h"
#include "affair/caffair.h"
#include <QDebug>

TestPreprocessing::TestPreprocessing(QObject* parent)
    : QObject(parent), m_eCurStep(STEP_IDLE)
{
    m_pTimeoutTimer = new QTimer(this);
    m_pTimeoutTimer->setSingleShot(true);
    connect(m_pTimeoutTimer, &QTimer::timeout, this, [this]() {
        _abort("Step timeout");
    });
}

TestPreprocessing::~TestPreprocessing()
{
    m_pTimeoutTimer->stop();
}

void TestPreprocessing::start(QString strParams)
{
    if (m_eCurStep != STEP_IDLE && m_eCurStep != STEP_DONE && m_eCurStep != STEP_ERROR) {
        emit sigPreprocessingFailed("Preprocessing already running");
        return;
    }
    m_strParams = strParams;
    m_eCurStep = STEP_RESET;
    emit sigStepChanged("RESET");
    _startReset();
}

void TestPreprocessing::_startReset()
{
    m_pTimeoutTimer->start(60000); // 60s超时
    connect(&CAffair::getInstance(), &CAffair::sigResetFinished,
            this, &TestPreprocessing::_onResetFinished, Qt::UniqueConnection);
    CAffair::getInstance().ResetProcess();
}

void TestPreprocessing::_onResetFinished(bool success, const QString& msg)
{
    m_pTimeoutTimer->stop();
    disconnect(&CAffair::getInstance(), &CAffair::sigResetFinished,
               this, &TestPreprocessing::_onResetFinished);

    if (!success) {
        _abort("Reset failed: " + msg);
        return;
    }
    m_eCurStep = STEP_SAMPLE_EXIST;
    emit sigStepChanged("SAMPLE_EXIST");
    _startSampleExist();
}

void TestPreprocessing::_startSampleExist()
{
    m_pTimeoutTimer->start(30000); // 30s超时
    connect(&CAffair::getInstance(), &CAffair::sigSampleExistFinished,
            this, &TestPreprocessing::_onSampleExistFinished, Qt::UniqueConnection);
    CAffair::getInstance().SampleExistProcess(m_strParams);
}

void TestPreprocessing::_onSampleExistFinished(bool success, const QString& msg)
{
    m_pTimeoutTimer->stop();
    disconnect(&CAffair::getInstance(), &CAffair::sigSampleExistFinished,
               this, &TestPreprocessing::_onSampleExistFinished);

    if (!success) {
        _abort("SampleExist failed: " + msg);
        return;
    }
    
    if (!m_strParams.isEmpty())
    {
        m_eCurStep = STEP_SAMPLE_SCAN;
        emit sigStepChanged("SAMPLE_SCAN");
        _startSampleScan();
    }
    else
    {
        m_eCurStep = STEP_DONE;
        emit sigStepChanged("DONE");
        emit sigPreprocessingSuccess();
    }
}

void TestPreprocessing::_startSampleScan()
{
    m_pTimeoutTimer->start(60000); // 60s超时
    connect(&CAffair::getInstance(), &CAffair::sigSampleScanFinished,
            this, &TestPreprocessing::_onSampleScanFinished, Qt::UniqueConnection);
    CAffair::getInstance().SampleScanProcess();
}

void TestPreprocessing::_onSampleScanFinished(bool success, const QString& msg)
{
    m_pTimeoutTimer->stop();
    disconnect(&CAffair::getInstance(), &CAffair::sigSampleScanFinished,
               this, &TestPreprocessing::_onSampleScanFinished);

    if (!success) {
        _abort("SampleScan failed: " + msg);
        return;
    }
    m_eCurStep = STEP_DONE;
    emit sigStepChanged("DONE");
    emit sigPreprocessingSuccess();
}

void TestPreprocessing::_abort(const QString& errMsg)
{
    qDebug() << "Error m_eCurStep" << m_eCurStep << "errMsg" << errMsg;
    m_eCurStep = STEP_ERROR;
    emit sigStepChanged("ERROR");
    emit sigPreprocessingFailed(errMsg);
}
