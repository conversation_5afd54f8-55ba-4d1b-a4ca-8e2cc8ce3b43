/*****************************************************
  * Copyright: 万孚生物
  * Author: mflin
  * Date: 2024-2-1
  * Description:  卡条管理
  * -----------------------------------------------------------------
  * History:
  *1.创建卡条管理类
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CSTRIP_H
#define CSTRIP_H
#include<QtGlobal>
#include<QMutex>
#include<QVector>

#define STRIP_MAX_SIZE 16
class CStrip
{
public:
    static CStrip &getInstance();

    bool Is<PERSON>tri<PERSON>nough(quint16 uiSize);

    //设置卡条的数量,在扫描后可得到卡条数量
    void SetStripSize(quint16 uiSize);

    /**
     * @brief Consume 消耗卡条
     * @param uiConsumeSize 消耗数量
     * @return 返回消耗的卡条位置集合
     */
    QVector<quint8> Consume(quint16 uiConsumeSize);

    /**
     * @brief InitStrip
     */
    void InitStrip();

    /**
     * @brief SetMagneticRodSleevePositionBit 设置磁棒套/卡条光耦位置信息
     * @param bitPos 磁棒套/卡条光耦位置   bitValue 磁棒套/卡条光耦是否存在
     * @return 
     */
    void SetMagneticRodSleevePositionBit(int bitPos, int bitValue);
    /**
     * @brief GetMagneticRodSleevePositionBit 获取磁棒套/卡条光耦位置信息
     * @param bitPos 磁棒套/卡条光耦位置
     * @return 返回磁棒套/卡条光耦位置的状态
     */
    int GetMagneticRodSleevePositionBit(int bitPos);

    /**
     * @brief SetMagneticRodSleevePositionAll 设置磁棒套/卡条光耦全部位置信息
     * @param uiValue 磁棒套/卡条光耦位置信息   
     * @return 
     */
    void SetMagneticRodSleevePositionAll(quint16 uiValue);

    /**
     * @brief GetMagneticRodSleevePositionAll 获取磁棒套/卡条光耦全部位置信息 
     * @param null
     * @return 返回磁棒套/卡条光耦全部位置信息
     */
    quint16 GetMagneticRodSleevePositionAll();   

    /**
     * @brief GetMagneticRodSleeveQuantity 获取磁棒套/卡条光耦数量
     * @param null
     * @return 返回磁棒套/卡条光耦数量
     */
    int GetMagneticRodSleeveQuantity();

    /**
     * @brief GetMagneticRodSleevePositionAll 获取磁棒套/卡条光耦全部位置信息 
     * @param list   
     * @return 返回磁棒套/卡条光耦全部位置信息
     */
    void GetMagneticRodSleevePositionAll(QList<int>& list);    

    /**
     * @brief GetMagneticRodSleevePositionBit16 获取卡条16号位置光耦信息
     * @param    
     * @return true--有提取条 false--没有提取条
     */
    bool GetMagneticRodSleevePositionBit16();   

    /**
     * @brief GetCurConsume 获取当前消耗卡条
     * @return 返回消耗的卡条位置集合
     */
    QVector<quint8> GetCurConsume();

    /**
     * @brief GetNextConsume 获取下一个消耗卡条
     * @param uiConsumeSize 消耗数量
     * @return 返回消耗的卡条位置集合
     */
    QVector<quint8> GetNextConsume(quint16 uiConsumeSize); 
    
    /**
     * @brief ResetStripSize 根据光耦重置提取条数量
     * @return 
     */
    void ResetStripSize(); 

    /**
     * @brief CheckLastStripInLeftNext 下一个提取条在最左判断(用来规避移液泵和样本抓手干涉问题)
     * @return 是否最后一个
     */
    bool CheckLastStripInLeftNext(); 

    /**
     * @brief CheckLastStripInLeft 当前提取条在最左判断(用来规避移液泵和样本抓手干涉问题)
     * @return 是否最后一个
     */
    bool CheckLastStripInLeft();     
    
    /**
     * @brief CheckStripInOrder 检测提取条是否连续
     * @return 是否连续
     */
    bool CheckStripInOrder(); 
    
    /**
     * @brief GetStripValidSize 获取提取条有效数量
     * @return 提取条数量
     */
    quint8 GetStripValidSize();    
private:
    CStrip();

private:
    QMutex m_qMutex;
    quint16 m_uiSize;
    qint8 m_uiPreIndex;                       // 最新已被使用的位置索引
    quint16 m_uiMagneticPos;                  // Bit0~15：磁棒套/卡条光耦1~16（右边起）
    QVector<quint8> m_vCurConsume;            // 当前正在使用卡条
    qint8 m_uiLastIndex;                      // 最后使用位置索引
};

#endif // CSTRIP_H
