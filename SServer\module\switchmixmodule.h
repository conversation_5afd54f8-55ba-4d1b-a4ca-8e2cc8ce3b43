#ifndef SWITCHMIXMODULE_H
#define SWITCHMIXMODULE_H

#include "devicemodule.h"

enum SwitchMixTaskID
{
    SMTI_SWITCH_AND_MIX = 0,//转移和混匀
};


class SwitchMixModule : public DeviceModule {
    Q_OBJECT

public:
    SwitchMixModule(bool bUseThread = false, quint8 quCatchType = DEVICE_CATCH_TYPE); // 添加默认值
    void SetCatchType(quint8 quCatchType); // 新增设置 m_quCatchType 变量的函数
public:
    // 重载基类的虚函数，实现两个版本的添加任务逻辑
    void SlotAddSubTask(quint8 uiSubTaskID, const QString& strCommandStr, const QString& strParamStr) override;
    void SlotAddTask(const CmdTask& task) override;
private:
    void _ProcessSubTask() override;
    void _AddSwitchAndMixTask();
private:
    quint8 m_uiCatchType; // 添加成员变量
};

#endif // SWITCHMIXMODULE_H
