#include "pcrresource.h"
#include <QDebug>
#include"./SystemConfig/SystemConfig.h"
#include "datacontrol/cprojectdb.h"
#include "datacontrol/ctiminginfodb.h"

#define CHECK_PCR_HOLE 0
#define PCR_ABANDON_TIME 60// 60秒pcr管丢弃时间

PCRResource &PCRResource::getInstance()
{
    static PCRResource instance;
    return instance;
}

bool PCRResource::IsPCRResourceEnough(quint8 uiTotalUseSize)
{
    // 如果支持下一批继续测试，需要确定即将释放孔位
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            QDFUN_LINE << "PCRResource::IsPCRResourceEnough: " << i << j << m_pcrResInfo[i][j].uiPCRST << m_pcrResInfo[i][j].uiRemain << uiTotalUseSize;
            if(m_pcrResInfo[i][j].uiPCRST <= PCRST_ADDING && m_pcrResInfo[i][j].uiRemain>0)
            {
                if(uiTotalUseSize>m_pcrResInfo[i][j].uiRemain)
                {
                    uiTotalUseSize -= m_pcrResInfo[i][j].uiRemain;
                }
                else
                {
                    bResult = true;
                }
            }
        }
    }
    qDebug() << "IsPCRResourceEnough bResult: " << bResult << uiTotalUseSize;
    return bResult;
}

bool PCRResource::GetNextSingleIdlePCRPos(quint8 &uiRowIndex, quint8 &uiColumnIndex,
                                          quint8 &uiSubAreaRowIndex,
                                          quint8 &uiSubAreaColumnIndex,
                                          bool &bNeedOpenCap,
                                          QString &strTecName)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            //相同TEC时序PCR区域或者未被占用区域允许
            if(m_pcrResInfo[i][j].uiPCRST <= PCRST_ADDING && m_pcrResInfo[i][j].uiRemain>0
                    && (m_pcrResInfo[i][j].strTecName == ""  || m_pcrResInfo[i][j].strTecName == strTecName))
            {
                uiRowIndex = i;
                uiColumnIndex = j;
                uiSubAreaRowIndex = GetPCRSubAreaRowIndex(m_pcrResInfo[i][j].uiNextSinglePos);// m_pcrResInfo[i][j].uiNextSinglePos/m_uiPCRSubAreaColumnSize;
                uiSubAreaColumnIndex = GetPCRSubAreaRowIndex(m_pcrResInfo[i][j].uiNextSinglePos);
                //m_pcrResInfo[i][j].uiNextSinglePos%m_uiPCRSubAreaColumnSize;
                bResult = true;
                if(m_pcrResInfo[i][j].uiPCRST == PCRST_IDLE)
                {
                    bNeedOpenCap =  true;
                    m_pcrResInfo[i][j].strTecName = strTecName;
                }
                return bResult;
            }
        }
    }
    return bResult;
}

bool PCRResource::GetNextDoubleIdlePCRPos(quint8& uiRowIndex,
                                          quint8& uiColumnIndex,
                                          quint8& uiSubAreaRowIndex,
                                          quint8& uiSubAreaColumnIndex,
                                          bool &bNeedOpenCap,
                                          QString &strTecName)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            qDebug()<<"GetNextDoubleIdlePCRPos: "<<i<<j<<m_pcrResInfo[i][j].uiPCRST<<m_pcrResInfo[i][j].uiRemain<<m_pcrResInfo[i][j].strTecName<<m_pcrResInfo[i][j].uiNextDoublePos;
            if(m_pcrResInfo[i][j].uiPCRST <= PCRST_ADDING && m_pcrResInfo[i][j].uiRemain>1
                    && (m_pcrResInfo[i][j].strTecName == ""  || m_pcrResInfo[i][j].strTecName == strTecName))
            {
                uiRowIndex = i;
                uiColumnIndex = j;
#if CHECK_PCR_HOLE
                quint8 uiAreaIndex = _GetRegionIndexByRowColumn(uiRowIndex,uiColumnIndex);
                _GetSubAreaInfo(uiAreaIndex,uiSubAreaRowIndex,uiSubAreaColumnIndex);
#else
                uiSubAreaRowIndex = GetPCRSubAreaRowIndex(m_pcrResInfo[i][j].uiNextDoublePos); //m_pcrResInfo[i][j].uiNextDoublePos/m_uiPCRSubAreaColumnSize;
                uiSubAreaColumnIndex = GetPCRSubAreaColumnIndex(m_pcrResInfo[i][j].uiNextDoublePos); //m_pcrResInfo[i][j].uiNextDoublePos%m_uiPCRSubAreaColumnSize;
#endif                
                bResult = true;
                if(m_pcrResInfo[i][j].uiPCRST == PCRST_IDLE)
                {
                    bNeedOpenCap =  true;
                    m_pcrResInfo[i][j].strTecName = strTecName;
                }
                return bResult;
            }
        }
    }
    return bResult;
}

void PCRResource::_InitPCRResInfo(PCRResInfo* pResInfo, quint8 uiRowIndex, quint8 uiColumnIndex)
{
    if(pResInfo)
    {
        pResInfo->uiPCRST = PCRST_IDLE;
        pResInfo->uiRemain = m_uiPCRSubAreaSize;
        pResInfo->uiCapacity = m_uiPCRSubAreaSize;
        pResInfo->uiRowIndex = uiRowIndex;
        pResInfo->uiColumnIndex = uiColumnIndex;
        pResInfo->uiNextSinglePos= pResInfo->uiCapacity -1;
        pResInfo->uiNextDoublePos = 0;
        pResInfo->strTecName = "";
        QDFUN_LINE << pResInfo->printInfo();
    }
}

void PCRResource::SetPCRSizeType(quint8 uiSizeType)
{
    qDebug()<<"SetPCRResource Type:"<<uiSizeType;
    QMutexLocker qLocker(&m_qMutex);
    m_uiPCRSizeType = uiSizeType;
    m_uiPCRTotalSize = (m_uiPCRSizeType == PCR_SIZE_64) ? 64: 32;
    m_uiPCRSubAreaSize = m_uiPCRTotalSize/PCR_MODULE_SIZE;
    m_uiPCRSubAreaRowSize = m_uiPCRSubAreaSize/PCR_SUB_AREA_COLUMN_SIZE;
    m_uiPCRSubAreaColumnSize = PCR_SUB_AREA_COLUMN_SIZE;
}

void PCRResource::ResetDBData()
{
    for(int i= 0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            PCRResInfo resInfo;
            resInfo.uiRowIndex = i;
            resInfo.uiColumnIndex = j;
            _InitPCRResInfo(&resInfo, i, j);
            m_persister.savePCRRes(i*PCR_COLUMN_SIZE+j, resInfo);
        }
    }
    qDebug()<<"ResetDBData:";
}

QList<PCRResInfo> PCRResource::getAllNeedCleanPCRRes()
{
    QList<PCRResInfo> resInfos = m_persister.getAllPCRRes();
    QList<PCRResInfo> qDstResInfos;
    for(int i=0; i< resInfos.size();i++)
    {
        PCRResInfo resInfo = resInfos.at(i);
        if(resInfo.uiRemain != resInfo.uiCapacity )//有使用才需要清理
        {
            qDstResInfos.push_back(resInfo);
        }
    }
    qDebug()<<"getAllNeedCleanPCRRes:"<<qDstResInfos.size();
    return qDstResInfos;
}

quint8 PCRResource::GetPCRSubAreaRowIndex(quint8 uiPosIndex)
{
    return uiPosIndex/m_uiPCRSubAreaColumnSize;
}

quint8 PCRResource::GetPCRSubAreaColumnIndex(quint8 uiPosIndex)
{
    return uiPosIndex%m_uiPCRSubAreaColumnSize;
}

bool PCRResource::FreePCRSubArea(quint8 uiRowIndex, quint8 uiColumnIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bResult = false;
    if(uiRowIndex<PCR_ROW_SIZE && uiColumnIndex<PCR_COLUMN_SIZE)
    {
        bResult = true;
        PCRResInfo* pResInfo = &m_pcrResInfo[uiRowIndex][uiColumnIndex];
        _InitPCRResInfo(pResInfo, uiRowIndex, uiColumnIndex);
    }
    return bResult;
}

void PCRResource::FreeAllPCR()
{
    QMutexLocker qLocker(&m_qMutex);
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            _InitPCRResInfo(&m_pcrResInfo[i][j], i, j);
        }
    }
    m_qPCRAreaBatchInfoRecord.clear();
    m_qPCRAreaTimeRecord.clear();
    qDebug()<<"FreeAllPCR:";
}

void PCRResource::UsePCR(quint8 uiRowIndex, quint8 uiColumnIndex, bool & bNeedCloseCap,const QVector<SystemBuildInfo> qVect)
{
    QMutexLocker qLocker(&m_qMutex);
    PCRResInfo* pResInfo = &m_pcrResInfo[uiRowIndex][uiColumnIndex];
    if(pResInfo)
    {    
        quint8 u8UseHole = qVect.size();
        pResInfo->uiPCRST = PCRST_ADDING;
        pResInfo->uiRemain -= u8UseHole;
        pResInfo->uiNextDoublePos += u8UseHole;        
        if(pResInfo->uiRemain == 0)
        {
            pResInfo->uiPCRST = PCRST_USING;
            bNeedCloseCap = true;
        }

        // 需要pcr区域的行列转化为uiHoleIndex
        quint8 uiTubeRowIndex = 0;
        quint8 uiTubeColumnIndex = 0;   
        for (auto& info : qVect)
        {
            uiTubeRowIndex    = info.pcrPos.uiRowIndex;
            uiTubeColumnIndex = info.pcrPos.uiColumnIndex;
            quint8 uiHoleIndex = uiTubeColumnIndex + (uiTubeRowIndex * PCR_SUB_AREA_COLUMN_SIZE);// uiTubeRowIndex 0或者1
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiHoleIndex].uiPCRST = PCRST_USING;      // 设置已经使用的pcr位置
            QDFUN_LINE << m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiHoleIndex].printInfo();
            pResInfo->strBatchNo = info.strBatchNo;
        }
        m_persister.savePCRRes(uiRowIndex*PCR_COLUMN_SIZE+uiColumnIndex, *pResInfo);
        QDFUN_LINE << pResInfo->printInfo();
    }
}

void PCRResource::GetNeedCloseCap(quint8 uiRowIndex, quint8 uiColumnIndex, bool &bNeedCloseCap)
{
    QMutexLocker qLocker(&m_qMutex);
    PCRResInfo* pResInfo = &m_pcrResInfo[uiRowIndex][uiColumnIndex];
    if(pResInfo)
    {
        if(pResInfo->uiRemain == 0)
        {
            bNeedCloseCap = true;
        }
    }    
}

void PCRResource::SetPCRST(quint8 uiRowIndex, quint8 uiColumnIndex, quint8 uiPCRST)
{
    QMutexLocker qLocker(&m_qMutex);
    m_pcrResInfo[uiRowIndex][uiColumnIndex].uiPCRST = uiPCRST;
    if (uiPCRST == PCRST_IDLE)
    {
        m_pcrResInfo[uiRowIndex][uiColumnIndex].strBatchNo.clear();
        m_pcrResInfo[uiRowIndex][uiColumnIndex].uiRemain = m_uiPCRSubAreaSize;
    }
    QDFUN_LINE << m_pcrResInfo[uiRowIndex][uiColumnIndex].printInfo();
}

void PCRResource::SetPCRST(quint8 uiPCRIndex, quint8 uiPCRST )
{
    quint8 uiRowIndex = 0; 
    quint8 uiColumnIndex = 0;
    _GetRegionIndex(uiPCRIndex, uiRowIndex, uiColumnIndex);
    SetPCRST(uiRowIndex, uiColumnIndex,uiPCRST);
}

quint8 PCRResource::GetPCRSizeType()
{
    QMutexLocker qLocker(&m_qMutex);
    return  m_uiPCRSizeType;
}

quint8 PCRResource::GetPCRSubAreaSize()
{
    QMutexLocker qLocker(&m_qMutex);
    return  m_uiPCRSubAreaSize;
}

bool PCRResource::_GetSubAreaInfo(quint8 uiSubAreaIndex, quint8 &uiSubAreaRowIndex, quint8 &uiSubAreaColumnIndex)
{
    bool bHoleDouble = true;
    if (m_qHashHoleDoubleValidInfo.contains(uiSubAreaIndex))
    {
        QQueue<QPair<qint8, qint8>> qHoleInfo = m_qHashHoleDoubleValidInfo[uiSubAreaIndex];
        if (qHoleInfo.size() == 0 && m_qHashHoleDoubleInvalidInfo.contains(uiSubAreaIndex))// 需要判断连孔已经用完，继续使用单孔
        {
            qHoleInfo = m_qHashHoleDoubleInvalidInfo[uiSubAreaIndex];
            bHoleDouble = false;
            qDebug()<<"_GetSubAreaInfo Invalid keys:"<<m_qHashHoleDoubleInvalidInfo.keys();
        }
        if (!qHoleInfo.isEmpty())
        {
            auto info = qHoleInfo.front();
            uiSubAreaRowIndex = GetPCRSubAreaRowIndex(info.first); 
            uiSubAreaColumnIndex = GetPCRSubAreaColumnIndex(info.first); 
            qDebug()<<"_GetSubAreaInfo:"<<uiSubAreaIndex<<uiSubAreaRowIndex<<uiSubAreaColumnIndex<<info.first;
        }
        else
        {
            QDFUN_LINE << "Error: PCR Sub Area Info is empty.";//"PCR 子区域信息为空。"
        }
    }
    else
    {
        bHoleDouble = false;
        QDFUN_LINE << "Error: bHoleDouble = false";
    }
    qDebug() << "_GetSubAreaInfo Valid keys:" << m_qHashHoleDoubleValidInfo.keys() << bHoleDouble; // (1, 0, 3, 2)
    return bHoleDouble;
}

bool PCRResource::_GetInvalidSubAreaInfo(quint8 uiSubAreaIndex, quint8 &uiSubAreaRowIndex, quint8 &uiSubAreaColumnIndex)
{
    bool bRet = false;
    if (m_qHashHoleDoubleInvalidInfo.contains(uiSubAreaIndex))
    {
        auto& qHoleInfo = m_qHashHoleDoubleInvalidInfo[uiSubAreaIndex];
        if (qHoleInfo.size() != 0)
        {
            auto info = qHoleInfo.front();

            uiSubAreaRowIndex = GetPCRSubAreaRowIndex(info.first); 
            uiSubAreaColumnIndex = GetPCRSubAreaColumnIndex(info.first); 
            bRet = true;
            qDebug()<<"_GetInvalidSubAreaInfo:"<<uiSubAreaRowIndex<<uiSubAreaColumnIndex<<info.first;
        }
        else
        {
            QDFUN_LINE << "Error: PCR Sub Area Info is empty.";//"PCR 子区域信息为空。"
        }
    }
    return bRet;    
}

void PCRResource::_UpdateSubAreaInfo(quint8 uiRowIndex, quint8 uiColumnIndex,bool bSingleHole)
{
    bool bFindSingleHole = false;
    quint8 uiAreaIndex = _GetRegionIndexByRowColumn(uiRowIndex,uiColumnIndex);
    if (m_qHashHoleDoubleValidInfo.contains(uiAreaIndex))
    {
        QQueue<QPair<qint8, qint8>>& qHoleInfo = m_qHashHoleDoubleValidInfo[uiAreaIndex];
        if (qHoleInfo.size() == 0 && m_qHashHoleDoubleInvalidInfo.contains(uiAreaIndex))
        {
            qHoleInfo = m_qHashHoleDoubleInvalidInfo[uiAreaIndex];
            bFindSingleHole = true;
        }
        if (qHoleInfo.size() != 0)
        {
            if (bSingleHole && !bFindSingleHole)// 单个孔位，需要修改孔位信息，因为是当前使用PCR区域最后一个孔位，不会影响其他逻辑
            {
                if (qHoleInfo.size() == 1)
                {
                    qHoleInfo.pop_front();
                }
                else
                {
                    QPair<qint8, qint8> info = qHoleInfo.front();
                    info.first = info.second;
                    m_qHashHoleDoubleValidInfo[uiAreaIndex].replace(0,info);
                }
                qDebug()<<"_UpdateSubAreaInfo bSingleHole:"<<uiAreaIndex<<qHoleInfo.size();
            }
            else
            {
                qHoleInfo.pop_front();// 删除已经使用的孔位
            }
        }
        qDebug()<<"_UpdateSubAreaInfo:"<<uiAreaIndex<<qHoleInfo.size()<<m_qHashHoleDoubleValidInfo.keys();
    }
}

PCRResource::PCRResource()
{
    SetPCRSizeType(PCR_SIZE_64);
    FreeAllPCR();
    getAllNeedCleanPCRRes();
}

void PCRResource::_Init()
{
    FreeAllPCR();
}

void PCRResource::setAllPCRAreaNeedClean()
{
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            PCRResInfo resInfo;
            resInfo.uiRowIndex =i;
            resInfo.uiColumnIndex = j;
            _InitPCRResInfo(&resInfo, resInfo.uiRowIndex, resInfo.uiColumnIndex);
            resInfo.uiCapacity = PCR_SUB_AREA_SIZE2;
            resInfo.uiRemain = 0;
            resInfo.uiNextDoublePos = PCR_SUB_AREA_SIZE2;
            resInfo.uiNextSinglePos = PCR_SUB_AREA_SIZE2-1;
            resInfo.uiPCRST = PCRST_USING;
            QDFUN_LINE << resInfo.printInfo();
            m_persister.savePCRRes(resInfo.uiRowIndex*PCR_COLUMN_SIZE+resInfo.uiColumnIndex, resInfo);
        }
    }
}

void PCRResource::SetDisablePcrArea(const QString &strParam)
{
    if (!m_qPCRAreaTimeRecord.keys().isEmpty())
    {
        qDebug()<<"SetDisablePcrArea m_qPCRAreaTimeRecord is not empty:"<<m_qPCRAreaTimeRecord.keys();
        return;
    }    

    QStringList params = strParam.split(",");    
    if (params.size() == PCR_MODULE_SIZE)
    {
        quint8 uiRowIndex = 0;
        quint8 uiColumnIndex = 0;        
        quint8 param = 0;
        PCRState sta = PCRState::PCRST_IDLE;
        for (size_t i = 0; i < params.size(); i++)
        {
            _GetRegionIndex(i,uiRowIndex,uiColumnIndex);
            param = params[i].toUInt();
            sta   = (param == 0)?PCRState::PCRST_IDLE:PCRState::PCRST_USING;
            m_pcrResInfo[uiRowIndex][uiColumnIndex].uiPCRST = sta;
            QDFUN_LINE << m_pcrResInfo[uiRowIndex][uiColumnIndex].printInfo();
            qDebug()<<"SampleControl::SetDisablePcrArea"<<uiRowIndex<<uiColumnIndex<<sta;
        } 
    }
}

void PCRResource::LoadValidPcrArea()
{
    // 开始测试前，检测禁用区域
    QMetaEnum metaFieldType = QMetaEnum::fromType<SystemConfig::EnumPcrType>();
    QString strField = metaFieldType.valueToKey(static_cast<int>(SystemConfig::pcr_disable_area));
    QString strValue = CSystemDB::getInstance().getStringValueFromKey(strField);  
    SetDisablePcrArea(strValue);
    qDebug()<<"LoadValidPcrArea: "<<strValue;
}

void PCRResource::SetDisablePcrAreaHole(const QString &strParam)
{
    if (!m_qPCRAreaTimeRecord.keys().isEmpty())
    {
        qDebug()<<"SetDisablePcrAreaHole m_qPCRAreaTimeRecord is not empty:"<<m_qPCRAreaTimeRecord.keys();
        return;
    } 
    QMutexLocker qLocker(&m_qMutex);
    const quint8 ui8HoleMax = PCR_SUB_AREA_SIZE2*PCR_MODULE_SIZE;//pcr 最大孔数量
    QString strValue = strParam;
    if (strParam.isEmpty())
    {
        QStringList strListDefault;// 默认值
        for (size_t i = 0; i < ui8HoleMax; i++)
        {
            strListDefault.append("0");
        }
        strValue = strListDefault.join(",");
    }
    QStringList strList = strValue.split(',');
    if(strList.size() < ui8HoleMax)
    {
        qDebug() << "PCR hole info list size error!";
        return;
    }
    qDebug()<<"SetPCRHoleST strInfo: "<<strValue;

    // 16个为一个区域
    // 再区分区域的行与列(有2行8列)
    PCRState sta = PCRState::PCRST_IDLE;
    quint8 uiRemain = 0;
    quint8 uiRowIndex = 0;
    quint8 uiColumnIndex = 0;
    quint8 uiCount = 0;

    m_qVectHoleRemain.clear();
    for(int i = 0; i < PCR_MODULE_SIZE; i++)
    {
        size_t startIdx = i * PCR_SUB_AREA_SIZE2;
        size_t endIdx   = (i + 1) * PCR_SUB_AREA_SIZE2;
        
        _GetRegionIndex(i,uiRowIndex,uiColumnIndex);
        for (size_t j = startIdx; j < endIdx; j++)
        {
            sta = PCRState::PCRST_IDLE;
            if (strList[j] == "1")
            {
                sta = PCRState::PCRST_USING;
            }
            else
            {
                uiRemain++;
            }
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiCount].uiRowIndex = uiRowIndex;
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiCount].uiColumnIndex = uiColumnIndex;
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiCount].uiPos = uiCount;
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiCount].uiPCRST = sta;
            QDFUN_LINE << m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiCount].printInfo();
            uiCount++;
        }
        m_pcrResInfo[uiRowIndex][uiColumnIndex].uiRemain = uiRemain;//当前PCR区域可用孔位数量
        m_qVectHoleRemain.append(uiRemain);// 预分配每个区域孔位有效数量
        uiRemain = 0;
        uiCount = 0;
        qDebug()<<"SetPCRHoleST region:"<<uiRowIndex<<uiColumnIndex;
    }           
}

void PCRResource::LoadValidPcrAreaHole()
{
    QMetaEnum metaFieldType = QMetaEnum::fromType<SystemConfig::EnumPcrType>();
    QString strField = metaFieldType.valueToKey(static_cast<int>(SystemConfig::pcr_disable_area_hole));
    QString strValue = CSystemDB::getInstance().getStringValueFromKey(strField);    
    SetDisablePcrAreaHole(strValue);   
    ResetAllAreaNextPos();
    qDebug()<<"LoadValidPcrAreaHole:";
}

bool PCRResource::_IsDoubleHole(quint8 uiRowIndex, quint8 uiColumnIndex,quint8& uiNextDoublePos)
{
    bool bResult = false;
    if (uiNextDoublePos > PCR_SUB_AREA_SIZE2)
    {
        qDebug() << "IsDoubleHole uiNextDoublePos error!";
        return bResult;
    }
    
    // 需要判断列结尾(有2行8列)
    if ((uiNextDoublePos+1) % (PCR_SUB_AREA_COLUMN_SIZE) == 0)
    {
        qDebug() << "IsDoubleHole uiNextDoublePos column end"<<uiNextDoublePos;// 列结尾，直接返回false不连续
        return bResult;
    }

    auto hole1 = m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiNextDoublePos].uiPCRST;
    auto hole2 = m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiNextDoublePos+1].uiPCRST;
    if (hole1 == PCRState::PCRST_IDLE && hole2 == PCRState::PCRST_IDLE)
    {
        bResult = true;
    }
    
    qDebug()<<"IsDoubleHole uiNextDoublePos:"<<uiNextDoublePos<<bResult;
    return bResult;
}

bool PCRResource::IsDoubleHole(quint8 uiRowIndex, quint8 uiColumnIndex)
{
    bool bResult = false;
    bResult = _IsDoubleHole(uiRowIndex,uiColumnIndex,m_pcrResInfo[uiRowIndex][uiColumnIndex].uiNextDoublePos);
    qDebug()<<"IsDoubleHole uiNextDoublePos:"<<uiRowIndex<<uiColumnIndex<<m_pcrResInfo[uiRowIndex][uiColumnIndex].uiNextDoublePos<<bResult;  
    return bResult;
}

void PCRResource::ResetAllAreaNextPos()// 重置(有下一批开始)
{
    quint8 uiRowIndex = 0;
    quint8 uiColumnIndex = 0;
    for(int i = 0; i < PCR_MODULE_SIZE; i++)
    {
        _GetRegionIndex(i,uiRowIndex,uiColumnIndex);
        m_pcrResInfo[uiRowIndex][uiColumnIndex].uiNextDoublePos = _GetValidNextPos(uiRowIndex,uiColumnIndex);
    }
    _SortPCRHoleInfo();
    qDebug()<<"ResetAllAreaNextPos";
}

void PCRResource::_GetSamplePCRHoleInfo(QQueue<QVector<SystemBuildInfo>>& qCalcSystemBuildInfo,QVector<quint8>& qVectHoleRemain,QVector<SystemBuildInfo> &infoCur,qint8& uiAreaIndexLast)  
{
    if (qVectHoleRemain.size() == 0)
    {
        qDebug()<<"_GetSamplePCRHoleInfo qVectHoleRemain is empty"<<qVectHoleRemain.size();
        return;
    }
    QDFUN_LINE << "qVectHoleRemain" << qVectHoleRemain;
    quint8 uiRowIndex = 0;
    quint8 uiColumnIndex = 0;
    quint8 uiSubAreaRowIndex = 0;
    quint8 uiSubAreaColumnIndex = 0;
    quint8 uiAreaIndex = 0;
    bool bHoleDouble = true;
    bool bNeedOpenCap = false;
    quint8 uiUseHoleCount = 0;// 计算已经使用的孔位数量
             
    for(int i=0;i<PCR_ROW_SIZE;i++)
    {
        for(int j=0;j<PCR_COLUMN_SIZE;j++)
        {
            auto& pcrResInfo = m_pcrResInfo[i][j];
            qDebug()<<"_GetSamplePCRHoleInfo PCRST_USING:"<<i<<j<<pcrResInfo.uiPCRST;
            if (pcrResInfo.uiPCRST == PCRST_USING)
            {
                continue;
            }
            
            uiAreaIndex = _GetRegionIndexByRowColumn(i,j);
            auto& u8Remain = qVectHoleRemain[uiAreaIndex];
            if(u8Remain == 0)
            {
                qDebug()<<"CalcSamplePCRHoleInfo u8Remain: "<<i<<j<<u8Remain;
                continue;
            }

            if (uiAreaIndexLast != uiAreaIndex)// 区域改变，需要打开pcr区域盖
            {
                bNeedOpenCap = true;
            }
            QDFUN_LINE << "uiAreaIndexLast" << uiAreaIndexLast << "uiAreaIndex" << uiAreaIndex << "bNeedOpenCap" << bNeedOpenCap;
            uiRowIndex = i;
            uiColumnIndex = j;
            bHoleDouble = _GetSubAreaInfo(uiAreaIndex,uiSubAreaRowIndex,uiSubAreaColumnIndex);// 获取现在能用的第一个区域，获取可用的孔位数量，先连续后单孔 

            if (!bHoleDouble && infoCur.size() == 2)// 孔位不连续且样本数量为2，需要拆成两个vetor
            {
                for (size_t i = 0; i < infoCur.size(); i++)
                {
                    QVector<SystemBuildInfo> infoNew;
                    auto sampleInfo         = infoCur[i];
                    sampleInfo.pcrPos       = {uiSubAreaRowIndex, uiSubAreaColumnIndex, uiRowIndex * PCR_COLUMN_SIZE + uiColumnIndex};    // PCR管
                    sampleInfo.pcrAreaPos   = {uiRowIndex, uiColumnIndex, uiRowIndex * PCR_COLUMN_SIZE + uiColumnIndex};                  // PCR区域
                    sampleInfo.bNeedOpenCap = bNeedOpenCap;
                    infoNew.append(sampleInfo);
                    _UpdateSubAreaInfo(uiRowIndex,uiColumnIndex);                              // 删除已经使用PCR管
                    _GetSubAreaInfo(uiAreaIndex,uiSubAreaRowIndex,uiSubAreaColumnIndex);       // 不用再判断是否连续
                    qCalcSystemBuildInfo.enqueue(infoNew); 
                    uiUseHoleCount++; 
                    qDebug()<<"_GetSamplePCRHoleInfo not sequential: "<<uiSubAreaRowIndex<<uiSubAreaColumnIndex;
                }               
            }
            else// 连续孔位，区分一个或者两个样本
            {
                bool bNoDisableHole = false;
                if (infoCur.size() == 1)// 单孔样本，需要获取单孔信息(禁用孔位，造成的单个孔)
                {
                    bNoDisableHole = _GetInvalidSubAreaInfo(uiAreaIndex,uiSubAreaRowIndex,uiSubAreaColumnIndex);
                    qDebug() << "bNoDisableHole" << bNoDisableHole;
                    if (bNoDisableHole)// 能获取成功才赋值
                    {
                        auto& info = infoCur[0];
                        info.pcrPos       = {uiSubAreaRowIndex, uiSubAreaColumnIndex, 0};    // PCR管
                        info.pcrAreaPos   = {uiRowIndex, uiColumnIndex, 0};                  // PCR区域
                        info.bNeedOpenCap = bNeedOpenCap;            
                        uiUseHoleCount++;   
                        QDFUN_LINE << info.printInfo();
                    }
                    // else
                    // {
                    //     auto& info = infoCur[0];
                    //     info.pcrPos       = {uiSubAreaRowIndex, ++uiSubAreaColumnIndex, 0};    // PCR管
                    //     info.pcrAreaPos   = {uiRowIndex, uiColumnIndex, 0};                  // PCR区域
                    //     info.bNeedOpenCap = bNeedOpenCap;            
                    //     uiUseHoleCount += 2;  
                    //     ++uiSubAreaColumnIndex; 
                    //     QDFUN_LINE << info.printInfo();
                    // }
                }
                
                bool bSingleHole = false;
                if(infoCur.size() == 2 || !bNoDisableHole)// 双样本，或者单样本没有禁孔
                {
                    _GetSubAreaInfo(uiAreaIndex,uiSubAreaRowIndex,uiSubAreaColumnIndex); // 不用再判断是否连续 
                    for (auto& info : infoCur)
                    {
                        if (infoCur.size() == 2)
                        {
                            info.pcrPos       = {uiSubAreaRowIndex, uiSubAreaColumnIndex++, 0};    // PCR管(位置是连续的，需要++)
                        }
                        else if (!bNoDisableHole)
                        {
                            // 单个pcr管(现在时序使用Z2轴转移pcr管，不需要跳一个孔位)
                            info.pcrPos       = {uiSubAreaRowIndex, uiSubAreaColumnIndex, 0};    // PCR管(单个样本)
                            uiUseHoleCount++; 
                        }
                        info.pcrAreaPos   = {uiRowIndex, uiColumnIndex, 0};                    // PCR区域
                        info.bNeedOpenCap = bNeedOpenCap;  // 不管一个还是两个样本，都是一个区域，所以不需要再判断是否连续，开盖的逻辑也是一样的
                        uiUseHoleCount++;  
                        QDFUN_LINE << info.printInfo();
                    }  
                    
                    // 需要判断单个样本，并修改因为禁孔导致不能使用单孔，即使修改因为是当前使用PCR区域最后一个孔位，不会影响其他逻辑
                    if (!bNoDisableHole && infoCur.size() == 1)
                    {
                        bSingleHole = true;
                    }
                }
                _UpdateSubAreaInfo(uiRowIndex,uiColumnIndex,bSingleHole); // 删除已经使用PCR管(单管需要修改孔位信息，不能直接pop，两个匹配pcr孔位都用完再pop)
                qDebug()<<"_GetSamplePCRHoleInfo sequential: "<<infoCur.size()<<bNoDisableHole<<uiSubAreaRowIndex<<uiSubAreaColumnIndex<<uiUseHoleCount<<bSingleHole;                

                qCalcSystemBuildInfo.enqueue(infoCur);
            }  
            
            qDebug()<<"_GetSamplePCRHoleInfo: "<<i<<j<<u8Remain<<infoCur.size()<<bNeedOpenCap<<uiAreaIndexLast<<uiAreaIndex;
            bNeedOpenCap = false;  
            uiAreaIndexLast = uiAreaIndex;          
            u8Remain -= uiUseHoleCount;
            return;
        }
    }
    qDebug()<<"_GetSamplePCRHoleInfo: "<<uiUseHoleCount;
}

void PCRResource::CalcSamplePCRHoleInfo(QQueue<QVector<SystemBuildInfo>>& qCalcSystemBuildInfo)
{
    // 记录所有样本信息，根据每次加入新的信息进行排序，得到最优的结果
    // 排序的规则是：
    // 1.孔位连续，连续的优先(根据孔位索引排序)，把连续的转移完成后，再转移不连续的
    // 2.孔位不连续，根据孔位索引排序(最先进来的样本先转移)，即样本先进这个函数的样本(非连续最早样本)，优先转移
    // 直接拆分和排序SampleControl中的m_qSystemBuildSTMap
    
    QMutexLocker qLocker(&m_qMutex);
    if (m_qCalcSystemBuildInfo.size() == 0)
    {
        qDebug()<<"CalcSamplePCRHoleInfo m_qCalcSystemBuildInfo size 0 ";
        return;
    }

    // 计算样本所需的孔位数量，确定需要用到几个区域(尽量用同一个区域)
    // 计算当前批次需要的区域和孔位数量
    // auto qVectHoleRemain = m_qVectHoleRemain;
    qint8 uiAreaIndexLast = -1;// 上次使用区域
    for (auto& infoCur : m_qCalcSystemBuildInfo)// 是否需要获取同一个区域???????  
    {
        _GetSamplePCRHoleInfo(qCalcSystemBuildInfo,m_qVectHoleRemain,infoCur,uiAreaIndexLast);
    }
    
    qDebug()<<"CalcSamplePCRHoleInfo: "<<m_qCalcSystemBuildInfo.size();
    // 清空缓存信息
    m_qCalcSystemBuildInfo.clear();   
}

void PCRResource::AddWaitCapSampleInfo(QQueue<QVector<SystemBuildInfo>>& qDstQueue)
{
    QMutexLocker qLocker(&m_qMutex);
    m_qCalcSystemBuildInfo.append(qDstQueue);  
    qDebug()<<"AddWaitCapSampleInfo:" << m_qCalcSystemBuildInfo.size() << qDstQueue.size(); 
}

quint8 PCRResource::CalcCurSampleUsePCRHoles(QList<QString> qSampleInfoList)
{
    // 计算当前批次需要的区域和孔位数量
    quint8 u8SampleSize = qSampleInfoList.size();
    quint8 u8HoleNum = 0;
    if(qSampleInfoList.size() == 0)
    {
        qDebug()<<"CalcCurSampleUsePCRHoles qSampleInfoList size is 0";
        return u8HoleNum;
    } 
    
    // 获取组分数量，开始计算孔位数量
    for (const QString &strProj : qSampleInfoList)
    {
        QStringList qProjList = strProj.split(",", QString::SkipEmptyParts);
        for (int iProjIdx = 0; iProjIdx < qProjList.size(); iProjIdx++)
        {
            QString strProjID = qProjList[iProjIdx];
            quint8 uiCompNum = CProjectInformation::getInstance().getTubeCountFromProjectLot(strProjID);
            for(int i = 0;i<uiCompNum;i++)
            {
                u8HoleNum++;
            }
            qDebug()<<"CalcCurSampleUsePCRHoles:"<<qProjList.size()<<strProjID<<uiCompNum;
        }
    }
    m_u8HoleNum = u8HoleNum;
    qDebug()<<"CalcCurSampleUsePCRHoles:"<<u8SampleSize<<u8HoleNum;
    return u8HoleNum;       
}

quint8 PCRResource::GetCalcCurSampleUsePCRHoles()
{
    qDebug()<<"GetCalcCurSampleUsePCRHoles:"<<m_u8HoleNum;
    return m_u8HoleNum;
}

bool PCRResource::CheckCurPatchHolesEnough(QList<QString> qSampleInfoList)
{
    bool bResult = false;
    quint8 u8RemainAll = 0;
    quint8 u8HoleNum = CalcCurSampleUsePCRHoles(qSampleInfoList);
    if (m_qVectHoleRemain.size() != PCR_MODULE_SIZE)
    {
        return bResult;
    }
    
    for (size_t i = 0; i < PCR_MODULE_SIZE; i++)
    {
        auto& u8Remain = m_qVectHoleRemain[i];
        u8RemainAll +=u8Remain;
    }
    if (u8RemainAll>=u8HoleNum)
    {
        bResult = true; // 当前批次孔位充足
    }
    qDebug()<<"CheckCurPatchHolesEnough:"<<u8HoleNum<<u8RemainAll<<bResult;
    return bResult;
}

void PCRResource::_GetRegionIndex(quint8 uiAreaIndex,quint8& uiRowIndex, quint8& uiColumnIndex)
{
    QList<QPair<int, int>> regionList;
    _GetRegionList(regionList);
    if (regionList.size() > uiAreaIndex)
    {
        auto& region = regionList[uiAreaIndex];
        uiRowIndex    = region.first;
        uiColumnIndex = region.second;
    }
    qDebug()<<"_GetRegionIndex:"<<regionList.size()<<uiAreaIndex<<uiRowIndex<<uiColumnIndex;
}

quint8 PCRResource::_GetRegionIndexByRowColumn(const quint8 &uiRowIndex, const quint8 &uiColumnIndex)
{
    quint8 uiAreaIndex = 0;
    QList<QPair<int, int>> regionList;
    _GetRegionList(regionList);    
    for (size_t i = 0; i < regionList.size(); i++)
    {
        auto& region = regionList[i];
        if (region.first == uiRowIndex && region.second == uiColumnIndex)
        {
            uiAreaIndex = i;
            break;
        }
    }
    qDebug()<<"_GetRegionRowColumnIndex:"<<uiRowIndex<<uiColumnIndex<<uiAreaIndex;
    return uiAreaIndex;
}

void PCRResource::_GetRegionList(QList<QPair<int, int>> &list)
{
    list.clear();
    static QList<QPair<int, int>> regionList;
    if (regionList.size() == 0)// 将区域位置信息存储到 QList 中
    {
        regionList << qMakePair(0, 0);  // 区域1
        regionList << qMakePair(0, 1);  // 区域2
        regionList << qMakePair(1, 0);  // 区域3
        regionList << qMakePair(1, 1);  // 区域4
    }  
    list = regionList;  
}

quint8 PCRResource::_GetValidNextPos(quint8& uiRowIndex, quint8& uiColumnIndex)
{
    quint8 index = 0;
    for (size_t j = 0; j < PCR_SUB_AREA_SIZE2; j++)
    {
        if (m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][j].uiPCRST == PCRState::PCRST_IDLE)
        {
            index = j;
            break;
        }
    }
    qDebug()<<"GetValidNextDoublePos index:"<<uiRowIndex<<uiColumnIndex<<index;
    return index;
}

void PCRResource::_SortPCRHoleInfo()
{
    // 把连续孔位找出来
    quint8 uiRowIndex = 0;
    quint8 uiColumnIndex = 0;
    bool bDoubleHole = false;
    m_qHashHoleDoubleValidInfo.clear();
    m_qHashHoleDoubleInvalidInfo.clear();
    for(int i = 0; i < PCR_MODULE_SIZE; i++)
    {
        QQueue<QPair<qint8,qint8>> qVectHoleValid;
        QQueue<QPair<qint8,qint8>> qVectHoleInvalid;        
        _GetRegionIndex(i,uiRowIndex,uiColumnIndex);  
        quint8 j = 0;
        while (j < PCR_SUB_AREA_SIZE2)
        {
            bDoubleHole = false;
            // 需要判断当前区域是否是空闲状态，再获取有效孔位
            if (m_pcrResInfo[uiRowIndex][uiColumnIndex].uiPCRST        == PCRState::PCRST_IDLE &&
                m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][j].uiPCRST == PCRState::PCRST_IDLE)
            {
                bDoubleHole = _IsDoubleHole(uiRowIndex,uiColumnIndex,j);
                if (bDoubleHole)
                {
                    qVectHoleValid.enqueue(qMakePair(j, j+1));
                }
                else
                {
                    qVectHoleInvalid.enqueue(qMakePair(j, -1));// -1 表示不连续
                }
            }
            qDebug()<<"_SortPCRHoleInfo:"<<uiRowIndex<<uiColumnIndex<<j<<m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][j].uiPCRST;
            j++; 
            if (bDoubleHole)
            { 
                j++;// 连孔位，直接跳过下一个
            }                               
        } 
        if (qVectHoleValid.size() > 0)
        {
            m_qHashHoleDoubleValidInfo.insert(i, qVectHoleValid);     
        }
        if (qVectHoleInvalid.size() > 0)
        {
            m_qHashHoleDoubleInvalidInfo.insert(i, qVectHoleInvalid);         
        }
        qDebug() << "qVectHoleValid" << i << qVectHoleValid;
        qDebug() << "qVectHoleInvalid" << i << qVectHoleInvalid;
        qDebug() << "m_pcrResInfo[uiRowIndex][uiColumnIndex].uiPCRST" << m_pcrResInfo[uiRowIndex][uiColumnIndex].uiPCRST;
    }  
}

void PCRResource::AddRecordPCRAreaDuration(const QString& strBatch,QVector<PCRResourceTimeInfo>& qPCRTimeInfoList)
{
    // 是否在样本添加批次信息
    QMutexLocker qLocker(&m_qMutex);
    QDateTime startTime = QDateTime::currentDateTime();
    for (auto& info : qPCRTimeInfoList)
    {
        quint32 uiDurationSeconds = CTimingDB::getInstance().getTecRunTimeFromName(info.strTecName);
        info.startTime = startTime;
        info.endTime   = startTime.addSecs(uiDurationSeconds);
        qDebug() << "[PCR Timing] Area:" << info.uiAreaIndex 
                 << "Start:" << info.startTime.toString("HH:mm:ss")
                 << "Expire:" << info.endTime.toString("HH:mm:ss");        
    }
    m_qPCRAreaTimeRecord.insert(strBatch, qPCRTimeInfoList);
    qDebug() << "AddRecordPCRAreaDuration Batch:" << strBatch << "Size:" << qPCRTimeInfoList.size();
}

void PCRResource::DelRecordPCRAreaDuration(const QString& strBatch)
{
    QMutexLocker qLocker(&m_qMutex);
    if (m_qPCRAreaTimeRecord.contains(strBatch))
    {
        m_qPCRAreaTimeRecord.remove(strBatch);
    }
    
    // 根据m_qPCRAreaBatchInfoRecord的value，获取key,然后根据key获取区域索引，然后重置PCR区域孔位状态
    for (auto& key : m_qPCRAreaBatchInfoRecord.keys())
    {
        if (m_qPCRAreaBatchInfoRecord[key] != strBatch)
        {
            continue;
        }
        quint8 uiRowIndex = 0;
        quint8 uiColumnIndex = 0;
        _GetRegionIndex(key,uiRowIndex,uiColumnIndex);
        for (size_t i = 0; i < PCR_SUB_AREA_SIZE2; i++)
        {
            m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][i].uiPCRST = PCRST_IDLE;
        }  
        qDebug() << "DelRecordPCRAreaDuration m_pcrResHoleInfo:" << key<<uiRowIndex<<uiColumnIndex;      
    }
    qDebug() << "DelRecordPCRAreaDuration:" << strBatch<<m_qPCRAreaTimeRecord.keys();
}

bool PCRResource::CheckRecordPCRAreaDuration(const QString& strBatch)
{
    QMutexLocker qLocker(&m_qMutex);
    bool bRet = false;
    if (m_qPCRAreaTimeRecord.contains(strBatch))
    {
        bRet = true;
    }
    qDebug() << "CheckRecordPCRAreaDuration:" << bRet<<m_qPCRAreaTimeRecord.keys();
    return bRet;
}

qint64 PCRResource::CaclNextBatchWaitTime(quint8 uiTotalUseSize)
{
    // 计算使用量需要多少个pcr区域，再根据批次使用的pcr区域数量，计算等待时间
    quint8 u8NeedAreaNum = (uiTotalUseSize + PCR_SUB_AREA_SIZE2 - 1) / PCR_SUB_AREA_SIZE2;//向上取整的除法运算
    // 按照结束时间排序
    QList<QString> keys = m_qPCRAreaTimeRecord.keys();
    std::sort(keys.begin(), keys.end(), [&](const QString& a, const QString& b) {
        return m_qPCRAreaTimeRecord.value(a).first().endTime < m_qPCRAreaTimeRecord.value(b).first().endTime;
    });

    quint8 u8PCRAreaTimeRecordAreaCount = 0;
    quint8 u8PCRAreaAbandonTime = 0;
    qint64 secondsRemaining = 0;
    // 找到即将释放的批次和对应的pcr区域，需要找出准备测试样本批次需要的pcr区域和孔位，准备释放的和需要的批次对比
    // 需要找到u8NeedAreaNum个区域
    QDateTime curTime = QDateTime::currentDateTime(); 
    // 重构后的核心逻辑：
    QDateTime latestEndTime;
    quint8 totalAbandonTime = 0;
    quint8 totalAreasFound = 0;

    for (auto& key : keys) {
        auto& batchAreas = m_qPCRAreaTimeRecord.value(key);
        totalAreasFound += batchAreas.size();
        
        // 记录本批次最晚结束时间
        QDateTime batchEnd = batchAreas.last().endTime; 
        if (batchEnd > latestEndTime || latestEndTime.isNull()) {
            latestEndTime = batchEnd;
        }

        // 当累计区域满足需求时
        if (totalAreasFound >= u8NeedAreaNum) {
            // 计算实际需要的区域数（可能超过部分不需要）
            quint8 actualUsedAreas = u8NeedAreaNum - (totalAreasFound - batchAreas.size());
            // 添加边界检查
            actualUsedAreas = qMin(actualUsedAreas, static_cast<quint8>(batchAreas.size()));
            
            totalAbandonTime = actualUsedAreas * PCR_ABANDON_TIME;
            
            // 添加越界保护
            if(actualUsedAreas > 0 && actualUsedAreas <= batchAreas.size()) {
                QDateTime actualEnd = batchAreas[actualUsedAreas - 1].endTime;
                secondsRemaining = qMax(qint64(0), curTime.secsTo(actualEnd)) + totalAbandonTime;
            } else {
                qWarning() << "Invalid actualUsedAreas:" << actualUsedAreas << "batch size:" << batchAreas.size();
                // 降级处理：取整个批次的最后时间
                QDateTime actualEnd = batchAreas.last().endTime;
                secondsRemaining = qMax(qint64(0), curTime.secsTo(actualEnd)) + totalAbandonTime;
            }
            break;
        }
    }

    // 当所有现有区域仍不足时
    if (totalAreasFound < u8NeedAreaNum && !keys.isEmpty()) {
        totalAbandonTime = totalAreasFound * PCR_ABANDON_TIME;
        secondsRemaining = qMax(qint64(0), curTime.secsTo(latestEndTime)) + totalAbandonTime;
        
        // 添加警告日志
        qDebug() << "Insufficient PCR areas! Existing:" << totalAreasFound 
                 << "Needed:" << u8NeedAreaNum;
    }
    
    qDebug() << "CaclNextBatchWaitTime:" << keys<<secondsRemaining;
    return secondsRemaining;
}

void PCRResource::AddRecordPCRAreaBatchInfo(const quint8 uiPCRIndex,const QString strBatchInfo)
{
    QMutexLocker qLocker(&m_qMutex);
    m_qPCRAreaBatchInfoRecord.insert(uiPCRIndex, strBatchInfo);
    qDebug() << "AddRecordPCRAreaBatchInfo:" << uiPCRIndex << strBatchInfo<<m_qPCRAreaBatchInfoRecord.keys();
}

QString PCRResource::GetRecordPCRAreaBatchInfo(const quint8 uiPCRIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    QString strBatchInfo;
    if (m_qPCRAreaBatchInfoRecord.contains(uiPCRIndex))
    {
        strBatchInfo = m_qPCRAreaBatchInfoRecord.value(uiPCRIndex);
    }
    qDebug()<<"GetRecordPCRAreaBatchInfo:"<<uiPCRIndex<<strBatchInfo<<m_qPCRAreaBatchInfoRecord.keys();
    return strBatchInfo;
}

QSet<QString> PCRResource::GetAllRecordPCRAreaBatchInfo()
{
    QMutexLocker qLocker(&m_qMutex);
    QSet<QString> qSetBatchInfo;
    qSetBatchInfo = m_qPCRAreaBatchInfoRecord.values().toSet();
    qDebug()<<"GetAllRecordPCRAreaBatchInfo:"<<qSetBatchInfo<<m_qPCRAreaBatchInfoRecord.keys();
    return qSetBatchInfo;
}    

QString PCRResource::GetPCRAreaRemainTimeRecord()
{
    QStringList strList;
    QString strInfo;
    QDateTime curTime = QDateTime::currentDateTime(); 
    for (auto& key : m_qPCRAreaTimeRecord.keys())
    {
        auto& qPCRTimeInfoList = m_qPCRAreaTimeRecord.value(key);
        for (auto& info : qPCRTimeInfoList)
        {
            qint64 secondsRemaining = qAbs(curTime.secsTo(info.endTime))+PCR_ABANDON_TIME;
            strInfo = QString("%1,%2,%3").arg(info.uiAreaIndex).arg(secondsRemaining).arg(key);
            strList.append(strInfo);
        }
    }
    strInfo = strList.join(";");
    qDebug() << "GetPCRAreaRemainTimeRecord:" << strInfo;
    return strInfo;
}

void PCRResource::ResetPCRResourceAreaStatus(quint8 uiAreaIndex)
{
    QMutexLocker qLocker(&m_qMutex);
    quint8 uiRowIndex = 0;
    quint8 uiColumnIndex = 0;
    _GetRegionIndex(uiAreaIndex,uiRowIndex,uiColumnIndex);
    m_pcrResInfo[uiRowIndex][uiColumnIndex].strBatchNo.clear();
    qDebug()<<"ResetPCRResourceAreaAtatus:"<<uiRowIndex<<uiColumnIndex<<uiAreaIndex;
}

void PCRResource::ResetPCRResourceHoletatus(quint8 uiRowIndex, quint8 uiColumnIndex, quint8 uiHoleIndex)
{
    if(uiRowIndex >= PCR_ROW_SIZE || uiColumnIndex >= PCR_COLUMN_SIZE || uiHoleIndex >= PCR_SUB_AREA_SIZE2)
    {
        qDebug() << "Error uiRowIndex:"<<uiRowIndex<<"uiColumnIndex:"<<uiColumnIndex<<"uiHoleIndex:"<<uiHoleIndex<<"is out of range";
        return;
    }
    QMutexLocker qLocker(&m_qMutex);
    m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiHoleIndex].uiPCRST = PCRST_IDLE;
    qDebug()<<"ResetPCRResourceHoletatus:"<<uiRowIndex<<uiColumnIndex<<uiHoleIndex<<m_pcrResHoleInfo[uiRowIndex][uiColumnIndex][uiHoleIndex].uiPCRST;
}

bool PCRResource::IsCurBatchAllPCRIndexValid(const quint8 uiPCRIndex)
{
    bool bResult = false;
    if (m_qPCRAreaBatchInfoRecord.contains(uiPCRIndex))
    {
        QString strBatchInfo = m_qPCRAreaBatchInfoRecord[uiPCRIndex];
        if (!m_qPCRAreaTimeRecord.contains(strBatchInfo))
        {
            bResult = true;
        }
    }
    qDebug()<<"IsCurBatchPCRIndexValid:"<<bResult;
    return bResult;
}

QList<PCRResInfo> PCRResource::GetAllPCRRes()
{
    QList<PCRResInfo> allInfos;
    QList<QPair<int, int>> regionList;
    _GetRegionList(regionList);
    for (auto& region : regionList)
    {
        PCRResInfo resInfo;
        _InitPCRResInfo(&resInfo, region.first, region.second);
        resInfo.uiCapacity = PCR_SUB_AREA_SIZE2;
        resInfo.uiRemain = 0;// 全部用完
        resInfo.uiNextDoublePos = PCR_SUB_AREA_SIZE2;
        resInfo.uiNextSinglePos = PCR_SUB_AREA_SIZE2-1;
        resInfo.uiPCRST = PCRST_USING;
        QDFUN_LINE << resInfo.printInfo();
        allInfos << resInfo;
    }
    return allInfos;
}