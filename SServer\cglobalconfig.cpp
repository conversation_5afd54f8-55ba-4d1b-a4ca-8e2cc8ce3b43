﻿#include "cglobalconfig.h"
#include <QCoreApplication>
#include <QDir>
#include <QDebug>
#include <QNetworkInterface>
#include <QDateTime>
#include <QTimer>
#include <QCryptographicHash>
#include <QJsonDocument>
#include <QJsonObject>
#include <QString>

#if Q_OS_QML
#include "datacontrol/cmotorinfodb.h"
#endif
#include "datacontrol/ctiminginfodb.h"
#include "datacontrol/CSystemDB.h"
#include "cglobalconfig.h"
#include "publicfunction.h"
#include "./SystemConfig/SystemConfig.h"

CGlobalConfig::CGlobalConfig(QObject *parent) : QObject(parent)
{
    //默认日志保存路径
    m_strLogSaveDir = QCoreApplication::applicationDirPath() + "/data/";
    m_strMBMethodNameList << "start"<< "stop" << "pause" << "resume"
                          << "status" << "valve_fan_on" << "valve_fan_off"
                          << "sys_info" << "env_temp"<< "ht_info" << "read_motor_cmd" << "set_motor_cmd"
                          << "read_all_cmds" << "reset_all_cmds" << "delete_motor_cmd"
                          << "as_debug" << "mlog" << "mlog_req" << "mlog_data" << "mlog_end" << "mlog_info"
                          << "opt_byte" << "timing_file" << "timing_step" << "notify_flag" << "rtc" << "upgrade_req"
                          << "upgrade_data" << "upgrade_end" << "machine_reset" << "no_reset" << "reboot" << "notify"
                          << "power_off" << "heart_beat"<< "beep_flag"
                          << "beep_cfg" << "erase_flash" << "dev_id" << "fl_data" << "valve_on" << "valve_off"
                          << "pressure_on" << "pressure_stop" << "pressure_info" << "valve2_on" << "valve2_off";
    m_strMotorMCHKMethodNameList
            << "MCHK" << "SRST" << "ETRST" << "ETMTA" << "ETACL" << "ETMTB"
            << "ETBCL" << "ETMTC" << "ETMTD" << "ETBTC" << "ETMTF" << "OFRST"
            << "OFMTPFH" << "OFMTPSH" << "OFDET" << "FRST" << "FMTSH" << "PCRRST"
            << "PCRCLP" << "OFMTFH" << "FDET"
            << "TV1RST" << "YV1PR" << "TV2RST" << "YV2PR" << "PM1RST" << "PM1PR"
            << "PM2RST" << "PM2PR" << "PM3RST" << "PM3PR" << "NZRST" << "NZPR"
            << "PCRVRST" << "PCRVPR" << "MRST" << "MMIX" << "VRST" << "VMTA"
            << "VMTH" << "HRST" << "HMTH" << "HMTA" << "HMTM" << "PRST" << "PEXT"
            << "PINJ" << "PETMC" << "PITEC" << "PITDC" << "PFEXT";
    m_strMotorSCMPMethodNameList
            << "SCMP" << "GCMP" << "RLCMP" << "SDIR" << "GDIR" << "SPAM" << "GPAM"
            << "MOVE" << "GMSL" << "SMSL" << "RLPAM" << "GCID" << "OPB" << "CFFL"
            << "STAT" << "GXIO" << "RLCMP" << "ICMP" << "ECMP" << "IPAM" << "EPAM"
            << "SREGA" << "RREGA" << "CREGA" << "SRCHOP" << "RRCHOP" << "CRCHOP"
            << "CLEARPOS" << "ACTUALPOS" << "ENN" << "GTCOC" << "MSWST" << "MSWFLAG"
            << "GMCLK" << "SMCLK" << "RLCFG" << "MOTOR_COMPOSE";

    m_strPCRMethodNameList << "pcr_start" << "pcr_stop" << "tec_table_req" << "tec_table_data"
                           << "tec_table_end" << "pcr_info" << "set_info_interval" << "pcr_signal"
                           << "version" << "reboot" << "pcr_upgrade_req" << "pcr_upgrade_data"
                           << "pcr_upgrade_end" << "save_env" << "set_tpid" << "get_tpid" << "set_ipid"
                           << "get_ipid" << "set_vpid" << "get_vpid" << "set_treach" << "set_tcalib"
                           << "wait_signal";

    m_strFLLightMethodNameList << "FLLED"  << "FLADC" << "FLCST"
                               << "FLCDT" << "FLCSP" << "FLFREQ"
                               << "FLMST" << "FLMSP" << "FLMDT";

    m_strHTMethodNameList<<"HTST"<<"HTSP"<<"HTSET"<<"HTGET"<<"HTCALC"<<"ht_param" << "HTCHK";
    m_strUSMethodNameList << "USST" << "USSP" << "USPSET" << "USMSET" << "AMP"
                          << "GPWR" << "USREBOOT" << "USPARAM" << "USFTY" << "USVERSION"
                          << "USINFO";

    m_strVTMethodNameList << "DELAY" << "loop_st" << "loop" << "jump" << "communication";


    m_strClawNameList << "claw_InitClaw" << "claw_InitRotate" << "claw_EmerStop" << "claw_ClawTorque"
                      << "claw_ClawRunSpeed" << "claw_RunClawToPos" << "claw_SetRotateTorque"
                      << "claw_RotateRunSpeed" << "claw_RunRotateToPos" << "claw_GetClawInitState"
                      << "claw_GetRotateInitState" << "claw_GetClawRunState" << "claw_GetRotateRunState"
                      << "claw_GetClawCurPos" << "claw_GetRotateCurPos" << "claw_ClawInitDir" << "claw_RotateInitDir"
                      << "claw_SaveData" << "claw_SetDevId" << "claw_ReleaseOrEnableClaw" << "claw_ReleaseOrEnableRotate";

    m_strPipettingPumpNameList << "pump_init" << "pump_absorb" << "pump_drain" << "pump_ejectTip"
                               << "pump_LiquidDetect" << "pump_moveAbsPos" << "pump_relPosMoveUp" << "pump_relPosMoveDown"
                               << "pump_writeReg" << "pump_readReg" << "pump_queryState" << "pump_loopStart" << "pump_loopEnd"
                               << "pump_delay" << "pump_holdOn" << "pump_stop" << "pump_run" << "pump_unset" << "pump_restoreFac"
                               << "pump_saveParams";

    _InitMethodIDNameMap();
    _InitTimingIDMap();
    _InitComplexIDMap();
    m_bSoftUpdating = false;
    m_iMaxPayloadLength = 1024;
    m_bSwithcFan = false;
    m_bSwithcLight = false;
    m_bRunning = false;
    m_eMachineStatus = Machine_Fault;

    m_bSoftUpdating = false;
    m_iMotroMaxNumber = 0;

    m_iUserPower = -1;
#ifdef __arm__
    m_bIsWin = false;
#else
    m_bIsWin = true;
    m_iUserPower = 2;
#endif
    m_bIsReadAllMotor = false;

    m_iUpdateOnePieceLen = 1024;
    m_strBGRYList << "B" << "G" << "Y" << "R";
    m_fInterferenceValue = 0;
    m_fInterferenceValueGY = 0;
    m_bAging = true;
#ifdef Q_OS_ARM
    m_strUDiskDir = "/media/udisk0/";
#else
    m_strUDiskDir = "./";
#endif
    //
    // 使用结构体数组来初始化
    SCmdIDStruct sSCmdIDStruct[] = {
        {0x00, "Command"},
        {0x01, "Data"},
        {0x02, "Ack"},
        {0x03, "Reply"},
        {0x04, "Notification"},
        {0x05, "Bulletin"}
    };
    for (const auto& strCmdID : sSCmdIDStruct) {
        m_strCmdIDName[strCmdID.id] = strCmdID.strDescription;
    }
    SDestinationIDStruct sSDestinationIDStruct[] = {
        {0x00, "MiddleWare"},
        {0x01, "Function_manager_Ctrl"},
        {0x02, "Motor_Drive_Board_1"},
        {0x03, "Motor_Drive_Board_2"},
        {0x04, "Motor_Drive_Board_3"},
        {0x05, "Temp_Ctrl"},
        {0x06, ""},
        {0x07, "PCR MainCtrl"},
        {0x08, "PCR_Ctrl"},
        {0x09, "PCR_Ctrl_1"},
        {0x0A, "PCR_Ctrl_2"},
        {0x0B, "PCR_Ctrl_3"},
        {0x0C, "PCR_Ctrl_4"},
        {0x10, "Motor_Drive_Board_4"},        
        {0x11, "RFID_1"},
        {0x12, "RFID_2"},
        {0x13, "RFID_3"},
        {0x19, "Fluorence"},
        {0x1A, "GarbageStateCtrl "},
        {0x1B, "Scan_Board_1"},
        {0x1C, "Scan_Board_2"},
        {0x1D, "UpperHost"}
    };
    for (const auto& strDestinationID : sSDestinationIDStruct) {
        m_strDestinationIDName[strDestinationID.id] = strDestinationID.strDescription;
    }

    SystemConfig::getInstance().Load();//加载本地配置文件
}
CGlobalConfig &CGlobalConfig::getInstance()
{
    static CGlobalConfig cCGlobalConfig;
    return cCGlobalConfig;
}



QString CGlobalConfig::GetResourcesDir()
{
    QString strDir = QCoreApplication::applicationDirPath() + "/Resources/";
    return strDir;
}

QString CGlobalConfig::getCurrentDateTimeMin()
{
    QString strTime = QDateTime::currentDateTime().toString("yyMMdd-hhmmss");
    return strTime;
}

QString CGlobalConfig::getCurrentDateTime()
{
    QString strTime = QDateTime::currentDateTime().toString(gk_strDataTimeFormat);
    return strTime;
}

bool CGlobalConfig::hasUDisk()
{
    QString strDir = "/udisk/";
#ifndef __arm__
    strDir = "./";
#endif

    QDir qUdiskDir(strDir);
    bool bUdisk = qUdiskDir.exists();
    return bUdisk;
}

QString CGlobalConfig::getUDiskPath()
{
    QString strDir = "/udisk/";
#ifndef __arm__
    strDir = "./";
#endif
    return strDir;
}

bool CGlobalConfig::hasUDiskFile(QString strUDiskFile)
{
    bool bHas = QFile::exists(strUDiskFile);
    return bHas;
}

QString CGlobalConfig::getTimingInfoDBName()
{
    return m_strTimingInfoDBName;
}

// 输入格式yyyy-MM-dd hh:mm:ss
void CGlobalConfig::setSystemTime(QDateTime qTime)
{
    QString strTime = qTime.toString(gk_strDataTimeFormat);
    strTime = "date -s \"" + strTime  +"\"";
    system(strTime.toLatin1().data());
    qDebug() << "strTime " << strTime;
    //强制写入到CMOS
    system("hwclock -w");
}

QList<QVariant> CGlobalConfig::getSystemTime()
{
    QList<QVariant> strTimeList;
    strTimeList.push_back(QDate::currentDate().year());
    strTimeList.push_back(QDate::currentDate().month());
    strTimeList.push_back(QDate::currentDate().day());
    strTimeList.push_back(QTime::currentTime().hour());
    strTimeList.push_back(QTime::currentTime().minute());
    strTimeList.push_back(QTime::currentTime().second());
    return strTimeList;
}


QString CGlobalConfig::getRegisterItemValue(QString strIhold,
                                            QString strIrun, QString strIholddelay)
{
    quint8 q8IHOLD  = strIhold.toUInt();
    quint16 q16IRUN  = strIrun.toUInt();
    quint32 q32Left = strIholddelay.toUInt();
    qDebug() << "q8IHOLD " << q8IHOLD << q16IRUN << q32Left;
    //
    q8IHOLD = ((q8IHOLD << 4) >> 4);
    quint16 qRightValue = q8IHOLD;
    q16IRUN = (q16IRUN << 12)  >> 4;
    qRightValue = qRightValue | q16IRUN;
    q32Left = (q32Left << 28) >> 12;
    quint32 q32Value = qRightValue;
    q32Value = q32Value | q32Left;
    qDebug() << "q8IHOLD " << q8IHOLD << q16IRUN << q32Left << q32Value;
    QString strValue = "";
    if(q32Value > 0)
    {
        strValue = QString::number(q32Value);
    }
    return strValue;
}

QVariantList CGlobalConfig::getRegisterItemValueFromData(QString strValue)
{
    QVariantList strReturn;
    quint32 q32Value = strValue.toUInt();
    quint16 q16RightValue = q32Value;
    quint8 q8IHOLD = q16RightValue;
    q8IHOLD = (q8IHOLD << 4) >> 4;
    strReturn.push_back(QString::number(q8IHOLD));
    q16RightValue = (q16RightValue << 4) >> 12;
    quint8 q8IRUN = q16RightValue;
    strReturn.push_back(QString::number(q8IRUN));
    q32Value = (q32Value << 12) >> 28;
    quint8 q8IHOLDDELAY = q32Value;
    strReturn.push_back(QString::number(q8IHOLDDELAY));
    return strReturn;
}


QString CGlobalConfig::getChopconfItemValue(QVariantList strValueList)
{
    QList<quint32> q32ValueList;
    for(int i = 0; i < strValueList.count(); ++i)
    {
        q32ValueList.push_back(strValueList[i].toUInt());
    }
    QString str32Value = "";
    if(q32ValueList.count() >= 13)
    {
        quint32 qu32Value = q32ValueList[0]  | (q32ValueList[1] << 4) | (q32ValueList[2] << 7)
                | (q32ValueList[3] << 14) | (q32ValueList[4] << 15) | (q32ValueList[5] << 18) | (q32ValueList[6] << 19)
                | (q32ValueList[7] << 20) | (q32ValueList[8] << 24) | (q32ValueList[9] << 28) | (q32ValueList[10] << 29)
                | (q32ValueList[11] << 30) | (q32ValueList[12] << 31);

        qDebug() << "qu32Value " << qu32Value ;
        str32Value = QString::number(qu32Value);
    }
    return str32Value;
}

QVariantList CGlobalConfig::getChopconfItemValueFromData(QString strValue)
{
    QVariantList strReturn;
    quint32 uiValue = strValue.toUInt();
    quint32 qu8Value = uiValue  << 28 >> 28;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 25 >> 29;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 21 >> 28;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 17 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 15 >> 30;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 13 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 12 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 8 >> 28;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 4 >> 28;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 3 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 2 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  << 1 >> 31;
    strReturn.push_back(qu8Value);
    qu8Value = uiValue  >> 31;
    strReturn.push_back(qu8Value);
    return strReturn;
}

void CGlobalConfig::setRunning(bool bRunning)
{
    m_bRunning = bRunning;
}

bool CGlobalConfig::getRunning()
{
    return m_bRunning;
}

void CGlobalConfig::setMachineStatus(int eMachineStatus)
{
    m_eMachineStatus = (EMachineStatus)eMachineStatus;
    qDebug() << "set machine status " << m_eMachineStatus;
}

int CGlobalConfig::getMachineStatus()
{
    qDebug() << "get machine status " << m_eMachineStatus;
    return  m_eMachineStatus;
}

void CGlobalConfig::setSoftUpdating(bool bSoftUpdating)
{
    m_bSoftUpdating = bSoftUpdating;
}

bool CGlobalConfig::getSoftUpdating()
{
    return m_bSoftUpdating;
}

QString CGlobalConfig::getPlainAesWord([[maybe_unused]] QString strEncryptData)
{
    QString strPlainData;
    //    aesDecode(strEncryptData, strPlainData);
    //    qDebug() << "strPlainData:" << strPlainData;
    return strPlainData;
}

bool CGlobalConfig::getIsWin()
{
    return m_bIsWin;
}

bool CGlobalConfig::getIsReadAllMotor()
{
    return m_bIsReadAllMotor;
}


void CGlobalConfig::setIsReadAllMotor(bool bRead)
{
    m_bIsReadAllMotor = bRead;
}

int CGlobalConfig::getUserPower()
{
    return m_iUserPower;
}

void CGlobalConfig::setUserPower(int iPower)
{
    m_iUserPower = iPower;
}

void CGlobalConfig::setLogStandardOut(bool bOut)
{
    emit sigLogStandardOut(bOut);
}

void CGlobalConfig::setLanguageFlag(int iLanguage)
{
    m_iLanguageFlag = iLanguage;
    m_strFaultCodeFileName = "faultcode_ch.xlsx";
    m_strTimingInfoDBName = "TimingInfo_ch.db";
    switch (m_iLanguageFlag) {
    case 0:
    {
        m_strFaultCodeFileName =  "faultcode_ch.xlsx";
        m_strTimingInfoDBName = "TimingInfo_ch.db";
        break;
    }
    case 1:
    {
        m_strFaultCodeFileName = "faultcode_en.xlsx";
        m_strTimingInfoDBName = "TimingInfo_en.db";
        break;
    }
    default:
        break;
    }
}

int CGlobalConfig::getLanguageFlag()
{
    return m_iLanguageFlag;
}

void CGlobalConfig::delayMSec(int iMSecTime)
{
    QEventLoop loop;
    QTimer::singleShot(iMSecTime, &loop, SLOT(quit()));
    loop.exec();
}

bool CGlobalConfig::writeJsonMap(QString strParamsType, QString strKey, QString strValue)
{
    bool bWrite = _WriteJsonMap(QCoreApplication::applicationDirPath() + "/Resources/sn.json",
                                strParamsType, strKey, strValue);
    return bWrite;
}

QString CGlobalConfig::readJsonMap(QString strParamsType, QString strKey)
{
    QString strValue = _ReadJsonMap(QCoreApplication::applicationDirPath() + "/Resources/sn.json",
                                    strParamsType, strKey);
    return strValue;
}

void CGlobalConfig::shutdownSystem()
{
    system("shutdown -h now");
}

void CGlobalConfig::setUpdateOnePieceLen(int iLength)
{
    m_iUpdateOnePieceLen = iLength;
}

int CGlobalConfig::getUpdateOnePieceLen()
{
    return m_iUpdateOnePieceLen;
}

QString CGlobalConfig::getFTPFilePath()
{
    QString strFilePath = QCoreApplication::applicationDirPath() + "/FTP/";
    return strFilePath;
}

QString CGlobalConfig::getDBDir()
{
    QString strFilePath = QCoreApplication::applicationDirPath() + "/db/";
    return strFilePath;
}


QString CGlobalConfig::GetLogJsonFilePath()
{
    QString strLogJsonPath = QCoreApplication::applicationDirPath() + "/Resources/log.json";
    return strLogJsonPath;
}

QString CGlobalConfig::GetLogSaveDir()
{
    return m_strLogSaveDir;
}

bool CGlobalConfig::SetLogSaveDir(QString strDir)
{
    if(strDir.isEmpty())
        return false;

    QDir dir(strDir);
    if(!dir.exists())
        return false;

    //    m_strLogSaveDir = strDir;

    return true;
}

QStringList CGlobalConfig::GetLogDirList()
{
    QString strLogGlobalDir  = m_strLogSaveDir + "global";
    QString strLogErrorDir   = m_strLogSaveDir + "error";
    QString strLogFatalDir   = m_strLogSaveDir + "fatal";
    QString strLogWarningDir = m_strLogSaveDir + "warning";
    QString strLogInfoDir    = m_strLogSaveDir + "info";
    QString strSlaveLogDir   = m_strLogSaveDir + "slave";

    QStringList strLogDirList;
    strLogDirList.push_back(strLogGlobalDir);
    strLogDirList.push_back(strLogErrorDir);
    strLogDirList.push_back(strLogFatalDir);
    strLogDirList.push_back(strLogWarningDir);
    strLogDirList.push_back(strLogInfoDir);
    strLogDirList.push_back(strSlaveLogDir);

    return strLogDirList;
}

QString CGlobalConfig::GetVersionPath()
{

    return QCoreApplication::applicationDirPath() + "/version.txt";
}

QString CGlobalConfig::GetAutoVersionPath()
{
    QString strVersionPath = QCoreApplication::applicationDirPath() + "/Resources/version.json";
    return strVersionPath;
}

QString CGlobalConfig::GetMethodName(int eMethod)
{
    QString strMethod = m_strMethodIDNameMap.value(eMethod,"");
    return strMethod;
}

int CGlobalConfig::getMethodID(QString strMethodName)
{
    int iMethodID = m_strMethodIDNameMap.key(strMethodName);
    return iMethodID;
}

int CGlobalConfig::getCmdIDFromTimingComposeIndex(int iIndex)
{
    int iMethodID = m_strTimingID2MethodIDNameMap.value(iIndex);
    return iMethodID;
}

int CGlobalConfig::getCmdIDFromComplexOrderIndex(int iIndex)
{
    int iMethodID = m_strComplexID2MethodIDNameMap.value(iIndex);
    return iMethodID;
}

QString CGlobalConfig::getCmdIDFromProcessComplexOrderIndex(int iIndex)
{
    QString iMethodIDName = m_strProcessComplexID2MethodIDNameMap.value(iIndex);
    return iMethodIDName;
}

QString CGlobalConfig::getRealTimingContent(QString strTiming)
{
    qDebug() << m_strTimingID2MethodIDNameMap;
    QString strCmdTiming = "";
    QStringList strTimingList = strTiming.split(";");
    QString strCmdMethodID;
    int iCmdMethodID = 0;
    QStringList strCmdList;
    int iCmdListCount = 0;
    for(int i = 0; i < strTimingList.count(); ++i)
    {
        strCmdList = strTimingList[i].split(",");
        iCmdListCount = strCmdList.length();
        if(iCmdListCount > 0)
        {
            strCmdMethodID = strCmdList.at(0);
            iCmdMethodID = strCmdMethodID.replace("+", "").toInt();
            switch (iCmdMethodID)
            {
            case Method_custom_timing_ID:
                if(iCmdListCount >= 2)
                {
                    if(strCmdList[0].contains("+"))
                    {
                        strCmdList[1] += "+";
                    }
                    strCmdList.pop_front();             // 第二个是MethodID
                }
                break;
            case Method_MOTOR_COMPOSE:
                if(iCmdListCount >= 2)
                {//修改后是真实电机组合时序ID
                    QStringList strMotorList = strCmdList.at(1).split("_");
                    if(strMotorList.size() > 0)
                    {
                        QString strMotorID = strMotorList.at(0);
                        strCmdList[1] = strMotorID;
                    }
                }
                break;
            case Method_HTST:
                if(iCmdListCount >= 3)
                {
                    strCmdList[2] = QString::number((int)(strCmdList[2].toFloat() * 100));
                }
                break;
            case Method_jump:
            case Method_wait_signal:
                if(iCmdListCount >= 3)
                {
                    strCmdList[2] = QString::number((int)(strCmdList[2].toInt()));
                }
                break;
            default:
                break;
            }
        }
        //        strCmdTiming += "24," + QString::number(i) + ";";
        strCmdTiming += strCmdList.join(",");
        strCmdTiming += ";";
    }
    strCmdTiming = strCmdTiming.replace(";436,", ";");
    strCmdTiming.remove(strCmdTiming.length()-1, 1);// z最后一个;
    qDebug() << "getRealTimingContent " << strTiming
             << strCmdTiming;
    return strCmdTiming;
}

QString CGlobalConfig::getRealComplexContent(QString strTiming)
{
    qDebug() << m_strComplexID2MethodIDNameMap;// 暂时与getRealTimingContent相同
    QString strCmdTiming = "";
    QStringList strTimingList = strTiming.split(";");
    QString strCmdMethodID;
    int iCmdMethodID = 0;
    QStringList strCmdList;
    int iCmdListCount = 0;
    for(int i = 0; i < strTimingList.count(); ++i)
    {
        strCmdList = strTimingList[i].split(",");
        iCmdListCount = strCmdList.length();
        if(iCmdListCount > 0)
        {
            strCmdMethodID = strCmdList.at(0);
            iCmdMethodID = strCmdMethodID.replace("+", "").toInt();
            switch (iCmdMethodID)
            {
            case Method_custom_timing_ID:
                if(iCmdListCount >= 2)
                {
                    if(strCmdList[0].contains("+"))
                    {
                        strCmdList[1] += "+";
                    }
                    strCmdList.pop_front();             // 第二个是MethodID
                }
                break;
            case Method_MOTOR_COMPOSE:
                if(iCmdListCount >= 2)
                {//修改后是真实电机组合时序ID
                    QStringList strMotorList = strCmdList.at(1).split("_");
                    if(strMotorList.size() > 0)
                    {
                        QString strMotorID = strMotorList.at(0);
                        strCmdList[1] = strMotorID;
                    }
                }
                break;
            case Method_HTST:
                if(iCmdListCount >= 3)
                {
                    strCmdList[2] = QString::number((int)(strCmdList[2].toFloat() * 100));
                }
                break;
            case Method_jump:
            case Method_wait_signal:
                if(iCmdListCount >= 3)
                {
                    strCmdList[2] = QString::number((int)(strCmdList[2].toInt()));
                }
                break;
            default:
                break;
            }
        }
        //        strCmdTiming += "24," + QString::number(i) + ";";
        strCmdTiming += strCmdList.join(",");
        strCmdTiming += ";";
    }
    strCmdTiming = strCmdTiming.replace(";436,", ";");
    strCmdTiming.remove(strCmdTiming.length()-1, 1);// z最后一个;
    qDebug() << "getRealTimingContent " << strTiming
             << strCmdTiming;
    return strCmdTiming;
}

QString CGlobalConfig::getRealProcessComplexContent(QString strTiming)
{
    qDebug() << m_strProcessComplexID2MethodIDNameMap;// 暂时与getRealTimingContent相同
    QString strCmdTiming = "";
    QStringList strTimingList = strTiming.split(";");
    QString strCmdMethodID;
    [[maybe_unused]] int iCmdMethodID = 0;
    QStringList strCmdList;
    int iCmdListCount = 0;
    for(int i = 0; i < strTimingList.count(); ++i)
    {
        strCmdList = strTimingList[i].split(",");
        iCmdListCount = strCmdList.length();
        if(iCmdListCount > 2)
        {
            strCmdMethodID = strCmdList.at(2);
            iCmdMethodID = strCmdMethodID.replace("+", "").toInt();

        }
        //        strCmdTiming += "24," + QString::number(i) + ";";
        strCmdTiming += strCmdList.join(",");
        strCmdTiming += ";";
    }
    strCmdTiming = strCmdTiming.replace(";436,", ";");
    strCmdTiming.remove(strCmdTiming.length()-1, 1);// z最后一个;
    qDebug() << "getRealTimingContent " << strTiming
             << strCmdTiming;
    return strTiming;
}


void CGlobalConfig::AddUnitItemIP(int iUnitItemID, QString strIP)
{
    qDebug() << __func__ << iUnitItemID << strIP;
    m_qMachineIPInfoMap.insert(iUnitItemID, strIP);
}

QString CGlobalConfig::GetUnitItemIP(int iUnitItemID)
{
    QString strIP = m_qMachineIPInfoMap.value(iUnitItemID);
    qDebug() << __func__ << iUnitItemID << strIP;
    return strIP;
}

int CGlobalConfig::GetUnitItemIDFromIP(QString strIP)
{
    int iUnitItemID = m_qMachineIPInfoMap.key(strIP);
    qDebug() << __func__ << iUnitItemID << strIP;
    return iUnitItemID;
}

int CGlobalConfig::GetPayloadMaxLength()
{
    return m_iMaxPayloadLength;
}

void CGlobalConfig::SetPayloadMaxLength(int iMax)
{
    m_iMaxPayloadLength = iMax;
}

void CGlobalConfig::initNetworkAddress()
{
#ifdef Q_OS_LINUX
    system("udhcpc -n &");
#if 0 //used for static wlan ip config
    QString strServerIP = CSystemDB::getInstance().getStringValueFromKey("wifi_ip");
    if(!strServerIP.isEmpty())
    {
        strServerIP = "ifconfig wlan0 "
                + strServerIP
                + " netmask *************";
        system(strServerIP.toLocal8Bit());
        qDebug() << "set wifi ip " << strServerIP;
    }
    else
    {
        system("udhcpc -n &");
        qDebug() << "udhcpc -n ";
    }
#endif
#endif
}

void CGlobalConfig::recordRunLog([[maybe_unused]] QString strLog,[[maybe_unused]] RunLogLevel eRunLogLevel)
{

}


void CGlobalConfig::setTextFiledInfo(int x, int y, int height)
{
    emit sigTextFiledInfo(x,y,height);
}


QString CGlobalConfig::getTimingInfoDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/TimingInfo.db";
    return strDBDir;
}

Q_INVOKABLE QString CGlobalConfig::getTimingDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/Timing.db";
    return strDBDir;
}

QString CGlobalConfig::GetMotorInfoDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/MotorInfo.db";
    return strDBDir;
}

QString CGlobalConfig::GetHistoryDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/History.db";
    return strDBDir;
}
QString CGlobalConfig::GetSystemDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/System.db";
    return strDBDir;
}
QString CGlobalConfig::GetMotorDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/" + gk_strMotorDBName;
    return strDBDir;
}

QString CGlobalConfig::GetGraphDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/GraphData.db";
    return strDBDir;
}

QString CGlobalConfig::GetClientDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/client.db";
    return strDBDir;
}

QString CGlobalConfig::getProjectDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/project.db";
    return strDBDir;
}

QString CGlobalConfig::GetErrorCodeDBDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/db/errorCode.db";
    return strDBDir;
}

QString CGlobalConfig::GetKeyboardLibDir()
{
#ifdef Q_OS_WIN
    return QCoreApplication::applicationDirPath() + "/";
#endif

#ifdef __arm__
    return "/usr/lib/";
#endif

#ifdef Q_OS_LINUX
    return "/opt/Qt5.12.8/5.12.8/gcc_64/lib/";
#endif

    return "./";
}

QString CGlobalConfig::getFaultCodeXlsxPath()
{
    QString strXlsxPath = GetResourcesDir() + gk_strFaultCodeXlsxName;
    return strXlsxPath;
}
QString CGlobalConfig::getServerIPAddress()
{
    QList<QNetworkInterface> interfaceList = QNetworkInterface::allInterfaces();

    QString strIP = "";
    int iIndex = 0;
    foreach(QNetworkInterface interfaceItem, interfaceList)
    {
        if(interfaceItem.flags().testFlag(QNetworkInterface::IsUp)
                &&interfaceItem.flags().testFlag(QNetworkInterface::IsRunning)
                &&interfaceItem.flags().testFlag(QNetworkInterface::CanBroadcast)
                &&interfaceItem.flags().testFlag(QNetworkInterface::CanMulticast)
                &&!interfaceItem.flags().testFlag(QNetworkInterface::IsLoopBack)
                &&!interfaceItem.humanReadableName().contains("VMware"))
        {
            QList<QNetworkAddressEntry> addressEntryList=interfaceItem.addressEntries();
            foreach(QNetworkAddressEntry addressEntryItem, addressEntryList)
            {
                if(addressEntryItem.ip().protocol()==QAbstractSocket::IPv4Protocol)
                {
                    qDebug()<<"------------------------------------------------------------";
                    qDebug()<<"Adapter Name:"<<interfaceItem.name();
                    qDebug()<<"Adapter Address:"<<interfaceItem.hardwareAddress();
                    qDebug()<<"IP Address:"<<addressEntryItem.ip().toString();
                    qDebug()<<"IP Mask:"<<addressEntryItem.netmask().toString();
                    qDebug()<< "broadcast"<<addressEntryItem.broadcast().toString();
                    iIndex++;
                    strIP += QString::number(iIndex) + "：";
                    strIP += addressEntryItem.ip().toString();
                    strIP += "//\r\n  ";
                }
            }
        }
    }
    return strIP;
}


QStringList CGlobalConfig::getMotorComposeIDNameListFromDB()
{// ID_Name
#if Q_OS_QML
    m_strMotorComposeIDNameList = CMotorInfoDB::getInstance().getAllMotorComposeCMDIDAndCMDNames();
#endif
    return m_strMotorComposeIDNameList;
}

int CGlobalConfig::GetMotorComposeIndex(QString strIDName)
{
    return m_strMotorComposeIDNameList.indexOf(strIDName);
}

int CGlobalConfig::GetListIndexFromMotorComposeID(int id)
{
    QString searchStr = QString::number(id);
    for (int i = 0; i < m_strMotorComposeIDNameList.size(); ++i) {
        QStringList splitList = m_strMotorComposeIDNameList[i].split('_');
        if(splitList.size() > 0)
        {
            if (splitList[0] == searchStr)
            {
                return i;
                break;
            }
        }
    }
    return 0;
}

QString CGlobalConfig::GetMotorComposeIDFromListIndex(int index)
{
    if(index >= 0 && index < m_strMotorComposeIDNameList.size())
    {
        QStringList splitList = m_strMotorComposeIDNameList.at(index).split('_');
        if(splitList.size() > 0)
        {
            return splitList[0];
        }
    }
    return "";
}

void CGlobalConfig::setInterferenceValue(qreal fValue)
{
    m_fInterferenceValue = fValue;
}

qreal CGlobalConfig::getInterferenceValue()
{
    return m_fInterferenceValue;
}

void CGlobalConfig::setInterferenceValueGY(qreal fValue)
{
    m_fInterferenceValueGY = fValue;
}

qreal CGlobalConfig::getInterferenceValueGY()
{
    return m_fInterferenceValueGY;
}


void CGlobalConfig::_InitMethodIDNameMap()
{
    for(int i = 0; i != m_strMBMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_start, m_strMBMethodNameList[i]);
    }
    for(int i = 0; i != m_strMotorMCHKMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_MDB1_MCHK, m_strMotorMCHKMethodNameList[i]);
    }
    m_strMethodIDNameMap.insert(Method_RIGHT, "RIGHT");
    m_strMethodIDNameMap.insert(Method_GAP, "GAP");
    m_strMethodIDNameMap.insert(Method_SAP, "SAP");
    for(int i = 0; i != m_strMotorSCMPMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_SCMP, m_strMotorSCMPMethodNameList[i]);
    }

    for(int i=0; i<m_strPCRMethodNameList.size(); i++)
    {
        if(i < 8)
            m_strMethodIDNameMap.insert(i+Method_pcr_start, m_strPCRMethodNameList[i]);
        else
            m_strMethodIDNameMap.insert(i+Method_pcr_version-8, m_strPCRMethodNameList[i]);
    }

    for(int i=0; i<m_strFLLightMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_FLLED, m_strFLLightMethodNameList[i]);
    }
    for(int i=0; i<m_strHTMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_HTST, m_strHTMethodNameList[i]);
    }
    for(int i=0; i<m_strUSMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_US_USST, m_strUSMethodNameList[i]);
    }
    for(int i = 0; i < m_strVTMethodNameList.size(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_DELAY, m_strVTMethodNameList[i]);
    }
    for(int i=0; i<m_strClawNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_claw_InitClaw, m_strClawNameList[i]);
    }
    for(int i=0; i<m_strPipettingPumpNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_pump_init, m_strPipettingPumpNameList[i]);
    }
}

int CGlobalConfig::GetTimingIndexByCmdID(int iCmdID)
{
    return m_strTimingID2MethodIDNameMap.key(iCmdID,0);
}

int CGlobalConfig::getComplexIndexByCmdID(int iCmdID)
{
    return m_strComplexID2MethodIDNameMap.key(iCmdID,0);
}

int CGlobalConfig::GetProcessComplexIndexByCmdID(QString iCmdIDName)
{
    return m_strProcessComplexID2MethodIDNameMap.key(iCmdIDName,0);
}

void CGlobalConfig::setMotorNumber(int iMotorNumber)
{
    m_intMotorNumberList.push_back(iMotorNumber);
    if(iMotorNumber > m_iMotroMaxNumber)
    {
        m_iMotroMaxNumber = iMotorNumber;
    }
}

int CGlobalConfig::getMotorNumber(int iMotorBoardIndex)
{
    int imotorBoardIndex = iMotorBoardIndex-2;
    if(imotorBoardIndex >=0 && imotorBoardIndex <= 2)
        return m_intMotorNumberList[iMotorBoardIndex];
    return 0;
}

QString CGlobalConfig::getTECChartDataDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/chartData/TEC";
    return strDBDir;
}

QString CGlobalConfig::getContinuLightingChartDataDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/chartData/contFluoSampling";
    return strDBDir;
}

QString CGlobalConfig::getSportsLightingDataDir()
{
    QString strDBDir = QCoreApplication::applicationDirPath() + "/chartData/motionFluoSampling";
    return strDBDir;
}

void CGlobalConfig::_InitTimingIDMap()
{
    int index = 0;
    // 自定义
    m_strTimingID2MethodIDNameMap.insert(index++, Method_custom_timing_ID);
    for(int i = Method_DELAY; i <= Method_jump; i++)
        m_strTimingID2MethodIDNameMap.insert(index++, i);
    // 电机
    m_strTimingID2MethodIDNameMap.insert(index++, Method_MOTOR_COMPOSE);
    // 爪子
    for(int i = Method_claw_InitClaw; i <= Method_claw_RunRotateToPos; i++)
        m_strTimingID2MethodIDNameMap.insert(index++, i);
    // 移液泵
    for(int i = Method_pump_init; i <= Method_pump_moveAbsPos; i++)
        m_strTimingID2MethodIDNameMap.insert(index++, i);
    for(int i = Method_pump_writeReg; i <= Method_pump_run; i++)
        m_strTimingID2MethodIDNameMap.insert(index++, i);
    m_strTimingID2MethodIDNameMap.insert(index++,Method_pump_queryLiquidDetectResult);
}

void CGlobalConfig::_InitComplexIDMap()
{
    int index = 0;
    // 自定义
    m_strComplexID2MethodIDNameMap.insert(index++, Method_custom_timing_ID);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_comp_cmd_st);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_comp_cmd_exec_cond);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_DELAY);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_loop_st);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_loop);
    m_strComplexID2MethodIDNameMap.insert(index++, Method_jump);
}

void CGlobalConfig::updateProcessComplexIDMap()
{
    int index = 0;
    m_strProcessComplexID2MethodIDNameMap.clear();
    QStringList strComplexIDNameList = CTimingInfoDB::getInstance().getAllComplexTimingNames();
    for(auto strIDName : strComplexIDNameList)
    {
        m_strProcessComplexID2MethodIDNameMap.insert(index++, strIDName);
    }
    emit sigUpdateComplexIDNameList(strComplexIDNameList);
}

void CGlobalConfig::printMessageInfo(QByteArray &qMessage, const QString &strTitle)
{
    if(qMessage.count() >= gk_iFrameLengthNotData)// 帧长
    {// 只做MethodID初步解析
        m_pFramePos = qMessage.data() + gk_iMethodIDPos;//指令执行ID
        m_iMethodID = GetByte2Int(m_pFramePos);
        m_iCmdID  = *((quint8*)qMessage.data() + gk_iCmdIDPos);
        m_iDestinationID = *((quint8*)qMessage.data() + gk_iDestinationIDPos);//指令执行ID
        m_iSourceID  = *((quint8*)qMessage.data() + gk_iSourceIDPos);
        m_iSync  = *((quint8*)qMessage.data() + gk_iSyncPos);
        m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
        m_pFramePos = qMessage.data() + gk_iSeqPos;
        m_iResult  = *((quint8*)qMessage.data() + gk_iResultPos);
        m_iSeqNumber =  GetByte2Int(m_pFramePos);
        if(m_iMethodID != Method_heart_beat)
        {
            QString strLogMessage = QString("%1 m_iCmdID: %2 m_iMethodID: %3 "
                                            "m_iDestinationID: %4 m_iSourceID: %5 iSeqNumber:%6 m_iSync: %7 "
                                            "m_iResult: %8 ")
                    .arg(strTitle).arg(m_strCmdIDName[m_iCmdID]).arg(m_iMethodID).arg(m_strDestinationIDName[m_iDestinationID])
                    .arg(m_strDestinationIDName[m_iSourceID]).arg(m_iSeqNumber).arg(m_iSync).arg(m_iResult);
#ifndef ShortOutPutLog
            // qInfo() << strLogMessage << qMessage << qMessage.toHex(':').toUpper();
            qInfo() << strTitle.leftJustified(22, ' ') << qMessage.toHex(':').toUpper();
#endif
        }
    }
    else
    {
        qInfo() << strTitle.leftJustified(22, ' ') << qMessage.toHex(':').toUpper() << qMessage.count();
    }
}

bool CGlobalConfig::getAgingMode()
{
    m_bAging = CSystemDB::getInstance().getIntValueFromKey("AgingMode");
    return m_bAging;
}

void CGlobalConfig::setAgingMode(bool bAging)
{
    m_bAging = bAging;
}

bool CGlobalConfig::getLiquidDetect()
{
    return CSystemDB::getInstance().getIntValueFromKey("LiquidLevelDetection");
}

bool CGlobalConfig::getSampleGripperDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("SampleGripperDetectFlag");
}

bool CGlobalConfig::getPumpDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("PumpDetectFlag");
}

bool CGlobalConfig::getPCRGripperDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("PCRGripperDetectFlag");
}

bool CGlobalConfig::getSampleTubeExistFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("SampleTubeExistFlag");
}

bool CGlobalConfig::getPCRTubeExistFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("PCRTubeExistFlag");
}

bool CGlobalConfig::getMBoard1OptDetectFlag()
{
     return CSystemDB::getInstance().getIntValueFromKey("MBoard1OptDetectFlag");
}

bool CGlobalConfig::getMBoard2OptDetectFlag()
{
     return CSystemDB::getInstance().getIntValueFromKey("MBoard2OptDetectFlag");
}

bool CGlobalConfig::getMBoard3OptDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("MBoard3OptDetectFlag");
}

bool CGlobalConfig::getPCRAreaCapDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("PCRAreaCapDetectFlag");
}

bool CGlobalConfig::getCentrifugeOptDetectFlag()
{
     return CSystemDB::getInstance().getIntValueFromKey("CentrifugeOptDetectFlag");
}

bool CGlobalConfig::getMagnetTubeDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("MagnetTubeDetectFlag");
}

bool CGlobalConfig::getBlockedNeedleDetectFlag()
{
    return CSystemDB::getInstance().getIntValueFromKey("AirSuctionOrNeedleBlockageDetectFlag");
}

bool CGlobalConfig::_WriteJsonMap(QString strFileName, QString strParamsType, QString strKey, QString strValue)
{
    QFile qConfigfile(strFileName);
    if(!qConfigfile.open(QIODevice::ReadWrite))
    {
        qDebug() << "Cannot open file for writing: ";
        return false;
    }
    QByteArray qJsonByteArray = qConfigfile.readAll();
    QString strFile = QString::fromLocal8Bit(qJsonByteArray.data());
    /*配置文件为空，直接按json格式写入*/
    if(qJsonByteArray.isEmpty())
    {
        QVariantMap qConfigureVariantMap;
        QMap<QString,QVariant> qParamsMap;
        qParamsMap.insert(strKey, strValue);
        QVariant qConfiguretQVariant = static_cast <QVariant> (qParamsMap);
        qConfigureVariantMap.insert(strParamsType,qConfiguretQVariant);
        // 转化为 JSON 文档
        QJsonDocument qJsonDoucment = QJsonDocument::fromVariant(static_cast <QVariant>(qConfigureVariantMap));
        QByteArray qJosnByteArray = qJsonDoucment.toJson(QJsonDocument::Compact);
        QString strJson(qJosnByteArray);
        QTextStream qTextStreamOut(&qConfigfile);
        //清空文件
        qConfigfile.resize(0);
        //写入配置文件
        qTextStreamOut << strJson;
    }
    else
    {
        //配置文件不为空
        QJsonParseError sJsonError;
        // 转化为 JSON 文档
        QJsonDocument qJsonDoucment = QJsonDocument::fromJson(strFile.toStdString().data(), &sJsonError);
        if (!qJsonDoucment.isNull() && (sJsonError.error == QJsonParseError::NoError))
        {  // 解析未发生错误
            if (qJsonDoucment.isObject())
            { // JSON 文档为对象
                QJsonObject qJosnObject = qJsonDoucment.object();  // 转化为对象
                QVariantMap qConfigureVariantMap = qJosnObject.toVariantMap(); // json文件全部数据
                QVariant qParamsTypeParamsVariant;
                QMap<QString,QVariant> qParamsTypeParamsMap;     //存放新的参数
                if(qConfigureVariantMap.contains(strParamsType)) //存在该配置项
                {
                    //读出原配置项内容,不存在为空
                    qParamsTypeParamsVariant = qConfigureVariantMap.value(strParamsType);
                    qParamsTypeParamsMap = qParamsTypeParamsVariant.toMap();
                }
                qParamsTypeParamsMap.insert(strKey, strValue);
                QVariant qConfiguretQVariant = static_cast <QVariant>(qParamsTypeParamsMap);
                //重新插入QVariantMap
                qConfigureVariantMap.insert(strParamsType,qConfiguretQVariant);
                // 转化为 JSON 文档
                qJsonDoucment = QJsonDocument::fromVariant(static_cast <QVariant>(qConfigureVariantMap));
                QByteArray qJosnByteArray = qJsonDoucment.toJson(QJsonDocument::Compact);
                QString strJson(qJosnByteArray);
                QTextStream qTextStreamOut(&qConfigfile);
                //清空文件
                qConfigfile.resize(0);
                //写入配置文件
                qTextStreamOut << strJson;
            }
        }
    }
    qConfigfile.flush();
    qConfigfile.close();
    return true;
}

QString CGlobalConfig::_ReadJsonMap(QString strFileName, QString strParamsType, QString strKey)
{
    QString strValue = "";
    QFile qConfigfile(strFileName);
    if(!qConfigfile.open(QIODevice::ReadWrite))
    {
        return strValue;
    }
    QByteArray qJosnByteArray = qConfigfile.readAll();
    QString strFile = QString::fromLocal8Bit(qJosnByteArray.data());
    QJsonParseError qJsonError;
    QJsonDocument qJsonDocument = QJsonDocument::fromJson(strFile.toStdString().data(), &qJsonError);  // 转化为 JSON 文档
    if (!qJsonDocument.isNull() && (qJsonError.error == QJsonParseError::NoError))
    {  // 解析未发生错误
        if (qJsonDocument.isObject())
        { // JSON 文档为对象
            QJsonObject qJosnObject = qJsonDocument.object();  // 转化为对象
            if (qJosnObject.contains(strParamsType))
            {  // 包含指定的 key
                // 获取指定 key 对应的 value
                QMap<QString, QVariant> qJsonValueMap = qJosnObject.value(strParamsType).toVariant().toMap();
                if(!qJsonValueMap.isEmpty())
                {
                    strValue = qJsonValueMap.value(strKey).toString();
                    qConfigfile.close();
                    return strValue;
                }
            }
        }
    }
    qConfigfile.flush();
    qConfigfile.close();
    return strValue;
}

inline QString GetTimingStep(int iStep, EnumTimingAction eAction)
{
    QString strTiming = QString::number(Method_timing_step) + ","
            + QString::number(iStep) + "," + QString::number(eAction) + ";";
    return strTiming;
}


QString CGlobalConfig::getUDiskDir()
{
    return m_strUDiskDir;
}

QString CGlobalConfig::getUDiskAppFileUpdatePath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strAppName;
}

QString CGlobalConfig::getUDiskMiddleAppFileUpdatePath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strMiddleFileName;
}

QString CGlobalConfig::getUDiskAlgorithmFileUpdatePath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strAlgorithmName;
}

QString CGlobalConfig::getUDiskMyShFileUpdatePath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strMyShName;
}

QString CGlobalConfig::getUDiskSlaveFileUpdatePath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strSlaveName;
}

QString CGlobalConfig::getUDiskPCRFileUpdatePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strPCRMainUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strPCRMainUpgradeName;
    }
}

QString CGlobalConfig::getUDiskTECUpgradePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strTECUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strTECUpgradeName;
    }
}

QString CGlobalConfig::getUDiskFLUpgradePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strFLUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strFLUpgradeName;
    }
}

QString CGlobalConfig::getUDiskRFIDUpgradePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strRFIDUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strRFIDUpgradeName;
    }
}


QString CGlobalConfig::getUDiskBoardFunctionUpdatePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strBoardFunctionUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strBoardFunctionUpgradeName;
    }
}

QString CGlobalConfig::getUpdateBoardVersionCheckPath()
{
    return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strBoardVersionCheckName;
}

QString CGlobalConfig::getUDiskBoardMotorUpdatePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strBoardMotorUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strBoardMotorUpgradeName;
    }
}

QString CGlobalConfig::getUDiskBoardPowerUpdatePath(int iExtractPath)
{
    if(iExtractPath==0)
    {
        return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strBoardPowerUpgradeName;
    }
    else
    {
        return  QCoreApplication::applicationDirPath()+"/"+gk_strUDiskUpdateTempDir+"/"+gk_strBoardPowerUpgradeName;
    }
}

QString CGlobalConfig::getUDiskAutoUpgradeZipPath()
{
    return m_strUDiskDir + gk_strUDiskUpdateDir + gk_strAtuoUpgradeZipName;
}

bool CGlobalConfig::isDirExist(QString strDirPath)
{
    QDir dir(strDirPath);
    return dir.exists();
}

bool CGlobalConfig::isFileExist(QString strFilePath)
{
    return QFile::exists(strFilePath);
}

bool CGlobalConfig::copyFile(QString strOldPath, QString strNewPath)
{
    if(QFile::exists(strNewPath))
        QFile::remove(strNewPath);

    return  QFile::copy(strOldPath,strNewPath);
}

void CGlobalConfig::setMotorMaxNumber(int iNumber)
{
    m_iMotroMaxNumber = iNumber;
}

int CGlobalConfig::getMotorMaxNumber()
{
    return m_iMotroMaxNumber;
}
