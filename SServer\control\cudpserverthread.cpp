#include "cudpserverthread.h"
#include "control/coperationunit.h"
#include "cglobalconfig.h"
#include "publicconfig.h"
#include "ccommunicationobject.h"

#include <unistd.h>
CUdpServerThread::CUdpServerThread(int iPort, QHostAddress strHostIP,
                                   QString strHostMac, QObject *parent)
    : QObject(parent)
    , m_qHostAddress(strHostIP)
    , m_iPort(iPort)
    , m_strHostMacAddress(strHostMac)
{    
    m_bThreadExit = true;
    m_strConnected = "0";
    m_pBroadcastTimer = new QTimer();
    m_pBroadcastTimer->setSingleShot(false);
    m_pHeartBeatTimer = new QTimer();
    m_pHeartBeatTimer->setSingleShot(false);
    m_pThread = new QThread();
    m_pUdpSocket = new QUdpSocket();

    m_pUdpSocket->moveToThread(m_pThread);
    m_pBroadcastTimer->moveToThread(m_pThread);
    m_pHeartBeatTimer->moveToThread(m_pThread);
    this->moveToThread(m_pThread);

    connect(this, &CUdpServerThread::sigInitServer,
            this, &CUdpServerThread::_slotInitServer, Qt::QueuedConnection);
    connect(m_pBroadcastTimer,SIGNAL(timeout()),this,SLOT(_slotBroadcast()));
    connect(m_pHeartBeatTimer,SIGNAL(timeout()),this,SLOT(_slotHeartBeatTimer()));

    m_pThread->start();
    emit sigInitServer();

}

CUdpServerThread::~CUdpServerThread()
{
    m_bThreadExit = false;
}

void CUdpServerThread::slotReInitServer(QHostAddress qHostAddress, QString strHostMac)
{
    m_qHostAddress = qHostAddress;
    m_strHostMacAddress = strHostMac;
    _initUdpInfo();
    qDebug() << __FUNCTION__ << m_strSendMessage;
}

void CUdpServerThread::slotServerConnect(bool bConnect)
{
    m_strConnected = bConnect ? "1" : "0";
    _updateSendMessag();
}

void CUdpServerThread::_slotInitServer()
{
    _initUdpInfo();
}

void CUdpServerThread::_slotBroadcast()
{
//        qDebug() << __FUNCTION__ << m_strSendMessage;
    m_pUdpSocket->writeDatagram(m_strSendMessage.toLocal8Bit().data(),
                                m_strSendMessage.size(), QHostAddress::Broadcast, 30079);
}

void CUdpServerThread::_slotHeartBeatTimer()
{
//    qDebug() << __FUNCTION__ << m_strSendMessage;
    if(CCommunicationObject::getInstance().getClientConnect())
    {// 连接状态才会发送
        COperationUnit::getInstance().sendBulletin(Method_heart_beat, Machine_UpperHost, 0x00);
    }

    if(CCommunicationObject::getInstance().getPCRConnect())
    {// 连接状态才会发送
        COperationUnit::getInstance().sendBulletin(Method_heart_beat, Machine_PCR_MainCtrl, 0x00);
    }
}

void CUdpServerThread::_initUdpInfo()
{
    _updateSendMessag();
    if(!m_qHostAddress.isNull())
    {
        m_pUdpSocket->bind(QHostAddress::Any, 30079, QUdpSocket::ShareAddress);
        if(!m_pBroadcastTimer->isActive())
        {
            m_pBroadcastTimer->start(1000); // 每秒广播一次
        }
        if(!m_pHeartBeatTimer->isActive())
        {
            m_pHeartBeatTimer->start(3000); //
        }
    }
}

void CUdpServerThread::_updateSendMessag()
{
    if(m_iPort == 30080)
    {
        m_strSendMessage = "@MServer_" ;
    }
    else
    {
        m_strSendMessage = "@MPCR_" ;
    }
    m_strSendMessage += m_qHostAddress.toString() + "_"
            + m_strHostMacAddress + "_"
            + m_strConnected;
}


