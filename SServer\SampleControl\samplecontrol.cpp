#include "samplecontrol.h"
#include <QDebug>
#include <QString>
#include "publicconfig.h"
#include "consumables/cstrip.h"
#include "datacontrol/cprojectdb.h"
#include "datacontrol/ctiminginfodb.h"
#include "consumables/pcrresource.h"
#include <QRegularExpression>
#include"./SystemConfig/SystemConfig.h"
#include "error/cerrornotify.h"
#include "consumables/cstrip.h"
#include <algorithm>
#include <iterator>

SampleControl &SampleControl::getInstance()
{
    static SampleControl instance;
    return instance;
}

void SampleControl::SetCatchType(quint8 uiCatchType)
{
    m_uiCatchType = uiCatchType;
}

void SampleControl::SetCurBatchSamples(QString strCurBatch)
{
    m_strCurBatchSamples = strCurBatch;
}

bool SampleControl::AddCurBatch()
{
    return  AddBatch(m_strCurBatchSamples);
}

bool SampleControl::CheckSampleIsStandard(const QString &strSampleNo,QString& strStandardProject)
{
    QRegularExpression regex("^WFIS\\+");//WFIS+M10022B221234  WFIS+M10021234
    bool bMatch = regex.match(strSampleNo).hasMatch();
    if (bMatch)
    {
        QStringList sampleFields = strSampleNo.split("+");
        if(sampleFields.size() == 2)
        {
            QString batchInfo = sampleFields[1];
            strStandardProject = batchInfo;
            strStandardProject.chop(5);//删除最后5位(年月日)           
        }
    }
    qDebug()<<"SampleControl::CheckSampleIsStandard"<<bMatch<<strSampleNo<<strStandardProject;
    return bMatch;
}

void SampleControl::ResetCurBatchStatus()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    m_sCurBatch.iCurOpRowIndex = -1;
    m_sCurBatch.iCurOpColumnIndex = -1; 
}

quint8 SampleControl::GetNextWaitProjectStripIndex()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    quint8 uiWaitProjectStripIndex = 0;
    QVector<SampleInfo> infoVect =  GetNextWaitCatchAndMixSampleInfos();
    for (auto &info : infoVect)
    {    
        for (auto &projectInfo : info.qProjInfoVect)
        {
            QList<quint8> qStripIndex;
            GetProjectStripIndex(projectInfo.strProjID,qStripIndex); 
            uiWaitProjectStripIndex += qStripIndex.size();              
        }
    }  
    qDebug()<<"GetNextWaitProjectStripIndex:"<<infoVect.size()<<uiWaitProjectStripIndex;   
    return uiWaitProjectStripIndex;
}

bool SampleControl::GetNextWaitSampleInfo(QString &strProjectName, quint8 &uiStripIndex, quint8 &uiColumnIndex)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    bool bSearch = false;
    QVector<SampleInfo> infoVect =  GetNextWaitCatchAndMixSampleInfos();
    for (auto &info : infoVect)
    {
        // for (auto &projectInfo : info.qProjInfoVect)// 上位机下发样本信息中，内标没有对应的项目
        {
            QList<quint8> qStripIndex;
            GetProjectStripIndex(info.strStandardProject,qStripIndex);               
            for (auto &stripIndex : qStripIndex)
            {
                strProjectName = info.strStandardProject;
                uiStripIndex = stripIndex;
                uiColumnIndex = info.uiColumnIndex;
                bSearch = true;
                break;             
            }
            qDebug()<<"GetNextWaitSampleInfo strStandardProject:"<<info.strStandardProject<<qStripIndex;
        }
        qDebug()<<"GetNextWaitSampleInfo qProjInfoVect:"<<info.qProjInfoVect.size();
    }
    qDebug()<<"GetNextWaitSampleInfo:"<<infoVect.size()<<strProjectName<<uiStripIndex<<uiColumnIndex; 
    return bSearch;  
}

void SampleControl::SetCurBatchSystemBuildSize(quint8 uiSize)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    m_uiCurBatchSystemBuildSize = uiSize;
    qDebug()<<"SetCurBatchSystemBuildSize: "<<m_uiCurBatchSystemBuildSize;
}

quint16 SampleControl::GetCurBatchSystemBuildSize()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    auto& qSystemBuildST = m_qSystemBuildSTMap[SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE];
    int totalCount = 0;
    for (const auto& vec : qSystemBuildST) {
        totalCount += vec.size();
    }    
    qDebug()<<"GetCurBatchSystemBuildSize: "<<totalCount;
    return totalCount;
}

void SampleControl::SetCalcSystemBuildInfo(QQueue<QVector<SystemBuildInfo>> &qCalcSystemBuildInfo)
{
    if (qCalcSystemBuildInfo.size() == 0)
    {
        qDebug()<<"SetCalcSystemBuildInfo qCalcSystemBuildInfo is empty";
        return;
    }
    
    QMutexLocker qLocker(&m_qSampleSTMutex);
    // 获取准备转移的样本信息
    auto itor = m_qSystemBuildSTMap.find(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE);
    if(itor != m_qSystemBuildSTMap.end())
    {
        m_qSystemBuildSTMap[SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE].clear();// 清空并重新赋值
        m_qSystemBuildSTMap[SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE] = qCalcSystemBuildInfo;
    }    
    qDebug()<<"SetCalcSystemBuildInfo: "<<qCalcSystemBuildInfo.size();
}

SampleControl::SampleControl()
{
    m_iWriteIndex = 0;
    m_iReadIndex = 0;
    m_iNextWriteIndex = 0;
    m_iCurrentWriteIndex = 0;
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    m_uiCatchType = DEVICE_CATCH_TYPE ;
    m_sCurBatch.bExist = false;

    BatchInfo emptyBatch = {};
    for(int i=0;i<BATCH_MAX_SIZE;i++)
        m_sBatchInfoList[i] = emptyBatch;
    m_bCurOpSingleRightSample = false;
}

SampleControl::~SampleControl()
{

}

bool SampleControl::AddBatch(QString strBatchInfo)
{
    qDebug()<<"SampleControl::AddBatch"<<strBatchInfo;
    bool bResult = false;
    m_iStandardSampleUsedCount = 0;
    m_iCurrentWriteIndex = m_iWriteIndex.load();
    m_iNextWriteIndex = (m_iCurrentWriteIndex+ 1) % BATCH_MAX_SIZE;
    QDFUN_LINE << "m_iReadIndex" << m_iReadIndex.load() << "m_iWriteIndex" << m_iWriteIndex.load() << "m_iNextWriteIndex" << m_iNextWriteIndex;
    if (m_iNextWriteIndex== m_iReadIndex.load())
    {
        // 队列已满，无法添加新的批次信息
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Test_Process, FT_TestQueueFull, "Test queue is full.");
        qDebug() << "Test queue is full.";
        return bResult;
    }
    BatchInfo sBatchInfo;
    if(ParseBatchInfo(strBatchInfo, sBatchInfo))
    {
        m_qBatchNo.insert(sBatchInfo.strBatchNo);
        // sBatchInfo.strBatchNo = m_iCurrentWriteIndex;
        m_sBatchInfoList[m_iCurrentWriteIndex] = sBatchInfo;
        m_iWriteIndex.store(m_iNextWriteIndex);
        bResult = true;
        QMutexLocker locker(&m_qBatchMutex);
        if(!m_sCurBatch.bExist)
        {
            _GetNextWaitExecBatch(false);
        }
        else
        {
            CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Test_Process, FT_CurBatchExist, "CurBatch exist.");
            qDebug() << "CurBatch exist." << m_sCurBatch.strBatchNo << sBatchInfo.strBatchNo;
        }
    }
    else
    {
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Test_Process, FT_ParseBatchInfoFailed, "ParseBatchInfo failed.");
        qDebug() << "ParseBatchInfo failed.";
    }

    return bResult;
}

void SampleControl::AddTstSamples(BatchInfo& sBatchInfo)
{
    sBatchInfo.bExist = true;
    sBatchInfo.iCurOpRowIndex = -1;
    sBatchInfo.iCurOpColumnIndex = -1;
    sBatchInfo.strExtract = "";
    sBatchInfo.iSampleSize = 0;
    //添加空样本
    for(int i=0;i<SAMPLE_ROW_SIZE;i++)
    {
        for(int j= 0; j<SAMPLE_COLUMN_SIZE;j++)
        {
            SampleInfo sEmptyInfo;
            sEmptyInfo.bExist = false;
            sEmptyInfo.bIsCaped = false;
            sEmptyInfo.uiRowIndex = i;
            sEmptyInfo.uiColumnIndex = j;
            sBatchInfo.qSampleInfos[sEmptyInfo.uiRowIndex][sEmptyInfo.uiColumnIndex] = sEmptyInfo;
        }
    }
    //添加实质样本
    ProjectInfo sProjInfo1= {"FluA/FLuB/RSV", "M100","FluA/FLuB/RSV",1,""};
    ProjectInfo sProjInfo2= {"MP/ADV/HPIV", "M101","MP/ADV/HPIV",1,""};

    SampleInfo sInfo0;
    sInfo0.bExist=true;
    sInfo0.bIsCaped = true;
    sInfo0.uiRowIndex=0;
    sInfo0.uiColumnIndex = 0;

    sInfo0.qProjInfoVect.push_back(sProjInfo1);
    sBatchInfo.qSampleInfos[sInfo0.uiRowIndex][sInfo0.uiColumnIndex] = sInfo0;
    sBatchInfo.iSampleSize++;

    SampleInfo sInfo1;
    sInfo1.bExist=true;
    sInfo1.bIsCaped = true;
    sInfo1.uiRowIndex=1;
    sInfo1.uiColumnIndex = 0;
    sInfo1.qProjInfoVect.push_back(sProjInfo1);
    sBatchInfo.qSampleInfos[sInfo1.uiRowIndex][sInfo1.uiColumnIndex] = sInfo1;
    sBatchInfo.iSampleSize++;

    SampleInfo sInfo2;
    sInfo2.bExist=true;
    sInfo2.bIsCaped = true;
    sInfo2.uiRowIndex=2;
    sInfo2.uiColumnIndex = 0;
    sInfo2.qProjInfoVect.push_back(sProjInfo1);
    sBatchInfo.qSampleInfos[sInfo2.uiRowIndex][sInfo2.uiColumnIndex] = sInfo2;
    sBatchInfo.iSampleSize++;

    //    SampleInfo sInfo3;
    //    sInfo3.bExist=true;
   //     sInfo3.bIsCaped = true;
    //    sInfo3.uiRowIndex=3;
    //    sInfo3.uiColumnIndex =0;
    //    sInfo3.qProjInfoVect.push_back(sProjInfo2);
    //    sBatchInfo.qSampleInfos[sInfo3.uiRowIndex][sInfo3.uiColumnIndex] = sInfo3;
    //    sBatchInfo.iSampleSize++;

    SampleInfo sInfo5;
    sInfo5.bExist=true;
    sInfo5.bIsCaped = true;
    sInfo5.uiRowIndex=0;
    sInfo5.uiColumnIndex = 1;
    sInfo5.qProjInfoVect.push_back(sProjInfo1);
    sBatchInfo.qSampleInfos[sInfo5.uiRowIndex][sInfo5.uiColumnIndex] = sInfo5;
    sBatchInfo.iSampleSize++;

    SampleInfo sInfo6;
    sInfo6.bExist=true;
    sInfo6.bIsCaped = true;
    sInfo6.uiRowIndex=1;
    sInfo6.uiColumnIndex = 1;
    sInfo6.qProjInfoVect.push_back(sProjInfo1);
    sBatchInfo.qSampleInfos[sInfo6.uiRowIndex][sInfo6.uiColumnIndex] = sInfo6;
    sBatchInfo.iSampleSize++;

    //    SampleInfo sInfo7;
    //    sInfo7.bExist=true;
    //    sInfo7.bIsCaped = true;
    //    sInfo7.quRowIndex=0;
    //    sInfo7.quColumnIndex = 6;
    //    sInfo7.qProjInfoVect.push_back(sProjInfo);
    //    sBatchInfo.qSampleInfos[sInfo7.quRowIndex][sInfo7.quColumnIndex] = sInfo7;
    //    sBatchInfo.iSampleSize++;

    //    SampleInfo sInfo8;
    //    sInfo8.bExist=true;
    //    sInfo8.bIsCaped = true;
    //    sInfo8.quRowIndex=1;
    //    sInfo8.quColumnIndex = 5;
    //    sInfo8.qProjInfoVect.push_back(sProjInfo);
    //    sBatchInfo.qSampleInfos[sInfo8.quRowIndex][sInfo8.quColumnIndex] = sInfo8;
    //    sBatchInfo.iSampleSize++;

}

bool SampleControl::GetCurBatchInfo(BatchInfo &sBatchInfo)
{
    bool bResult = false;
    QMutexLocker qLocker(&m_qBatchMutex);
    if(m_sCurBatch.bExist)
    {
        bResult = true;
        sBatchInfo = m_sCurBatch;
    }
    return bResult;
}

quint8 SampleControl::GetBatchSampleSize(QString strBatchNo)
{
    quint8 quSampleSize = 0;
    for(int i=0;i<BATCH_MAX_SIZE;i++)
    {
        if(m_sBatchInfoList[i].bExist && m_sBatchInfoList[i].strBatchNo == strBatchNo)
        {
            quSampleSize = _GetBatchSampleSize(m_sBatchInfoList[i]);
        }
    }
    return  quSampleSize;
}

quint8 SampleControl::GetCurBatchSampleSize()
{
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    return _GetBatchSampleSize(m_sCurBatch);
}

QString SampleControl::GetCurBatchNo()
{
    QMutexLocker qLocker(&m_qBatchMutex);
    return m_sCurBatch.strBatchNo;
}

void SampleControl::AddSampleToNextState(QVector<SampleInfo> &qVect, quint8 uiNextExecST, bool &bResult, bool bNeedLocker)
{
    if(bNeedLocker)
    {
        QMutexLocker qLocker(&m_qSampleSTMutex);
        _AddSampleToNextStateNoLocker(qVect, uiNextExecST, bResult);
    }
    else
    {
        _AddSampleToNextStateNoLocker(qVect, uiNextExecST, bResult);
    }
}
#if 1
void SampleControl::_AddSampleToNextStateNoLocker(QVector<SampleInfo>& qVect, quint8 uiNextExecST, bool& bResult)
{
    auto nextItor = m_qSampleSTMap.find(uiNextExecST);
    if(nextItor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pNextQueue = &nextItor.value();
        if(pNextQueue)
        {
            pNextQueue->push_back(qVect);
            bResult = true;
        }
    }
    else
    {
        QQueue <QVector <SampleInfo>> pNextQueue;
        pNextQueue.push_back(qVect);
        m_qSampleSTMap[uiNextExecST] = pNextQueue;
        bResult = true;
    }
    qDebug()<<"_AddSampleToNextStateNoLocker"<<uiNextExecST<<m_qSampleSTMap.keys()<<qVect.size();
}
#endif


void SampleControl::ShowSampleSTMap()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    for (auto itor =m_qSampleSTMap.begin();itor!=m_qSampleSTMap.end();itor++)
    {
        quint8 currentExecST = itor.key();
        const QQueue<QVector<SampleInfo>>& sampleQueue = itor.value();

        if(sampleQueue.size()>0)
        {
            for (const QVector<SampleInfo>& sampleInfoVect : sampleQueue)
            {
                for (const SampleInfo& sample : sampleInfoVect)
                {
                    if(sample.tpData.qTubePosVect.size()>0)
                    {
                        for(int i=0;i<sample.tpData.qTubePosVect.size();i++)
                        {
                            qDebug()<<"Sample State: " << kObjectID[currentExecST]<<"SampleRow Index:"<<sample.uiRowIndex
                                   << "SampleColumn Index:"<< sample.uiColumnIndex<<"Strip Index:"<< sample.uiStripIndex
                                   <<"TubeRowIndex:"<<sample.tpData.qTubePosVect.at(i).uiRowIndex
                                   <<"TubeColumnIndex:"<<sample.tpData.qTubePosVect.at(i).uiColumnIndex;
                        }
                    }
                    else
                    {
                        qDebug()<<"Sample State: " << kObjectID[currentExecST]<<"SampleRow Index:"<<sample.uiRowIndex
                                << "SampleColumn Index:"<< sample.uiColumnIndex<<"Strip Index:"<< sample.uiStripIndex<<"Sample No:"<<sample.strSampleNo;
                    }
                }
            }
        }
    }
}

void SampleControl::ShowSystemBuildSTMap()
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    for (auto itor =m_qSystemBuildSTMap.begin();itor!=m_qSystemBuildSTMap.end();itor++)
    {
        quint8 currentExecST = itor.key();
        const QQueue<QVector<SystemBuildInfo>>& queue = itor.value();

        if(queue.size()>0)
        {
            for (const QVector<SystemBuildInfo>& qInfoVect : queue)
            {
                for (const SystemBuildInfo& info : qInfoVect)
                {

                    qDebug()<<"SystemBuildState: " << kObjectID[currentExecST]<<"SampleRow Index:"<<info.uiSampleIndex/SAMPLE_COLUMN_SIZE
                            <<"SampleColumn Index:"<< info.uiSampleIndex%SAMPLE_COLUMN_SIZE<<"Strip Index:"<< info.uiStripIndex
                            <<"TubeRowIndex:"<<info.tubePos.uiRowIndex<<"TubeColumnIndex:"<<info.tubePos.uiColumnIndex
                            <<"ReagentRowIndex:"<<info.reagentPos.uiRowIndex<<"ReagentColumnIndex:"<<info.reagentPos.uiColumnIndex
                            <<"PCRRowIndex:"<<info.pcrPos.uiRowIndex<<"PCRColumnIndex:"<<info.pcrPos.uiColumnIndex
                            <<"TipRowIndex:"<<info.tipPos.uiRowIndex<<"TipColumnIndex:"<<info.tipPos.uiColumnIndex
                            <<"OpenPCRCap"<<info.bNeedOpenCap<<" ClosePCRCap"<<info.bNeedCloseCap;

                }
            }
        }
    }
}

void SampleControl::ShowSTMapInfo()
{
    qDebug()<<"SampleControl ShowSTMapInfo";
    ShowSampleSTMap();
    ShowSystemBuildSTMap();
}

bool SampleControl::GetCurExecSampleInfo(quint8 uiCurExecST, QVector<SampleInfo>& qVect)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    bool bResult = false;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(uiCurExecST);
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            qVect = pQueue->front();
            bResult = true;
        }
    }
    qDebug()<<"GetCurExecSampleInfo: "<<uiCurExecST<<m_qSampleSTMap.keys()<<bResult;
    return bResult;
}

qint32 SampleControl::GetCurExecSampleCapacity()
{
    const quint8 kCapacity = 100;//容量系数
    qint32 iDefaultCapacity =  270 * kCapacity;
    QVector<SampleInfo> qVect;
    bool bGet = GetCurExecSampleInfo(SEST_WAIT_SAMPLING, qVect);
    if(!bGet || qVect.size()==0)//存在待转移样本
    {
        qDebug() << "Error: No samples to get capacity.";
        return iDefaultCapacity;
    } 

    qint32 iMaxCapacity =  0;
    qint32 iTempCapacity =  0;
    for (auto& info : qVect)
    {
        for(auto& t : info.qProjInfoVect)
        {
            if(!t.strProjID.isEmpty())
            {
                iTempCapacity = CProjectInformation::getInstance().getFloatFiledFromProjectLot(t.strProjID,CProjectDBImpl::FieldName_ProjectInfo::sampleCalibrationCapacity)*kCapacity;
                if(iTempCapacity > iMaxCapacity)
                {
                    iMaxCapacity = iTempCapacity;
                }
            }
        }
    }     
    if (iMaxCapacity == 0)
    {
        iMaxCapacity = iDefaultCapacity;
        qDebug() << "Error: Sample capacity is zero.";
    }
    return iMaxCapacity;
}

bool SampleControl::UpdateCurExecSampleInfo(quint8 uiCurExecST, QVector<SampleInfo> &qVect)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    bool bResult = false;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(uiCurExecST);
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            pQueue->replace(0, qVect);
            bResult = true;
        }
    }
    return bResult;
}
bool SampleControl::UpdateCurExecStripAndSamplingSTIndex(QString& strParam, quint8& uiSize)
{
    bool bResult = false;
    quint8 uiColumnIndex = 0;
    QMutexLocker qLocker(&m_qSampleSTMutex);
    auto itor = m_qSampleSTMap.find(SEST_WAIT_SAMPLING);
    m_bCurOpSingleRightSample = false;
    
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            QVector<SampleInfo>* qVect = &pQueue->front();
            if(qVect && qVect->size()>0)
            {
                QVector<quint8> qStripIndexVect = CStrip::getInstance().Consume(qVect->size());
                for(int i=0;i<qVect->size();i++)
                {
                    SampleInfo info = qVect->at(i);
                    info.uiStripIndex = qStripIndexVect.at(i);
                    qVect->replace(i, info);
                    if(0 == i)
                    {
                        strParam += QString(",%1,%2").arg(info.uiStripIndex).arg(info.uiColumnIndex);
                        uiColumnIndex = info.uiColumnIndex;
                    }
                    qDebug()<<"UpdateCurExecStripAndSamplingSTIndex bIsStandard"<<info.bIsStandard<<info.uiStripIndex<<uiColumnIndex<<strParam;
                }
                uiSize = qVect->size();
                bResult = true;
            }
        }
    }
    if(uiSize == 1&& (uiColumnIndex==0))
    {
        m_bCurOpSingleRightSample = true;
    }
    qDebug()<<"UpdateCurExecStripAndSamplingSTIndex: "<<m_qSampleSTMap.keys()<<m_bCurOpSingleRightSample<<bResult;
    return bResult;
}

void SampleControl::UpdateCurExecStripAndSamplingSTIndex(quint8& uiSize)
{
    bool bResult = false;
    quint8 uiColumnIndex = 0;
    QMutexLocker qLocker(&m_qSampleSTMutex);
    auto itor = m_qSampleSTMap.find(SEST_WAIT_SAMPLING);
    m_bCurOpSingleRightSample = false;
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            QVector<SampleInfo>* qVect = &pQueue->front();
            if(qVect && qVect->size()>0)
            {
                for(int i=0;i<qVect->size();i++)
                {
                    SampleInfo info = qVect->at(i);
                    uiColumnIndex = info.uiColumnIndex;
                }
                uiSize = qVect->size();
                bResult = true;
            }
        }
    }
    if(uiSize == 1&& (uiColumnIndex==0))
    {
        m_bCurOpSingleRightSample = true;
    }
    qDebug()<<"UpdateCurExecStripAndSamplingSTIndex: "<<m_qSampleSTMap.keys()<<m_bCurOpSingleRightSample<<bResult;
}


bool SampleControl::GetStripIndexParamStr(quint8 uiState, QString &strParam, quint8& uiSize)
{
    bool bResult = false;
    QMutexLocker qLocker(&m_qSampleSTMutex);
    auto itor = m_qSampleSTMap.find(uiState);
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            QVector<SampleInfo> qVect = pQueue->front();
            qDebug()<<"GetStripIndexParamStr qVect:"<<qVect.size();
            for(int i=0; i<qVect.size(); i++)
            {
                strParam += QString(",%1").arg(qVect.at(i).uiStripIndex);
            }
            uiSize = qVect.size();
            if(qVect.size())
            {
                bResult = true;
            }
        }
    }
    qDebug()<<"GetStripIndexParamStr:"<<uiState<<strParam<<uiSize;
    return bResult;
}

bool SampleControl::IsCurBatchSampleSpecificActionDone(quint8 uiCurExecST)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    bool bResult = false;
    //TODO改为当前可测试的样本数量
    qint8 iCurBatchSampleSize = m_sCurBatch.iSampleSize;
    qint8 iCurActionSampleSize = 0;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(uiCurExecST);
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            for(int i =0; i<pQueue->size(); i++)
            {
                iCurActionSampleSize += pQueue->at(i).size();
            }
        }
    }
    qDebug()<<"IsCurBatchSampleSpecificActionDone:"<<iCurBatchSampleSize<<iCurActionSampleSize<<m_qSampleSTMap.keys();
    if(iCurBatchSampleSize == iCurActionSampleSize)
    {
        bResult = true;
    }

    return bResult;
}

bool SampleControl::IsCurBatchSampleStandardActionDone()
{
    return m_sCurBatch.iStandardSampleSize == m_iStandardSampleUsedCount;
}

void SampleControl::AddSampleStandardActionCount()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    m_iStandardSampleUsedCount++;
    qDebug() << "AddSampleStandardActionCount" << m_iStandardSampleUsedCount;
}

bool SampleControl::IsCurBatchSampleStandardAvailable()
{
    return m_bStandardAndSampleCountMatch;
}

qint8 SampleControl::GetSampleSize(BatchInfo &batchInfo)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    if(batchInfo.iSampleSize==-1)
    {
        batchInfo.iSampleSize = 0;
        for(int i=0;i<SAMPLE_ROW_SIZE;i++)
        {
            for(int j=0; j<SAMPLE_COLUMN_SIZE; j++)
            {
                if(batchInfo.qSampleInfos[i][j].bExist)
                {
                    batchInfo.iSampleSize++;
                }
            }
        }
    }
    return batchInfo.iSampleSize;
}


QQueue<QVector<SampleInfo> > SampleControl::GetBatchSampleInfo(quint8 uiState)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    QQueue <QVector <SampleInfo>> qQueue;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(uiState);
    if(itor != m_qSampleSTMap.end())
    {
        qQueue = itor.value();
    }
    qDebug()<<"GetBatchSampleInfo"<<uiState<<m_qSampleSTMap.keys();
    return qQueue;
}

void SampleControl::UpdateBacthSampleInfo(quint8 uiState, QQueue<QVector<SampleInfo> > qQueue)
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    m_qSampleSTMap[uiState] = qQueue;
}

#if 0
void SampleControl::RearrayBatchSampleInfoToSystemBuildInfo()
{
    QMutexLocker qLocker(&m_qSampeSTMutex);
    QQueue <QVector <SampleInfo>> qSrcQueue;
    QQueue <QVector <SampleInfo>> qDstQueue;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(SEST_WAIT_EXTRACT);
    QVector<SampleInfo> qTmpVect;
    if(itor != m_qSampleSTMap.end())
    {

        qSrcQueue = itor.value();
        for(int i=0; i<qSrcQueue.size();i++)
        {
            QVector<SampleInfo> qVect = qSrcQueue.at(i);
            for(int j=0; j<qVect.size(); j++)
            {
                SampleInfo sampleInfo = qVect.at(j);
                qTmpVect.push_back(sampleInfo);
            }
        }
        //重新排序，排序規則：單樣本單組分連續兩個項目可同批操作，其他均單個操作
        for(int i=0;i<qTmpVect.size();i++)
        {
            QVector<SampleInfo> qVect;
            SampleInfo info1 = qTmpVect.at(i);
            if(i<qTmpVect.size()-1)
            {
                SampleInfo info2 = qTmpVect.at(i+1);
                //連續兩個樣本單項目單組分，规则重新定制
                if(m_uiCatchType == CT_DOUBLE && info1.qProjInfoVect.size()==1 &&info1.qProjInfoVect.at(0).uiCompNum == 1
                        && info2.qProjInfoVect.size()==1 &&info2.qProjInfoVect.at(0).uiCompNum == 1 )
                {
                    qVect.push_back(info1);
                    qVect.push_back(info2);
                    i=i+1;
                }
                else
                {
                    qVect.push_back(info1);
                }
            }
            else
            {
                qVect.push_back(info1);
            }
            qDstQueue.push_back(qVect);
        }
        m_qSampleSTMap[SEST_WAIT_TRANS_PURIFY] = qDstQueue;
        qSrcQueue.clear();
        m_qSampleSTMap[SEST_WAIT_EXTRACT] = qSrcQueue;//Empty Queue
    }
}
#endif

#if 1 //current used version 2024.5.21
void SampleControl::RearraySampleInfoToSystemBuildInfo()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    QQueue <QVector <SampleInfo>> qSrcQueue;
    QQueue <QVector <SystemBuildInfo>> qDstQueue;
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(SEST_WAIT_EXTRACT);
    QVector<SystemBuildInfo> qTmpVect;
    if(itor != m_qSampleSTMap.end())
    {
        // 按照提取条的样本顺序排序(排序前和排序后)
        // 按照卡条顺序发即可
        QVector<SampleInfo> vSampleInfo;
        qSrcQueue = itor.value();
        for(int i=0; i<qSrcQueue.size();i++)
        {
            vSampleInfo.append(qSrcQueue.at(i));
        }          
    
        std::sort(vSampleInfo.begin(), vSampleInfo.end(), [](const SampleInfo &a, const SampleInfo &b){
            return a.uiStripIndex < b.uiStripIndex;
        });       

        for(int j=0; j<vSampleInfo.size(); j++)
        {
            SampleInfo sampleInfo = vSampleInfo.at(j);
            qDebug()<<"RearraySampleInfoToSystemBuildInfo bIsStandard"<<sampleInfo.bIsStandard<<sampleInfo.strSampleNo;
            if (sampleInfo.bIsStandard)// 内标不进行体系构建
            {
                continue;
            }
            
            for(int k=0;k<sampleInfo.qProjInfoVect.size();k++)
            {
                ProjectInfo projInfo = sampleInfo.qProjInfoVect.at(k);                   
                if(projInfo.uiCompNum<1)
                {
                    qDebug() << "Error" << "projInfo.uiCompNum" << projInfo.uiCompNum;
                    projInfo.uiCompNum = 1;
                }
                for(int l = 0;l<projInfo.uiCompNum;l++)
                {
                    SystemBuildInfo buildInfo = {};
                    buildInfo.uiBatchIndex = 0;
                    buildInfo.uiSampleIndex = sampleInfo.uiRowIndex*SAMPLE_COLUMN_SIZE+sampleInfo.uiColumnIndex;
                    buildInfo.uiStripIndex = sampleInfo.uiStripIndex;
                    buildInfo.uiProjIndex = k;
                    buildInfo.uiAmplifyCompIndex = l;
                    buildInfo.strProjID = projInfo.strProjID;
                    buildInfo.strSampleID = sampleInfo.strSampleNo;
                    buildInfo.strTecName = projInfo.strTecName;
                    buildInfo.bLastOneInCurTecBatch = false;
                    buildInfo.strBatchNo = sampleInfo.strBatchNo;
                    // qDebug()<<"RearraySampleInfoToSystemBuildInfo add new elem strip:"<<sampleInfo.uiStripIndex<<k<<l<<sampleInfo.strSampleNo<<sampleInfo.strBatchNo;
                    qTmpVect.push_back(buildInfo);
                    QDFUN_LINE << buildInfo.printInfo();
                }
            }
        }
        m_uiCurBatchSystemBuildSize = qTmpVect.size();

        QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
#if 0
        //重新排序，排序規則：若支持兩個同時操作，存在連續兩個樣本，
        //這兩個樣本前一個樣本的最後一個構建及後一個樣本的第一個構建可同時操作，其他單獨操作
        for(int i=0;i<qTmpVect.size();i++)
        {
            QVector<SystemBuildInfo> qVect;
            SystemBuildInfo info1 = qTmpVect.at(i);
            if(i<qTmpVect.size()-1)
            {
                SystemBuildInfo info2 = qTmpVect.at(i+1);
                //連續兩個樣本單項目單組分，规则重新定制
                if(m_uiCatchType == CT_DOUBLE && info1.uiSampleIndex != info2.uiSampleIndex )
                {
                    qVect.push_back(info1);
                    qVect.push_back(info2);
                    i=i+1;
                }
                else
                {
                    qVect.push_back(info1);
                }
            }
            else
            {
                qVect.push_back(info1);
            }
            qDstQueue.push_back(qVect);
        }
#endif
#if 1
        //排序规则：单个抓取，则每次只抓取一个；双个抓取，有临近的卡条直接双个操作，没有临近的卡条独立操作
        if(CT_SINGLE == m_uiCatchType)//
        {
            for(int i=0;i<qTmpVect.size();i++)
            {
                QVector<SystemBuildInfo> qVect;
                qVect.push_back(qTmpVect.at(i));
                qDstQueue.push_back(qVect);
            }
        }
        else if(CT_DOUBLE == m_uiCatchType)
        {
            while(qTmpVect.size()>0)
            {
                QVector<SystemBuildInfo> qVect;
                SystemBuildInfo info1 = qTmpVect.at(0);
                if(qTmpVect.size()>1)
                {
                    for(int i=1;i<qTmpVect.size();i++)
                    {
                        if(qTmpVect.at(i).uiStripIndex == info1.uiStripIndex && (i!=qTmpVect.size()-1)) //非最后一个卡条，遇到相同卡条跳过
                            continue;
                        else if(qTmpVect.at(i).uiStripIndex == (info1.uiStripIndex+1))//遇到相邻卡条，匹配成对操作
                        {
                            //pair 按照卡条和机械的顺序，顺序是卡条位置越大(即往左边)，uiStripIndex越大是先push_back
                            // qVect.push_back(info1);
                            // qVect.push_back(qTmpVect.at(i));

                            qVect.push_back(qTmpVect.at(i)); 
                            qVect.push_back(info1);

                            qDebug()<<"Double pair elem1[stripindex:"<<info1.uiStripIndex <<",ampIndex:" <<info1.uiAmplifyCompIndex
                                   <<"] elem2[stripindex:"<<qTmpVect.at(i).uiStripIndex<<",ampIndex:"<<qTmpVect.at(i).uiAmplifyCompIndex<<"]";
                            qTmpVect.removeAt(i);
                            qTmpVect.remove(0);
                            break;
                        }
                        else //整个队列都是相同卡条或者最新元素与当前配对的元素不是相邻卡条，都是独立操作
                        {
                            qVect.push_back(info1);
                            qDebug()<<"single pair elem[stripindex:"<<info1.uiStripIndex<<",ampIndex:"<<info1.uiAmplifyCompIndex<<"]";
                            qTmpVect.removeAt(0);
                            break;
                        }
                    }
                }
                else
                {
                    qVect.push_back(info1);
                    qDebug()<<"single pair elem[stripindex:"<<info1.uiStripIndex<<",ampIndex:"<<info1.uiAmplifyCompIndex<<"]";
                    qTmpVect.removeAt(0);
                }
                qDstQueue.push_back(qVect);
            }
        }
        else
        {
            qDebug()<<"Error catch Type";
        }
#endif
        m_qSystemBuildSTMap[SEST_WAIT_TRANS_REAGENT] = qDstQueue;
        // m_qSystemBuildSTMap[SEST_WAIT_TRANS_PURIFY] = qDstQueue;
        qSrcQueue.clear();
        m_qSampleSTMap[SEST_WAIT_EXTRACT] = qSrcQueue;//Empty Queue
    }
}

#endif

void SampleControl::RearraySystemBuildRegentInfo()
{
    QMutexLocker qSysReagentLocker(&m_qSystemBuildReagentMutex);
    m_qSytemBuildReagentMap.clear();
    QQueue<QVector<SampleInfo>> qQueue = GetBatchSampleInfo(SEST_WAIT_EXTRACT);
    for(int i=0; i<qQueue.size();i++)
    {
        QVector<SampleInfo> qVect = qQueue.at(i);
        for(int j=0; j<qVect.size(); j++)
        {
            SampleInfo sampleInfo = qVect.at(j);
            for(int k =0; k<sampleInfo.qProjInfoVect.size();k++)
            {
                QString strKey = sampleInfo.qProjInfoVect.at(k).strProjID;
                QMap<QString, quint8>::iterator itor = m_qSytemBuildReagentMap.find(strKey);
                if(itor != m_qSytemBuildReagentMap.end())
                {
                    m_qSytemBuildReagentMap[strKey] = ++itor.value();
                }
                else
                    m_qSytemBuildReagentMap[strKey] = 1;
            }
        }
    }
    qDebug()<<"RearraySystemBuildRegentInfo"<<qQueue.size()<<m_qSytemBuildReagentMap.keys()<<m_qSytemBuildReagentMap.values();
}

QMap<QString, quint8> SampleControl::GetSystemBuildReagentInfo()
{
    QMutexLocker qSysReagentLocker(&m_qSystemBuildReagentMutex);
    return  m_qSytemBuildReagentMap;
}

bool SampleControl::DequeueNextSystemBuildReagentInfo(QString& strProj, quint8& uiProjSize)
{
    bool bExist = false;
    QMutexLocker qSysReagentLocker(&m_qSystemBuildReagentMutex);
    qDebug()<<"SampleControl::DequeueNextSystemBuildReagentInfo keys"<<m_qSytemBuildReagentMap.keys();
    if(m_qSytemBuildReagentMap.size()>0)
    {
        strProj = m_qSytemBuildReagentMap.begin().key();
        uiProjSize = m_qSytemBuildReagentMap.begin().value();
        bExist = true;
        m_qSytemBuildReagentMap.erase(m_qSytemBuildReagentMap.begin());
    }
    qDebug()<<"SampleControl::DequeueNextSystemBuildReagentInfo"<<m_qSytemBuildReagentMap.keys()<<strProj<<uiProjSize;
    return bExist;
}
#if 0
bool SampleControl::UpdateSampleExecSTToNext(quint8 uiCurExecST, quint8 uiNextExecST)
{
    bool bResult = false;
    QMutexLocker qLocker(&m_qSampeSTMutex);
    QMap<quint8, QQueue <QVector<SampleInfo> > >::iterator itor = m_qSampleSTMap.find(uiCurExecST);
    if(itor != m_qSampleSTMap.end())
    {
        QQueue <QVector <SampleInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue.size()>0)
        {
            QVector<SampleInfo> qVect = pQueue->front();
            AddSampleToNextState(qVect, uiNextExecST, bResult, false);
            pQueue->pop_front();
        }
    }
    return bResult;
}
#endif

bool SampleControl::UpdateSampleExecSTToNext(quint8 uiCurExecST, quint8 uiNextExecST)
{
    bool bResult = false;
    QMutexLocker qLocker(&m_qSampleSTMutex);
    qDebug()<<"UpdateSampleExecSTToNext"<<uiCurExecST<<"->"<<uiNextExecST<<kObjectID[uiCurExecST]<<"->"<<kObjectID[uiNextExecST]<<m_qSampleSTMap.keys();
    auto itor = m_qSampleSTMap.find(uiCurExecST);
    if (itor != m_qSampleSTMap.end())
    {
        auto pQueue = &itor.value();
        if (pQueue && pQueue->size()>0)
        {
            QVector<SampleInfo> qVect = pQueue->front();
            for (auto& s : qVect)
            {
                for(auto& s2 : s.qProjInfoVect)
                {
                    qDebug()<<"UpdateSampleExecSTToNext SampleInfo"<<s2.strProjID<<s2.strTecName<<s.strSampleNo;
                }
            }
            
            AddSampleToNextState(qVect, uiNextExecST, bResult, false);
            pQueue->pop_front();
        }
    }

    return bResult;
}


bool SampleControl::UpdateSampleExecSTToNext(quint8 uiCurExecST, QVector<quint8> qNextExecSTVect)
{
    bool bResult = false;
    QMutexLocker qLocker(&m_qSampleSTMutex);
    auto itor = m_qSampleSTMap.find(uiCurExecST);
    if(itor != m_qSampleSTMap.end())
    {
        auto pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            QVector<SampleInfo> qVect = pQueue->front();
            for(int i =0;i<qNextExecSTVect.size();i++)
            {
                qDebug()<<"UpdateSampleExecSTToNext vecor: "<<uiCurExecST<<"->"<<qNextExecSTVect.at(i)<<kObjectID[uiCurExecST]<<"->"<<kObjectID[qNextExecSTVect.at(i)];
                _AddSampleToNextStateNoLocker(qVect, qNextExecSTVect.at(i), bResult);
            }
            pQueue->pop_front();
        }
    }
    return bResult;
}


bool SampleControl::UpdateExecSTToError(QVector<quint8> qImpactExecSTVect)
{
    bool bResult = false;
    for(int i=0;i<qImpactExecSTVect.size();i++)
    {
        quint8 uiCurExecST = qImpactExecSTVect.at(i);
        if(uiCurExecST<SEST_WAIT_TRANS_PURIFY)
        {
            bResult = UpdateSampleExecSTToNext(uiCurExecST, SEST_ERROR);
        }
        else
        {
            bResult = UpdateSystemBuildExecSTToNext(uiCurExecST, SEST_ERROR);
        }
    }
    return bResult;
}

quint8 SampleControl::_GetBatchSampleSize(BatchInfo &batchInfo)
{
    quint8 quSampleSize = 0;
    if(batchInfo.bExist)
    {
        for(int i=0;i<SAMPLE_ROW_SIZE;i++)
        {
            for(int j=0;j<SAMPLE_COLUMN_SIZE;j++)
            {
                if(batchInfo.qSampleInfos[i][j].bExist)
                {
                    quSampleSize++;
                }
            }
        }
    }
    return quSampleSize;
}

bool SampleControl::ParseBatchInfo(QString &strBatchInfo, BatchInfo &sBatchInfo)
{
#if 0
    bool bResult = true;
    AddTstSamples(sBatchInfo);
    return bResult;
#endif

#if 1
    //判断样本和内标项目是否一致
    QSet<QString> qProjID;        // 获取所有样本项目id
    QSet<QString> qStandardProjID;// 获取所有内标项目id

    QSet<QString> qTecTimeSeq;// tec时序
    QSet<QString> qExtractTimeSeq;// 提取时序
    QVector<SampleInfo> qSampleInfo;// 样本信息

    bool bResult = false;
    //batchInfo 确定格式并解析
    //[测试批号;样本行位置^樣本列位置(0^0),样本编号,是否带盖,项目编号1,项目编号2,项目编号N+$+样本位置,样本编号,是否带盖,项目编号1,项目编号2,项目编号N;提取时序名称;TEC时序名称]
    //strBatchInfo = "20240410170439;0^0,1,M100+$+0^1,7,M100+$+1^0,2,M100+$+1^1,8,M100+$+2^0,3,M100+$+3^0,4,M100+$+4^0,5,M100+$+5^0,6,M100;;TEC1";
    // Step 1: Split by semicolon to obtain BatchInfo fields
    //    strBatchInfo = "20240410170439;0^0,1,M100+$+1^0,2,M100+$+2^0,3,M100+$+3^0,4,M100+$+4^0,5,M100+$+5^0,6,M100;;TEC1";
    QStringList batchFields = strBatchInfo.split(";");
    if(batchFields.size()>=3)
    {
        sBatchInfo.bExist = true;
        sBatchInfo.strBatchNo = batchFields[0];
        QStringList qSampleRecordList = batchFields[1].split("+$+", QString::SkipEmptyParts);
        sBatchInfo.strExtract = batchFields[2];

        // 检查提取时序是否有效
        if(!_CheckExtractTimeSeqValid(sBatchInfo.strExtract))
        {
            qDebug()<<"extract timeSeq is invalid: "<<sBatchInfo.strExtract;
            return false;
        }

        quint8 uiSampleSize = 0;//内标不是样本
        qDebug()<<"qSampleRecordList: "<<qSampleRecordList;
        // Step 2: Process the samples field
        // Initialize SampleInfo arrays
        for (int row = 0; row < SAMPLE_ROW_SIZE; ++row) {
            for (int col = 0; col < SAMPLE_COLUMN_SIZE; ++col) {
                sBatchInfo.qSampleInfos[row][col].bExist = false;
            }
        }

        // Step 3 & 4: Parse each sample record
        // 样本行位置^樣本列位置(0^0),样本编号,项目编号1,项目编号2,项目编号
        for (const QString &strSampleRecord : qSampleRecordList) {
            QStringList sampleFields = strSampleRecord.split(",", QString::SkipEmptyParts);

            if (sampleFields.size() < 3) {
                // Handle invalid format or skip the record
                qDebug()<<"Invalid Sample info in batchstr.";
                continue;
            }

            // Extract sample position
            QStringList positionParts = sampleFields[0].split("^", QString::SkipEmptyParts);
            if (positionParts.size() != 2) {
                // Handle invalid position format or skip the record
                qDebug()<<"Invalid Sample pos info "<<sampleFields;
                continue;
            }

            qint8 iRowIndex = positionParts[0].toInt();
            qint8 iColumnIndex = positionParts[1].toInt();

            // Ensure the position is within the valid range
            if (iRowIndex >= 0 && iRowIndex < SAMPLE_ROW_SIZE &&
                    iColumnIndex >= 0 && iColumnIndex < SAMPLE_COLUMN_SIZE) {
                SampleInfo sampleInfo;
                sampleInfo.bExist = true;
                sampleInfo.bIsCaped = sampleFields[2].toUInt();//TODO 从字符串中解析
                sampleInfo.strSampleNo = sampleFields[1];
                sampleInfo.uiRowIndex = iRowIndex;
                sampleInfo.uiColumnIndex = iColumnIndex;
                sampleInfo.bIsStandard = CheckSampleIsStandard(sampleInfo.strSampleNo,sampleInfo.strStandardProject);
                sampleInfo.bIsStandardAdd = false;
                sampleInfo.strBatchNo = sBatchInfo.strBatchNo;
                
                if(!sampleInfo.bIsStandard)
                {
                    uiSampleSize++; // 内标不是样本
                }
                else
                {
                    qStandardProjID.insert(sampleInfo.strStandardProject); // 获取所有内标项目id
                }

                // Step 5: Parse project numbers and create ProjectInfo structures
                for (int iProjIdx = 3; iProjIdx < sampleFields.size(); iProjIdx++) {
                    ProjectInfo projInfo= {"", "", "", 1,""};
                    projInfo.strProjID = sampleFields[iProjIdx];
                    projInfo.uiCompNum = CProjectInformation::getInstance().getTubeCountFromProjectLot(projInfo.strProjID);
                    projInfo.strTecName = CProjectInformation::getInstance().getTecNameFromProjectLot(projInfo.strProjID);
                    // 检查时序是否有效
                    if (!_CheckTecTimeSeqValid(projInfo.strTecName))
                    {
                        qDebug()<<"tec timeSeq is invalid: "<<projInfo.strTecName;
                        return false;                        
                    }

                    // 检查项目是否有效
                    if (!_CheckProjIDValid(projInfo.strProjID))
                    {
                        qDebug()<<"project id is invalid: "<<projInfo.strProjID;
                        return false;                        
                    }                    
                    
                    sampleInfo.qProjInfoVect.push_back(projInfo);
                    qProjID.insert(projInfo.strProjID);
                    qDebug()<<"ParseBatchInfo: "<<projInfo.strProjID<<projInfo.uiCompNum<<projInfo.strTecName;
                }
                qSampleInfo.append(sampleInfo);
                qDebug()<<"ParseBatchInfo bIsStandard: "<<sampleInfo.bIsStandard<<iRowIndex<<iColumnIndex<<sampleInfo.strSampleNo;
                sBatchInfo.qSampleInfos[iRowIndex][iColumnIndex] = sampleInfo;
            }
        }
         
        // Step 6: Set sampleSize  field of BatchInfo
        sBatchInfo.iSampleSize = uiSampleSize;
        sBatchInfo.iStandardSampleSize = qSampleRecordList.size() - uiSampleSize;
        sBatchInfo.iCurOpRowIndex = sBatchInfo.iCurOpColumnIndex = -1;
        bResult = true;
    }

    _CalcStandardSampleInfo(qProjID,qStandardProjID,qSampleInfo);//计算内标相关参数

    qDebug()<<"ParseBatchInfo: "<<qProjID<<qStandardProjID<<m_bStandardAndSampleCountMatch;
    
    return bResult;
#endif
}

void SampleControl::_CalcStandardSampleInfo(QSet<QString> &qProjID,QSet<QString> &qStandardProjID,QVector<SampleInfo> &qSampleInfo)
{
    // 提前分配卡条位置(用于添加内标),内标不会分配项目，不用判断是不是内标 
    // FIXME 内标卡条分配有问题
    // 1、多个内标时，提取条分配有问题(提取条重置顺序)
    // 2、需要判断提取条对应的项目(不能添加错误位置)
    // 3、需要考虑并排问题
    for (auto& id : qStandardProjID)// 分配提取条
    {
        for (auto& info : qSampleInfo)// 开始查找样本
        {
            QVector<ProjectInfo> qProjInfoVect;
            for (auto& proj : info.qProjInfoVect)// 样本信息
            {
                if (proj.strProjID == id)// 样本包含的项目ID
                {
                    qProjInfoVect.append(proj);
                    break;
                }
            }
            
            if (!qProjInfoVect.isEmpty())
            {
                QVector<quint8> qStripIndexVect = CStrip::getInstance().Consume(1);// 一个样本消耗一个卡条
                for(int i=0;i<qStripIndexVect.size();i++)
                {
                    quint8 uiStripIndex = qStripIndexVect.at(i);
                    _UpdateProjectStripIndex(qProjInfoVect,uiStripIndex);
                }                 
            }
        }
    }

    CStrip::getInstance().ResetStripSize();// 重置提取条数量
    // 开始判断内标项目和样本项目数量是否一致
    if(!qStandardProjID.isEmpty())
    {
        QSet<QString> intersection = qStandardProjID.intersect(qProjID);// 获取两个集合的交集
        m_bStandardAndSampleCountMatch = (intersection.size() != 0)? true: false;
        // 删除不需要添加到普通样本的内标
        if (!m_bStandardAndSampleCountMatch)
        {
            // _DeleteStandardProjID(qStandardProjID,qProjID,sBatchInfo);
        }
    }
}

bool SampleControl::_GetNextWaitExecBatch(bool bNeedLocker)
{
    QDFUN_LINE << "m_iReadIndex" << m_iReadIndex.load() << "m_iWriteIndex" << m_iWriteIndex.load();
    bool bResult = false;
    if(this->m_iReadIndex.load() != this->m_iWriteIndex.load())
    {
        bResult = true;
        if(bNeedLocker)
        {
            QMutexLocker qLocker(&m_qBatchMutex);
            m_sCurBatch = m_sBatchInfoList[m_iReadIndex.load()];
        }
        else
        {
            m_sCurBatch = m_sBatchInfoList[m_iReadIndex.load()];
        }

        m_iReadIndex.store((m_iReadIndex.load() + 1) % BATCH_MAX_SIZE);
    }
    else
    {
        if(bNeedLocker)
        {
            QMutexLocker qLocker(&m_qBatchMutex);
            m_sCurBatch.bExist = false;
        }
        else
        {
            m_sCurBatch.bExist = false;
        }
    }
    qDebug()<<"_GetNextWaitExecBatch"<<m_sCurBatch.strBatchNo<<bResult;
    return bResult;
}
bool SampleControl::HasNextSampleCatchSize()
{
    quint8 quRowIndex = (m_sCurBatch.iCurOpRowIndex>=0) ? m_sCurBatch.iCurOpRowIndex+1 : 0;
    quint8 quColumnIndex = 0;
    int j =  quColumnIndex;
    qDebug()<<"HasNextSampleCatchSize"<<quRowIndex<<quColumnIndex;
    for(int i = quRowIndex;i<SAMPLE_ROW_SIZE;i++)
    {
        for(;j<SAMPLE_COLUMN_SIZE;j++)
        {
            qDebug()<<"HasNextSampleCatchSize for"<<i<<j;
            if(m_sCurBatch.qSampleInfos[i][j].bExist && 
              !m_sCurBatch.qSampleInfos[i][j].bIsStandard)// 不获取内标试管
            {
                return true;
            }
        }
    }
    return false;
}

quint8 SampleControl::GetNextSampleCatchAndMixSize()
{
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    m_qNextExecSampleInfoVect.clear();
    quint8 quCatchSize = 0;
    qDebug()<<"GetNextSampleCatchAndMixSize: "<<m_sCurBatch.iCurOpRowIndex<<m_sCurBatch.iCurOpColumnIndex;
GET_NEXT:
    switch(m_uiCatchType)
    {
    case CT_SINGLE:
    {
        //在上一个操作的基础上,下一个操作对象行索引不变，列索引加1
        quint8 quRowIndex = (m_sCurBatch.iCurOpRowIndex>=0) ? m_sCurBatch.iCurOpRowIndex : 0;
        quint8 quColumnIndex = m_sCurBatch.iCurOpColumnIndex+1;
        int j =  quColumnIndex;
        for(int i = quRowIndex;i<SAMPLE_ROW_SIZE;i++)
        {
            for(;j<SAMPLE_COLUMN_SIZE;j++)
            {
                if(m_sCurBatch.qSampleInfos[i][j].bExist && 
                  !m_sCurBatch.qSampleInfos[i][j].bIsStandard)// 不获取内标试管
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[i][j]);
                    m_sCurBatch.iCurOpColumnIndex = j;
                    m_sCurBatch.iCurOpRowIndex = i;
                    i = SAMPLE_ROW_SIZE;
                    j = SAMPLE_COLUMN_SIZE;
                    break;
                }
            }
            if(quCatchSize<0)
            {
                j=0;
            }
            else
            {
                break;
            }
        }

        break;
    }
    case CT_DOUBLE:
    {
        quint8 quRowIndex = (m_sCurBatch.iCurOpRowIndex>=0) ? m_sCurBatch.iCurOpRowIndex+1 : 0;
        quint8 quColumnIndex = 0;
        while(quRowIndex<SAMPLE_ROW_SIZE&&quColumnIndex<SAMPLE_COLUMN_SIZE)
        {
DOUBLE_BEGIN:
            if(quRowIndex<SAMPLE_ROW_SIZE&&quColumnIndex<SAMPLE_COLUMN_SIZE)
            {
                if(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bExist && 
                  !m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bIsStandard)// 不获取内标试管
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex]);
                }

                if((quColumnIndex+1)<SAMPLE_COLUMN_SIZE && 
                   m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bExist &&
                  !m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bIsStandard)// 不获取内标试管
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1]);
                }

                if(quCatchSize>0)
                {
                    m_sCurBatch.iCurOpColumnIndex = quColumnIndex;
                    m_sCurBatch.iCurOpRowIndex = quRowIndex;
                    break;
                }
                else
                {
                    quColumnIndex++;
                    if(quColumnIndex>=SAMPLE_COLUMN_SIZE)
                    {
                        quRowIndex++;
                        quColumnIndex=0;
                    }
                    goto DOUBLE_BEGIN;
                }
            }
        }
        break;
    }
    default:
        break;

    }
    if(quCatchSize ==0)
    {
        if(_GetNextWaitExecBatch(false))
        {
            goto GET_NEXT;
        }
    }

    if(quCatchSize>0)
    {
        bool bResult = false;
        AddSampleToNextState(m_qNextExecSampleInfoVect, SEST_WAIT_SAMPLE_CATCH, bResult, true);
    }

    return quCatchSize;
}

quint8 SampleControl::GetNextInternalStandardCatchAndMixSize()
{
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    m_qNextExecSampleInfoVect.clear();
    quint8 quCatchSize = 0;
GET_NEXT:
    switch(m_uiCatchType)
    {
    case CT_SINGLE:
    {
        //在上一个操作的基础上,下一个操作对象行索引不变，列索引加1
        quint8 quRowIndex = (m_sCurBatch.iCurOpRowIndex>=0) ? m_sCurBatch.iCurOpRowIndex : 0;
        quint8 quColumnIndex = m_sCurBatch.iCurOpColumnIndex+1;
        int j =  quColumnIndex;
        qDebug()<<"GetNextInternalStandardCatchAndMixSize single:"<<quRowIndex<<quColumnIndex;
        for(int i = quRowIndex;i<SAMPLE_ROW_SIZE;i++)
        {
            for(;j<SAMPLE_COLUMN_SIZE;j++)
            {
                if(m_sCurBatch.qSampleInfos[i][j].bExist && 
                   m_sCurBatch.qSampleInfos[i][j].bIsStandard)// 获取内标试管
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[i][j]);
                    m_sCurBatch.iCurOpColumnIndex = j;
                    m_sCurBatch.iCurOpRowIndex = i;
                    i = SAMPLE_ROW_SIZE;
                    j = SAMPLE_COLUMN_SIZE;
                    break;
                }
            }
            if(quCatchSize<0)
            {
                j=0;
            }
            else
            {
                break;
            }
        }

        break;
    }
    case CT_DOUBLE:
    {
        quint8 quRowIndex = (m_sCurBatch.iCurOpRowIndex>=0) ? m_sCurBatch.iCurOpRowIndex+1 : 0;
        quint8 quColumnIndex = 0;
        bool bAdd = false;
        while(quRowIndex<SAMPLE_ROW_SIZE&&quColumnIndex<SAMPLE_COLUMN_SIZE)
        {
DOUBLE_BEGIN:
            if(quRowIndex<SAMPLE_ROW_SIZE&&quColumnIndex<SAMPLE_COLUMN_SIZE)
            {
                if(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bExist &&         // 获取内标试管
                   m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bIsStandard && 
                   !m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bIsStandardAdd)
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex]);
                    m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bIsStandardAdd =  true;
                    bAdd = true;
                }

                if((quColumnIndex+1)<SAMPLE_COLUMN_SIZE && 
                   m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bExist &&      // 获取内标试管
                   m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bIsStandard &&
                   !m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bIsStandardAdd && 
                   !bAdd)// 确保只添加一个内标样本(不能同时添加两个样本，因为不能确保提取条中每个样本都需要当前两个内标)
                {
                    quCatchSize++;
                    m_qNextExecSampleInfoVect.push_back(m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1]);
                    m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bIsStandardAdd =  true;
                }

                if(quCatchSize>0)
                {
                    if (m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex].bIsStandardAdd && 
                        m_sCurBatch.qSampleInfos[quRowIndex][quColumnIndex+1].bIsStandardAdd)// 同一行内标都添加之后才继续累加
                    {
                        m_sCurBatch.iCurOpColumnIndex = quColumnIndex;
                        m_sCurBatch.iCurOpRowIndex = quRowIndex;
                    }
                    break;
                }
                else
                {
                    quColumnIndex++;
                    if(quColumnIndex>=SAMPLE_COLUMN_SIZE)
                    {
                        quRowIndex++;
                        quColumnIndex=0;
                    }
                    goto DOUBLE_BEGIN;
                }
            }
        }
        break;
    }
    default:
        break;

    }
    qDebug()<<"GetNextInternalStandardCatchAndMixSize:"<<quCatchSize;

    if(quCatchSize ==0)
    {
        if(_GetNextWaitExecBatch(false))
        {
            goto GET_NEXT;
        }
    }

    if(quCatchSize>0)
    {
        bool bResult = false;
        AddSampleToNextState(m_qNextExecSampleInfoVect, SEST_WAIT_SAMPLE_CATCH, bResult, true);
    }

    return quCatchSize;
}

QVector<SampleInfo> SampleControl::GetNextWaitCatchAndMixSampleInfos()
{
    qDebug()<<"GetNextWaitCatchAndMixSampleInfos"<<m_qNextExecSampleInfoVect.size();
    return  m_qNextExecSampleInfoVect;
}



bool SampleControl::UpdateSystemBuildExecSTToNext(quint8 uiCurExecST, quint8 uiNextExecST)
{
    bool bResult = false;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    qDebug()<<"UpdateSystemBuildExecSTToNext"<<uiCurExecST<<"->"<<uiNextExecST<<kObjectID[uiCurExecST] <<"->"<<kObjectID[uiNextExecST]<<m_qSystemBuildSTMap.keys();
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if (itor != m_qSystemBuildSTMap.end())
    {
        auto pQueue = &itor.value();
        if (pQueue && pQueue->size()>0)
        {
            QVector<SystemBuildInfo> qVect = pQueue->front();
            _AddSystemBuildToNextStateNoLock(qVect, uiNextExecST, bResult);
            pQueue->pop_front();
        }
    }

    return bResult;
}


bool SampleControl::UpdateSystemBuildExecSTToNext(quint8 uiCurExecST, QVector<quint8> qNextExecSTVect)
{
    bool bResult = false;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if(itor != m_qSystemBuildSTMap.end())
    {
        auto pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            QVector<SystemBuildInfo> qVect = pQueue->front();
            for(int i =0;i<qNextExecSTVect.size();i++)
            {
                qDebug()<<"UpdateSystemBuildExecSTToNext"<<uiCurExecST<<"->"<<qNextExecSTVect.at(i)<< kObjectID[uiCurExecST] <<"->"<<kObjectID[qNextExecSTVect.at(i)];
                _AddSystemBuildToNextStateNoLock(qVect, qNextExecSTVect.at(i), bResult);
            }
            pQueue->pop_front();
        }
    }
    return bResult;
}

bool SampleControl::GetCurExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo>& qVect)
{
    bool bResult = false;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if(itor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            qVect = pQueue->front();
            bResult = true; 
        }
    }
    qDebug()<<"SampleControl::GetCurExecSystemBuildInfo: "<<bResult<<uiCurExecST<<m_qSystemBuildSTMap.keys();
    return bResult;
}

bool SampleControl::GetLatestExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo>& qVect)
{
    bool bResult = false;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if(itor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            qVect = pQueue->back();
            bResult = true; 
        }
    }
    qDebug()<<"SampleControl::GetCurExecSystemBuildInfo: "<<bResult<<uiCurExecST<<m_qSystemBuildSTMap.keys();
    return bResult;
}

bool SampleControl::GetWaitAbandonExecSystemBuildInfo(QVector<SystemBuildInfo> &qVect)
{
    bool bResult = false;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    if (!m_qSystemBuildInfoBeforePCRAbandon.isEmpty())
    {
        qVect = m_qSystemBuildInfoBeforePCRAbandon.dequeue();
        bResult = true;
    }
    
    qDebug()<<"SampleControl::GetWaitAbandonExecSystemBuildInfo: "<<bResult<<qVect.size()<<m_qSystemBuildInfoBeforePCRAbandon.size();
    return bResult;
}

bool SampleControl::UpdateCurExecSystemBuildInfo(quint8 uiCurExecST, QVector<SystemBuildInfo> &qVect)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    bool bResult = false;
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    qDebug()<<"SampleControl::UpdateCurExecSystemBuildInfo"<<uiCurExecST;
    if(itor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()>0)
        {
            pQueue->replace(0, qVect);
            bResult = true;
        }
    }
    return bResult;
}

bool SampleControl::IsAllSystemBuildSpecificActionDone(quint8 uiCurExecST)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    bool bResult = true;
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if(itor != m_qSystemBuildSTMap.end())
    {
        bResult = false;
    }
    qDebug()<<"SampleControl::IsAllSystemBuildSpecificActionDone"<<bResult<<uiCurExecST<<m_qSystemBuildSTMap.keys();
    return bResult;
}

bool SampleControl::IsCurBatchSystemBuildSpecificActionDone(quint8 uiCurExecST,const QString strBatchNo)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    bool bResult = false;
    qint8 iCurActionSize = 0;
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    qDebug()<<"SampleControl::IsCurBatchSystemBuildSpecificActionDone"<<uiCurExecST;
    if(itor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
        if(pQueue)
        {
            qDebug()<<"SampleControl::IsCurBatchSystemBuildSpecificActionDone size"<<pQueue->size();
        }
        
        if(pQueue && pQueue->size()>0)
        {
            for(const auto& vec : *pQueue) 
            {
                iCurActionSize += std::count_if(vec.cbegin(), vec.cend(),
                    [&strBatchNo](const SystemBuildInfo& info){
                        return info.strBatchNo == strBatchNo;
                });
            }
        }
    }
    if(m_uiCurBatchSystemBuildSize == iCurActionSize)
    {
        bResult = true;
    }

    qDebug()<<"SampleControl::IsCurBatchSystemBuildSpecificActionDone"<<m_uiCurBatchSystemBuildSize<<iCurActionSize<<bResult;
    return bResult;
}

bool SampleControl::IsCurBatchSystemBuildSpecificActionDone(const QString strBatchNo)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    bool bResult = true;
    
    // 遍历 PCR 系统构建信息
    if (m_qSystemBuildSTMap.contains(SEST_WAIT_ABANDON))
    {
        QQueue<QVector<SystemBuildInfo>> qAbandon = m_qSystemBuildSTMap[SEST_WAIT_ABANDON];
        while (!qAbandon.isEmpty()) {
            const QVector<SystemBuildInfo>& batchInfos = qAbandon.dequeue();
            // 检查批次号是否匹配（假设每组信息的第一个元素包含批次信息）
            if (!batchInfos.isEmpty() && batchInfos[0].strBatchNo == strBatchNo) {
                bResult = false;
                break;
            }
        }        
    }
    qDebug()<<"SampleControl::IsCurBatchSystemBuildSpecificActionDone"<<strBatchNo<<bResult<<m_qSystemBuildInfoBeforePCRAbandon.size();
    return bResult;
}

bool SampleControl::IsAllBatchSystemBuildSpecificActionDone(const QString strBatchNo)
{
    bool bResult = false;
    if (IsCurBatchSystemBuildSpecificActionDone(strBatchNo))
    {
        if (m_qBatchNo.contains(strBatchNo))
        {
            QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
            m_qBatchNo.remove(strBatchNo);
        }
    }

    if (m_qBatchNo.isEmpty())
    {   
        bResult = true;
    }    
    qDebug()<<"SampleControl::IsAllBatchSystemBuildSpecificActionDone"<<bResult<<m_qBatchNo<<strBatchNo;
    return bResult;
}

void SampleControl::RearraySystemBuildInfoBeforePCRAbandon(quint16 uiPCRIndex)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    QQueue <QVector<SystemBuildInfo> > qSrcQueue;
    auto itor = m_qSystemBuildSTMap.find(SEST_WAIT_PCR_AMPLIFY);
    if(itor != m_qSystemBuildSTMap.end())
    {
        // m_qSystemBuildSTMap[SEST_WAIT_ABANDON] =  itor.value();
        // qSrcQueue.clear();
        // m_qSystemBuildSTMap[SEST_WAIT_PCR_AMPLIFY] = qSrcQueue;//Empty Queue

        QString strBatchInfo = PCRResource::getInstance().GetRecordPCRAreaBatchInfo(uiPCRIndex);
        // 需要根据pcr区域(批次),找到对应的批次,然后将批次内的所有样本重新排序
        auto& qWaitPcrQueue = m_qSystemBuildSTMap[SEST_WAIT_PCR_AMPLIFY];
        // 遍历队列，将匹配的元素添加到m_qSystemBuildInfoBeforePCRAbandon中，其余元素保留在qWaitPcrQueue中
        QQueue<QVector<SystemBuildInfo>> tempQueue;
        while (!qWaitPcrQueue.isEmpty()) {
            QVector<SystemBuildInfo> pcrInfo = qWaitPcrQueue.dequeue();
            if (pcrInfo[0].strBatchNo == strBatchInfo) {
                m_qSystemBuildInfoBeforePCRAbandon.enqueue(pcrInfo);
                m_qSystemBuildSTMap[SEST_WAIT_ABANDON].enqueue(pcrInfo);// 每次执行这个函数存在上次还没清除完成的pcr管帽，执行状态转换需要 
            } else {
                tempQueue.enqueue(pcrInfo);
            }
        }
        qWaitPcrQueue = tempQueue;  // 回写未匹配的元素
        qDebug()<<"RearraySystemBuildInfoBeforePCRAbandon qWaitPcrQueue:"<<qWaitPcrQueue.size()<<tempQueue.size()<<strBatchInfo;
    }
    qDebug()<<"SampleControl::RearraySystemBuildInfoBeforePCRAbandon"<<m_qSystemBuildInfoBeforePCRAbandon.size()<<uiPCRIndex;
}

void SampleControl::_GroupByTecName(
        QQueue<QVector<SystemBuildInfo>>& qSrcQueue,
        QMap<QString, QVector<SystemBuildInfo>>& qTecSystemBuildMap)
{

    while (!qSrcQueue.isEmpty())
    {
        // 从队列中取出一个QVector<SystemBuildInfo>
        QVector<SystemBuildInfo> qCurrentVect = qSrcQueue.dequeue();

        for (const SystemBuildInfo& info : qCurrentVect)
        {
            // 获取当前样本的strTecName
            QString strTecName = info.strTecName;

            // 如果QMap中已有该tecName，则直接将当前info添加到对应的QVector中
            if (qTecSystemBuildMap.contains(strTecName))
            {
                qTecSystemBuildMap[strTecName].append(info);
            }
            else
            {
                // 否则，为该tecName创建一个新的QVector，并将info添加进去
                QVector<SystemBuildInfo> qNewVect;
                qNewVect.append(info);
                qTecSystemBuildMap.insert(strTecName, qNewVect);
            }
        }
    }
    qDebug()<<"_GroupByTecName size:"<<qSrcQueue.size()<<qTecSystemBuildMap.size()<<qTecSystemBuildMap.values().size();
}

void SampleControl::_ReorganizeWithPriorityAdjacentPairing(
        QMap<QString, QVector<SystemBuildInfo>>& qTecSystemBuildMap,
        QQueue<QVector<SystemBuildInfo>>& qDstQueue)
{
    for (auto it = qTecSystemBuildMap.begin();  it != qTecSystemBuildMap.end(); ++it)
    {
        QVector<SystemBuildInfo>& qVect = it.value();
        qDebug()<<"_ReorganizeWithPriorityAdjacentPairing qVect"<<qVect.size()<<it.key();
        
        // 修改判断单样本的规则，只有一个样本也按照新规则转移
        QString strSampleID = "";
        QList<QString> qSampleIDList;
        for (auto& info : qVect)
        {
            if (strSampleID != info.strSampleID)
            {
                qSampleIDList.append(info.strSampleID);
                strSampleID = info.strSampleID;
            }
        }
        qDebug()<<"_ReorganizeWithPriorityAdjacentPairing qSampleIDList"<<qSampleIDList.size();

        // 先判断是不是基数样本，如果是基数样本，则需要把最后一个基数样本的数据单独提取出来，再处理
        QString strLastOddSampleID = "";
        QVector<SystemBuildInfo> qOddVect;
        if(qSampleIDList.size() % 2 == 1)
        {
            // 获取最后一个基数样本的样本编号
            strLastOddSampleID = qVect.last().strSampleID;
            
            // 使用std::copy_if找出和这个样本编号相同的样本，然后单独提取出来
            std::copy_if(qVect.begin(), qVect.end(), std::back_inserter(qOddVect),
                        [&strLastOddSampleID](const SystemBuildInfo& item) {
                            return item.strSampleID == strLastOddSampleID;
                        });
        }
        
        // 移除最后一个基数样本(需要判断strLastOddSampleID是否为空，不为空，开始移除和 strLastOddSampleID 样本编号相同的样本)
        if(!strLastOddSampleID.isEmpty())
        {
            // 使用std::remove_if移除所有与strLastOddSampleID相同的样本
            auto newEnd = std::remove_if(qVect.begin(), qVect.end(),
                                        [&strLastOddSampleID](const SystemBuildInfo& item) {
                                            return item.strSampleID == strLastOddSampleID;
                                        });
            qVect.erase(newEnd, qVect.end());
        }
        // 优先处理相邻元素配对
        int i = 0;
        while (i < qVect.size())
        {
            bool foundAdjacent = false;
            
            if (i + 1 < qVect.size())
            {
                const PosInfo& firstTubePos = qVect[i].tubePos;
                const PosInfo& secondTubePos = qVect[i + 1].tubePos;

                qDebug()<<"_ReorganizeWithPriorityAdjacentPairing TubePos1:"<<qVect[i].strSampleID<<firstTubePos.uiAreaIndex<<firstTubePos.uiRowIndex<<firstTubePos.uiColumnIndex<<qVect[i].uiAmplifyCompIndex;
                qDebug()<<"_ReorganizeWithPriorityAdjacentPairing TubePos2:"<<qVect[i+1].strSampleID<<secondTubePos.uiAreaIndex<<secondTubePos.uiRowIndex<<secondTubePos.uiColumnIndex<<qVect[i+1].uiAmplifyCompIndex;

                // 确保在同一区域和同一行
                if (firstTubePos.uiRowIndex == secondTubePos.uiRowIndex &&
                    qAbs(firstTubePos.uiColumnIndex - secondTubePos.uiColumnIndex) == 1)
                {
                    if(firstTubePos.uiColumnIndex < secondTubePos.uiColumnIndex)
                    {
                        qDstQueue.enqueue({qVect[i], qVect[i + 1]});
                    }
                    else
                    {
                        qDstQueue.enqueue({qVect[i+1], qVect[i]});
                    }
                    qDebug()<<"_ReorganizeWithPriorityAdjacentPairing seq:"<<firstTubePos.uiColumnIndex<<secondTubePos.uiColumnIndex<<(firstTubePos.uiColumnIndex<secondTubePos.uiColumnIndex);
                    qVect.remove(i, 2); // 移除已配对的两个元素
                    foundAdjacent = true;
                    // 移除元素后，不增加i，因为新元素已经移到当前位置
                    // 重新开始寻找相邻配对，从头开始以确保不错过任何可能的配对
                    i = 0;
                }
            }
            
            if (!foundAdjacent)
            {
                ++i; // 没有找到相邻配对，检查下一个位置
            }
        }



        // 对剩余元素进行两两配对
        while (qVect.size() >= 2)
        {
            QVector<SystemBuildInfo> pairGroup;
            pairGroup << qVect.takeFirst();
            pairGroup << qVect.takeFirst();
            qDebug()<<"_ReorganizeWithPriorityAdjacentPairing TubePos3:"<<pairGroup[0].strSampleID<<pairGroup[0].tubePos.uiAreaIndex<<pairGroup[0].tubePos.uiRowIndex<<pairGroup[0].tubePos.uiColumnIndex<<pairGroup[0].uiAmplifyCompIndex;
            qDebug()<<"_ReorganizeWithPriorityAdjacentPairing TubePos4:"<<pairGroup[1].strSampleID<<pairGroup[1].tubePos.uiAreaIndex<<pairGroup[1].tubePos.uiRowIndex<<pairGroup[1].tubePos.uiColumnIndex<<pairGroup[1].uiAmplifyCompIndex;
            qDstQueue.enqueue(pairGroup);
        }

        // 处理基数样本(qVect不存在单个样本情况)
        qDebug()<<"_ReorganizeWithPriorityAdjacentPairing qOddVect"<<qOddVect.size();
        _HandlePairingBaseSample(qOddVect,qDstQueue);

        // 处理完当前qVect的所有元素后，检查并标记qDstQueue中最后一个元素
        if (!qDstQueue.isEmpty())
        { // 确保队列不为空再操作
            QVector<SystemBuildInfo>& lastPair = qDstQueue.back(); // 获取队列中的最后一组
            if (lastPair.size() > 0)
            { // 确保这一组内有元素
                lastPair.last().bLastOneInCurTecBatch = true; // 标记为最后一个
                qDebug()<<"_ReorganizeWithPriorityAdjacentPairing bLastOneInCurTecBatch"<<lastPair.last().strSampleID<<qDstQueue.size();
            }
        }
    }
    qDebug()<<"_ReorganizeWithPriorityAdjacentPairing"<<qDstQueue.size()<<qTecSystemBuildMap.size();
}

void SampleControl::_HandlePairingBaseSample(QVector<SystemBuildInfo>& qOddVect,QQueue<QVector<SystemBuildInfo>>& qDstQueue)
{
    // 处理基数样本
    if(qOddVect.isEmpty())
    {
        qDebug()<<"_HandlePairingBaseSample qOddVect is empty";
        return;
    }  
    
    // 先按行号排序，确保同一行的样本在一起
    std::sort(qOddVect.begin(), qOddVect.end(), [](const SystemBuildInfo& a, const SystemBuildInfo& b) {
        quint8 aRow = a.tubePos.uiRowIndex;
        quint8 bRow = b.tubePos.uiRowIndex;
        if (aRow != bRow) {
            return aRow > bRow;  // 行大的在前
        }
        // 同一行内按列号排序
        return a.tubePos.uiColumnIndex > b.tubePos.uiColumnIndex;  // 列大的在前
    });
    
    QList<QVector<SystemBuildInfo>> qOddVectList;
     // 用while循环处理每一行(单个样本最多只有三行)
     // 需要区分pcr管区域，如果区域不一样，行号一样，也判断为不同行
    while (!qOddVect.isEmpty()) {
        quint8 currentRow = qOddVect[0].tubePos.uiRowIndex;
        quint8 currentArea = qOddVect[0].tubePos.uiAreaIndex;
        
        // 使用std::remove_if找出当前行的所有样本，更安全
        QVector<SystemBuildInfo> currentRowSamples;
        std::copy_if(qOddVect.begin(), qOddVect.end(), std::back_inserter(currentRowSamples),
                    [currentRow, currentArea](const SystemBuildInfo& item) {
                        return (item.tubePos.uiRowIndex) == currentRow && item.tubePos.uiAreaIndex == currentArea;
                    });
        
        // 移除当前行的样本
        auto newEnd = std::remove_if(qOddVect.begin(), qOddVect.end(),
                                    [currentRow, currentArea](const SystemBuildInfo& item) {
                                        return (item.tubePos.uiRowIndex) == currentRow && item.tubePos.uiAreaIndex == currentArea;
                                    });
        qOddVect.erase(newEnd, qOddVect.end());

        qOddVectList.append(currentRowSamples);                 
        qDebug()<<"_HandlePairingBaseSample currentRow"<<currentRowSamples.size()<<currentRow<<currentArea;
    }

    // 对qOddVectList进行排序，排序规则为：组分数uiAmplifyCompIndex小的在前
    if (qOddVectList.size() >1)// 边界条件检查缺失
    {
        std::sort(qOddVectList.begin(), qOddVectList.end(), [](const QVector<SystemBuildInfo>& a, const QVector<SystemBuildInfo>& b) {
            if (a.isEmpty() || b.isEmpty()) {
                qDebug() << "Warning: Empty vector found in qOddVectList";
                return !a.isEmpty(); // 非空的排在前面
            }
            
            // 找到每个向量中uiAmplifyCompIndex的最小值进行比较（更高效）
            auto minA = std::min_element(a.begin(), a.end(), [](const SystemBuildInfo& x, const SystemBuildInfo& y) {
                return x.uiAmplifyCompIndex < y.uiAmplifyCompIndex;
            });
            auto minB = std::min_element(b.begin(), b.end(), [](const SystemBuildInfo& x, const SystemBuildInfo& y) {
                return x.uiAmplifyCompIndex < y.uiAmplifyCompIndex;
            });
            
            return minA->uiAmplifyCompIndex < minB->uiAmplifyCompIndex;
        });
    }
    
    // 对qOddVectList进行两两配对，一行剩下只有一个样本的，单独处理
    QList<QVector<SystemBuildInfo>> qVectListSingleSample;
    for(QVector<SystemBuildInfo>& currentRowSamples : qOddVectList)
    {
        qDebug()<<"_HandlePairingBaseSample currentRowSamples"<<currentRowSamples.size();
        // 处理当前行的样本
        // 先进行两两配对
        while (currentRowSamples.size() >= 2) {
            QVector<SystemBuildInfo> pairGroup;
            pairGroup << currentRowSamples.takeFirst();
            pairGroup << currentRowSamples.takeFirst();
            // 右移液泵为原点，多以需要将pairGroup[0]中的样本的tubePos.uiColumnIndex 和 pairGroup[1]中的样本的tubePos.uiColumnIndex 进行交换
            PosInfo tempPos0 = pairGroup[0].tubePos;
            pairGroup[0].tubePos = pairGroup[1].tubePos;
            pairGroup[1].tubePos = tempPos0;

            PosInfo tempPos1 = pairGroup[0].tubeCapPos;
            pairGroup[0].tubeCapPos = pairGroup[1].tubeCapPos;
            pairGroup[1].tubeCapPos = tempPos1;

            qDebug()<<"_HandlePairingBaseSample pairGroup1"<<pairGroup[0].strSampleID<<pairGroup[0].tubePos.uiAreaIndex<<pairGroup[0].tubePos.uiRowIndex<<pairGroup[0].tubePos.uiColumnIndex<<pairGroup[0].uiAmplifyCompIndex;
            qDebug()<<"_HandlePairingBaseSample pairGroup2"<<pairGroup[1].strSampleID<<pairGroup[1].tubePos.uiAreaIndex<<pairGroup[1].tubePos.uiRowIndex<<pairGroup[1].tubePos.uiColumnIndex<<pairGroup[1].uiAmplifyCompIndex;
            qDstQueue.enqueue(pairGroup);
        }
    
        // 处理剩余的单个样本
        while (!currentRowSamples.isEmpty()) {
            qVectListSingleSample.append({currentRowSamples.takeFirst()});
        }                        
    }   

    // // 对qVectListSingleSample进行两两配对，需要按列小排序
    // std::sort(qVectListSingleSample.begin(), qVectListSingleSample.end(), [](const QVector<SystemBuildInfo>& a, const QVector<SystemBuildInfo>& b) {
    //     return a[0].tubePos.uiColumnIndex < b[0].tubePos.uiColumnIndex;
    // });

    // 屏蔽不同行两两配对，按照单个处理
    // while (qVectListSingleSample.size() >= 2) {
    //     QVector<SystemBuildInfo> pairGroup;
    //     pairGroup << qVectListSingleSample.takeFirst();
    //     pairGroup << qVectListSingleSample.takeFirst();
    //     qDstQueue.enqueue(pairGroup);
    // }

    // 处理剩余的单个样本
    while (!qVectListSingleSample.isEmpty()) {
        qDstQueue.enqueue({qVectListSingleSample.takeFirst()});
    }

    qDebug()<<"_HandlePairingBaseSample qDstQueue"<<qDstQueue.size();
}

QString SampleControl::getCurBatchExtractProgram()
{
    return m_sCurBatch.strExtract;
}

void SampleControl::RearraySystemBuildInfoByTecType(QQueue<QVector<SystemBuildInfo>>& qQueue)
{
    QMutexLocker  qSysLocker(&m_qSytemBuildSTMutex);
    QQueue <QVector<SystemBuildInfo> > qSrcQueue;
    auto itor = m_qSystemBuildSTMap.find(SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE);
    if(itor != m_qSystemBuildSTMap.end())
    {
        QMap<QString,QVector<SystemBuildInfo>>  qTecSystemBuildMap;
        QQueue <QVector<SystemBuildInfo> > qDstQueue;
        qSrcQueue = itor.value();
        _GroupByTecName(qSrcQueue, qTecSystemBuildMap);
        _ReorganizeWithPriorityAdjacentPairing(qTecSystemBuildMap, qDstQueue);
        qSrcQueue.clear();
        m_qSystemBuildSTMap[SEST_WAIT_CLOSE_TUBE_CAP_AND_TRANS_TUBE] =  qDstQueue;
        qQueue = qDstQueue;
    }
}

bool SampleControl::IsExistSingleOperateTestInRemainQueue(quint8 uiCurExecST)
{
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    bool bExist = false;
    for(int j=0;j<=uiCurExecST;j++)
    {
        auto itor = m_qSystemBuildSTMap.find(j);
        if(itor != m_qSystemBuildSTMap.end())
        {
            QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
            if(pQueue && pQueue->size()>0)
            {
                int i=0;
                if(j == uiCurExecST)//需要除去当前状态的一个待测试才是剩下的
                    i = 1;
                for(; i<pQueue->size(); i++)
                {
                    if(pQueue->at(i).size() == 1)
                    {
                        bExist = true;
                        break;
                    }
                }
            }
        }
    }
    return bExist;
}

bool SampleControl::IsCurTestTheLastOneInSpecST(quint8 uiCurExecST, quint8& uiRemainOpSize)
{
    bool bLastOne = false;
    uiRemainOpSize = 0;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    auto itor = m_qSystemBuildSTMap.find(uiCurExecST);
    if(itor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
        if(pQueue && pQueue->size()==1)
        {
            bLastOne = true;
        }
        uiRemainOpSize = pQueue->size();
    }
    return bLastOne;
}

bool SampleControl::IsCurTestThelastOpOneInBatch(quint8 uiCurExecST)
{
    bool bLastOne = false;
    quint8 uiPreSTRemainTstSize = 0;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    for(int iST=0;iST<=uiCurExecST;iST++)
    {
        auto itor = m_qSystemBuildSTMap.find(iST);
        if(itor != m_qSystemBuildSTMap.end())
        {
            QQueue <QVector <SystemBuildInfo>>* pQueue = &itor.value();
            if(pQueue->size()>0)
            {
                uiPreSTRemainTstSize += pQueue->size();
            }

        }
    }
    if(uiPreSTRemainTstSize == 1)
    {
        bLastOne = true;
    }
    return bLastOne;
}

QMap<quint8, QString> SampleControl::GetCurBatchPCRAreaUsingList(const QString strBatchNo)
{
    //PCR区域索引= RowIndex*2+ColumnIndex
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    if (!m_qSystemBuildSTMap.contains(SEST_WAIT_PCR_AMPLIFY)) {
        return {};
    }
    QQueue<QVector<SystemBuildInfo>> qQueue = m_qSystemBuildSTMap[SEST_WAIT_PCR_AMPLIFY];
    QMap<quint8, QString> qUniquePCRMap= {};

    while (!qQueue.isEmpty()) {
        const QVector<SystemBuildInfo> &buildInfos = qQueue.dequeue();
        for (const SystemBuildInfo &info : buildInfos) {
            if (info.strBatchNo != strBatchNo)// 获取相同批次
            {
                continue;
            }

            const PosInfo &pcrAreaPos = info.pcrAreaPos;
            quint8 uiPCRAreaIndex = pcrAreaPos.uiRowIndex * 2 + pcrAreaPos.uiColumnIndex;          
            if (!qUniquePCRMap.contains(uiPCRAreaIndex)) {
                qUniquePCRMap.insert(uiPCRAreaIndex, info.strTecName);
            }
            qDebug()<<"GetCurBatchPCRAreaUsingList uiPCRAreaIndex: "<<uiPCRAreaIndex<<info.strTecName<<info.strBatchNo;
        }
    }

    qDebug()<<"GetCurBatchPCRAreaUsingList keys: "<<qUniquePCRMap.keys()<<strBatchNo;
    return qUniquePCRMap;

}

QString SampleControl::GetAllPCRInfoString(const QString strBatchNo)
{
    //数据组织格式：孔id1,样本id,项目货号,项目组分id;....;
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    QString strPCRInfos = "";
    if (!m_qSystemBuildSTMap.contains(SEST_WAIT_PCR_AMPLIFY)) {
        return strPCRInfos;
    }
    QQueue<QVector<SystemBuildInfo>> qQueue = m_qSystemBuildSTMap[SEST_WAIT_PCR_AMPLIFY];

    quint8 u8PCRSubAreaSize = PCRResource::getInstance().GetPCRSubAreaSize();
    u8PCRSubAreaSize = u8PCRSubAreaSize * PCR_MODULE_SIZE;//孔位

    QStringList vPCRInfos;
    
    qDebug()<<"GetAllPCRInfoString"<<qQueue.size()<<m_qSystemBuildSTMap.keys()<<strBatchNo;
    while (!qQueue.isEmpty()) {
        const QVector<SystemBuildInfo> &buildInfos = qQueue.dequeue();
        qDebug()<<"GetAllPCRInfoString buildInfos.size"<<buildInfos.size();
        for (const SystemBuildInfo &info : buildInfos) {
            if (info.strBatchNo != strBatchNo)// 获取相同批次
            {
                continue;
            }
            
            QDFUN_LINE << "info.pcrAreaPos.uiRowIndex:" << info.pcrAreaPos.uiRowIndex << "info.pcrAreaPos.uiColumnIndex:" << info.pcrAreaPos.uiColumnIndex
                       << "info.pcrPos.uiRowIndex:" << info.pcrPos.uiRowIndex << "info.pcrPos.uiColumnIndex:" << info.pcrPos.uiColumnIndex
                       <<  "info.uiHoleIndex:" << info.uiHoleIndex << "info.uiAmplifyCompIndex" << info.uiAmplifyCompIndex;
            // // 单个pcr管(现在时序使用Z1轴转移pcr管，会造成pcr区域跳一个孔位，有空隙，所以测试信息要跳一个孔位)
            // if(buildInfos.size() == 1)
            // {
            //     uiHoleIndex += buildInfos.size();
            // }

            strPCRInfos = QString("%1,%2,%3,%4").arg(info.uiHoleIndex).arg(info.strSampleID).arg(info.strProjID).arg(info.uiAmplifyCompIndex);
            if(info.uiHoleIndex < u8PCRSubAreaSize)
            {
                vPCRInfos.append(strPCRInfos);
            }
        }
    }
    strPCRInfos = vPCRInfos.join(";");
    // [流水号:孔id1,样本id,项目货号,项目组分id;孔id2,样本id,项目货号,项目组分id;.....;孔idN,样本id,项目货号,项目组分id]（只需要给当前流水号用到的孔信息即可，不用64个）
    strPCRInfos = strBatchNo + ":" +strPCRInfos;
    return strPCRInfos;
}

void SampleControl::ClearSampleSTMap()
{
    QMutexLocker qLocker(&m_qSampleSTMutex);
    m_qSampleSTMap.clear();
}

bool SampleControl::isCurOpSamplesSingleRight()
{
    return  m_bCurOpSingleRightSample;
}

void SampleControl::ClearCurBatchData()
{
    qDebug()<<"ClearCurBatchData";
    ClearSampleSTMap();
    QMutexLocker qSysReagentLocker(&m_qSystemBuildReagentMutex);
    m_qSytemBuildReagentMap.clear();
    QMutexLocker qSysLocker(&m_qSytemBuildSTMutex);
    m_qSystemBuildSTMap.clear();
    QMutexLocker qBatchLocker(&m_qBatchMutex);
    m_qNextExecSampleInfoVect.clear();
    m_sCurBatch.bExist = false;
    m_strCurBatchSamples = "";
    m_qProjectStripIndexMap.clear();
    m_qSystemBuildInfoBeforePCRAbandon.clear();
    m_qBatchNo.clear();
    m_sCurBatch.iCurOpRowIndex = -1;
    m_sCurBatch.iCurOpColumnIndex = -1; 
}

void SampleControl::_AddSystemBuildToNextStateNoLock(QVector<SystemBuildInfo>& qVect, quint8 uiNextExecST, bool& bResult)
{
    qDebug()<<"SampleControl::_AddSystemBuildToNextStateNoLock"<<uiNextExecST<<qVect.size()<<m_qSystemBuildSTMap.size()<<m_qSystemBuildSTMap.keys();
    if(uiNextExecST == SEST_WAIT_PCR_AMPLIFY)
    {
        qDebug()<<"PCR AMPLIFY ST add new elem.";
    }
    auto nextItor = m_qSystemBuildSTMap.find(uiNextExecST);
    if(nextItor != m_qSystemBuildSTMap.end())
    {
        QQueue <QVector <SystemBuildInfo>>* pNextQueue = &nextItor.value();
        if(pNextQueue)
        {
            pNextQueue->push_back(qVect);
            bResult = true;
        }  
        qDebug()<<"SampleControl::_AddSystemBuildToNextStateNoLock update";
    }
    else
    {
        QQueue <QVector <SystemBuildInfo>> pNextQueue;
        pNextQueue.push_back(qVect);
        m_qSystemBuildSTMap[uiNextExecST] = pNextQueue;
        bResult = true;    
        qDebug()<<"SampleControl::_AddSystemBuildToNextStateNoLock add"<<kObjectID[uiNextExecST];
    }
    for (auto info : qVect)
    {
        qDebug()<<"SampleControl::_AddSystemBuildToNextStateNoLock qVect"<<info.strTecName<<info.strProjID<<info.strSampleID;
    }    
}

void SampleControl::AddSystemBuildToNextState(QVector<SystemBuildInfo> &qVect, quint8 uiNextExecST, bool &bResult, bool bNeedLocker)
{
    if(bNeedLocker)
    {
        QMutexLocker qLocker(&m_qSytemBuildSTMutex);
        _AddSystemBuildToNextStateNoLock(qVect, uiNextExecST, bResult);
    }
    else
    {
        _AddSystemBuildToNextStateNoLock(qVect, uiNextExecST, bResult);
    }
}

bool SampleControl::_UpdateProjectStripIndex(const QVector<ProjectInfo>& vProjectInfo, const quint8& uiStripIndex)
{
    for (auto strProjectInfo : vProjectInfo)
    {
        if(strProjectInfo.strProjID.isEmpty())
        {
            qDebug()<<"_UpdateProjectStripIndex project name is empty";
            continue;
        }

        if (m_qProjectStripIndexMap.contains(strProjectInfo.strProjID))
        {
            auto& vProjectStripIndex =  m_qProjectStripIndexMap[strProjectInfo.strProjID];
            if(vProjectStripIndex.count(uiStripIndex) == 0)
            {
                vProjectStripIndex.push_back(uiStripIndex);
                std::sort(vProjectStripIndex.begin(), vProjectStripIndex.end(),[](quint8 a, quint8 b) { return a < b; });
            }
        }
        else
        {
            QVector<quint8> vStripIndex;
            vStripIndex.push_back(uiStripIndex);
            m_qProjectStripIndexMap.insert(strProjectInfo.strProjID, vStripIndex);
        }        
    }
    qDebug()<<"SampleControl::_UpdateProjectStripIndex"<<vProjectInfo.size()<<uiStripIndex<<m_qProjectStripIndexMap.keys();
    return true;
}

void SampleControl::GetProjectStripIndex(const QString &strProjID, QList<quint8> &qStripIndex)
{
    QMutexLocker qLocker(&m_qSytemBuildSTMutex);
    if (m_qProjectStripIndexMap.contains(strProjID))
    {
        qStripIndex = m_qProjectStripIndexMap[strProjID].toList();
    }  
    qDebug()<<"SampleControl::GetProjectStripIndex"<<strProjID<<qStripIndex<<m_qProjectStripIndexMap.keys();  
}

bool SampleControl::UpdateProjectStripIndex(const QString &strProjID, const quint8 &uiStripIndex)
{
    QMutexLocker qLocker(&m_qSytemBuildSTMutex);
    if (m_qProjectStripIndexMap.contains(strProjID))
    {
        QVector<quint8>& vStripIndex = m_qProjectStripIndexMap[strProjID];
        vStripIndex.removeAll(uiStripIndex);
        qDebug()<<"SampleControl::UpdateProjectStripIndex"<<strProjID<<vStripIndex<<m_qProjectStripIndexMap.keys();
    }      
    return true;
}

bool SampleControl::_CheckTecTimeSeqValid(const QString& strName)
{
    QString strTecContent = CTimingDB::getInstance().getTecContentFromName(strName);
    bool bRet = !strTecContent.isEmpty();
    if(strTecContent.isEmpty())//即时序没有保存，需要上报故障 项目不一致也要报故障
    {
        // 上报故障
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceCmdParamError, QString("tec timeseq %1 isn't exist.").arg(strName));
    }    
    qDebug()<<"_CheckTecTimeSeqValid: "<<strName<<bRet;
    return bRet;
}

bool SampleControl::_CheckExtractTimeSeqValid(const QString& strName)
{
    QString strExtract = CTimingDB::getInstance().getExtractTimingUIContentFromName(strName);//提取时序
    bool bRet = !strExtract.isEmpty();
    if (strExtract.isEmpty())
    {
        // 上报故障
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Timing, FT_SequenceCmdParamError, QString("extract timeseq %1 isn't exist.").arg(strName));
    }
    
    qDebug()<<"_CheckExtractTimeSeqValid: "<<strName<<bRet;
    return bRet;
}

bool SampleControl::_CheckProjIDValid(const QString& strName)
{
    QStringList projects = CProjectInformation::getInstance().getAllProjectNames();//提取时序
    bool bRet = !projects.contains(strName);
    if (projects.contains(strName))
    {
        // 上报故障
        CErrorNotify::getInstance().addErrorInfoItem(Mid_Sub_Db, FT_Database_QueryError, QString("project id %1 isn't exist.").arg(strName));
    }
    
    qDebug()<<"_CheckProjIDValid: "<<strName<<bRet;
    return bRet;
}

void SampleControl::_DeleteStandardProjID(QSet<QString> &qStandardProjID, QSet<QString> &qProjID,BatchInfo &sBatchInfo)
{
    // 获取无效的内标项目ID
    QSet<QString> qInvalidProjIDList;
    for (auto strProjID : qStandardProjID)
    {
        if(!qProjID.contains(strProjID))
        {
            qInvalidProjIDList.insert(strProjID);
        }
    }
    qDebug()<<"_DeleteStandardProjID: "<<qInvalidProjIDList;
    
    // 把无效的内标项目从批次信息中删除(bExist置为false)
    for (int row = 0; row < SAMPLE_ROW_SIZE; ++row) // 遍历批次信息的每一行
    {
        for (int col = 0; col < SAMPLE_COLUMN_SIZE; ++col) // 遍历批次信息的每一列
        {
            // 如果当前样本存在且是内标样本
            if (sBatchInfo.qSampleInfos[row][col].bExist && sBatchInfo.qSampleInfos[row][col].bIsStandard)
            {
                // 检查当前样本的内标项目ID是否在无效的内标项目ID列表中
                if (qInvalidProjIDList.contains(sBatchInfo.qSampleInfos[row][col].strStandardProject))
                {
                    // 如果是，则将当前样本的bExist标记为false
                    sBatchInfo.qSampleInfos[row][col].bExist = false;
                    qDebug()<<"_DeleteStandardProjID bExist: "<<sBatchInfo.qSampleInfos[row][col].strStandardProject;
                }
            }
        }
    }
}

void SampleControl::ClearFinishSystemBuildInfo(const QString strBatchNo)
{
    QMutexLocker qLocker(&m_qSytemBuildSTMutex);  // 改用系统构建专用的互斥锁
    if (!m_qSystemBuildSTMap.contains(SEST_FINISH)) {
        return;
    }

    QQueue<QVector<SystemBuildInfo>>& finishQueue = m_qSystemBuildSTMap[SEST_FINISH];
    QQueue<QVector<SystemBuildInfo>> tempQueue;  // 临时队列保存需要保留的条目

    while (!finishQueue.isEmpty()) {
        QVector<SystemBuildInfo> vec = finishQueue.dequeue();
        // 过滤掉属于当前批次的条目
        QVector<SystemBuildInfo> filtered;
        for (const SystemBuildInfo& info : vec) {
            if (info.strBatchNo != strBatchNo) {
                filtered.append(info);
            }
        }
        // 只保留还有元素的vector
        if (!filtered.isEmpty()) {
            tempQueue.enqueue(filtered);
        }
    }
    
    // 更新完成状态队列
    finishQueue = tempQueue;
    qDebug() << "ClearFinishSystemBuildInfo: " << strBatchNo 
             << "Remaining items:" << finishQueue.size();
}
