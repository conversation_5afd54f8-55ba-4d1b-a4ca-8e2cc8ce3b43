#ifndef PUBLICFUNCTION_H
#define PUBLICFUNCTION_H

#include <QObject>
#include <QSqlDatabase>
#include <QIODevice>
#include <QPointF>
#include <QNetworkInterface>
#include "publicconfig.h"
#if Q_OS_QML
#include "qxlsx/xlsxdocument.h"
#endif
//#define ShortOutPutLog

/**
 * @brief getCRC16  获取CRC16
 * @param data  计算的数据
 * @param len  数据的长度
 * @param oldCRC16  上一个CRC16的值，用于循环计算大文件的CRC16。第一个数据的CRC16则传入0x0。
 * @return
 */
unsigned short GetCRC16(const char *pData, unsigned long ulLen,unsigned long ulOldCRC16 = 0);
quint16 GetSmallByte(quint16 qDate);
quint32 GetSmallByte32(quint32 qDate);

void CreateDir(QString strDir);
void Delay_MSec(uint iMSecTime);

bool ReadFile(QString strFileName,QString& strFileContext,QIODevice::OpenModeFlag flag = QIODevice::ReadOnly);
bool WriteFile(QString strFileName,QString strFileContext,QIODevice::OpenModeFlag flag = QIODevice::WriteOnly);
bool WriteFile(QString strFileName,QByteArray byteFileContext,QIODevice::OpenModeFlag flag = QIODevice::WriteOnly);
quint16 GetByte2Int(char *pByte);
quint32 GetByte4Int(char *pByte);

/**
  * @brief 连接数据库
  * @param strDBName：数据库路径名称
  * @return
  */
QSqlDatabase ConnectDataBase(const QString &kstrDBName, const QString &kstrConnectName);
// mac adress hostname: eth0, eth1, wlan0
QString GetHostMac(const QString &interfaceName);
QString GetHostIP(const QString &interfaceName);
QHostAddress getInterfaceAddress(const QString &interfaceName) ;
//
QString JoinStringFromList(QList<qreal> dData, QString strSplit);
QList<qreal> SplitDoubleFromQString(QString strData, QString strSplit);

void TestError();

std::vector<double> getYVectorFromQPontF(const QList<QPointF> &qSrc);
std::vector<double> getXVectorFromQPontF(const QList<QPointF> &qSrc);
void getXYVectorFromQPontF(const QList<QPointF> &qSrc, std::vector<double> &dXVector, std::vector<double> &dYVector);
QList<qreal> getYListFromQPontF(const QList<QPointF> &qSrc);
QList<qreal> getXListFromQPontF(const QList<QPointF> &qSrc);
void getXYListFromQPontF(const QList<QPointF> &qSrc, QList<qreal> & qXList, QList<qreal> & qYList);
QByteArray GetSendData(const SCanBusDataStruct & sSCanBusDataStruct);
QList<quint32> convertUint32ToData(quint32 iData, const QString& strPos);//strPos格式为"0-3,4-8....."
QStringList convertByteArrayToAsciiList(QByteArray qbReadBuffArray);

// 重置流程调试字符串A&B&C_D_E --> A&B&C
QString processStringAsProcessDebug(const QString &strInput);
// 单次QTimer调用
void setupTimer(int iTimeout, QObject *pReceiver, const char* pMethod);
//根据指令ID查找指令名称
QString findCommandNameById(QString id);
// 遍历文件夹，返回文件夹中所有文件名称列表
QStringList getFilesFromDirectory(const QString &strDirectoryPath);
// 升级文件拆包
std::tuple<qint64, QList<QByteArray>, QByteArray> splitUpgradeBinaryFile(const QString &filePath, qint64 packageSize);




#endif // PUBLICFUNCTION_H
