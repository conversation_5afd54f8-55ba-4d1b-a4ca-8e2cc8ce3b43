#ifndef ERRORCONFIG_H
#define ERRORCONFIG_H
#include <QString>
#include <SampleControl/samplecontrol.h>

#define ERROR_CODE_LEN 8
//// 中位机相关子模块枚举
enum  MidMachineSubmodule
{
    Mid_Sub_Upper_TCP_Comm,    // 00 上位机 TCP通信
    Mid_Sub_PCR_TCP_Comm,      // 01 PCR TCP通信
    Mid_Sub_CAN0_Comm,         // 02 CAN0 通信
    Mid_Sub_CAN1_Comm,         // 03 CAN1 通信
    Mid_Sub_Barcode_Serial_Comm,// 04 提取扫码串口通信
    Mid_Sub_Cons,              // 05 中位机耗材
    Mid_Sub_RFID,              // 06 中位机RFID
    Mid_Sub_BarcodeScan,        // 07 中位机卡条扫码
    Mid_Sub_Db,                // 08 中位机数据库
    Mid_Sub_Timing,            // 09 中位机时序
    Mid_Sub_Sys,               // 0A 中位机系统
    Mid_Sub_PwrSupply,         // 0B 中位机电源
    Mid_Sub_Mem,               // 0C 中位机内存
    Mid_Sub_Cons_Recycling1,   // 0D 中位机耗材回收1
    Mid_Sub_Cons_Recycling2,   // 0E 中位机耗材回收2
    Mid_Sub_Upgrade,           // 0F 中位机升级
    Mid_Sub_Params,            // 10 中位机参数
    Mid_Sub_FTP_Comm,          // 11 FTP 通信
    Mid_Sub_Sample_Barcode,    // 12 样本扫码串口通信
    Mid_Sub_Test_Process,      // 13 测试流程
};

enum  ErrorID
{
    //----------------------电机错误-------------------------------
    FT_ElectricShortCircuit_APhase = 1, // A相供电短路告警
    FT_ElectricShortCircuit_BPhase, // B相供电短路告警
    FT_MotorOverTemperatureAlarm, // 电机过温异常
    FT_MotorOverTemperatureWarning, // 电机过温预警
    FT_MotorCmdTimeout, // 电机指令执行超时
    FT_MotorStepLoss, // 电机运动丢步
    FT_MotorStall, // 电机堵转
    FT_DriveStatusError, // 驱动状态错误
    FT_ResetOptocouplerError, // 复位光耦状态异常
    FT_ResetOptocouplerNotFound, // 找不到复位光耦
    FT_SPICommError, // SPI通信异常
    FT_MotorMotionConflict, // 电机运动冲突
    FT_EncoderTypeError, // 编码器类型配置错误
    FT_EncoderPhaseError, // 编码器AB相序接反
    FT_EncoderConfigError, // 编码器配置错误
    FT_EncoderDeviationExceeded, // 编码器偏差超出设定阈值
    FT_MotorParamLoadFailed, // 电机配置参数加载失败
    FT_MotorMotionParamLoadFailed, // 电机运动参数加载失败
    FT_MotorCompensationParamLoadFailed, // 电机补偿参数加载失败
    FT_MotorParamSaveFailed, // 电机配置参数保存失败
    FT_MotorMotionParamSaveFailed, // 电机运动参数保存失败
    FT_MotorCompensationParamSaveFailed, // 电机补偿参数保存失败
    FT_FPGACommError, // FPGA通信异常
    //----------------------------通信类错误------------------------------
    FT_Comm_OpenFail = 101, // 通信打开失败
    FT_Comm_PacketLengthAbnormal, // 通信包长度异常
    FT_Comm_PacketCRCError, // 通信包CRC校验异常
    FT_Comm_Interrupt, // 通信中断
    FT_Comm_ConnectionTimeout, // 通信连接超时
    FT_Comm_CacheFull, // 通信缓存满
    FT_Comm_IDError, // 通信对象ID错误
    FT_Comm_Resend, // 通信重发
    FT_Comm_OtherError,//通信其他错误
    //-----------------------------耗材类错误--------------------------
    FT_Material_NotLoaded = 201, // 未装载
    FT_Material_Expired, // 已过期
    FT_Material_LowStockAlert, // 余量低于报警线
    FT_Material_StockOverflow, // 余量超过容量，请确认
    FT_Material_NotInPosition, // 装载未到位
    FT_Material_ReadError, // 耗材信息读取错误
    FT_Material_ParseError, // 耗材信息解析错误
    FT_Material_UpdateError, // 耗材信息更新错误
    FT_Material_Empty, // 已用完
    FT_Material_Loaded, // 装载（TRACE级别）
    FT_Material_Unloaded, // 卸载（TRACE级别）

    //-------------------------RFID错误   ----------------
    FT_AntennaIDConversionError = 601, // 转换天线ID异常
    FT_RFIDDataParseError_TypeMismatch, // 解析RFID数据异常，传入耗材类型不符合
    FT_RFIDDataParseError_GroupCountMismatch, // 解析RFID数据异常，组份数与组份项目名称个数不相等
    FT_RFIDDataParseError_ConverBoxTypeError, // 解析RFID数据异常，转换boxType 错误
    FT_RFIDDataParseError_DataFormatError, // 解析RFID数据异常，日期格式错误
    FT_RFIDDataWriteError_TypeMismatch, // 写入RFID数据异常，传入耗材类型不符合
    FT_RFIDDataReadTimeout, // 读取RFID数据超时
    FT_RFIDDataReadError, // 读取RFID数据失败
    FT_RFIDDataWriteTimeout, // 写入RFID数据超时
    FT_RFIDDataWriteError, // 写入RFID数据失败
    FT_RFIDInitializationError, // RFID初始化异常
    FT_RFIDTagNotExist, // RFID标签不存在
    //------------------------------扫码----------------------------
    FT_BarcodeScanner_TriggerModeError = 701, // 扫码器启动指令触发模式错误
    FT_BarcodeScanner_AutoSenseModeError, // 扫码器启动自动感应模式错误
    FT_BarcodeScanner_InitFailed, // 扫码器未成功初始化，请联系客服
    FT_BarcodeScanner_Disconnected, // 扫码器未连接，请联系客服
    FT_BarcodeScanner_ScanFailed_FastInsertion, // 未扫到样本码，插入速度过快或与扫码器兼容的码制不符
    FT_BarcodeScanner_RecognitionError, // 条码识别错误，请重新插入
    FT_BarcodeScanner_CodeLengthMismatch, // 条形码位数与规定的不符
    FT_BarcodeScanner_FormatMismatch, // 条形码格式与规定的不符
    FT_BarcodeScanner_RackPositionError, // 样本架位置码或有无码出错
    //---------------------------数据库-------------------------------
    FT_Database_TableCreateError = 801, // 数据表创建失败
    FT_Database_TableDeleteError, // 删除表失败
    FT_Database_InsertError, // 数据插入失败
    FT_Database_QueryError, // 数据查询失败
    FT_Database_DeleteError, // 数据删除失败
    FT_Database_DataCorruption, // 数据损坏
    //-----------------------------时序-----------------------------
    FT_SystemSelfCheckFailed = 1101, // 整机自检失败
    FT_SequenceCmdParamError, // 时序指令参数错误
    FT_SequenceActionTimeout, // 时序动作超时
    FT_SequenceActionFailed, // 时序动作失败
    FT_InsufficientMaterial, // 耗材余量不足以满足本次检测
    FT_MissingReagentKit, // 无配套项目试剂
    FT_SequenceStartFailed, // 启动时序失败
    FT_SequencePauseFailed, // 暂停时序失败
    FT_SequenceStopFailed, // 停止时序失败
    FT_SequenceResumeFailed, // 恢复时序失败
    //--------------------------------系统--------------------------
    FT_WatchdogReset = 1301, // 看门狗重启
    FT_OtherErrorReset, // 其他错误重启
    FT_ClockNotSynchronized, // 时钟不同步
    FT_DiskSpaceLow_WriteFailed, // 硬盘空间不足，写入失败
    //----------------------------电源------------------------------
    FT_PowerLossDuringTest = 1401, // 测试过程中异常掉电
    //-----------------------------内存-----------------------------
    FT_MemoryAllocFailed, // 内存不足，分配失败
    FT_MemoryWriteFailed, // 内存空间不足，写入失败
    FT_CommandQueueFull, // 指令容器满
    FT_FLASHInitFailed, // FLASH初始化失败
    //------------------------------耗材回收----------------------------
    FT_WasteBinFull = 1601, // 废物箱已满
    FT_WasteLevelHighAlert, // 废物量高于报警线
    //------------------------------升级----------------------------
    FT_UpgradeFileChecksumError = 2001, // 升级文件校验失败
    FT_UpgradeFileNotExist, // 升级文件不存在
    FT_UpgradeFileOpenFailed, // 升级文件无法打开
    FT_VersionQueryFailed, // 版本查询失败
    FT_VersionQueryTimeout, // 版本查询超时
    FT_UpgradePackageIDError, // 升级包ID有误
    FT_UpgradeFailed, // 升级失败
    //-------------------------------参数---------------------------
    FT_ParameterRangeError = 2101, // 参数范围异常
    //-------------------------------测试---------------------------
    FT_TestQueueFull = 3001, // 测试队列已满
    FT_StartProcessFailed = 3002, // 启动测试流程失败
    FT_AddBatchFailed = 3003, // 添加批次失败
    FT_ParseBatchInfoFailed = 3004, // 解析批次信息失败
    FT_CurBatchExist = 3005, // 当前批次已存在
    FT_SampleSizeOut = 3006, // 样本量超出范围
    FT_NoSample = 3007, // 未检测到样本
    
};

//// 电机板1子模块枚举
//enum  MotorBoard1Submodule {
//    MTR1_Sub_CAN_Comm,     // 00 中位机CAN通信
//    MTR1_Sub_Right_Catcher,// 01 右抓手电机
//    MTR1_Sub_Left_Catcher, // 02 左抓手电机
//    MTR1_Sub_Cap_Z_Motor1, // 03 开盖Z电机1
//    MTR1_Sub_Cap_Z_Motor2, // 04 开盖Z电机2
//    MTR1_Sub_Cap_X_Motor,  // 05 开盖横轴电机
//    MTR1_Sub_Cap_Clamp,    // 06 开盖夹紧电机
//    MTR1_Sub_Cassette_X,   // 07 卡盒架移动电机
//    MTR1_Sub_Puncture,     // 08 刺破电机
//    MTR1_Sub_Magnet_Bar,   // 09 磁棒电机
//    MTR1_Sub_Magnet_Sleeve // 0A 磁套电机
//};

//// 电机板2子模块枚举
//enum  MotorBoard2Submodule {
//    MTR2_Sub_CAN_Comm,      // 00 中位机CAN通信
//    MTR2_Sub_Pump1,         // 01 泵1
//    MTR2_Sub_Pump2,         // 02 泵2
//    MTR2_Sub_Pipette_X_Motor, // 03 移液泵X轴电机
//    MTR2_Sub_Pipette_Y_Motor, // 04 移液泵Y轴电机
//    MTR2_Sub_Pipette_Z1_Motor,// 05 移液泵Z1轴电机
//    MTR2_Sub_Pipette_Z2_Motor,// 06 移液泵Z2轴电机
//    MTR2_Sub_Pipette_Theta,  // 07 移液变距电机
//    MTR2_Sub_Reagent_Cover,  // 08 试剂冷藏盖开合电机
//    MTR2_Sub_Scan_Motor     // 09 提取扫码电机
//};

//// 电机板3子模块枚举
//enum  MotorBoard3Submodule {
//    MTR3_Sub_CAN_Comm,       // 00 中位机CAN通信
//    MTR3_Sub_Gripper,       // 01 抓手
//    MTR3_Sub_PCR_Transfer,   // 02 PCR管传输电机
//    MTR3_Sub_Gripper_X,     // 03 抓手X轴电机
//    MTR3_Sub_Gripper_Y,     // 04 抓手Y轴电机
//    MTR3_Sub_Gripper_Z,     // 05 抓手Z轴电机
//    MTR3_Sub_Centrifuge    // 06 离心电机
//};

enum  ImpactScope
{
    IS_GlobalLevel,          // 全局：影响整个系统或应用程序，通常指的是跨越多个模块或组件的设置、配置、资源等
    IS_ModuleLevel,      // 模块级：影响范围限于一个独立的模块内部，包括该模块的所有组件，但不直接影响其他模块
    IS_ComponentLevel  // 组件级：影响局限于单一组件内部，
};


struct DevSubModuleItem
{
    quint8 uiModOrCompID;//故障子模块所属的逻辑模块或者组件ID
    quint8 uiImpactScope;//影响范围
};


enum ErrorLevel
{
    ERR_TRACE = 0,
    ERR_WARNING,
    ERR_ERROR,
    ERR_ALERT,
    ERR_EMERG
};

struct ErrorInfoItem
{
    QString strErrorCode;
    QString strExtraInfo;
};

bool findExactSubModuleItem(const QString &strSubModuleID,DevSubModuleItem& devSubModuleItem);
bool findSubModule(const quint8 &subModuleID,QString& strSubModule);
bool findErrorCodeID(const ErrorID &errorID,QString& strErrorID);
QString getErrorCode(MidMachineSubmodule subModule, ErrorID errorID);

#endif // ERRORCONFIG_H
