#include "coperationunit.h"

#include <QDataStream>
#include <QString>
#include "ccommunicationobject.h"
#include "publicfunction.h"
COperationUnit::COperationUnit(QObject *parent) : QObject(parent)
{
}

COperationUnit &COperationUnit::getInstance()
{
    static COperationUnit cCOperationUnit;
    return cCOperationUnit;
}

quint8 inline getDestinationIDFromMethodIDAndMachineID(const int &iMethodID, const int &iMachineID)
{
    quint8 quDestinationID = 0x00;
    if(iMachineID > Machine_Middle_Host && iMachineID <= Machine_UpperHost)
    {// 如果指定，则不再根据methodID查找
        quDestinationID = iMachineID;
        return quDestinationID;
    }
    EnumMachineID eMachineID = (EnumMachineID)iMachineID;
    if(iMethodID < 0x0100)
    {// 中位机
        quDestinationID = 0x00;
    }
    else if((iMethodID >= 0x0100 && iMethodID < 0x0500)
            || (iMethodID >= 0x0800 && iMethodID < 0x0A00))
    {//  要求电机指令必须指定machineID
        switch (eMachineID) {
        case Machine_Motor_1:
            quDestinationID = Machine_Motor_1;
            break;
        case Machine_Motor_2:
            quDestinationID = Machine_Motor_2;
            break;
        case Machine_Motor_3:
            quDestinationID = Machine_Motor_3;
            break;
        case Machine_Motor_4:
            quDestinationID = Machine_Motor_4;            
            break;
        default:// 以指令为准
            quDestinationID = Machine_Motor_1;
            break;
        }
    }
    else if(iMethodID >= 0x0500 && iMethodID < 0x0600)
    {
        quDestinationID = Machine_Temp_Ctrl;
    }
    else if(iMethodID >= 0x0600 && iMethodID < 0x0700)
    {//  /要求RFID指令必须指定machineID
        quDestinationID = iMachineID;
    }
    else if(iMethodID >= 0x0700 && iMethodID < 0x0800)
    {// 功能管理控制板
        quDestinationID = Machine_Function_manager_Ctrl;
    }
    else if(iMethodID >= 0x0E00 && iMethodID < 0x0F00)
    {//  PCR
        quDestinationID = Machine_PCR_MainCtrl;
    }
    else if(iMethodID >= 0x0F00 && iMethodID < 0x1000)
    {//  TEC，必须指定

    }
    else if(iMethodID >= 0x1000 && iMethodID < 0x1100)
    {//  FL
        quDestinationID = Machine_Fluorence;
    }
    else if(iMethodID >= 0x1DD9 && iMethodID < 0x1DE3)
    {//  中位机其他指令集
        quDestinationID = Machine_Middle_Host;
    }
    else if(iMethodID >= 0x2329 && iMethodID < 0x2337)
    {//  电源管理板
        quDestinationID = Machine_Power_Ctrl;
    }       
    else
    {// todo ,指令集未划分完成
        quDestinationID = iMachineID;
    }
    return quDestinationID;
}


void COperationUnit::sendCmd(const int &iMethodID, const quint8 & quiMachineID ,
                             const quint8 &quiSync )
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendCmdData(quDestinationID, iMethodID, CmdType_Command, quDestinationID, quiSync);
}

void COperationUnit::sendDataList(const int &iMethodID, const QStringList & strDataList,
                                  const quint8 & quiMachineID, const quint8 &quiSync , const quint8 & quCmdID)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendListData(quDestinationID, iMethodID, strDataList, quCmdID, quDestinationID, quiSync);
}

void COperationUnit::sendStringData(const int &iMethodID, const QString & strData,
                                    const quint8 & quiMachineID, const quint8 &quiSync , const quint8 & quCmdID)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendStringData(quDestinationID, iMethodID, strData,quCmdID, quDestinationID, quiSync);
}

 void COperationUnit::sendQByteData(const int &iMethodID, const QByteArray & byteData,
                                const quint8 &quiMachineID  , const quint8 &quiSync ,  const quint8 & quCmdID )
 {
      quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
       _SendData(quiMachineID, iMethodID, quCmdID, quDestinationID, quiSync, byteData);
 }

void COperationUnit::sendDigitalList(const int &iMethodID, const QStringList &strDataList, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendListData(quDestinationID, iMethodID, strDataList, CmdType_Reply, quDestinationID, 0x01, quiResult);
}

void COperationUnit::sendStringDigital(const int &iMethodID, const QString &strData, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendStringData(quDestinationID, iMethodID, strData,CmdType_Reply, quDestinationID, 0x01, quiResult);
}

void COperationUnit::sendResult(const int &iMethodID, const quint8 & quiMachineID, const quint8 & quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendCmdData(quDestinationID, iMethodID, CmdType_Reply, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendResultList(const int &iMethodID, const QStringList & strDataList, const quint8 & quiMachineID, const quint8 & quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendListData(quDestinationID, iMethodID, strDataList, CmdType_Reply, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendStringResult(const int &iMethodID, const QString & strData, const quint8 & quiMachineID, const quint8 & quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendStringData(quDestinationID, iMethodID, strData,CmdType_Reply, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendNotify(const int &iMethodID, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendCmdData(quDestinationID, iMethodID, CmdType_Notification, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendNotifyList(const int &iMethodID, const QStringList &strDataList, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendListData(quDestinationID, iMethodID, strDataList, CmdType_Notification, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendStringNotify(const int &iMethodID, const QString &strData, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendStringData(quDestinationID, iMethodID, strData,CmdType_Notification, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendBulletin(const int &iMethodID, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendCmdData(quDestinationID, iMethodID, CmdType_Bulletin, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendBulletinList(const int &iMethodID, const QStringList &strDataList, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendListData(quDestinationID, iMethodID, strDataList, CmdType_Bulletin, quDestinationID, 0x00, quiResult);
}

void COperationUnit::sendStringBulletin(const int &iMethodID, const QString &strData, const quint8 &quiMachineID, const quint8 &quiResult)
{
    quint8 quDestinationID = getDestinationIDFromMethodIDAndMachineID(iMethodID, quiMachineID);
    _AddSendStringData(quDestinationID, iMethodID, strData,CmdType_Bulletin, quDestinationID, 0x00, quiResult);
}

void COperationUnit::_AddSendCmdData(const quint8 & quiMachineID, const int &iMethodID, const quint8 & quCmdID,
                                     const quint8 & quObjectID, const quint8 & quSync, const quint8 & quiResult)
{
    _SendData(quiMachineID, iMethodID, quCmdID, quObjectID, quSync, "", quiResult);
}

void COperationUnit::_AddSendListData(const quint8 & quiMachineID, const int &iMethodID, const QStringList  & strDataList,
                                      const quint8 & quCmdID, const quint8 & quObjectID, const quint8 & quSync, const quint8 & quiResult)
{
    QString strDataString = "";
    if(strDataList.length() > 1)
    {
        strDataString = "[" + strDataList.join(",") + "]";
    }
    else if(strDataList.length() == 1)
    {
        strDataString = "[" + strDataList[0] + "]";
    }
    else
    {

    }
    _SendData(quiMachineID, iMethodID, quCmdID, quObjectID, quSync, strDataString.toLocal8Bit(), quiResult);
}

void COperationUnit::_AddSendStringData(const quint8 & quiMachineID, const int &iMethodID, const QString &strData,
                                        const quint8 & quCmdID, const quint8 & quObjectID, const quint8 & quSync, const quint8 & quiResult)
{
    QString  strDataString = "";
    if(!strData.isEmpty())
    {
        strDataString = "[" + strData + "]";
    }
    else
    {

    }
    _SendData(quiMachineID, iMethodID, quCmdID, quObjectID, quSync, strDataString.toLocal8Bit(), quiResult);
}

void COperationUnit::_SendData(const quint8 & quiMachineID, const int & iMethodID, const quint8 & quCmdID,
                               const quint8 & quObjectID, const quint8 & quSync,  const QByteArray & qPayload, const quint8 & quiResult)
{
    SCanBusDataStruct sSCanBusDataStruct;
    sSCanBusDataStruct.quMachineID = quiMachineID;
    sSCanBusDataStruct.quCmdID = quCmdID;
    sSCanBusDataStruct.quDestinationID = quObjectID;
    sSCanBusDataStruct.quMethonID = iMethodID;
    sSCanBusDataStruct.quSync = quSync;
    sSCanBusDataStruct.quResult = quiResult;
    sSCanBusDataStruct.qbPayload = qPayload;
    sSCanBusDataStruct.quFrameSeq = 0;
    QByteArray qSendByteArray = GetSendData(sSCanBusDataStruct);
    CCommunicationObject::getInstance().sendMessageToMachine(qSendByteArray, quObjectID);
    #ifndef ShortOutPutLog
        if(iMethodID != Method_heart_beat)
        {
            qDebug() << "send data" << QString("%1").arg(quObjectID, 2, 10, QChar('0')) << qSendByteArray.toHex(':').toUpper();
        }
    #endif
}

