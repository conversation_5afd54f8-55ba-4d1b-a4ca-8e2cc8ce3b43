#ifndef REAGENT_H
#define REAGENT_H
#define REAGENT_MAX_SIZE 4
#include <QObject>
#include <QDate>
#include <QMap>
#include <QVector>
#include <QMutex>
#include "publicconfig.h"

struct TestProjectInfo
{
    QString strProjID;
};

enum REAGENT_STATE
{
    REGST_VALID,//试剂可用
    REGST_EMPTY,//试剂空
    REGST_INVALID,//试剂不可用(过效期，过开瓶有效期)
};



struct SubPackData //需分装的试剂信息
{
    QString strProjID;//分装的项目ID
    quint8 uiRowIndex;//分装的冻干粉行位置
    quint8 uiColumnIndex;//分装的试剂列位置
    quint8 uiDuilentRowIndex;//分装所需的稀释液位置
    bool bNeedPunchDuilent;//是否需要刺破稀释液
    bool bNeedOpenCap;
    bool bNeedCloseCap;
    quint8 uiAmplifyCompIndex;//组分

    QString printInfo() const
    {
        QString info;
        info += "strProjID:" + strProjID + " ";
        info += "uiRowIndex:" + QString::number(uiRowIndex) + " ";
        info += "uiColumnIndex:" + QString::number(uiColumnIndex) + " ";
        info += "uiDuilentRowIndex:" + QString::number(uiDuilentRowIndex) + " ";
        info += "bNeedPunchDuilent:" + QString::number(bNeedPunchDuilent) + " ";
        info += "bNeedOpenCap:" + QString::number(bNeedOpenCap) + " ";
        info += "bNeedCloseCap:" + QString::number(bNeedCloseCap) + " ";
        info += "uiAmplifyCompIndex:" + QString::number(uiAmplifyCompIndex);
        return info;
    }
};

/**
 * @brief The Reagent class扩增试剂操作类-
 */
class Reagent
{
public:
    static Reagent &getInstance();
    
    /**
     * @brief GetNextAvrReagent 根据当前项目ID获取当前可用的试剂获取位置
     * @param uiProjID 项目ID
     * @param uiRowIndex
     * @param uiColumnIndex
     * @param uiAmplifyCompIndex 组分索引
     * @return
     */
    bool GetNextAvrReagent(QString strProjID, quint8& uiRowIndex, quint8& uiColumnIndex,quint8& uiAmplifyCompIndex);//获取下一个可用的特定项目试剂位信息

    /**
     * @brief GetNextAvrReagent 根据当前项目ID获取当前可用的试剂获取位置
     * @param data1 获取第一个试剂信息
     * @param data2 获取第二个试剂信息
     * @return
     */
    bool GetNextAvrReagent(SubPackData& data1, SubPackData& data2);//获取下一个可用的特定项目试剂位信息

    /**
     * @brief AddReagentBox 特定试剂位添加扩增信息
     * @param uiColumnIndex
     * @param reagentBox
     */
    void AddReagentBox(quint8 uiColumnIndex, ReagentBox reagentBox,int iNeedWriteRFID=1);//新增特定位试剂卡盒

    /**
     * @brief AddVirtualReagentBox 添加虚拟耗材
     * @param uiColumnIndex
     * @param strProjID
     * @param uiCompNum
     * @param uiRemain
     */
    void AddVirtualReagentBox(quint8 uiColumnIndex, QString strProjID,  quint8 uiCompNum, quint8 uiRemain);

    void RemoveReagentBox(quint8 uiColumnIndex);//删除特定位试剂卡盒

    /**
     * @brief IsReagentEnough
     * @param uiProjID
     * @param uiTotalUseSize 总使用量
     * @return
     */
    bool IsReagentEnough(QString strProjID, quint8 uiTotalUseSize);//试剂是否足够

    /**
     * @brief ConsumeReagent 使用特定项目试剂，更新剩余数量及下次可用位置信息
     * @param uiColumnIndex
     * @param uiUseSize
     */
    void ConsumeReagent(quint8 uiColumnIndex, quint8 uiUseSize = 1);

    //1.返回所有试剂信息，更新UI
    /**
     * @brief GetReagentBallSubPackDatas 返回冻干球需要分装的数据信息
     * @return
     */
    QMap<QString, QMap<quint8, QVector<SubPackData>>> GetReagentBallSubPackDatas();

    QVector<SubPackData> GetReagentBallSubPackDatasVect();

    void _ClearSubPackDatas();

    /**
     * @brief AddReagentBallSubPackDataToVect 确认并添加需要分装的试剂孔位信息
     * @param uiColumnIndex
     * @param uiSubPackStartIndex
     * @param uiSubPackEndIndex
     * @param reagentBox     试剂信息
     */
    void _AddReagentBallSubPackDataToVect(quint8 uiColumnIndex, quint8 uiSubPackStartIndex,
                                         quint8 uiSubPackEndIndex, QString& strProjID, ReagentBox reagentBox);

    /**
     * @brief CheckReagentStatus 检查试剂状态
     * @return
     */
    void CheckReagentStatus();  

    /**
     * @brief UpdateReagentStatus 更新试剂有效期
     * @param data 分装试剂信息
     * @return
     */
    void UpdateReagentStatus(SubPackData data);     

    /**
     * @brief UpdateReagentPunchInfo 更新试剂刺破信息
     * @param data 分装试剂信息
     * @return
     */
    void UpdateReagentPunchInfo(SubPackData data);  

    /**
     * @brief SetSystemBuildBusyStatus 设置系统构建状态(判断试剂是否在剩余量足够的前提卸载了)
     * @param bIsBusy  是否繁忙
     * @return
     */
    void SetSystemBuildBusyStatus(bool bIsBusy);         
private:
    Reagent();

    /**
     * @brief _ConsumeAction 消耗试剂
     * @param reagentBox
     * @param uiUseSize
     * @param uiColumnIndex
     */
    void ConsumeAction(ReagentBox reagentBox, quint8 uiUseSize, quint8 uiColumnIndex);

    /**
     * @brief _GetNextAvrPos 获取当前试剂的可用位置所在行
     * @param reagentBox
     * @return
     */
    quint8 _GetNextAvrPos(ReagentBox &reagentBox);

    /**
     * @brief _GetCeilIndex 获取指定数值目前所在的索引位置
     * @param uiNum 目标数值
     * @param uiSingleHoleCapacity 单孔人分数
     * @return
     */
    quint8 _GetCeilIndex(quint8 uiNum, quint8 uiSingleHoleCapacity);

    quint8 _GetFloorIndex(quint8 uiNum, quint8 uiSingleHoleCapacity);

    /**
     * @brief _GetFloorSection 获取刺破试剂开始和结束位置
     * @param pReagentBox     试剂信息
     * @param subPackStart    刺破索引开始
     * @param subPackEnd      刺破索引结束
     * @param uiTotalUseSize  使用的试剂量
     * @return  
     */
    void _GetFloorSection(ReagentBox* pReagentBox,quint8& subPackStart,quint8& subPackEnd,quint8 uiTotalUseSize);

        /**
     * @brief _NeedSwitchGroup 获取刺破试剂开始和结束位置
     * @param pReagentBox     试剂信息
     * @param uiTotalUseSize  使用的试剂量
     * @return  需要刺破
     */
    bool _NeedSwitchGroup(ReagentBox* pBox, quint8 uiTotalUseSize);
    /**
     * @brief _UpdateStatus 更新锁和灯状态
     * @param state     试剂状态
     * @param cType     RFID映射类型
     * @return  
     */
    void _UpdateStatus(REAGENT_STATE status,RFIDConsumableType cType);  

    /**
     * @brief _CheckReagentEnough 检查试剂是否足够
     * @param uiIndex   类型对应的索引
     * @return  返回试剂状态
     */
    REAGENT_STATE _CheckReagentEnough(quint16 uiIndex);

    /**
     * @brief _SortReagentLists 试剂排序(同一个项目根据时间和余量排序)
     * @param strProjID    项目ID
     * @param list         排序后的列表
     * @return  返回试剂状态
     */
    void _SortReagentLists(const QString& strProjID,QList<quint8>* list);

    /**
     * @brief _SortReagentLists 获取下一个试剂位置
     * @param reagentBox         试剂信息
     * @param uiNextAvrRowPos    试剂行条位置
     * @param uiSampleCompNum    样本组分索引
     * @return  是否获取成功
     */
    bool _GetNextAvrRowPos(ReagentBox reagentBox,quint8& uiNextAvrRowPos,quint8 uiSampleCompNum);    
protected:
    bool _IsReagentValid(ReagentBox &reagentBox);

private:
    QMap<quint8, ReagentBox> m_qReagentMap;//key is column Index
    QMap<QString, QList<quint8>> m_qRelationshipMap;//当前装载的试剂信息,key是项目ID, value是同项目的所有试剂列索引
    QVector<SubPackData> m_qReagentBallSubPackDataVect;
//    QVector<SubPackData> m_qDuilentSubPackDataVect;

    QMap<QString, QMap<quint8, QVector<SubPackData>>> m_qReagentSubPackDataMap;//external key is strProjID, internal key is compIndex
    QMutex m_qMutex;
    QMap<quint8, quint8> m_qReagentFloorCompNum;//组分对应试剂条用到的孔位数
    bool m_bSystemBuildBusyStatus; //系统构建状态
};

#endif // REAGENT_H
