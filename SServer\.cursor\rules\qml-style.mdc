---
description: 
globs: 
alwaysApply: true
---
---
description: Comprehensive QML code-style & best practices
globs: ["**/*.qml", "**/*.js"]
alwaysApply: false            # 只有编辑/查看 QML/JS 时才注入
---

# 1. 文件与组件命名
- 组件文件采用 `PascalCase.qml`；目录按功能分层，避免扁平。
- 一个文件只暴露**一个**根组件；若仅作工具脚本，使用 `*.js` 并置于 `scripts/`。
- 根元素推荐 `Item` 或最贴近业务的可复用基类；忌直接用 `Rectangle` 充当容器。

# 2. import 顺序
1. 官方模块（QtQuick / QtQuick.Controls / QtQuick.Layouts 等），版本号写全：`import QtQuick 2.12`
2. 第三方模块
3. 工程内自定义模块  
> 每类之间留一空行。

# 3. 属性与成员变量
| 场景 | 规则 |
| --- | --- |
| **id**          | 使用 `camelCase`；表意清晰，如 `buttonSubmit`；避免缩写。 |
| **property**    | 数据属性 ➜ `camelCase`；常量加 `readonly`：<br>`readonly property int defaultTimeout: 5000` |
| **私有成员**    | 组件内部临时变量前加 `m_`：`property var m_iCache` |
| **别名**        | `alias` 以被映射对象的语义命名：`property alias text: label.text` |
| **枚举**        | 用 `QtObject { id: Roles; readonly property int FooRole: 1 }` 固定枚举容器 |
| **变量**    | 变量总是以类型开始: strName, iCount, buttonOpen|

# 4. 函数 / Handler
- JS 函数名用 `camelCaseVerbNoun()`；只在 `.js` 或 `<function>` 块中书写逻辑。
- 信号处理直接写 `onClicked: doSomething()`；复杂 handler 抽离到函数。
- 尽量返回值，避免隐式修改外部状态。

# 5. 信号 & 槽
- 信号名用过去式或被动式：`signal sigSubmitted(string id)`
- 槽（处理函数）对应信号：`function slotSubmitted(id) { … }`
- 避免在信号中携带复杂对象；若需要可传 `QtObject` 封装。

# 6. 语法 & 格式化
- **分号可省略**，但多语句同行必须加：`foo(); bar()`  
  > 推荐统一“不写”分号，除非静态检查器有特殊要求。
- 每行 ≤ 120 列；属性链过长时换行对齐点 `.`：
  ```qml
  anchors.right: parent
  anchors.rightMargin: theme.spacingLarge
  对象字面量 { … } 前留空格，内部键值对后加空格：{ x: 10, y: 20 }

- 多属性声明时，按 geometry → layout → visual → behavior → data 排序。

7. 布局与性能
- 尽量使用 ColumnLayout / RowLayout 代替手写 anchors 网。
- 大列表 ➜ ListView + DelegateRecycler 或 Repeater + Loader，避免巨型静态数组。
- 避免在绑定表达式里做 heavy JS 计算；改用 Connections 或 Binding 缓存结果。

8. 可访问性 & 国际化
- 所有可交互控件加 accessible.name / accessible.description。
- UI 文本走 qsTr()；不在 JS 逻辑里硬编码中文。
- 带参数的字符串用 qsTr("Delete %1 item(s)").arg(count)。

9. 注释
- // 单行注释放在被注释代码上方；块注释仅用于文件头和复杂算法解释。
- TODO/FIXME 统一格式：// TODO(username YYYY-MM-DD): 描述任务。

10. 静态检查 & CI
- 开启 qmlformat 自动格式化（或在 Git 钩子中运行）。
- ESLint/TSLint（若用 QML JS parser）配合同款规则。
- 代码审查中，PR 若违反本规范可拒绝合并。

11. 禁止事项（❌）
- 在 UI 线程执行 XMLHttpRequest 同步请求。
- 在 Component.onCompleted 里做耗时 I/O。
- 直接操作 parent 的几何属性，改用信号或依赖注入。



- 滥用 forceActiveFocus() 造成无障碍冲突。