#ifndef CTCPSERVERTHREAD_H
#define CTCPSERVERTHREAD_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTcpServer>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QTimer>
#include <QTime>
#include <QQueue>
#include <atomic>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CTcpServerThread : public QObject
{
    Q_OBJECT
public:
    explicit CTcpServerThread(QObject *parent = nullptr);
    explicit CTcpServerThread(int iPort, QHostAddress strHostIP = QHostAddress::Any,
                              QString strHostMac = "",
                              QObject *parent = nullptr);
    ~CTcpServerThread();

signals:
    void sigInitServer();
    void signalReadTimer();
    void sigReciveMessage(QByteArray qByteArray);
    void sigACKOut(QString strIP, int iPort);

    void sigNewNetworkConnect(QString strIP, int iPort, bool bConnect);
    void sigNewNetworkDisConnect(QString strIP, int iPort);

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    void slotWaitACK(quint16 iFrameNumber);
    void slotSendAckBack(QByteArray qSendMsgAarry);
    void slotSendMessage(QByteArray qMsgBtyeArray);
    void slotReInitServer(QHostAddress qHostAddress, QString strHostMac );
private slots:
    void _slotInitServer();
    void _slotStartTimer();
    void _slotReadFramesTimer();
    void slot_newconnect(); //建立新连接的槽
    void slot_disconnect(); //取消连接的槽
    void slot_recvmessage();
    void _slotReSendTimer();
private:
    void _sendFrameData(QByteArray &qSendMsgAarry, bool bACKSend = false);
    void _toListen();
    void _reSetFrameNumber(QByteArray & qByteArray);// 重置发送帧号及CRC
private:

    QTimer *m_pReadFramesTimer;
    QThread *m_pThread;
    QTcpServer *m_pTCPServer; //QTcpServer服务器
    QTcpSocket *m_pConnectTcpSocket; //与客户端连接套接字，只连接一个
    int m_iConnected;// 0: no 1: one
    int m_iPort;
    int m_iDisConnectedCount;// 链接计数
    QHostAddress m_qHostAddress;
    QString m_strHostMacAddress;
    QString m_strClientName;// 仅用于输出调试
    // 数据
    QByteArray m_sCurrentSendMessage;
    QByteArray m_qSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;
    // 重发队列
    QByteArray m_qReSendMessageList[BUFFER_SIZE];
    std::atomic<int> m_iReSendWriteIndex{0};
    std::atomic<int> m_iReSendReadIndex{0};
    int m_iReSendNextWriteIndex;
    int m_iReSendCurrentWriteIndex;
    // 重发机制中的环形队列
    bool m_bWaitAck[BUFFER_SIZE];
    QTimer *m_pResendTimer;
    MessageInfo m_sRingMessageInfoList[BUFFER_SIZE];
    std::atomic<int> m_iRingWriteIndex{0};
    std::atomic<int> m_iRingReadIndex{0};
    int m_iRingCurrentWriteIndex;
    int m_iRingNextWriteIndex;
    int m_iRingCurrentReadIndex;
    quint16 m_iSeqNumber;// 帧号 0-65535
    char *m_pFramePos;
    //
    quint8 m_iCmdID;
    quint16 m_iMethodID;
    quint8 m_iDestinationID;
    quint8 m_iSourceID;
    quint8 m_iSync;
    // 原子标志位，防止双保险机制竞争
    QAtomicInt m_isReading{0};
};

#endif // CTCPSERVERTHREAD_H
