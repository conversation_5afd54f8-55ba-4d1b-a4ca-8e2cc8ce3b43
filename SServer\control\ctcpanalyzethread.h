/*****************************************************
  * Copyright: 万孚生物
  * Author: qliu
  * Date: 2023-10-11
  * Description: 协议解析线程，与上位机同一份代码
  * -----------------------------------------------------------------
  * History:
  *
  *
  *
  * -----------------------------------------------------------------
  ****************************************************/
#ifndef CTCPANALYZETHREAD_H
#define CTCPANALYZETHREAD_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTime>
#include <QMap>
#include <QTimer>
#include "publicconfig.h"
#include "error/errorconfig.h"

class CTcpAnalyzeThread : public QObject
{
    Q_OBJECT
public:
    explicit CTcpAnalyzeThread(QObject *parent = nullptr);
    ~CTcpAnalyzeThread();

signals:
    void sigWaitACK(quint16 iFrameNumber);
    void sigSendACKBack(QByteArray qSendMsgByteArray);// 发送数据
    void sigReciveMessage(QByteArray qReciveMsgByteArray);// 完整帧

    /**
     * @brief sigError 异常信号
     * @param errorID 异常ID
     * @param strExtraInfo 补充信息
     */
    void sigError(ErrorID errorID, QString strExtraInfo);

public slots:
    void slotReciveOriginalMessageData(QByteArray qReadMsgArray);// 接受原始数据
    void slotCleanupReceivedPackets();
private:
    static void* _createThreadHandleList(void* arg);
    void _handleReceiveList();
    void _addReadData(const QByteArray &qMsgBtyeArray);
    bool _isValidFrameHeader(const QByteArray& buffer);
    uint64_t _combineIDs( uint16_t cmd_type, uint16_t frame_id, uint16_t command_id);
    void _processPacket(const quint16 &iCmdType, const quint16 &iSeqNumber, const quint16 &iMethodID);


private:
    QByteArray m_qbReadBuffArray;
    int m_iRedaBuffArrayLength;
    int m_iReadPayloadLength;
    quint16 m_iReadSeqNumber;
    bool m_bReCalculation;

    char *pLength;
    QList<QByteArray> m_qReciveDataByteArrayList;// 解析后完整帧
    QByteArray m_qCurrentSendPortDataByte;
    QByteArray m_qCurrentReciveDataByteArray;// 获取插入List临时变量
    QByteArray m_qGetReciveDataByteArray;// 获取的临时变量


    std::condition_variable m_conditionVariable;
    std::mutex m_mutex;
    QByteArray m_qSendMessageInfoList[BUFFER_SIZE];//原始读取数据  m_qCanReadMsgList
    std::atomic<int> m_iWriteIndex{0};
    std::atomic<int> m_iReadIndex{0};
    int m_iNextWriteIndex;
    int m_iCurrentWriteIndex;

    QByteArray m_qFindHeaderByteArray;// 去除头部，为了查找下一帧头
    int m_iMinPayloadLength;
    QByteArray m_qCRCByteArray;
    quint16 m_iReadDataCRC;
    quint16 m_iGetCRC;
    bool m_bOk;
    quint32 m_iMethodACK;

    quint16 m_iFrameNumber;
    bool m_bSendDataCount;
    bool m_bReadMsgCount;
    int m_iPayloadMaxLength;
    //
    bool m_bThreadExit;
    int m_iiPackID;
    // 相同帧包过滤
    QMutex m_qSameFrameMutex;
    QMap<uint64_t, QDateTime> m_qReceivedPackets;
    QTimer *m_pCleanupTimer ;

};

#endif // CTCPANALYZETHREAD_H
